ENV['MATCH_GIT_PRIVATE_KEY'] = '/Users/<USER>/.ssh/id_rsa'
git_url("https://gitlab.com/tts/certificates")

storage_mode("git")

type("development") # The default type, can be: appstore, adhoc, enterprise or development

app_identifier(["tts.com.ThiTruongSi", "com.thitruongsi.buyer.sandbox"])
username("<EMAIL>") # Your Apple Developer Portal username
clone_branch_directly(true)
git_private_key("")
keychain_name("")
# For all available options run `fastlane match --help`
# Remove the # in the beginning of the line to enable the other options

# The docs are available on https://docs.fastlane.tools/actions/match
