fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

### bump

```sh
[bundle exec] fastlane bump
```

Bump build numbers, and set the version to match the pacakage.json version.

----


## Android

### android staging

```sh
[bundle exec] fastlane android staging
```

Submit a new Staging Build

### android beta

```sh
[bundle exec] fastlane android beta
```

Submit a new Beta Build

----


## iOS

### ios beta

```sh
[bundle exec] fastlane ios beta
```



### ios staging

```sh
[bundle exec] fastlane ios staging
```



### ios release

```sh
[bundle exec] fastlane ios release
```



----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
