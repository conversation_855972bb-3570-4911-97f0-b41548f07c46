default_platform(:android)

desc 'Android: Increment versionCode and set versionName to package.json version.'
package = load_json(json_path: "./package.json")

private_lane :inc_ver_and do
  increment_version_code(
    gradle_file_path: "./android/app/build.gradle",
  )

  increment_version_name(
    gradle_file_path: "./android/app/build.gradle",
    version_name: package['version']
  )
end


desc 'iOS: Increment build number and set the version to package.json version.'
private_lane :inc_ver_ios do
package = load_json(json_path: "./package.json")
  increment_build_number(
    xcodeproj: './ios/' + package['name'] + '.xcodeproj'
  )
  increment_version_number(
    xcodeproj: './ios/' + package['name'] + '.xcodeproj',
    version_number: package['version']
  )
end


desc 'Bump build numbers, and set the version to match the pacakage.json version.'
lane :bump do
  inc_ver_ios
  inc_ver_and
end

platform :android do


  desc "Submit a new Staging Build"
  lane :staging do
    yarn(
      command: "set:env:staging"
    )
    increment_version_code(
      gradle_file_path: "./android/app/build.gradle",
    )
    gradle(
      project_dir: "android",
      # task: "clean assembleRelease"
      task: "assemble",
      build_type: "releaseStaging",
      properties: {
        # "android.injected.signing.store.file" => "/Users/<USER>/Works/TTS/certificates/android_buyer_thitruongsi.jks",
        "android.injected.signing.store.file" => "/Users/<USER>/credentials/android_dsbuyer_thitruongsi.jks",
        "android.injected.signing.store.password" => "AOSteam1102!",
        "android.injected.signing.key.alias" => "thitruongsi",
        "android.injected.signing.key.password" => "AOSteam1102!",
      }
    )
    firebase_app_distribution(
        app: "1:1096063466071:android:ca7e134dbb3c7065bf4112",
        testers: "<EMAIL>,<EMAIL>,<EMAIL>",
        release_notes: "Staging version"
    )
  end

  desc "Submit a new Beta Build"
  lane :beta do
    yarn(
      command: "set:env:release"
    )
    increment_version_code(
      gradle_file_path: "./android/app/build.gradle",
    )
    gradle(
      project_dir: "android",
      # task: "clean assembleRelease"
      task: "assemble",
      build_type: "Release",
      properties: {
        # "android.injected.signing.store.file" => "/Users/<USER>/Works/TTS/certificates/android_buyer_thitruongsi.jks",
        "android.injected.signing.store.file" => "/Users/<USER>/credentials/android_dsbuyer_thitruongsi.jks",
        "android.injected.signing.store.password" => "AOSteam1102!",
        "android.injected.signing.key.alias" => "thitruongsi",
        "android.injected.signing.key.password" => "AOSteam1102!",
      }
    )
    firebase_app_distribution(
        app: "1:1096063466071:android:ca7e134dbb3c7065bf4112",
        testers: "<EMAIL>,<EMAIL>,<EMAIL>",
        release_notes: "Lots of amazing new features to test out!"
    )
  end
  
 
end

platform :ios do
  lane :beta do
    yarn(
      command: "set:env:release"
    )
    increment_build_number(
      xcodeproj: './ios/' + package['name'] + '.xcodeproj'
    )
    # sync_code_signing(type: "appstore")
    build_app(scheme: "dsbuyer",
              workspace: "ios/dsbuyer.xcworkspace",
              include_bitcode: false,
              include_symbols: true,
              export_method: "development",
              configuration: "Release",
              clean: false
            )

    firebase_app_distribution(
            app: "1:1096063466071:ios:9f026f741bca29c9bf4112",
            testers: "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
            release_notes: "Beta"
        )
  end

  lane :staging do
    yarn(
      command: "set:env:staging"
    )
    increment_build_number(
      xcodeproj: './ios/' + package['name'] + '.xcodeproj'
    )
    build_app(scheme: "dsbuyer",
              workspace: "ios/dsbuyer.xcworkspace",
              include_bitcode: true,
              include_symbols: true,
              export_method: "development",
              configuration: "Staging",
              clean: false
            )
    firebase_app_distribution(
              app: "1:1096063466071:ios:9f026f741bca29c9bf4112",
              testers: "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
              release_notes: "Staging"
          )
  end

  lane :release do
    yarn(
      command: "set:env:release"
    )
    increment_build_number(
      xcodeproj: './ios/' + package['name'] + '.xcodeproj'
    )
    sync_code_signing(type: "appstore")    
    build_app(scheme: "dsbuyer",
              workspace: "ios/dsbuyer.xcworkspace",
              include_bitcode: true,
              include_symbols: true,
              export_method: "app-store",
              configuration: "Release",
            )
    upload_to_app_store 
  end
end
