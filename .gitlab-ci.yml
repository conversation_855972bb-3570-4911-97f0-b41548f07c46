services:
  - docker:dind

variables:
  DOCKER_DRIVER: overlay2

.setup-ssh-agent: &setup-ssh-agent
  before_script:
    - 'which ssh-agent || (apk add --no-cache openssh)'
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan gitlab.com >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts

stages:
  - build
  - package
  - deploy
  - develop
  - release
  - staging
  - production

build:develop:yarn:
  <<: *setup-ssh-agent
  image: node:18-alpine
  stage: build
  variables:
    API_HOSTNAME: https://api-sandbox.thitruongsi.com
    SITE_URL: https://m2.thitruongsi.com
    DESKTOP_SITE_URL: https://v2-sandbox.thitruongsi.com
    APP_ENV: staging
  cache: {paths: ['node_modules/']}
  artifacts: {paths: ['node_modules/', 'web/dist/']}
  script:
    - apk add --no-cache git
    - yarn install && yarn build:web:staging
  except:
    - 'tags'
  only: ['develop']

build:production:yarn:
  <<: *setup-ssh-agent
  image: node:18-alpine
  stage: build
  variables:
    API_HOSTNAME: https://api.thitruongsi.com
    SITE_URL: https://m.thitruongsi.com
    DESKTOP_SITE_URL: https://thitruongsi.com
    APP_ENV: production
  cache: {paths: ['node_modules/']}
  artifacts: {paths: ['node_modules/', 'web/dist/']}
  script:
    - apk add --no-cache git
    - yarn install && yarn build:web
  except:
    - 'tags'
  only: ['main']

package:develop:docker:
  image: docker:stable
  stage: package
  variables:
    DOCKER_DRIVER: overlay2
  script:
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" "$CI_REGISTRY" --password-stdin
    - export TAG_NAME=`date +"%Y%m"`-${CI_PIPELINE_ID}
    - docker build -t $CI_REGISTRY_IMAGE:$TAG_NAME .
    - docker push $CI_REGISTRY_IMAGE:$TAG_NAME
  only: ['develop']
  except:
    - 'tags'
  dependencies:
    - build:develop:yarn

package:production:docker:
  image: docker:stable
  stage: package
  variables:
    DOCKER_DRIVER: overlay2
  script:
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" "$CI_REGISTRY" --password-stdin
    - export TAG_NAME=`date +"%Y%m"`-${CI_PIPELINE_ID}
    - docker build -t $CI_REGISTRY_IMAGE:$TAG_NAME .
    - docker push $CI_REGISTRY_IMAGE:$TAG_NAME
  only: ['main']
  except:
    - 'tags'
  dependencies:
    - build:production:yarn

deploy:develop:k8s:
  image: thitruongsi/ci-deploy-mini
  stage: develop
  variables:
    GIT_STRATEGY: none
    KUBE_NAMESPACE: default
    PROJECT_NAME: dropshipbuyer-dev
    MEM_MIN: 64Mi
    MEM_MAX: 256Mi
    CPU_MIN: 50m
    CPU_MAX: 500m
    SCALE_MIN: '1'
    SCALE_MAX: '1'
  before_script:
    - mkdir -p ~/.kube
    - echo $KSECRET | base64 -d > ~/.kube/config
    - export KUBECONFIG=~/.kube/config

    - kubectl config set-cluster $KCLUSTER
    - kubectl config set-context $KCONTEXT --namespace=development
    - kubectl config use-context $KCONTEXT
    - kubectl cluster-info
  script:
    - export DOCKER_TAG=`date +"%Y%m"`-${CI_PIPELINE_ID}
    - export TIMESTAMP=$(date +%s)
    - export PROJECT_NAME=dropshipbuyer-dev

    - wget https://raw.githubusercontent.com/thitruongsicom/ci-template/master/nginx.k8s.yaml?t=$TIMESTAMP -O nginx.k8s.yaml
    - echo "Deploy to k8s using image $CI_REGISTRY_IMAGE:$DOCKER_TAG"
    - find . -type f  -maxdepth 1 -name "*.k8s.yaml" -print0 | xargs -0 -n1 sh -c 'envsubst < $0 | kubectl apply -f -'
  only: ['develop']
  dependencies:
    - package:develop:docker

deploy:production:k8s:
  image: thitruongsi/ci-deploy-mini
  stage: production
  variables:
    GIT_STRATEGY: none
    KUBE_NAMESPACE: default
    PROJECT_NAME: $CI_PROJECT_NAME
    MEM_MIN: 128Mi
    MEM_MAX: 512Mi
    CPU_MIN: 50m
    CPU_MAX: 500m
    SCALE_MIN: '1'
    SCALE_MAX: '2'
  before_script:
    - mkdir -p ~/.kube
    - echo $KSECRET | base64 -d > ~/.kube/config
    - export KUBECONFIG=~/.kube/config
    - kubectl config set-cluster $KCLUSTER
    - kubectl config set-context $KCONTEXT --namespace=production
    - kubectl config use-context $KCONTEXT
    - kubectl cluster-info
  script:
    - export DOCKER_TAG=`date +"%Y%m"`-${CI_PIPELINE_ID}
    - export TIMESTAMP=$(date +%s)

    - wget https://raw.githubusercontent.com/thitruongsicom/ci-template/master/nginx.k8s.yaml?t=$TIMESTAMP -O nginx.k8s.yaml
    - echo "Deploy to k8s production using image $CI_REGISTRY_IMAGE:$DOCKER_TAG"
    - find . -type f  -maxdepth 1 -name "*.k8s.yaml" -print0 | xargs -0 -n1 sh -c 'envsubst < $0 | kubectl apply -f -'
  only: ['main']
  dependencies:
    - package:production:docker
