import React, {useCallback, useEffect} from 'react';
import {LinkingOptions, NavigationContainer} from '@react-navigation/native';
import NavigationProvider, {RootStackParamList} from '~utils/navigation/NavigationProvider';
import {QueryClient, focusManager, onlineManager} from '@tanstack/react-query';
import {AppStateStatus, Platform, AppState, StatusBar, Linking} from 'react-native';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import AuthProvider from '~contexts/auth.context';
import {ThemeProvider} from '@rneui/themed';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {BottomSheetModalProvider} from '@gorhom/bottom-sheet';
import {ToastProvider} from 'react-native-toast-notifications';
import 'dayjs/locale/vi';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import duration from 'dayjs/plugin/duration';
import rneTheme from '~utils/config/themes/rne-theme';
import GlobalImageViewerModal from '~components/shared/GlobalImageViewerModal';
// import ChatWoot from '~components/shared/ChatWoot';
import NetInfo from '@react-native-community/netinfo';
import LoginRequiredModal from '~components/shared/LoginRequiredModal';
import useAvoidSoftInput from '~hooks/useAvoidSoftInput';
import {MenuProvider} from 'react-native-popup-menu';

import * as Sentry from '@sentry/react-native';
import RNBootSplash from 'react-native-bootsplash';
import {createAsyncStoragePersister} from '@tanstack/query-async-storage-persister';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {PersistQueryClientProvider} from '@tanstack/react-query-persist-client';
import analytics from '@react-native-firebase/analytics';
import useAppOpen from '~hooks/useAppOpen';
import useDynamicLinks from '~hooks/useDynamicLinks';
import 'react-native-get-random-values';
import {PhoneHyperLinkBottomSheet} from '~utils/helpers/phone-hyperlink';
import {navigationRef} from '~utils/navigation/RootNavigation';

Sentry.init({
  ignoreErrors: ['Request failed with status code 401'],
  dsn: __DEV__ ? '' : 'https://<EMAIL>/29',
});

dayjs.locale('vi');
dayjs.extend(relativeTime);
dayjs.extend(customParseFormat);
dayjs.extend(duration);

onlineManager.setEventListener(setOnline => {
  return NetInfo.addEventListener(state => {
    setOnline(Boolean(state.isConnected));
  });
});

function onAppStateChange(status: AppStateStatus) {
  if (Platform.OS !== 'web') {
    focusManager.setFocused(status === 'active');
  }
}

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      staleTime: 0,
      retry: false,
    },
  },
});

if (__DEV__) {
  require('./src/utils/ReactotronConfig').default(queryClient);
}

const asyncStoragePersister = createAsyncStoragePersister({
  storage: AsyncStorage,
});

let App = () => {
  const routeNameRef = React.useRef<string>();
  const failedPath = React.useRef<string>();

  useEffect(() => {
    // if (Platform.OS === 'android') {
    //   // fix https://github.com/react-navigation/react-navigation/issues/10531
    //   enableLayoutAnimations(false);
    // }
    const subscription = AppState.addEventListener('change', onAppStateChange);

    return () => {
      subscription.remove();
    };
  }, []);

  useAvoidSoftInput();
  useAppOpen();
  useDynamicLinks();

  const linking: LinkingOptions<RootStackParamList> = {
    prefixes: ['http://localhost:3000', 'https://dropship.thitruongsi.com', 'https://dropship-sandbox.thitruongsi.com', 'tts.com.dsbuyer://'],
    config: {
      initialRouteName: 'MainTabs',
      screens: {
        MainTabs: {
          initialRouteName: 'Home',
          screens: {
            Order: 'orders',
            Account: 'user',
            Wishlist: 'saved-products',
          },
        },
        ProductDetail: 'product/:productId',
        Cart: 'cart',
        OrderConfirm: 'confirm-order',
        CreateCustomer: 'create-customer',
        CustomerSelect: 'customer-select',
        OrderDetail: 'orders/:orderId',
        FulfillmentEvents: 'orders/:orderId/fulfillment-events',
        Search: 'search-modal',
        SearchResult: 'search',
        CategoryDetail: 'cat/:slug/:categoryId',
        FAQ: 'faq',
        AccountInfoSettings: 'settings/account-info',
        AccountAndPayment: 'settings/payment',
        Settings: 'settings',
        AddBankAccount: 'settings/payment/add-bank-account',
        BankAccountList: 'settings/payment/bank-accounts',
        Customers: 'customers',
        Feedback: 'feedback',
        Transactions: 'transactions',
        Instruction: 'instruction',
        LoginCallback: 'login/callback',
        OrderPayment: 'order-payment',
        Notifications: 'notifications',
        AddToCart: 'add-to-cart/:productId',
        SupplierDetail: 'supplier/:shopId',
        Withdraw: 'payment/withdraw',
        EscrowTransactions: 'lich-su/tien-cho-doi-soat',
        WebView: 'webview',
        InviteFriends: 'invite-friends',
        CollectionDetail: 'collection/:collectionId',
        MyReferrers: 'my-referrers',
        Inspiration: 'inspiration',
        OrderRate: 'order/:orderId/rate',
        RateDetail: 'rate/:actionId/view',
        UpdateRating: 'order/:orderId/rate/update',
        AllReviews: 'product/:productId/all-reviews',
        OrderTickets: 'order/:orderId/tickets/new',
        SearchByImage: 'search-by-image',
        SupplierReviews: 'supplier/:shopId/all-reviews',
        PartnerShopProducts: 'partners/:partnerShopId/products',
        IdentityVerification: 'account/id-verification',
        TaxInformation: 'account/tax-information',
        ProductPickerGame: 'product-picker-game',
      },
    },
    // getInitialURL: async () => {
    //   const url = await Linking.getInitialURL();
    //   console.log('getInitialURL', url);
    //   if (url) {
    //     return url;
    //   }

    //   return 'tts.com.dsbuyer://';
    // },
    subscribe(listener) {
      const linkingSubscription = Linking.addEventListener('url', ({url}) => {
        if (!navigationRef?.isReady()) {
          failedPath.current = url;
          return;
        }
        return listener(url);
      });

      return () => {
        linkingSubscription.remove();
      };
    },
  };

  const handleNavigationReady = useCallback(async () => {
    setTimeout(() => {
      RNBootSplash.hide();
    }, 1000);
    routeNameRef.current = navigationRef?.getCurrentRoute()?.name;
    setTimeout(async () => {
      if (failedPath.current) {
        const canOpenURL = await Linking.canOpenURL(failedPath.current);
        if (canOpenURL) {
          Linking.openURL(failedPath.current);
        }

        failedPath.current = '';
      }
    }, 300);
  }, []);

  return (
    <GestureHandlerRootView style={{flex: 1}}>
      <ToastProvider>
        <NavigationContainer
          ref={navigationRef as any}
          onReady={handleNavigationReady}
          onStateChange={async () => {
            const previousRouteName = routeNameRef.current;
            const currentRouteName = navigationRef?.getCurrentRoute()?.name;

            if (previousRouteName !== currentRouteName) {
              await analytics().logScreenView({
                screen_name: currentRouteName,
                screen_class: currentRouteName,
              });
            }
            routeNameRef.current = currentRouteName;
          }}
          linking={linking}>
          <ThemeProvider theme={rneTheme}>
            <PersistQueryClientProvider client={queryClient} persistOptions={{persister: asyncStoragePersister}}>
              <AuthProvider>
                <SafeAreaProvider>
                  <MenuProvider>
                    <BottomSheetModalProvider>
                      <StatusBar barStyle={'dark-content'} backgroundColor="#fff" />
                      <NavigationProvider />
                      <GlobalImageViewerModal />
                      <LoginRequiredModal />
                      <PhoneHyperLinkBottomSheet />
                    </BottomSheetModalProvider>
                  </MenuProvider>
                </SafeAreaProvider>
              </AuthProvider>
            </PersistQueryClientProvider>
          </ThemeProvider>
        </NavigationContainer>
      </ToastProvider>
    </GestureHandlerRootView>
  );
};

export default App;
