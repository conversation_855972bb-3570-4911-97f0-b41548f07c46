import React, {useState} from 'react';
import {LinkingOptions, NavigationContainer, Route} from '@react-navigation/native';
import NavigationProvider, {RootStackParamList} from '~utils/navigation/NavigationProvider';
import {QueryClient, QueryClientProvider} from '@tanstack/react-query';
import {ReactQueryDevtools} from '@tanstack/react-query-devtools';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import AuthProvider from '~contexts/auth.context';
import {ThemeProvider as RNEThemeProvider} from '@rneui/themed';
import {ToastProvider} from 'react-native-toast-notifications';
import 'dayjs/locale/vi';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import duration from 'dayjs/plugin/duration';
import rneTheme from '~utils/config/themes/rne-theme';
import LoginRequiredModal from '~components/shared/LoginRequiredModal';
import * as Sentry from '@sentry/react';

// @ts-ignore
import TagManager from 'react-gtm-module';
import {env} from '~utils/config';
import {MenuProvider} from 'react-native-popup-menu';
import WebContainer from '~components/shared/web/WebContainer';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

import isInZaloMiniApp from '~utils/helpers/isInZaloMiniApp';
import DownloadAppSticky from '~components/shared/DownloadAppSticky';
import {PhoneHyperLinkBottomSheet} from '~utils/helpers/phone-hyperlink';
import inInTTSDropshipWebview from '~utils/helpers/isInTTSDropshipWebview';
import GlobalImageViewerModal from '~components/shared/GlobalImageViewerModal';

if (typeof navigator !== 'undefined' && !(navigator.userAgent.includes('Prerender') || navigator.userAgent.includes('prerender'))) {
  TagManager.initialize(env.WEB_GTM_ARGS);
}

Sentry.init({
  dsn: __DEV__ ? '' : 'https://<EMAIL>/29',
  // for finer control
  tracesSampleRate: 1.0,
  autoSessionTracking: false,
});

dayjs.locale('vi');
dayjs.extend(relativeTime);
dayjs.extend(customParseFormat);
dayjs.extend(duration);

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      staleTime: 0,
      retry: false,
    },
  },
});

const linking: LinkingOptions<RootStackParamList> = {
  prefixes: ['http://localhost:3000', 'https://dropship.thitruongsi.com', 'https://dropship-sandbox.thitruongsi.com'],
  config: {
    // initialRouteName: 'MainTabs',
    screens: {
      MainTabs: {
        // path: '/zapps/3803906043616540845',
        // initialRouteName: 'Home',
        screens: {
          Home: '',
          Order: 'orders',
          Account: 'user',
          Wishlist: 'saved-products',
          Inspiration: 'inspiration',
        },
      },
      ProductDetail: '/product/:productId',
      Cart: 'cart',
      OrderConfirm: 'confirm-order',
      CreateCustomer: 'create-customer',
      CustomerSelect: 'customer-select',
      OrderDetail: 'orders/:orderId',
      FulfillmentEvents: 'orders/:orderId/fulfillment-events',
      Search: 'search-modal',
      SearchResult: 'search',
      CategoryDetail: 'cat/:slug/:categoryId',
      FAQ: 'faq',
      AccountInfoSettings: 'settings/account-info',
      AccountAndPayment: 'settings/payment',
      Settings: 'settings',
      AddBankAccount: 'settings/payment/add-bank-account',
      BankAccountList: 'settings/payment/bank-accounts',
      Customers: 'customers',
      Feedback: 'feedback',
      Transactions: 'transactions',
      Instruction: 'instruction',
      LoginCallback: 'login/callback',
      OrderPayment: 'order-payment',
      Notifications: 'notifications',
      PrivacySettings: 'settings/privacy',
      AccountDeletion: 'settings/privacy/delete-account',
      SupplierDetail: 'supplier/:shopId',
      Withdraw: 'payment/withdraw',
      EscrowTransactions: 'lich-su/tien-cho-doi-soat',
      DownloadApp: 'tai-ung-dung',
      InviteFriends: 'invite-friends',
      Invite: 'invite',
      CollectionDetail: 'collection/:collectionId',
      Login: 'login',
      Register: 'register',
      PhoneVerification: 'phone',
      OrderRate: 'order/:orderId/rate',
      RateDetail: 'rate/:actionId/view',
      UpdateRating: 'order/:orderId/rate/update',
      AllReviews: 'product/:productId/all-reviews',
      SupplierReviews: 'supplier/:shopId/all-reviews',
      OrderTickets: 'order/:orderId/tickets/new',
      IdentityVerification: 'account/id-verification',
      TaxInformation: 'account/tax-information',
      WebView: 'webview',
      Appeton: 'appeton',
      WholeFoodsLink: 'wholefoodslink',
      MyReferrers: 'my-referrers',
      Lookback: 'lookback',
      PartnerShopProducts: 'partners/:partnerShopId/products',
      ProductPickerGame: 'product-picker-game',
    },
  },
};

const App = () => {
  const [isZaloMiniApp] = useState(isInZaloMiniApp());
  const [isTTSDropshipWebview] = useState(inInTTSDropshipWebview());

  return (
    <ToastProvider>
      <NavigationContainer
        linking={linking}
        documentTitle={{
          enabled: true,
          formatter: (options: Record<string, any> | undefined, route: Route<string> | undefined) => {
            return `${options?.title ?? route?.name}${route?.name === 'Home' ? '' : ' - TTS Dropship'}`;
          },
        }}>
        <RNEThemeProvider theme={rneTheme}>
          <QueryClientProvider client={queryClient}>
            <AuthProvider>
              <SafeAreaProvider>
                <WebContainer>
                  <MenuProvider>
                    <NavigationProvider />
                    <ReactQueryDevtools />
                    <LoginRequiredModal />
                    <PhoneHyperLinkBottomSheet />
                    <GlobalImageViewerModal />
                    {!isZaloMiniApp && !isTTSDropshipWebview && navigator?.userAgent && /Mobi/.test(navigator.userAgent) && <DownloadAppSticky />}
                  </MenuProvider>
                </WebContainer>
              </SafeAreaProvider>
            </AuthProvider>
          </QueryClientProvider>
        </RNEThemeProvider>
      </NavigationContainer>
    </ToastProvider>
  );
};

export default App;
