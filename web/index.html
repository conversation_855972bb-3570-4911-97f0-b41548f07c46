<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <link rel="apple-touch-icon" sizes="57x57" href="/static/icons/apple-icon-57x57.png" />
    <link rel="apple-touch-icon" sizes="60x60" href="/static/icons/apple-icon-60x60.png" />
    <link rel="apple-touch-icon" sizes="72x72" href="/static/icons/apple-icon-72x72.png" />
    <link rel="apple-touch-icon" sizes="76x76" href="/static/icons/apple-icon-76x76.png" />
    <link rel="apple-touch-icon" sizes="114x114" href="/static/icons/apple-icon-114x114.png" />
    <link rel="apple-touch-icon" sizes="120x120" href="/static/icons/apple-icon-120x120.png" />
    <link rel="apple-touch-icon" sizes="144x144" href="/static/icons/apple-icon-144x144.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/static/icons/apple-icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/static/icons/apple-icon-180x180.png" />
    <link rel="icon" type="image/png" sizes="192x192" href="/static/icons/android-icon-192x192.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/static/icons/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="96x96" href="/static/icons/favicon-96x96.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/static/icons/favicon-16x16.png" />
    <link rel="manifest" href="/static/manifest.json" />
    <meta name="msapplication-TileColor" content="#ffffff" />
    <meta name="msapplication-TileImage" content="/ms-icon-144x144.png" />
    <meta name="theme-color" content="#ffffff" />
    <title>TTS Dropship</title>

    <style>
      * {
        touch-action: manipulation;
      }
      html,
      body {
        width: 100%;
        height: 100%;
        max-height: 100vh;
        font-family: -apple-system, BlinkMacSystemFont, Helvetica, Roboto, Arial, sans-serif;
        font-size: 16px;
        line-height: 21px;
      }
      #app-root {
        display: flex;
        flex: 1 1 100%;
        height: 100%;
        /* max-width: 428px; */
        margin: 0 auto;
      }
      p {
        margin: 4px 0px;
        font-size: 16px;
      }
      input:focus,
      textarea:focus,
      button:focus {
        outline: none;
      }
      .product-body-html img {
        max-width: 100%;
      }
      div[aria-modal='true'][role='dialog'] {
        max-width: 992px;
        left: unset;
        right: 50%;
        width: 100%;
        transform: translateX(50%);
      }
      div[aria-modal='true'][role='dialog'] > div > div {
        max-width: 992px;
      }
    </style>
  </head>
  <body>
    <div id="app-root"></div>
  </body>
</html>
