const path = require('path');

const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const {CleanWebpackPlugin} = require('clean-webpack-plugin');

const appDirectory = '..';
const {presets} = require(`${appDirectory}/babel.config.js`);

const navigationLoader = {
  test: /\.js$/,
  include: [path.resolve(__dirname, appDirectory, 'node_modules/@react-navigation'), path.resolve(__dirname, appDirectory, 'node_modules/@react-native-async-storage/async-storage')],
  type: 'javascript/auto',
  use: {
    loader: 'babel-loader',
    options: {
      cacheDirectory: true,
      presets,
      plugins: ['react-native-web'],
    },
  },
};

const compileNodeModules = [
  // Add every react-native package that needs compiling
  // 'react-native-gesture-handler',
  'babel-polyfill',
  'react-native-reanimated',
  'react-native-swiper-flatlist',
  'react-native-ratings',
  'react-native-animatable',
  'react-native-inappbrowser-reborn',
  'react-native-progress',
  'rn-fetch-blob',
  'react-native-confetti-cannon',
  'react-native-webview',
  '@react-native-firebase/messaging',
  'react-native',
  '@sentry/react-native',
  '@react-native/assets-registry',
  '@rneui',
].map(moduleName => path.resolve(__dirname, appDirectory, `node_modules/${moduleName}`));

const babelLoaderConfiguration = {
  test: /\.js$|tsx?$/,
  // Add every directory that needs to be compiled by Babel during the build.
  include: [
    path.resolve(__dirname, appDirectory, 'index.web.js'), // Entry to your application
    path.resolve(__dirname, appDirectory, 'App.web.tsx'), // Change this to your main App file
    path.resolve(__dirname, appDirectory, 'src'),
    ...compileNodeModules,
  ],
  use: {
    loader: 'babel-loader',
    options: {
      cacheDirectory: true,
      presets,
      plugins: ['react-native-web'],
    },
  },
};

const svgLoaderConfiguration = {
  test: /\.svg$/,
  use: [
    {
      loader: '@svgr/webpack',
    },
  ],
};

const imageLoaderConfiguration = {
  test: /\.(gif|jpe?g|png|svg)$/,
  use: {
    loader: 'file-loader',
    options: {
      name: '[path][name].[ext]',
    },
  },
};

const styleLoaderConfiguration = {
  test: /\.css$/i,
  use: ['style-loader', 'css-loader'],
};

const fontLoaderConfiguration = {
  test: /\.ttf$/,
  loader: 'file-loader', // or directly file-loader
  include: [
    path.resolve(__dirname, appDirectory, 'node_modules/react-native-vector-icons/Fonts/Ionicons.ttf'),
    path.resolve(__dirname, appDirectory, 'node_modules/react-native-vector-icons/Fonts/MaterialCommunityIcons.ttf'),
  ],
};

module.exports = {
  entry: {
    app: path.join(__dirname, appDirectory, 'index.web.js'),
  },
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: '[name].[hash].js',
    publicPath: '/',
  },
  resolve: {
    extensions: ['.web.tsx', '.web.ts', '.tsx', '.ts', '.web.js', '.js'],
    alias: {
      'react-native$': 'react-native-web',
      'react-native-linear-gradient': 'react-native-web-linear-gradient',
    },
  },
  module: {
    rules: [navigationLoader, babelLoaderConfiguration, imageLoaderConfiguration, svgLoaderConfiguration, fontLoaderConfiguration, styleLoaderConfiguration],
  },
  devServer: {
    historyApiFallback: true,
    static: {
      directory: path.join(__dirname, 'public'),
    },
  },
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          safari10: true,
        },
      }),
    ],
    splitChunks: {
      cacheGroups: {
        reactVendor: {
          test: /[\\/]node_modules[\\/](react|react-dom|react-router-dom|@tanstack\/react-query|@sentry\/react|@react-navigation\/native|zmp-sdk|react-native-web|@rneui|lodash)[\\/]/,
          name: 'vendor-react',
          chunks: 'all',
        },
        reanimated: {
          test: /[\\/]node_modules[\\/](react-native-reanimated)[\\/]/,
          name: 'vendor-reanimated',
          chunks: 'all',
        },
        corejsVendor: {
          test: /[\\/]node_modules[\\/](core-js)[\\/]/,
          name: 'vendor-corejs',
          chunks: 'all',
        },
        vectorIcons: {
          test: /[\\/]node_modules[\\/](react-native-vector-icons)[\\/]/,
          name: 'vendor-icons',
          chunks: 'all',
        },
      },
    },
  },

  plugins: [
    new CleanWebpackPlugin({
      dry: true,
    }),
    new HtmlWebpackPlugin({
      template: path.join(__dirname, 'index.html'),
      publicPath: '/',
    }),
    new webpack.HotModuleReplacementPlugin(),
    new webpack.DefinePlugin({
      // See: https://github.com/necolas/react-native-web/issues/349
      __DEV__: JSON.stringify(true),
    }),
    new webpack.EnvironmentPlugin({JEST_WORKER_ID: null}),
    new webpack.DefinePlugin({process: {env: {}}}),
    // new webpack.SourceMapDevToolPlugin({}),
  ],
};
