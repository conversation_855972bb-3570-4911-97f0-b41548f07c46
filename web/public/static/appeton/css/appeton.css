.montserrat-headline {
  font-family: "Montserrat", sans-serif;
  font-optical-sizing: auto;
  font-style: normal;
}

#prod-title {
  color: #ffffff;
  margin-top: 30%;
}
.text-centered {
  text-align: center;
}
.text-left {
  text-align: left;
}
.hidden {
  opacity: 0;
  visibility: hidden;
}
.nobullet {
  display: block;
}
#top-menu.top-menu > .category > .dropdown-item:before {
  width: 0;
  left: 50%;
  position: absolute;
  content: '';
  height: 3px;
  bottom: -0.25rem;
  background: linear-gradient(90deg, #f0f12e, #16a16c);
  transition: width 200ms linear, left 200ms linear;
}
#top-menu.top-menu > .category > .dropdown-item:hover:before {
  width: calc(100% + 2rem);
  left: -1rem;
}

#top-menu {
  margin: 10px;
}

.row-1 {
  width: calc(100% - 20px);
  margin: 5px 10px 0px 10px;
  border-radius: 5px;
}

.row-2 {
  width: calc(50% - 20px);
  margin: 5px 10px 0px 10px;
  border-radius: 5px;
}
body {
  font: 14px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  line-height: 1.5;
}
.mbr-section-title {
  font-style: normal;
  line-height: 1.2;
}
.mbr-section-subtitle {
  line-height: 1.3;
}
.mbr-text {
  font-style: normal;
  line-height: 1.6;
}
.display-1 {
}
.display-2 {
  font-size: 2.3rem;
  line-height: 2.3rem;
}
.display-3 {
  font-size: 2rem;
}
.display-4 {
  font-size: 1rem;
}
.display-5 {
  font-size: 1.5rem;
}
.display-7 {
  font-size: 1.1rem;
}
.display-8 {
  font-size: 1rem;
}

.space-50 {
  height: 50px;
}
.green-color {
  color: #048e53 !important;
}
.sub-head {
  padding-top: 30px;
  padding-bottom: 30px;
  background-color: #048e53;
  text-align: center;
  color: #f0f12e;
}
.sub-head a {
  text-transform: initial;
  font-style: inherit;
}

.thumbnail {
  position: relative;
  background: #e0e0e0;
  padding-bottom: 1px;
}

.thumbnail img {
  width: 100%;
}
.caption {
  padding-top: 10px;
  padding-left: 10px;
  padding-right: 10px;
  padding-bottom: 10px;
  background: #ffffff;
  margin: 10px;
}

.caption a {
  padding-left: 1rem;
}
.caption ul {
  margin-bottom: 1rem;
}

.caption .bold-title {
  font-weight: 500;
  font-size: 1.5rem;
}
.promo-img {
  min-height: 350px;
  position: relative;
  background-repeat: no-repeat;
  background-position: center;
}
.promo-content {
  position: absolute;
  bottom: 20px;
  left: 50px;
}
span.promo-link {
  padding-bottom: 2px;
  border-bottom: 2px solid #000;
  line-height: 48px;
}

#mobile_top_menu_wrapper .top-menu .dropdown-item .float-xs-right {
  width: 100%;
  z-index: 1;
  position: relative;
}
#mobile_top_menu_wrapper .top-menu .navbar-toggler {
  width: 100%;
  text-align: right;
}
#mobile_top_menu_wrapper .top-menu .collapse-icons[aria-expanded='true'] .remove {
  float: right;
}
.navLabel {
  cursor: default;
}
.navLabel:nth-child(0),
.navLabel:nth-child(1) {
  cursor: pointer;
}
#top-menu .top-menu .navHome,
#top-menu .top-menu .navDiscover,
#top-menu .top-menu .navAbout {
  display: none;
}
/* Second navbar*/

.second-navbar {
  display: block;
  margin: auto;
  text-align: center;
  z-index: 1;
  padding-top: 1.563rem;
}
#product .container .row {
  padding-top: 75px;
}
ul#horizontal-list {
  list-style: none;
}
ul#horizontal-list li {
  display: inline-table;
  text-align: center;
  width: 230px;
  border: 2px solid #111111;
  border-radius: 30px;
  margin-left: 30px;
  margin-bottom: 10px;
  padding-top: 10px;
  padding-bottom: 10px;
}

#horizontal-list li a {
  color: #111111;
}

.second-navbar a {
  color: #111111;
  text-align: center;

  width: 200px;
}

.second-navbar a:hover {
  color: #f0f12e;
}
ul#horizontal-list li:hover {
  background: #16a16c;
  border: 2px solid #f0f12e;
  cursor: pointer;
}

ul#horizontal-list li.buy-button {
  background: #16a16c !important;
  border: 2px solid #16a16c;
}
ul#horizontal-list li.buy-button a {
  color: #f0f12e !important;
}
.product-main-desc {
  color: #ffffff;
  position: absolute;
  bottom: 15px;
}

/*product details css*/
#product #main {
  position: absolute;
  z-index: 1;
  margin-top: 150px;
  width: 100%;
}

#product #main .row {
  margin: auto;
  margin-top: 7%;
}
.product-details {
  padding-top: 120px;
  padding-bottom: 25px;
}
.overview-contents ul,
.benefits-contents ul,
.ingredients-dosage ul {
  list-style-type: disc;
  padding-left: 1.1rem;
}
.overview-section,
.ingredients-section,
.dosage-section {
  background: #ffffff;
}
.overview,
.benefits,
.ingredients-dosage {
  margin: auto 4%;
  padding: 0;
}
.overview-title,
.ingredients-title,
.dosage-title {
  color: #048e53;
  font-weight: 600;
  padding-bottom: 25px;
}
.overview-contents,
.ingredients-contents,
.dosage-contents {
  color: #000;
}
h2#expandIngredient {
  display: inline;
  cursor: pointer;
}
.ingredients-contents {
  display: none;
  cursor: pointer;
}
.ingredients-contents.show {
  display: block;
}
.ingredients-contents table {
  font-size: 1rem;
}
.benefits-section {
  background: #048e53;
}

.benefits-title {
  color: #f0f12e;
  font-weight: 600;
  padding-bottom: 25px;
}
.benefits-contents {
  color: #ffffff;
}
.smallDisclaimer {
  font-size: 0.7rem;
}
select {
  background: transparent;
  border: none;
  margin-left: 20px;
  font-weight: 500;
}
h2.direction {
  color: #048e53;
}
h2.recommend,
h2.clinicallyProven {
  font-size: 1.75rem;
  color: #f0f12e;
}
h2.references {
  font-size: 1rem;
  color: #f0f12e;
}
span.ref {
  font-size: 10px;
  vertical-align: top;
  line-height: 15px;
}
ol.references {
  font-size: 0.7rem;
  padding-left: 0.7rem;
}
/* home page */
.homeTopTopLabel,
.homeTopBtmLabel {
  background-color: #16a16c;
  color: #f0f12e;
  padding: 15px 0;
  font-size: 20px;
  font-weight: 600;
}
.homeTopMidContainer {
  background-color: #ffffff;
  display: flex;
}
.homeTopMidContainer .col-md-8 {
  padding: 50px;
  text-align: justify;
}
.homeTopMidContent {
  /*max-width: 1000px;*/
  left: 0;
  right: 0;
  margin: auto;
}
.homeTopMidContent .col-md-4 .apple {
  width: 100%;
  padding: 25px 0;
  margin-left: -35%;
}
/*.highlightContainer,#homeBotCMS1{
	display: -webkit-flex;
    display: flex;
    -webkit-flex-flow: column;
    flex-flow: row;
}*/
.highlightContainer {
  display: -webkit-flex;
  display: flex;
  -webkit-flex-flow: column;
  flex-flow: row;
}
#homeMidCMS1,
#homeMidCMS2,
#homeMidCMS3 {
  overflow: hidden;
}

#homeMidCMS1 .col-md-6:first-child,
#homeMidCMS3 .col-md-6:first-child {
  z-index: 1;
}
#cmsMiddle .col-md-6 {
  padding: 0;
  background-color: #ffffff;
}
.highlightContainer .homeHighlightImg {
  height: 101%;
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
  min-height: 100%;
  min-width: 100%;
  transform: translate(-50%, -50%);
}
#homeMidCMS3 .highlightContainer .homeHighlightImg {
  /*height: 101%;
	display: block;
    position: absolute;
    top: 50%;
    min-height: 100%;
    min-width: 100%;
    transform: translate(-50%, -50%);
	left: 34%;*/
}

.homeHighlightBg {
  display: block;
  width: 100%;
  height: 400px;
  background-image: url(http://nikki.noxs.org/presta/img/cms/AdultHealth/category_1.jpg);
  background-size: 100%;
  background-repeat: no-repeat;
  position: absolute;
}

.highlightBg {
  background-size: cover;
  overflow: hidden;
}
.homeHighlightText {
  position: absolute;
  color: #ffffff;
  margin: auto;
  text-align: center;
  left: 30px;
  right: 30px;
  top: 0;
  bottom: 0;
  height: 3.25rem;
  line-height: 1;
}
.homeHighlightOverlay {
  display: block;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  position: relative;
}
.mbr-section-btn.shop-btn {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  max-width: 155px;
  margin: auto !important;
  top: calc(7rem + (4.5 - 2.225) * ((100vw - 20rem) / (48 - 20)));
  height: 34px;
}
.mbr-section-btn a.btn {
  position: absolute;
  left: 0;
  right: 0;
  max-width: 155px;
  margin: auto !important;
  padding: 5px;
}
img.js-qv-product-cover {
  width: 85% !important;
}
.mbr-iconfont {
  display: block;
}
.cms-id-14 .homeTopTopLabel,
.cms-id-15 .homeTopTopLabel {
  text-align: center;
}
.cms-id-14 .homeTopTopLabel .navIcon,
.cms-id-15 .homeTopTopLabel .navIcon {
  width: calc(100% / 4);
  display: inline-block;
}
.cms-id-14 .homeTopTopLabel .mbr-iconfont,
.cms-id-15 .homeTopTopLabel .mbr-iconfont {
  display: block;
  color: #fff;
  font-size: 1.6rem;
}
.cms-id-14 .homeTopTopLabel .catNavLink,
.cms-id-15 .homeTopTopLabel .catNavLink {
  color: #fff;
  display: unset;
}
#category-nav span.mbr-iconfont {
  font-size: 1.6rem;
}
.prodHighlight {
  width: 50%;
  display: inline-block;
  float: left;
  margin: 20px 0;
}
.prodHighlightImg img {
  width: 100%;
}

.prodHighlightName {
  text-align: center;
  display: block;
}

#prodBackground img {
  width: 100%;
  height: auto;
  /*min-width: 768px;*/
  min-width: 100%;
}

.highlightContainer a {
  color: #000000;
  min-height: 48px;
  max-width: 175px;
  margin: auto;
}
.highlightContainer a:hover {
  color: #048e53;
}
#index #header {
  background: unset;
  top: 0;
}
#index #header.solid,
#header.solid {
  background: #ffffff;
}
#header #nav {
  padding-right: 25px;
}
#brand-overview,
/* #seller-benefits {
  margin-top: 20px;
} */
#brand-overview h2,
#top-products h2,
#seller-benefits h2 {
  font-size: 1.4rem;
  text-align: center;
}
#brand-overview img,
#top-products img {
  max-width: 100%;
}

#brand-overview .brand-overview-content,
#top-products .top-products-content,
#seller-benefits .seller-benefits-content {
  padding: 20px;
  text-align: justify;
}

#top-products .top-products-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

#top-products .top-products-name {
  text-align: center;
  font-size: 1.2rem;
  color: #fff;
  font-weight: bold;
  margin-bottom: 12px;
}

#top-products .top-products-description {
  text-align: center;
  font-size: 1rem;
  color: #fff;
  max-width: 400px;
}

.seller-cta {
  background-color: #048e53;
  border: none;
  outline: 0;
  padding: 8px 20px;
  color: #fff;
  border-radius: 100px;
  transition: transform 0.35s; /* Animation */
  font-size: 1.2rem;
}

.seller-cta:hover {
  background-color: #0fa866;
  cursor: pointer;
  transform: scale(1.2);
}

.seller-cta-microcopy {
  font-size: 0.8rem;
  color: #4d4d4d;
  margin-top: 10px;
}

#brand-overview img {
  text-align: center;
}
#header a:hover {
  color: #232323 !important;
}
#index #wrapper {
  padding: 0;
  margin: 0;
}
#index .cmsHeaderText .display-1 {
  color: #048e53;
}
#index .cmsHeaderText .display-2 {
  font-size: 3rem;
  color: #0b0a0a;
}
#category-nav {
  background-color: #f0f12e;
  padding: 0 10%;
  text-align: center;
}
.catNavLink {
  width: calc(100% / 7);
  display: inline-block;
  text-align: center;
  color: black;
  padding: 15px 0;
}
.catNavLink.child-5 {
  width: calc(100% / 5);
}
.catNavLink.child-4 {
  width: calc(100% / 4);
}
.catNavLink.child-2 {
  width: calc(100% / 2);
}
.catTitle {
  text-align: center;
  color: #048e53;
  font-weight: 600;

  font-size: 3rem;
}
.contactHeadTitle {
  text-align: center;
  color: #048e53;
  font-weight: 600;

  font-size: 2.5rem;
  text-transform: unset;
}
#category-description {
  text-align: center;

  font-size: 1.4rem;
}

#product .col-lg {
  padding: 0;
}

#product .product-information {
  display: none;
}
#products .products.row {
  width: 100%;
  margin: 0;
  padding: 0;
}
#products .product-miniature {
  width: 50%;
  margin: 0;
  padding: 0;
}
#products .thumbnail-container {
  width: 100%;
  height: auto;
  box-shadow: unset;
  position: relative;
  display: block;
}

#products .thumbnail.product-thumbnail {
  width: 40%;
  display: inline-block;
  margin-right: 30px;
  background-color: unset;
  vertical-align: top;
}

#products .thumbnail-container .product-description {
  width: 50%;
  height: unset;
  display: inline-block;
  position: relative;
  box-shadow: unset;
  top: 0;
  background-color: unset;
}
#products .thumbnail-container .product-description::after {
  box-shadow: unset;
  border-top: 0px;
}

#products .thumbnail-container .product-description .product-title a {
  color: #048e53;
  font-weight: 900;
  font-size: 18px;
}

.categoryProductDesc {
  /*line-height:20px;*/
}
.categoryProductDesc p span {
  color: unset !important;
  font-size: 12px !important;
}

#category #products {
  margin: 50px 0;
}
#category .highlighted-informations {
  display: none;
}

#category .block-category {
  min-height: unset;
}
#category #products .product-title {
  text-align: left;
  margin: 20px 0;
}
.catLearnMore {
  background-color: #048e53;
  display: inline;
  padding: 10px 20px;
  border-radius: 8px;
  color: #ffffff;
}
#footer .footer-container .links .h3 {
  display: block !important;
  color: #ffffff;
  font-weight: 600;
}

#footer .links .title {
  display: none !important;
}
.copyrightText {
  color: #ffffff !important;
}
.backToTop a {
  padding: 3px;
}
.backToTop {
  display: none;
}
#description.row {
  width: 100%;
  margin: 0;
}
#cms .row {
  margin: 0;
}
#cms #main .page-content {
  position: relative;
}
#cms #main .page-content,
#main .page-header {
  margin-bottom: unset;
}
#cms #main .page-content .contentContainer {
  width: calc(100% - 80px);
  margin: auto;
  overflow: auto;
  margin-top: 50px;
}
#cms.cms-id-16 #main .page-content .contentContainer {
  margin-bottom: 50px;
}
.contentContainer select {
  border: 1px solid rgba(0, 0, 0, 0.7);
  box-sizing: border-box;
  -webkit-appearance: menulist;
}

.contentContainer textarea {
  border: 1px solid rgba(0, 0, 0, 0.7);
}
.containerBorder {
  border: 1px solid rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  padding: 50px 30px;
}

.contactInfo {
  padding: 0 40px;
}
#cms.cms-id-17 .page-header {
  display: block;
  width: calc(100% - 80px);
  margin: auto;
  padding-top: 15px;
}

#cms #main .cmsMainText {
  display: block;
  position: absolute;
  top: 20%;
  width: 60%;
  margin-left: 40%;
  padding: 0;
  color: #ffffff;
  text-align: center;
}
#contactModal {
  display: none;
  background-color: white;
  width: 100%;
  height: 100%;
  position: absolute;
  margin: -50px -30px;
  border-radius: 8px;
}
#modalText {
  text-align: center;
  top: 50%;
  position: absolute;
  left: 0;
  right: 0;
}
.contactTitle {
  color: #048e53;

  font-size: 25px;
  padding: 0px 10px 20px 10px;
}
.formsubmit {
  background-color: #048e53;
  display: inline;
  padding: 5px 20px;
  border-radius: 8px;
  color: #f0f12e;
  float: right;
  margin: 10px;
  cursor: pointer;
}
.formsubmit a {
  color: #f0f12e;
  cursor: pointer;
}
.hotline {
  color: #048e53;

  font-size: 20px;
}
.hotlineNo {
  color: #048e53;

  font-size: 23px;
}
.aboutTitle {
  cursor: pointer;
  background-color: #16a16c;
  color: #f0f12e;
  padding: 5px 20px;
  width: 80%;
  display: inline-block;
  margin: 5px 10%;
  z-index: 1;
  position: relative;
}

.aboutTitle h2 {
  display: inline;
  font-size: 24px;
  vertical-align: middle;
  line-height: 24px;
}
.aboutTitle .expand {
  float: right;
}
.aboutContent {
  width: 75%;
  margin: auto;
  height: 0;
  opacity: 0;
  display: none;
  /*-webkit-transition: all 0.5s ease-out;
    transition: all 0.5s ease-out;*/
}
.aboutContent img {
  width: 100%;
}
.aboutContent.show {
  height: unset;
  opacity: 1;
  display: block;
}
#aboutContent5 ol {
  padding: 0;
}
.aboutBottomContainer ul {
  list-style-type: disc;
  padding: 0.5rem;
}
.marketingContainer {
  background-color: #ededed;
  margin: 10px 0;
  display: flex;
  flex-wrap: wrap;
}
img.aboutRow-2 {
  display: inline-block;
  vertical-align: top;
  width: 50%;
  height: 100%;
}
ul.aboutRow-2 {
  display: inline-block;
  vertical-align: top;
  width: 50%;
  padding: 25px;
  margin-bottom: 0;
  padding-left: 35px;
}
#cms .homeTopTopLabel h1 {
  text-align: center;
  font-weight: 900;
  font-size: 35px;
}
.cmsTopMid,
.cmsTopBottom {
  display: inline-block;
  width: 100%;
  margin-bottom: 10px;
  float: left;
}
.cmsTopMid img,
.cmsTopBottom img {
  width: 100%;
}

#cms .page-header {
  display: none;
}

a.tncLink {
  color: #2fb5d2;
}
a.btn-footer {
  margin-left: 18% !important;
}
.aboutDesc {
  padding: 80px;
}
.aboutDescLogo {
  padding: 80px;
}
.aboutDescLogo img {
  width: 100%;
}
.aboutImgContainer {
  padding: 0;
}
.aboutImgContainer img {
  width: 100%;
}
.aboutTop,
.aboutBottom {
  display: block;
  overflow: auto;
}
.cms-id-20 .aboutImgContainer.col-md-3 {
  padding: 10px;
}
.cms-id-20 .aboutImgContainer.col-md-3 img {
  width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.5);
}
.wtb-title {
  position: absolute;
  top: 35%;
  text-align: center;
  width: 100%;
  color: #048e53;
  font-weight: 600;
  padding-bottom: 25px;
  font-size: 30px;
}
.wtb-desc {
  position: absolute;
  top: 45%;
  text-align: center;
  width: 100%;
}
.cmsHeaderImgContainer {
  padding: 0;
}
.cmsHeaderImgContainer img {
  width: 100%;
}
.cmsHeaderText {
  position: absolute;
  right: 15%;
  top: 50%;
  color: #fff;
  text-align: right;
}
/*.aboutContent.dual ul{
	width: calc(50% - 4px);
    vertical-align: top;
    flex-direction: column;
    height: 100vh;
    display: flex;
    float: left;
    margin: 2px;
}*/
/*.aboutContent.dual > ul{
	border: 1px solid;
}*/
.aboutContent.dual ul ul {
  width: 100%;
  padding-left: 20px;
}
.aboutContent li {
  /*list-style-position: inside;*/
}
.marketingContainer ul.aboutRow-2 > li {
  margin: 10px 0;
}
.marketingContainer ul.aboutRow-2 li:first-child b {
  color: #16a16c;
}
.navDiscover.show #top_sub_menu_63316 {
  display: block !important;
}
.imgLabel {
  position: absolute;
  z-index: 1;
  bottom: 0px;
  background-color: rgba(255, 255, 255, 0.8);
  width: 100%;
  padding: 20px;
}
.imgLabel h1 {
  color: #048e53;
  font-weight: 400;

  font-size: 3rem;
  margin: 0;
}
.imgLabel span {
  /* font-size: 20px; */
  font-size: 15px;
  text-decoration: underline;
}
.imgLabel a {
  color: #000;
}
.col-md-6 .imgLabel {
  padding: 15px;
}
.col-md-6 .imgLabel h1 {
  font-size: 25px;
  line-height: 28px;
}
.col-md-4 .imgLabel {
  padding: 10px;
}
.col-md-4 .imgLabel h1 {
  /* font-size: 25px;
    line-height: 28px; */
  font-size: 20px;
  line-height: 25px;
}

.cms-id-12 .aboutImgContainer.col-md-4 {
  float: left;
  width: 33.33333%;
}
#footer .row {
  width: 100%;
  padding-top: unset !important;
}
.footer-container #footerRight .row {
  padding: 0;
}
#footerRight {
  width: 60%;
  display: block;
  float: left;
}
#footerRight .col-md-4 {
  width: 100%;
}
#footerRight .col-md-4 .col-md-6 {
  width: 30%;
}
.product-prices {
  display: none !important;
}
#products .product-price-and-shipping,
.featured-products .product-price-and-shipping,
.product-accessories .product-price-and-shipping,
.product-miniature .product-price-and-shipping {
  display: none !important;
}

.top-menu .sub-menu ul[data-depth='1'] > li {
  font-size: 12px;
}

.language-selector ul.dropdown-menu {
  padding: 6px 0;
  margin: 0px 0 0;
  min-width: unset;
}

.language-selector ul.dropdown-menu .dropdown-item {
  padding: 2px 15px;
}
@media (max-width: 767px) {
  #horizontal-list li a {
    color: #333;
  }
  #product #main .row {
    margin-top: 20%;
  }
  #header {
    padding: 0.5rem 4%;
    overflow-y: scroll;
  }
  #header.expandHeader {
    height: 100%;
  }
  #mobile_top_menu_wrapper {
    overflow-y: scroll;
  }
  .order1 {
    order: 1;
    -webkit-order: 1;
  }
  .order2 {
    order: 2;
    -webkit-order: 2;
  }
  body#product {
    overflow-x: hidden;
  }
  #product #main {
    margin-top: 0px;
  }
  #product #description .col-lg {
    padding: 0;
  }
  .product-information {
    display: none;
  }
  .images-container {
    width: 50%;
  }
  #prodBackground {
    /*background-size: cover;*/
    background-position: 50%;
    overflow: hidden;
    background-size: contain;
    background-repeat: no-repeat;
  }
  #prodBackground img {
    opacity: 0;
    /*min-width: 768px;*/
    min-width: 100%;
  }
  
  .second-navbar {
    margin-top: 20px;
    padding: 0;
  }

  #main-nav ul#horizontal-list li {
    border: 2px solid #16a16c;
  }

  .highlightContainer {
    width: 100%;
    display: block;
    display: flex;
    flex-wrap: wrap;
  }
  #cmsMiddle .col-md-6 {
    display: inline-block;
    position: relative;
    width: 100%;
  }
  .highlightContainer .homeHighlightImg {
    position: unset;
    opacity: 0;
    width: 100%;
  }
  #products .product-miniature {
    width: 100%;
    margin: 30px 0;
  }
  .cmsHeaderText {
    top: 30%;
    right: 0;
    left: 0;
    text-align: center;
  }
  .aboutContent.dual ul {
    width: 100%;
  }
  .footer-container .links ul {
    text-align: center;
  }
  #footerLeft {
    text-align: center;
  }

  #footer .footer-container .links .h3 {
    text-align: center;
  }
  #footerRight {
    width: 100%;
    margin-top: 25px;
  }
  #footerRight .col-md-4 .col-md-6 {
    width: 100%;
  }
  #product .container .row {
    display: flex;
    flex-wrap: wrap;
    padding: 10%;
    padding-top: 80px;
  }
  .overview {
    order: 2;
    -webkit-order: 2;
    width: 100%;
    display: block;
    margin-bottom: 20px;
  }
  .icon {
    order: 1;
    -webkit-order: 1;
    width: 85%;
    display: block;
    margin: auto;
    margin-bottom: 20px;
  }
  .aboutDesc,
  .aboutDescLogo {
    padding: 20px 50px;
  }
  .aboutTitle h2 {
    font-size: 16px;
  }
  .aboutContent {
    font-size: 14px;
    line-height: 18px;
  }
  #cms .homeTopTopLabel h1 {
    font-size: 25px;
  }
  #top-menu.top-menu > .category > .dropdown-item:hover:before {
    display: none;
  }
}
@media (max-width: 530px) {
  #product #main .row {
    margin-top: 15%;
  }

  ul#horizontal-list li {
    margin-left: 0;
  }
  #prodBackground img {
    /*min-width: 530px;*/
    min-width: 100%;
  }
  #category-nav {
    padding: 0 2%;
  }
  .catNavLink {
    font-size: 11px;
  }
  .catNavLink span {
    font-size: 1.4rem !important;
  }
  #category .block-category {
    padding: 10px 2%;
  }
  .catTitle {
    font-size: 2.3rem;
  }
  #category-description {
    font-size: 1.2rem;
    line-height: 1.5rem;
  }
  .display-2 {
    font-size: calc(1.1rem + (3 - 1.7) * ((100vw - 20rem) / (48 - 20)));
    line-height: calc(1.4 * (0.9rem + (3 - 1.7) * ((100vw - 20rem) / (48 - 20))));
  }
  #cms .imgLabel,
  #index .imgLabel {
    padding: 5px 15px;
  }
  #cms .imgLabel h1,
  #index .imgLabel h1 {
    font-size: 20px;
    line-height: 20px;
  }
  #cms .imgLabel span,
  #index .imgLabel span {
    font-size: 15px;
  }
  #contactModal {
    margin: -10px -10px -50px -10px;
  }
  .contactHeadTitle {
    font-size: 1.5rem;
  }
  .containerBorder {
    padding: 10px 10px 50px 10px;
    margin: 20px 0;
  }
  #cms #main .page-content .contentContainer {
    width: calc(100% - 30px);
    margin-top: 0;
  }
  img.aboutRow-2 {
    width: 100%;
    height: 100%;
  }
  .wtb-title {
    font-size: 20px;
  }
  .wtb-desc {
    top: 50%;
    font-size: 16px;
    line-height: 20px;
  }
  #index .cmsHeaderText {
    top: 15%;
    width: 100%;
    /*text-align: center;
		right: unset;*/
    right: 10%;
    left: unset;
    text-align: right;
  }
  .homeTopTopLabel,
  .homeTopBtmLabel {
    font-size: 16px;
    font-weight: 300;
  }
  a.btn-footer {
    margin-left: unset !important;
  }
}
@media (max-width: 439px) {
  #products .thumbnail-container .product-description .product-title a {
    font-size: 16px;
  }
  #category .categoryProductDesc {
    font-size: 14px;
    line-height: 16px;
  }
  #category #products .product-title {
    margin: 10px 0;
  }
  #category #products {
    margin: 0px 0;
  }
}
@media (max-width: 412px) {
  #prodBackground img {
    /*min-width: 430px;*/
    min-width: 100%;
  }
  .display-2 {
    font-size: 2.4rem;
    font-size: calc(1.25rem + (3 - 1.7) * ((100vw - 20rem) / (48 - 20)));
    line-height: calc(1.25rem + (3 - 1.7) * ((100vw - 20rem) / (48 - 20)));
  }
}

/* ---- Fluid typography for mobile devices ---- */
/* 1.4 - font scale ratio ( bootstrap == 1.42857 ) */
/* 100vw - current viewport width */
/* (48 - 20)  48 == 48rem == 768px, 20 == 20rem == 320px(minimal supported viewport) */
/* 0.65 - min scale variable, may vary */
@media (max-width: 768px) {
  #prod-title {
    margin-top: 0;
  }
  .display-1 {
    font-size: 3.6rem;
    font-size: calc(2.225rem + (4.5 - 2.225) * ((100vw - 20rem) / (48 - 20)));
    line-height: calc(1.4 * (2.225rem + (4.5 - 2.225) * ((100vw - 20rem) / (48 - 20))));
  }
  #index .cmsHeaderText .display-1 {
    line-height: calc(2.225rem + (4.5 - 2.225) * ((100vw - 20rem) / (48 - 20)));
  }

  .display-2 {
    font-size: 2.4rem;
    font-size: calc(1.1rem + (3 - 1.7) * ((100vw - 20rem) / (48 - 20)));
    line-height: calc(1.4 * (0.9rem + (3 - 1.7) * ((100vw - 20rem) / (48 - 20))));
  }
  #index .cmsHeaderText .display-2 {
    font-size: calc(1.75rem + (3 - 1.7) * ((100vw - 20rem) / (48 - 20)));
    line-height: calc(1.75rem + (3 - 1.7) * ((100vw - 20rem) / (48 - 20)));
  }
  .display-3 {
    font-size: 1.3rem;
  }
  .display-4 {
    font-size: 0.8rem;
    font-size: calc(1rem + (1 - 1) * ((100vw - 20rem) / (48 - 20)));
    line-height: calc(1.4 * (1rem + (1 - 1) * ((100vw - 20rem) / (48 - 20))));
  }
  .display-5 {
    font-size: 1.2rem;
    font-size: calc(1.175rem + (1.5 - 1.175) * ((100vw - 20rem) / (48 - 20)));
    line-height: calc(1.4 * (1.175rem + (1.5 - 1.175) * ((100vw - 20rem) / (48 - 20))));
  }
}
/* Buttons */
.btn {
  font-weight: 500;
  border-width: 2px;
  font-style: normal;
  letter-spacing: 1px;
  margin: 0.4rem 0.8rem;
  white-space: normal;
  transition-property: background-color, color, border-color, box-shadow;
  transition-duration: 0.3s, 0.3s, 0.3s, 2s;
  transition-timing-function: ease-in-out;
  padding: 1rem 2rem;
  border-radius: 3px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  word-break: break-word;
}
.btn .mbr-iconfont {
  font-size: 1.6rem;
}
.btn-sm {
  border: 1px solid;
  font-weight: 500;
  letter-spacing: 1px;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  padding: 0.8rem 1.5rem;
  border-radius: 3px;
}
.btn-md {
  font-weight: 500;
  letter-spacing: 1px;
  margin: 0.4rem 0.8rem !important;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  padding: 1rem 2rem;
  border-radius: 3px;
}
.btn-lg {
  font-weight: 500;
  letter-spacing: 1px;
  margin: 0.4rem 0.8rem !important;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  padding: 1.2rem 3.2rem;
  border-radius: 3px;
}
.bg-primary {
  background-color: #a38dfd !important;
}
.bg-success {
  background-color: #b2ccd2 !important;
}
.bg-info {
  background-color: #1ba1e2 !important;
}
.bg-warning {
  background-color: #82786e !important;
}
.bg-danger {
  background-color: #879a9f !important;
}
.btn-primary {
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  position: relative;
  overflow: hidden;
}
.btn-primary:before {
  content: '';
  position: absolute;
  z-index: -1;
  height: 2rem;
  width: 2rem;
  top: calc(50% - 1rem);
  left: calc(50% - 1rem);
  transform: scale(0);
  transition-duration: 0.5s;
  transition-timing-function: ease-out;
  background-color: #6642fc;
  border-color: #6642fc;
  border-radius: 100px;
}
.btn-primary,
.btn-primary:active,
.btn-primary.active {
  background-color: #a38dfd;
  border-color: #a38dfd;
  color: #ffffff !important;
}
.btn-primary:hover,
.btn-primary:focus,
.btn-primary.focus {
  color: #ffffff !important;
  background-color: #6642fc;
  border-color: #6642fc;
}
.btn-primary:hover:before,
.btn-primary:focus:before,
.btn-primary.focus:before {
  transform: scale(10);
}
.btn-primary.disabled,
.btn-primary:disabled {
  color: #ffffff !important;
  background-color: #6642fc !important;
  border-color: #6642fc !important;
}
.btn-secondary {
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  position: relative;
  overflow: hidden;
}
.btn-secondary:before {
  content: '';
  position: absolute;
  z-index: -1;
  height: 2rem;
  width: 2rem;
  top: calc(50% - 1rem);
  left: calc(50% - 1rem);
  transform: scale(0);
  transition-duration: 0.5s;
  transition-timing-function: ease-out;
  background-color: #0d5e3f;
  border-color: #0d5e3f;
  border-radius: 100px;
}
.btn-secondary,
.btn-secondary:active,
.btn-secondary.active {
  background-color: #16a16c;
  border-color: #16a16c;
  color: #f0f12e !important;
}
.btn-secondary:hover,
.btn-secondary:focus,
.btn-secondary.focus {
  color: #ffffff !important;
  background-color: #0d5e3f;
  border-color: #0d5e3f;
}
.btn-secondary:hover:before,
.btn-secondary:focus:before,
.btn-secondary.focus:before {
  transform: scale(10);
}
.btn-secondary.disabled,
.btn-secondary:disabled {
  color: #ffffff !important;
  background-color: #0d5e3f !important;
  border-color: #0d5e3f !important;
}
/*button footer */
.btn-footer {
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  position: relative;
  overflow: hidden;
  font-weight: bold;
}
.btn-footer:before {
  content: '';
  position: absolute;
  z-index: -1;
  height: 2rem;
  width: 2rem;
  top: calc(50% - 1rem);
  left: calc(50% - 1rem);
  transform: scale(0);
  transition-duration: 0.5s;
  transition-timing-function: ease-out;
  background-color: #0d5e3f;
  border-color: #0d5e3f;
  border-radius: 100px;
}
.btn-footer,
.btn-footer:active,
.btn-footer.active {
  background-color: #f0f12e;
  border-color: #f0f12e;
  color: #16a16c !important;
  border-radius: 100px;
  transition-property: background-color, color, border-color, box-shadow;
  transition-duration: 0.3s, 0.3s, 0.3s, 0.8s;
  transition-timing-function: ease-in-out;
  font-size: 0.8rem;
  padding: 0.6rem 1rem;
}
.btn-footer:hover,
.btn-footer:focus,
.btn-footer.focus {
  color: #ffffff !important;
  background-color: #0d5e3f;
  border-color: #0d5e3f;
}
.btn-footer:hover:before,
.btn-footer:focus:before,
.btn-footer.focus:before {
  transform: scale(10);
}
.btn-footer.disabled,
.btn-secondary:disabled {
  color: #ffffff !important;
  background-color: #0d5e3f !important;
  border-color: #0d5e3f !important;
}
.btn-info {
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  position: relative;
  overflow: hidden;
}
.btn-info:before {
  content: '';
  position: absolute;
  z-index: -1;
  height: 2rem;
  width: 2rem;
  top: calc(50% - 1rem);
  left: calc(50% - 1rem);
  transform: scale(0);
  transition-duration: 0.5s;
  transition-timing-function: ease-out;
  background-color: #13709e;
  border-color: #13709e;
  border-radius: 100px;
}
.btn-info,
.btn-info:active,
.btn-info.active {
  background-color: #1ba1e2;
  border-color: #1ba1e2;
  color: #ffffff !important;
}
.btn-info:hover,
.btn-info:focus,
.btn-info.focus {
  color: #ffffff !important;
  background-color: #13709e;
  border-color: #13709e;
}
.btn-info:hover:before,
.btn-info:focus:before,
.btn-info.focus:before {
  transform: scale(10);
}
.btn-info.disabled,
.btn-info:disabled {
  color: #ffffff !important;
  background-color: #13709e !important;
  border-color: #13709e !important;
}
.btn-success {
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  position: relative;
  overflow: hidden;
}
.btn-success:before {
  content: '';
  position: absolute;
  z-index: -1;
  height: 2rem;
  width: 2rem;
  top: calc(50% - 1rem);
  left: calc(50% - 1rem);
  transform: scale(0);
  transition-duration: 0.5s;
  transition-timing-function: ease-out;
  background-color: #82acb6;
  border-color: #82acb6;
  border-radius: 100px;
}
.btn-success,
.btn-success:active,
.btn-success.active {
  background-color: #b2ccd2;
  border-color: #b2ccd2;
  color: #ffffff !important;
}
.btn-success:hover,
.btn-success:focus,
.btn-success.focus {
  color: #ffffff !important;
  background-color: #82acb6;
  border-color: #82acb6;
}
.btn-success:hover:before,
.btn-success:focus:before,
.btn-success.focus:before {
  transform: scale(10);
}
.btn-success.disabled,
.btn-success:disabled {
  color: #ffffff !important;
  background-color: #82acb6 !important;
  border-color: #82acb6 !important;
}
.btn-warning {
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  position: relative;
  overflow: hidden;
}
.btn-warning:before {
  content: '';
  position: absolute;
  z-index: -1;
  height: 2rem;
  width: 2rem;
  top: calc(50% - 1rem);
  left: calc(50% - 1rem);
  transform: scale(0);
  transition-duration: 0.5s;
  transition-timing-function: ease-out;
  background-color: #59524b;
  border-color: #59524b;
  border-radius: 100px;
}
.btn-warning,
.btn-warning:active,
.btn-warning.active {
  background-color: #82786e;
  border-color: #82786e;
  color: #ffffff !important;
}
.btn-warning:hover,
.btn-warning:focus,
.btn-warning.focus {
  color: #ffffff !important;
  background-color: #59524b;
  border-color: #59524b;
}
.btn-warning:hover:before,
.btn-warning:focus:before,
.btn-warning.focus:before {
  transform: scale(10);
}
.btn-warning.disabled,
.btn-warning:disabled {
  color: #ffffff !important;
  background-color: #59524b !important;
  border-color: #59524b !important;
}
.btn-danger {
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  position: relative;
  overflow: hidden;
}
.btn-danger:before {
  content: '';
  position: absolute;
  z-index: -1;
  height: 2rem;
  width: 2rem;
  top: calc(50% - 1rem);
  left: calc(50% - 1rem);
  transform: scale(0);
  transition-duration: 0.5s;
  transition-timing-function: ease-out;
  background-color: #617479;
  border-color: #617479;
  border-radius: 100px;
}
.btn-danger,
.btn-danger:active,
.btn-danger.active {
  background-color: #879a9f;
  border-color: #879a9f;
  color: #ffffff !important;
}
.btn-danger:hover,
.btn-danger:focus,
.btn-danger.focus {
  color: #ffffff !important;
  background-color: #617479;
  border-color: #617479;
}
.btn-danger:hover:before,
.btn-danger:focus:before,
.btn-danger.focus:before {
  transform: scale(10);
}
.btn-danger.disabled,
.btn-danger:disabled {
  color: #ffffff !important;
  background-color: #617479 !important;
  border-color: #617479 !important;
}
.btn-black {
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  position: relative;
  overflow: hidden;
}
.btn-black:before {
  content: '';
  position: absolute;
  z-index: -1;
  height: 2rem;
  width: 2rem;
  top: calc(50% - 1rem);
  left: calc(50% - 1rem);
  transform: scale(0);
  transition-duration: 0.5s;
  transition-timing-function: ease-out;
  background-color: #0d0d0d;
  border-color: #0d0d0d;
  border-radius: 100px;
}
.btn-black,
.btn-black:active,
.btn-black.active {
  background-color: #333333;
  border-color: #333333;
  color: #ffffff !important;
}
.btn-black:hover,
.btn-black:focus,
.btn-black.focus {
  color: #ffffff !important;
  background-color: #0d0d0d;
  border-color: #0d0d0d;
}
.btn-black:hover:before,
.btn-black:focus:before,
.btn-black.focus:before {
  transform: scale(10);
}
.btn-black.disabled,
.btn-black:disabled {
  color: #ffffff !important;
  background-color: #0d0d0d !important;
  border-color: #0d0d0d !important;
}
.btn-white {
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  position: relative;
  overflow: hidden;
}
.btn-white:before {
  content: '';
  position: absolute;
  z-index: -1;
  height: 2rem;
  width: 2rem;
  top: calc(50% - 1rem);
  left: calc(50% - 1rem);
  transform: scale(0);
  transition-duration: 0.5s;
  transition-timing-function: ease-out;
  background-color: #d4d4d4;
  border-color: #d4d4d4;
  border-radius: 100px;
}
.btn-white,
.btn-white:active,
.btn-white.active {
  background-color: #ffffff;
  border-color: #ffffff;
  color: #ffffff !important;
}
.btn-white:hover,
.btn-white:focus,
.btn-white.focus {
  color: #ffffff !important;
  background-color: #d4d4d4;
  border-color: #d4d4d4;
}
.btn-white:hover:before,
.btn-white:focus:before,
.btn-white.focus:before {
  transform: scale(10);
}
.btn-white.disabled,
.btn-white:disabled {
  color: #ffffff !important;
  background-color: #d4d4d4 !important;
  border-color: #d4d4d4 !important;
}
.btn-white,
.btn-white:active,
.btn-white.active {
  color: #333333 !important;
}
.btn-white:hover,
.btn-white:focus,
.btn-white.focus {
  color: #333333 !important;
}
.btn-white.disabled,
.btn-white:disabled {
  color: #333333 !important;
}
.btn-primary-outline {
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  position: relative;
  overflow: hidden;
}
.btn-primary-outline:before {
  content: '';
  position: absolute;
  z-index: -1;
  height: 2rem;
  width: 2rem;
  top: calc(50% - 1rem);
  left: calc(50% - 1rem);
  transform: scale(0);
  transition-duration: 0.5s;
  transition-timing-function: ease-out;
  background-color: #a38dfd;
  border-color: #a38dfd;
  border-radius: 100px;
}
.btn-primary-outline,
.btn-primary-outline:active,
.btn-primary-outline.active {
  background: none;
  border-color: #5229fb;
  color: #5229fb !important;
}
.btn-primary-outline:hover,
.btn-primary-outline:focus,
.btn-primary-outline.focus {
  color: #ffffff !important;
  background-color: #a38dfd;
  border-color: #a38dfd;
}
.btn-primary-outline:hover:before,
.btn-primary-outline:focus:before,
.btn-primary-outline.focus:before {
  transform: scale(10);
}
.btn-primary-outline.disabled,
.btn-primary-outline:disabled {
  color: #ffffff !important;
  background-color: #a38dfd !important;
  border-color: #a38dfd !important;
}
.btn-secondary-outline {
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  position: relative;
  overflow: hidden;
}
.btn-secondary-outline:before {
  content: '';
  position: absolute;
  z-index: -1;
  height: 2rem;
  width: 2rem;
  top: calc(50% - 1rem);
  left: calc(50% - 1rem);
  transform: scale(0);
  transition-duration: 0.5s;
  transition-timing-function: ease-out;
  background-color: #16a16c;
  border-color: #16a16c;
  border-radius: 100px;
}
.btn-secondary-outline,
.btn-secondary-outline:active,
.btn-secondary-outline.active {
  background: none;
  border-color: #0a4730;
  color: #0a4730 !important;
}
.btn-secondary-outline:hover,
.btn-secondary-outline:focus,
.btn-secondary-outline.focus {
  color: #ffffff !important;
  background-color: #16a16c;
  border-color: #16a16c;
}
.btn-secondary-outline:hover:before,
.btn-secondary-outline:focus:before,
.btn-secondary-outline.focus:before {
  transform: scale(10);
}
.btn-secondary-outline.disabled,
.btn-secondary-outline:disabled {
  color: #ffffff !important;
  background-color: #16a16c !important;
  border-color: #16a16c !important;
}
.btn-info-outline {
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  position: relative;
  overflow: hidden;
}
.btn-info-outline:before {
  content: '';
  position: absolute;
  z-index: -1;
  height: 2rem;
  width: 2rem;
  top: calc(50% - 1rem);
  left: calc(50% - 1rem);
  transform: scale(0);
  transition-duration: 0.5s;
  transition-timing-function: ease-out;
  background-color: #1ba1e2;
  border-color: #1ba1e2;
  border-radius: 100px;
}
.btn-info-outline,
.btn-info-outline:active,
.btn-info-outline.active {
  background: none;
  border-color: #106087;
  color: #106087 !important;
}
.btn-info-outline:hover,
.btn-info-outline:focus,
.btn-info-outline.focus {
  color: #ffffff !important;
  background-color: #1ba1e2;
  border-color: #1ba1e2;
}
.btn-info-outline:hover:before,
.btn-info-outline:focus:before,
.btn-info-outline.focus:before {
  transform: scale(10);
}
.btn-info-outline.disabled,
.btn-info-outline:disabled {
  color: #ffffff !important;
  background-color: #1ba1e2 !important;
  border-color: #1ba1e2 !important;
}
.btn-success-outline {
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  position: relative;
  overflow: hidden;
}
.btn-success-outline:before {
  content: '';
  position: absolute;
  z-index: -1;
  height: 2rem;
  width: 2rem;
  top: calc(50% - 1rem);
  left: calc(50% - 1rem);
  transform: scale(0);
  transition-duration: 0.5s;
  transition-timing-function: ease-out;
  background-color: #b2ccd2;
  border-color: #b2ccd2;
  border-radius: 100px;
}
.btn-success-outline,
.btn-success-outline:active,
.btn-success-outline.active {
  background: none;
  border-color: #72a1ac;
  color: #72a1ac !important;
}
.btn-success-outline:hover,
.btn-success-outline:focus,
.btn-success-outline.focus {
  color: #ffffff !important;
  background-color: #b2ccd2;
  border-color: #b2ccd2;
}
.btn-success-outline:hover:before,
.btn-success-outline:focus:before,
.btn-success-outline.focus:before {
  transform: scale(10);
}
.btn-success-outline.disabled,
.btn-success-outline:disabled {
  color: #ffffff !important;
  background-color: #b2ccd2 !important;
  border-color: #b2ccd2 !important;
}
.btn-warning-outline {
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  position: relative;
  overflow: hidden;
}
.btn-warning-outline:before {
  content: '';
  position: absolute;
  z-index: -1;
  height: 2rem;
  width: 2rem;
  top: calc(50% - 1rem);
  left: calc(50% - 1rem);
  transform: scale(0);
  transition-duration: 0.5s;
  transition-timing-function: ease-out;
  background-color: #82786e;
  border-color: #82786e;
  border-radius: 100px;
}
.btn-warning-outline,
.btn-warning-outline:active,
.btn-warning-outline.active {
  background: none;
  border-color: #4b453f;
  color: #4b453f !important;
}
.btn-warning-outline:hover,
.btn-warning-outline:focus,
.btn-warning-outline.focus {
  color: #ffffff !important;
  background-color: #82786e;
  border-color: #82786e;
}
.btn-warning-outline:hover:before,
.btn-warning-outline:focus:before,
.btn-warning-outline.focus:before {
  transform: scale(10);
}
.btn-warning-outline.disabled,
.btn-warning-outline:disabled {
  color: #ffffff !important;
  background-color: #82786e !important;
  border-color: #82786e !important;
}
.btn-danger-outline {
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  position: relative;
  overflow: hidden;
}
.btn-danger-outline:before {
  content: '';
  position: absolute;
  z-index: -1;
  height: 2rem;
  width: 2rem;
  top: calc(50% - 1rem);
  left: calc(50% - 1rem);
  transform: scale(0);
  transition-duration: 0.5s;
  transition-timing-function: ease-out;
  background-color: #879a9f;
  border-color: #879a9f;
  border-radius: 100px;
}
.btn-danger-outline,
.btn-danger-outline:active,
.btn-danger-outline.active {
  background: none;
  border-color: #55666b;
  color: #55666b !important;
}
.btn-danger-outline:hover,
.btn-danger-outline:focus,
.btn-danger-outline.focus {
  color: #ffffff !important;
  background-color: #879a9f;
  border-color: #879a9f;
}
.btn-danger-outline:hover:before,
.btn-danger-outline:focus:before,
.btn-danger-outline.focus:before {
  transform: scale(10);
}
.btn-danger-outline.disabled,
.btn-danger-outline:disabled {
  color: #ffffff !important;
  background-color: #879a9f !important;
  border-color: #879a9f !important;
}
.btn-black-outline {
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  position: relative;
  overflow: hidden;
}
.btn-black-outline:before {
  content: '';
  position: absolute;
  z-index: -1;
  height: 2rem;
  width: 2rem;
  top: calc(50% - 1rem);
  left: calc(50% - 1rem);
  transform: scale(0);
  transition-duration: 0.5s;
  transition-timing-function: ease-out;
  background-color: #333333;
  border-color: #333333;
  border-radius: 100px;
}
.btn-black-outline,
.btn-black-outline:active,
.btn-black-outline.active {
  background: none;
  border-color: #000000;
  color: #000000 !important;
}
.btn-black-outline:hover,
.btn-black-outline:focus,
.btn-black-outline.focus {
  color: #ffffff !important;
  background-color: #333333;
  border-color: #333333;
}
.btn-black-outline:hover:before,
.btn-black-outline:focus:before,
.btn-black-outline.focus:before {
  transform: scale(10);
}
.btn-black-outline.disabled,
.btn-black-outline:disabled {
  color: #ffffff !important;
  background-color: #333333 !important;
  border-color: #333333 !important;
}
.btn-white-outline {
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  position: relative;
  overflow: hidden;
}
.btn-white-outline:before {
  content: '';
  position: absolute;
  z-index: -1;
  height: 2rem;
  width: 2rem;
  top: calc(50% - 1rem);
  left: calc(50% - 1rem);
  transform: scale(0);
  transition-duration: 0.5s;
  transition-timing-function: ease-out;
  background-color: #ffffff;
  border-color: #ffffff;
  border-radius: 100px;
}
.btn-white-outline,
.btn-white-outline:active,
.btn-white-outline.active {
  background: none;
  border-color: #ffffff;
  color: #ffffff !important;
}
.btn-white-outline:hover,
.btn-white-outline:focus,
.btn-white-outline.focus {
  color: #333333 !important;
  background-color: #ffffff;
  border-color: #ffffff;
}
.btn-white-outline:hover:before,
.btn-white-outline:focus:before,
.btn-white-outline.focus:before {
  transform: scale(10);
}
.text-primary {
  color: #a38dfd !important;
}
.text-secondary {
  color: #16a16c !important;
}
.text-success {
  color: #b2ccd2 !important;
}
.text-info {
  color: #1ba1e2 !important;
}
.text-warning {
  color: #82786e !important;
}
.text-danger {
  color: #879a9f !important;
}
.text-white {
  color: #ffffff !important;
}
.text-black {
  color: #000000 !important;
}
a.text-primary:hover,
a.text-primary:focus {
  color: #5229fb !important;
}
a.text-secondary:hover,
a.text-secondary:focus {
  color: #0a4730 !important;
}
a.text-success:hover,
a.text-success:focus {
  color: #72a1ac !important;
}
a.text-info:hover,
a.text-info:focus {
  color: #106087 !important;
}
a.text-warning:hover,
a.text-warning:focus {
  color: #4b453f !important;
}
a.text-danger:hover,
a.text-danger:focus {
  color: #55666b !important;
}
a.text-white:hover,
a.text-white:focus {
  color: #b3b3b3 !important;
}
a.text-black:hover,
a.text-black:focus {
  color: #4d4d4d !important;
}
.alert-success {
  background-color: #70c770;
}
.alert-info {
  background-color: #1ba1e2;
}
.alert-warning {
  background-color: #82786e;
}
.alert-danger {
  background-color: #879a9f;
}
.mbr-section-btn a.btn:not(.btn-form) {
  border-radius: 100px;
  transition-property: background-color, color, border-color, box-shadow;
  transition-duration: 0.3s, 0.3s, 0.3s, 0.8s;
  transition-timing-function: ease-in-out;
}
.mbr-section-btn a.btn:not(.btn-form):hover {
  box-shadow: 0 10px 40px 0 rgba(0, 0, 0, 0.2);
}
.mbr-gallery-filter li a {
  border-radius: 100px !important;
}
.mbr-gallery-filter li.active .btn {
  background-color: #a38dfd;
  border-color: #a38dfd;
  color: #ffffff;
}
.mbr-gallery-filter li.active .btn:focus {
  box-shadow: none;
}
.nav-tabs .nav-link {
  border-radius: 100px !important;
}
.btn-form {
  border-radius: 0;
}
.btn-form:hover {
  cursor: pointer;
}
a,
a:hover {
  color: #b3b3b3;
}
.mbr-plan-header.bg-primary .mbr-plan-subtitle,
.mbr-plan-header.bg-primary .mbr-plan-price-desc {
  color: #ffffff;
}
.mbr-plan-header.bg-success .mbr-plan-subtitle,
.mbr-plan-header.bg-success .mbr-plan-price-desc {
  color: #ffffff;
}
.mbr-plan-header.bg-info .mbr-plan-subtitle,
.mbr-plan-header.bg-info .mbr-plan-price-desc {
  color: #d0ecf9;
}
.mbr-plan-header.bg-warning .mbr-plan-subtitle,
.mbr-plan-header.bg-warning .mbr-plan-price-desc {
  color: #beb8b2;
}
.mbr-plan-header.bg-danger .mbr-plan-subtitle,
.mbr-plan-header.bg-danger .mbr-plan-price-desc {
  color: #ced6d8;
}
/* Scroll to top button*/
.scrollToTop_wraper {
  opacity: 0 !important;
}
/* Others*/
.note-check a[data-value='Rubik'] {
  font-style: normal;
}
.mbr-arrow a {
  color: #ffffff;
}
.form-control-label {
  position: relative;
  cursor: pointer;
  margin-bottom: 0.357em;
  padding: 0;
}
.alert {
  color: #ffffff;
  border-radius: 0;
  border: 0;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1.875rem;
  padding: 1.25rem;
  position: relative;
}
.alert.alert-form::after {
  background-color: inherit;
  bottom: -7px;
  content: '';
  display: block;
  height: 14px;
  left: 50%;
  margin-left: -7px;
  position: absolute;
  transform: rotate(45deg);
  width: 14px;
}
.form-control {
  background-color: #f5f5f5;
  box-shadow: none;
  color: #565656;

  font-size: 1.1rem;
  line-height: 1.43;
  min-height: 3.5em;
  padding: 1.07em 0.5em;
}
.form-control,
.form-control:focus {
  border: 1px solid #e8e8e8;
}
.form-active .form-control:invalid {
  border-color: red;
}
.mbr-overlay {
  background-color: #000;
  bottom: 0;
  left: 0;
  opacity: 0.5;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 0;
}
blockquote {
  font-style: italic;
  padding: 10px 0 10px 20px;
  font-size: 1.09rem;
  position: relative;
  border-color: #a38dfd;
  border-width: 3px;
}
ul,
ol,
pre,
blockquote {
  margin-bottom: 2.3125rem;
}
pre {
  background: #f4f4f4;
  padding: 10px 24px;
  white-space: pre-wrap;
}
.inactive {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  pointer-events: none;
  -webkit-user-drag: none;
  user-drag: none;
}
.mbr-section__comments .row {
  justify-content: center;
}
/* Forms */
.mbr-form .btn {
  margin: 0.4rem 0;
}
.mbr-form .input-group-btn a.btn {
  border-radius: 100px !important;
}
.mbr-form .input-group-btn a.btn:hover {
  box-shadow: 0 10px 40px 0 rgba(0, 0, 0, 0.2);
}
.mbr-form .input-group-btn button[type='submit'] {
  border-radius: 100px !important;
  padding: 1rem 2rem;
}
.mbr-form .input-group-btn button[type='submit']:hover {
  box-shadow: 0 10px 40px 0 rgba(0, 0, 0, 0.2);
}
.special-form {
  border-radius: 100px !important;
}
@media (max-width: 767px) {
  .btn {
    font-size: 0.75rem !important;
  }
  .btn .mbr-iconfont {
    font-size: 1rem !important;
  }
}

/* Social block */
.btn-social {
  font-size: 20px;
  border-radius: 50%;
  padding: 0;
  width: 44px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  position: relative;
  border: 2px solid #c0a375;
  border-color: #a38dfd;
  color: #232323;
  cursor: pointer;
}
.btn-social i {
  top: 0;
  line-height: 44px;
  width: 44px;
}
.btn-social:hover {
  color: #fff;
  background: #a38dfd;
}
.btn-social + .btn {
  margin-left: 0.1rem;
}
/* Footer */
.mbr-footer-content li::before,
.mbr-footer .mbr-contacts li::before {
  background: #a38dfd;
}
.mbr-footer-content li a:hover,
.mbr-footer .mbr-contacts li a:hover {
  color: #a38dfd;
}
.footer3 input[type='email'],
.footer4 input[type='email'] {
  border-radius: 100px !important;
}
.footer3 .input-group-btn a.btn,
.footer4 .input-group-btn a.btn {
  border-radius: 100px !important;
}
.footer3 .input-group-btn button[type='submit'],
.footer4 .input-group-btn button[type='submit'] {
  border-radius: 100px !important;
}
/* Headers*/
.header13 .form-inline input[type='email'],
.header14 .form-inline input[type='email'] {
  border-radius: 100px;
}
.header13 .form-inline input[type='text'],
.header14 .form-inline input[type='text'] {
  border-radius: 100px;
}
.header13 .form-inline input[type='tel'],
.header14 .form-inline input[type='tel'] {
  border-radius: 100px;
}
.header13 .form-inline a.btn,
.header14 .form-inline a.btn {
  border-radius: 100px;
}
.header13 .form-inline button,
.header14 .form-inline button {
  border-radius: 100px !important;
}
.note-air-layout .dropup .dropdown-menu,
.note-air-layout .navbar-fixed-bottom .dropdown .dropdown-menu {
  bottom: initial !important;
}
html,
body {
  height: auto;
  min-height: 100vh;
  /*overflow-y: scroll;
    -webkit-overflow-scrolling: touch;*/
}
.scroll-background {
  background-repeat: repeat-x !important;
}
.cid-qGQPqWmbAg .dropdown-item:before {
  font-family: MobiriseIcons !important;
  content: '\e966';
  display: inline-block;
  width: 0;
  position: absolute;
  left: 1rem;
  top: 0.5rem;
  margin-right: 0.5rem;
  line-height: 1;
  font-size: inherit;
  vertical-align: middle;
  text-align: center;
  overflow: hidden;
  transform: scale(0, 1);
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
}
.cid-qGQPqWmbAg .nav-item:focus,
.cid-qGQPqWmbAg .nav-link:focus {
  outline: none;
}
@media (min-width: 992px) {
  .cid-qGQPqWmbAg .dropdown-item:hover:before {
    transform: scale(1, 1);
    width: 16px;
  }
  .cid-qGQPqWmbAg .nav-item .nav-link {
    position: relative;
  }
  .cid-qGQPqWmbAg .nav-item .nav-link::before {
    position: absolute;
    content: '';
    width: 0;
    height: 3px;
    bottom: -0.5rem;
    left: 50%;
    background: linear-gradient(90deg, #a38dfd, #16a16c);
    transition: width 200ms linear, left 200ms linear;
  }
  .cid-qGQPqWmbAg .nav-item.open .nav-link::before {
    bottom: 0.2rem;
    width: 80% !important;
    left: 10% !important;
  }
  .cid-qGQPqWmbAg .nav-item .nav-link:hover::before {
    width: calc(100% + 2rem);
    left: -1rem;
  }
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item {
  width: auto;
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item::after {
  right: 0.5rem;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item:hover {
  padding-right: 2.5385em;
  padding-left: 3.5385em;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item:hover .mbr-iconfont:before {
  transform: scale(0, 1);
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item .mbr-iconfont {
  margin-left: -1.8rem;
  padding-right: 1rem;
  font-size: inherit;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item .mbr-iconfont:before {
  display: inline-block;
  transform: scale(1, 1);
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
}
.cid-qGQPqWmbAg .collapsed .dropdown-menu .dropdown-item:before {
  display: none;
}
.cid-qGQPqWmbAg .collapsed .dropdown .dropdown-menu .dropdown-item {
  padding: 0.235em 1.5em 0.235em 1.5em !important;
  transition: none;
  margin: 0 !important;
}
.cid-qGQPqWmbAg .navbar {
  min-height: 77px;
  transition: all 0.3s;
  background: #ffffff;
}
.cid-qGQPqWmbAg .navbar.opened {
  transition: all 0.3s;
  background: #ffffff !important;
}
.cid-qGQPqWmbAg .navbar .dropdown-item {
  padding: 0.235rem 1.5rem;
}
.cid-qGQPqWmbAg .navbar .navbar-collapse {
  justify-content: flex-end;
  z-index: 1;
}
.cid-qGQPqWmbAg .navbar.collapsed.opened .dropdown-menu {
  top: 0;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu {
  background: transparent !important;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-submenu {
  left: 0 !important;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-item:after {
  right: auto;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-toggle[data-toggle='dropdown-submenu']:after {
  margin-left: 0.25rem;
  border-top: 0.35em solid;
  border-right: 0.35em solid transparent;
  border-left: 0.35em solid transparent;
  border-bottom: 0;
  top: 55%;
}
.cid-qGQPqWmbAg .navbar.collapsed ul.navbar-nav li {
  margin: auto;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-item {
  padding: 0.25rem 1.5rem;
  text-align: center;
}
.cid-qGQPqWmbAg .navbar.collapsed .icons-menu {
  padding-left: 0;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
@media (max-width: 991px) {
  .cid-qGQPqWmbAg .navbar.opened .dropdown-menu {
    top: 0;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu {
    background: transparent !important;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-submenu {
    left: 0 !important;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-item:after {
    right: auto;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-toggle[data-toggle='dropdown-submenu']:after {
    margin-left: 0.25rem;
    border-top: 0.35em solid;
    border-right: 0.35em solid transparent;
    border-left: 0.35em solid transparent;
    border-bottom: 0;
    top: 55%;
  }
  .cid-qGQPqWmbAg .navbar .navbar-logo img {
    height: 3.8rem !important;
  }
  .cid-qGQPqWmbAg .navbar ul.navbar-nav li {
    margin: auto;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-item {
    padding: 0.25rem 1.5rem !important;
    text-align: center;
  }
  .cid-qGQPqWmbAg .navbar .navbar-brand {
    flex-shrink: initial;
    flex-basis: 70%;
    word-break: break-word;
  }
  .cid-qGQPqWmbAg .navbar .navbar-toggler {
    flex-basis: 30%;
  }
  .cid-qGQPqWmbAg .navbar .icons-menu {
    padding-left: 0;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
  .product-main-desc {
    position: relative;
  }
}
.cid-qGQPqWmbAg .navbar.navbar-short {
  background: #ffffff !important;
  min-height: 60px;
}
.cid-qGQPqWmbAg .navbar.navbar-short .navbar-logo img {
  height: 3rem !important;
}
.cid-qGQPqWmbAg .navbar.navbar-short .navbar-brand {
  padding: 0;
}
.cid-qGQPqWmbAg .navbar-brand {
  flex-shrink: 0;
  align-items: center;
  margin-right: 0;
  padding: 0;
  transition: all 0.3s;
  word-break: break-word;
  z-index: 1;
}
.cid-qGQPqWmbAg .navbar-brand .navbar-caption {
  line-height: inherit !important;
}
.cid-qGQPqWmbAg .navbar-brand .navbar-logo a {
  outline: none;
}
.cid-qGQPqWmbAg .dropdown-item.active,
.cid-qGQPqWmbAg .dropdown-item:active {
  background-color: transparent;
}
.cid-qGQPqWmbAg .navbar-expand-lg .navbar-nav .nav-link {
  padding: 0;
}
.cid-qGQPqWmbAg .nav-dropdown .link.dropdown-toggle {
  margin-right: 1.667em;
}
.cid-qGQPqWmbAg .nav-dropdown .link.dropdown-toggle[aria-expanded='true'] {
  margin-right: 0;
  padding: 0.667em 1.667em;
}
.cid-qGQPqWmbAg .navbar.navbar-expand-lg .dropdown .dropdown-menu {
  background: #ffffff;
}
.cid-qGQPqWmbAg .navbar.navbar-expand-lg .dropdown .dropdown-menu .dropdown-submenu {
  margin: 0;
  left: 100%;
}
.cid-qGQPqWmbAg .navbar .dropdown.open > .dropdown-menu {
  display: block;
}
.cid-qGQPqWmbAg ul.navbar-nav {
  flex-wrap: wrap;
}
.cid-qGQPqWmbAg .navbar-buttons {
  text-align: center;
}
.cid-qGQPqWmbAg button.navbar-toggler {
  outline: none;
  width: 31px;
  height: 20px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  align-self: center;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span {
  position: absolute;
  right: 0;
  width: 30px;
  height: 2px;
  border-right: 5px;
  background-color: #232323;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(1) {
  top: 0;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(2) {
  top: 8px;
  transition: all 0.15s;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(3) {
  top: 8px;
  transition: all 0.15s;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(4) {
  top: 16px;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(1) {
  top: 8px;
  width: 0;
  opacity: 0;
  right: 50%;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(2) {
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  transition: all 0.25s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(3) {
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  transition: all 0.25s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(4) {
  top: 8px;
  width: 0;
  opacity: 0;
  right: 50%;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg .navbar-dropdown {
  padding: 0.5rem 1rem;
  position: fixed;
}
.cid-qGQPqWmbAg a.nav-link {
  justify-content: center;
  display: flex;
  align-items: center;
}
.cid-qGQPqWmbAg .mbr-iconfont {
  font-size: 1.5rem;
  padding-right: 0.5rem;
}
.cid-qGQPqWmbAg .icons-menu {
  flex-wrap: wrap;
  display: flex;
  justify-content: center;
  padding-left: 1rem;
  text-align: center;
  /* width: */
}
.cid-qGQPqWmbAg .icons-menu span {
  font-size: 20px;
  color: #232323;
}
.cid-qGQQt2SI1j {
  background-image: url('../../../assets/images/banner-home.jpg');
}

.cid-qGQQt2SI1j .mbr-section-title,
.cid-qGQQt2SI1j .mbr-section-subtitle {
  color: #879a9f;
}
.cid-qGQQt2SI1j .mbr-section-text {
  color: #767676;
}
.cid-qGQQt2SI1j .mbr-text,
.cid-qGQQt2SI1j .typed-text,
.cid-qGQQt2SI1j .mbr-section-text {
  letter-spacing: 0.03rem;
}
.cid-qGQQt2SI1j .btn {
  margin-left: 4px !important;
}
.cid-qGQQt2SI1j .animated-element {
  color: #16a16c;
}
.cid-qGQQt2SI1j .typed-cursor {
  opacity: 1;
  -webkit-animation: blink 0.7s infinite;
  -moz-animation: blink 0.7s infinite;
  animation: blink 0.7s infinite;
  color: #16a16c;
}

.cid-qGQQt2SI1j2 {
  background-image: url('../../../assets/images/happy-family-having-fun-outdoors.jpg');
}

.cid-qGQQt2SI1j2 .mbr-section-title,
.cid-qGQQt2SI1j2 .mbr-section-subtitle {
  color: #ffffff;
}
.cid-qGQQt2SI1j2 .mbr-section-text {
  color: #767676;
}
.cid-qGQQt2SI1j2 .mbr-text,
.cid-qGQQt2SI1j2 .typed-text,
.cid-qGQQt2SI1j2 .mbr-section-text {
  letter-spacing: 0.03rem;
}
.cid-qGQQt2SI1j2 .btn {
  margin-left: 4px !important;
}
.cid-qGQQt2SI1j2 .animated-element {
  color: #16a16c;
}
.cid-qGQQt2SI1j2 .typed-cursor {
  opacity: 1;
  -webkit-animation: blink 0.7s infinite;
  -moz-animation: blink 0.7s infinite;
  animation: blink 0.7s infinite;
  color: #16a16c;
}
@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@-webkit-keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@-moz-keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.cid-qGQQt2SI1j .mbr-section-title {
  color: #232323;
  text-align: right;
}
.cid-qGQQt2SI1j .mbr-section-subtitle,
.cid-qGQQt2SI1j .typed-text {
  color: #232323;
  text-align: right;
}
.cid-qGQQt2SI1j2 .mbr-section-title {
  color: #ffffff;
  text-align: right;
}
.cid-qGQQt2SI1j2 .mbr-section-subtitle,
.cid-qGQQt2SI1j2 .typed-text {
  color: #ffffff;
  text-align: right;
}
.cid-qGQTcoHUjb {
  /*
  padding-top: 45px;
  padding-bottom: 45px;
  */
  background-color: #ffffff;
}

.cid-qGQQt2SI1j .shop-btn {
  text-align: right;
}
.cid-qGQQt2SI1j2 .shop-btn {
  text-align: right;
}
.cid-qGQTcoHUjb .mbr-section-subtitle {
  color: #767676;
}
.cid-qGQTcoHUjb .underline {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.cid-qGQTcoHUjb .underline .line {
  width: 5rem;
  height: 3px;
  background: linear-gradient(90deg, #a38dfd, #16a16c);
  display: inline-block;
}
.cid-qGQTcoHUjb .image-element {
  position: relative;
  z-index: 0;
  overflow: hidden;
}
.cid-qGQTcoHUjb .image-element img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center center;
}
.cid-qGQTcoHUjb .wrapper {
  padding: 2rem 1rem 1rem 1rem;
  background: #bbbbbb;
}
.cid-qGQTcoHUjb .card-overlay {
  display: none;
}
.cid-qGQTcoHUjb .mbr-section-title,
.cid-qGQTcoHUjb .underline,
.cid-qGQTcoHUjb .mbr-section-subtitle {
  padding-left: 1rem;
  padding-right: 1rem;
}
@media (min-width: 768px) {
  .cid-qGQTcoHUjb .image-element:hover .card-overlay {
    opacity: 0.5;
    border-bottom-right-radius: 7rem;
  }
  .cid-qGQTcoHUjb .image-element:hover .wrapper {
    padding-top: 0;
    border-bottom-right-radius: 7rem;
  }
  .cid-qGQTcoHUjb .image-element:hover .wrapper .collapsed-content {
    transition: opacity 0.5s, max-height 3s;
    opacity: 1;
    max-height: 999px;
    border-bottom-right-radius: 7rem;
  }
  .cid-qGQTcoHUjb .image-element:hover .wrapper .collapsed-content .mbr-section-btn {
    border-bottom-right-radius: 7rem;
  }
  .cid-qGQTcoHUjb .image-element.popup-btn:hover .card-overlay {
    border-bottom-right-radius: 0 !important;
  }
  .cid-qGQTcoHUjb .image-element.popup-btn:hover .wrapper {
    border-bottom-right-radius: 0 !important;
  }
  .cid-qGQTcoHUjb .image-element.popup-btn:hover .wrapper .collapsed-content {
    border-bottom-right-radius: 0 !important;
  }
  .cid-qGQTcoHUjb .image-element.popup-btn:hover .wrapper .collapsed-content .mbr-section-btn {
    border-bottom-right-radius: 0 !important;
  }
  .cid-qGQTcoHUjb .wrapper {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 1rem;
    background: transparent;
  }
  .cid-qGQTcoHUjb .wrapper .collapsed-content {
    transition: opacity 0.5s, max-height 1s;
    opacity: 0;
    max-height: 0px;
    overflow: hidden;
  }
  .cid-qGQTcoHUjb .card-overlay {
    transition: all 0.5s;
    opacity: 0;
    display: block;
  }
}
@media (max-width: 767px) {
  .cid-qGQTcoHUjb .underline .line {
    height: 2px;
  }
  .cid-qGQTcoHUjb .card-title,
  .cid-qGQTcoHUjb .underline,
  .cid-qGQTcoHUjb .mbr-text,
  .cid-qGQTcoHUjb .mbr-section-btn,
  .cid-qGQTcoHUjb .mbr-section-subtitle,
  .cid-qGQTcoHUjb .mbr-section-title {
    text-align: center !important;
  }
}
@media (min-width: 560px) {
  /* .order-sm-12:{
    order:12;
  } */
}
@media (min-width: 992px) {
  .cid-qGQTcoHUjb .main {
    padding-left: 3rem;
    padding-right: 3rem;
  }
  .cid-qGQTcoHUjb .mbr-section-title,
  .cid-qGQTcoHUjb .underline,
  .cid-qGQTcoHUjb .mbr-section-subtitle {
    padding-left: 3rem;
    padding-right: 3rem;
  }
}
.cid-qGQTcoHUjb H3 {
  text-align: left;
}
.cid-qGS68GH0pc {
  padding-top: 60px;
  padding-bottom: 60px;
  background-color: #ffffff;
}
.cid-qGS68GH0pc .carousel-item {
  justify-content: center;
}
.cid-qGS68GH0pc .carousel-control-prev {
  justify-content: flex-end;
  align-items: flex-end;
}
.cid-qGS68GH0pc .carousel-control-next {
  justify-content: flex-start;
  align-items: flex-end;
}
.cid-qGS68GH0pc .carousel-item.active,
.cid-qGS68GH0pc .carousel-item-next,
.cid-qGS68GH0pc .carousel-item-prev {
  display: flex;
}
.cid-qGS68GH0pc .carousel-controls a {
  transition: opacity 0.5s;
}
.cid-qGS68GH0pc .carousel-controls a:hover span,
.cid-qGS68GH0pc .carousel-controls a:focus span {
  opacity: 1;
}
.cid-qGS68GH0pc .carousel-controls a:hover svg,
.cid-qGS68GH0pc .carousel-controls a:focus svg {
  transition: all 0.25s;
  stroke-width: 3;
}
.cid-qGS68GH0pc .carousel-controls a:active svg {
  transition: all 0.1s;
  stroke-width: 5;
}
.cid-qGS68GH0pc .user_image {
  overflow: hidden;
  display: flex;
}
.cid-qGS68GH0pc .user_image .user_image_inner {
  max-width: 200px;
  max-height: 200px;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  display: flex;
  margin: 0 auto 3rem auto;
}
.cid-qGS68GH0pc .user_image .user_image_inner img {
  width: 100%;
  min-width: 100%;
  min-height: 100%;
}
.cid-qGS68GH0pc .user_text {
  color: #767676;
}
.cid-qGS68GH0pc .underline {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.cid-qGS68GH0pc .underline .line {
  width: 5rem;
  height: 3px;
  background: linear-gradient(90deg, #a38dfd, #16a16c);
  display: inline-block;
}
.cid-qGS68GH0pc .testimonials-quote svg {
  height: 30px;
  width: 30px;
}
.cid-qGS68GH0pc svg.svg-gradient {
  position: absolute;
  opacity: 0;
  z-index: -100;
}
.cid-qGS68GH0pc .user_name {
  color: #5a31fb;
}
.cid-qGS68GH0pc .carousel-controls svg {
  height: 60px;
}
@media (max-width: 230px) {
  .cid-qGS68GH0pc .user_image_inner {
    width: 100%;
    height: auto;
  }
}
@media (max-width: 991px) {
  .cid-qGS68GH0pc .testimonials-quote,
  .cid-qGS68GH0pc .user_text,
  .cid-qGS68GH0pc .user_name,
  .cid-qGS68GH0pc .user_desk {
    text-align: center !important;
  }
  .cid-qGS68GH0pc .carousel-controls a span {
    top: auto;
    bottom: 0;
  }
}
@media (min-width: 768px) {
  .cid-qGS68GH0pc .mbr-section-title,
  .cid-qGS68GH0pc .underline {
    padding-left: 3rem;
    padding-right: 3rem;
  }
}

/* } */
@media (min-width: 1200px) {
  .cid-qGS68GH0pc .carousel-control-prev span {
    right: -1rem;
  }
  .cid-qGS68GH0pc .carousel-control-next span {
    left: -1rem;
  }
  .cid-qGS68GH0pc .user-text {
    padding-right: 2rem;
  }
  .cid-qGS68GH0pc .carousel-control-prev,
  .cid-qGS68GH0pc .carousel-control-next {
    align-items: center;
  }
}
.cid-qGS69tBEmc {
  padding-top: 75px;
  padding-bottom: 90px;
  background-color: #efefef;
}
.cid-qGS69tBEmc .input-form {
  display: flex;
  justify-content: center;
}
.cid-qGS69tBEmc .input-form .form1 {
  width: 100%;
}
.cid-qGS69tBEmc .mbr-form {
  position: relative;
  display: inline-block;
  width: 100%;
}
.cid-qGS69tBEmc .mbr-form input[type='email'] {
  font-size: 1.2rem;
  height: 3.5rem;
  font-style: italic;
  border: 0px solid;
  border-radius: 5px;
  width: 100%;
  margin-right: 0.6rem;
  padding: 2.5rem 1.6rem;
  display: block;
}
.cid-qGS69tBEmc .mbr-section-btn {
  display: inline-block;
  position: absolute;
  top: 8px;
  right: 10px;
}
.cid-qGS69tBEmc .underline {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.cid-qGS69tBEmc .underline .line {
  width: 5rem;
  height: 3px;
  background: linear-gradient(90deg, #a38dfd, #16a16c);
  display: inline-block;
}
@media (min-width: 992px) {
  .cid-qGS69tBEmc .input-form .form1 {
    width: 80%;
  }
}
@media (max-width: 767px) {
  .cid-qGS69tBEmc {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  .cid-qGS69tBEmc .social-media {
    padding: 0;
    display: block;
  }
  .cid-qGS69tBEmc .subtext-1,
  .cid-qGS69tBEmc .subtext-2 {
    text-align: center !important;
  }
  .cid-qGS69tBEmc .form-text {
    text-align: center !important;
  }
  .cid-qGS69tBEmc .input-form div {
    width: 100%;
  }
  .cid-qGS69tBEmc .mbr-form {
    flex-direction: column;
    width: 100% !important;
  }
  .cid-qGS69tBEmc .mbr-form input[type='email'] {
    width: 100%;
  }
  .cid-qGS69tBEmc .mbr-form .mbr-section-btn {
    margin-top: 1rem;
    position: static;
  }
  .cid-qGS69tBEmc .mbr-form .mbr-section-btn a {
    display: block;
  }
}
.cid-qGRlVNVoZS {
  padding-top: 30px;
  padding-bottom: 30px;
  background-color: #048e53;
}
.cid-qGRlVNVoZS .mbr-section-title span {
  display: block;
}
.cid-qGRlVNVoZS .first-column,
.cid-qGRlVNVoZS .second-column {
  margin-bottom: 1rem;
}
.cid-qGRlVNVoZS .first-column ul,
.cid-qGRlVNVoZS .second-column ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
.cid-qGRlVNVoZS .first-column ul li,
.cid-qGRlVNVoZS .second-column ul li {
  margin-bottom: 1rem;
}
.cid-qGRlVNVoZS .card-support {
  margin-top: 1.5rem;
  padding: 0;
  list-style: none;
}
.cid-qGRlVNVoZS .card-support li {
  margin-bottom: 0.4rem;
  margin-right: 0.4rem;
  display: inline-block;
}
.cid-qGRlVNVoZS .mbr-iconfont {
  padding: 0 0.5rem;
}
@media (max-width: 767px) {
  .cid-qGRlVNVoZS .card-support {
    text-align: center;
  }
}
.cid-qGRlVNVoZS .first-column-item {
  color: #767676;
}
.cid-qGQPqWmbAg .dropdown-item:before {
  font-family: MobiriseIcons !important;
  content: '\e966';
  display: inline-block;
  width: 0;
  position: absolute;
  left: 1rem;
  top: 0.5rem;
  margin-right: 0.5rem;
  line-height: 1;
  font-size: inherit;
  vertical-align: middle;
  text-align: center;
  overflow: hidden;
  transform: scale(0, 1);
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
}
.cid-qGQPqWmbAg .nav-item:focus,
.cid-qGQPqWmbAg .nav-link:focus {
  outline: none;
}
@media (min-width: 992px) {
  .cid-qGQPqWmbAg .dropdown-item:hover:before {
    transform: scale(1, 1);
    width: 16px;
  }
  .cid-qGQPqWmbAg .nav-item .nav-link {
    position: relative;
  }
  .cid-qGQPqWmbAg .nav-item .nav-link::before {
    position: absolute;
    content: '';
    width: 0;
    height: 3px;
    bottom: -0.5rem;
    left: 50%;
    background: linear-gradient(90deg, #a38dfd, #16a16c);
    transition: width 200ms linear, left 200ms linear;
  }
  .cid-qGQPqWmbAg .nav-item.open .nav-link::before {
    bottom: 0.2rem;
    width: 80% !important;
    left: 10% !important;
  }
  .cid-qGQPqWmbAg .nav-item .nav-link:hover::before {
    width: calc(100% + 2rem);
    left: -1rem;
  }
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item {
  width: auto;
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item::after {
  right: 0.5rem;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item:hover {
  padding-right: 2.5385em;
  padding-left: 3.5385em;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item:hover .mbr-iconfont:before {
  transform: scale(0, 1);
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item .mbr-iconfont {
  margin-left: -1.8rem;
  padding-right: 1rem;
  font-size: inherit;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item .mbr-iconfont:before {
  display: inline-block;
  transform: scale(1, 1);
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
}
.cid-qGQPqWmbAg .collapsed .dropdown-menu .dropdown-item:before {
  display: none;
}
.cid-qGQPqWmbAg .collapsed .dropdown .dropdown-menu .dropdown-item {
  padding: 0.235em 1.5em 0.235em 1.5em !important;
  transition: none;
  margin: 0 !important;
}
.cid-qGQPqWmbAg .navbar {
  min-height: 77px;
  transition: all 0.3s;
  background: #ffffff;
}
.cid-qGQPqWmbAg .navbar.opened {
  transition: all 0.3s;
  background: #ffffff !important;
}
.cid-qGQPqWmbAg .navbar .dropdown-item {
  padding: 0.235rem 1.5rem;
}
.cid-qGQPqWmbAg .navbar .navbar-collapse {
  justify-content: flex-end;
  z-index: 1;
}
.cid-qGQPqWmbAg .navbar.collapsed.opened .dropdown-menu {
  top: 0;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu {
  background: transparent !important;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-submenu {
  left: 0 !important;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-item:after {
  right: auto;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-toggle[data-toggle='dropdown-submenu']:after {
  margin-left: 0.25rem;
  border-top: 0.35em solid;
  border-right: 0.35em solid transparent;
  border-left: 0.35em solid transparent;
  border-bottom: 0;
  top: 55%;
}
.cid-qGQPqWmbAg .navbar.collapsed ul.navbar-nav li {
  margin: auto;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-item {
  padding: 0.25rem 1.5rem;
  text-align: center;
}
.cid-qGQPqWmbAg .navbar.collapsed .icons-menu {
  padding-left: 0;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
@media (max-width: 991px) {
  .cid-qGQPqWmbAg .navbar.opened .dropdown-menu {
    top: 0;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu {
    background: transparent !important;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-submenu {
    left: 0 !important;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-item:after {
    right: auto;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-toggle[data-toggle='dropdown-submenu']:after {
    margin-left: 0.25rem;
    border-top: 0.35em solid;
    border-right: 0.35em solid transparent;
    border-left: 0.35em solid transparent;
    border-bottom: 0;
    top: 55%;
  }
  .cid-qGQPqWmbAg .navbar .navbar-logo img {
    height: 3.8rem !important;
  }
  .cid-qGQPqWmbAg .navbar ul.navbar-nav li {
    margin: auto;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-item {
    padding: 0.25rem 1.5rem !important;
    text-align: center;
  }
  .cid-qGQPqWmbAg .navbar .navbar-brand {
    flex-shrink: initial;
    flex-basis: 70%;
    word-break: break-word;
  }
  .cid-qGQPqWmbAg .navbar .navbar-toggler {
    flex-basis: 30%;
  }
  .cid-qGQPqWmbAg .navbar .icons-menu {
    padding-left: 0;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
}
.cid-qGQPqWmbAg .navbar.navbar-short {
  background: #ffffff !important;
  min-height: 60px;
}
.cid-qGQPqWmbAg .navbar.navbar-short .navbar-logo img {
  height: 3rem !important;
}
.cid-qGQPqWmbAg .navbar.navbar-short .navbar-brand {
  padding: 0;
}
.cid-qGQPqWmbAg .navbar-brand {
  flex-shrink: 0;
  align-items: center;
  margin-right: 0;
  padding: 0;
  transition: all 0.3s;
  word-break: break-word;
  z-index: 1;
}
.cid-qGQPqWmbAg .navbar-brand .navbar-caption {
  line-height: inherit !important;
}
.cid-qGQPqWmbAg .navbar-brand .navbar-logo a {
  outline: none;
}
.cid-qGQPqWmbAg .dropdown-item.active,
.cid-qGQPqWmbAg .dropdown-item:active {
  background-color: transparent;
}
.cid-qGQPqWmbAg .navbar-expand-lg .navbar-nav .nav-link {
  padding: 0;
}
.cid-qGQPqWmbAg .nav-dropdown .link.dropdown-toggle {
  margin-right: 1.667em;
}
.cid-qGQPqWmbAg .nav-dropdown .link.dropdown-toggle[aria-expanded='true'] {
  margin-right: 0;
  padding: 0.667em 1.667em;
}
.cid-qGQPqWmbAg .navbar.navbar-expand-lg .dropdown .dropdown-menu {
  background: #ffffff;
}
.cid-qGQPqWmbAg .navbar.navbar-expand-lg .dropdown .dropdown-menu .dropdown-submenu {
  margin: 0;
  left: 100%;
}
.cid-qGQPqWmbAg .navbar .dropdown.open > .dropdown-menu {
  display: block;
}
.cid-qGQPqWmbAg ul.navbar-nav {
  flex-wrap: wrap;
}
.cid-qGQPqWmbAg .navbar-buttons {
  text-align: center;
}
.cid-qGQPqWmbAg button.navbar-toggler {
  outline: none;
  width: 31px;
  height: 20px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  align-self: center;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span {
  position: absolute;
  right: 0;
  width: 30px;
  height: 2px;
  border-right: 5px;
  background-color: #232323;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(1) {
  top: 0;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(2) {
  top: 8px;
  transition: all 0.15s;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(3) {
  top: 8px;
  transition: all 0.15s;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(4) {
  top: 16px;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(1) {
  top: 8px;
  width: 0;
  opacity: 0;
  right: 50%;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(2) {
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  transition: all 0.25s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(3) {
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  transition: all 0.25s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(4) {
  top: 8px;
  width: 0;
  opacity: 0;
  right: 50%;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg .navbar-dropdown {
  padding: 0.5rem 1rem;
  position: fixed;
}
.cid-qGQPqWmbAg a.nav-link {
  justify-content: center;
  display: flex;
  align-items: center;
}
.cid-qGQPqWmbAg .mbr-iconfont {
  font-size: 1.5rem;
  padding-right: 0.5rem;
}
.cid-qGQPqWmbAg .icons-menu {
  flex-wrap: wrap;
  display: flex;
  justify-content: center;
  padding-left: 1rem;
  text-align: center;
}
.cid-qGQPqWmbAg .icons-menu span {
  font-size: 20px;
  color: #232323;
}
.cid-qGSFS3drDP {
  padding-top: 150px;
  padding-bottom: 150px;
  background-image: url('../../../assets/images/mbr-3-1620x1080.jpg');
}
.cid-qGSFS3drDP .card-wrapper {
  z-index: 3;
}
.cid-qGSFS3drDP .card-wrapper .mbr-section-title {
  letter-spacing: 1px;
  transition: all 2.5s cubic-bezier(0, 0.74, 0.52, 1.2);
}
.cid-qGSFS3drDP:hover .mbr-section-title {
  letter-spacing: 10px;
}
.cid-qGSFS3drDP .full-link {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  font-size: 0;
  z-index: 2;
}
.cid-qGSFS3drDP .popup-btn.card-wrapper {
  z-index: 1 !important;
}
.cid-qGSFS3drDP .text-block {
  text-align: left !important;
}
.cid-qGSEXF9DkQ {
  padding-top: 0px;
  padding-bottom: 0px;
  background-color: #232323;
  padding-left: 0;
  padding-right: 0;
}
.cid-qGSEXF9DkQ ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.cid-qGSEXF9DkQ ul li {
  padding: 1rem 0;
}
.cid-qGSEXF9DkQ ul li:hover {
  color: #a38dfd;
}
.cid-qGSEXF9DkQ .socicon {
  color: #fff;
  font-size: 1.5rem;
}
.cid-qGSEXF9DkQ .mbr-iconfont {
  margin-right: 1em;
  display: inline-block;
  vertical-align: bottom;
}
.cid-qGSEXF9DkQ .google-map {
  height: 100%;
  position: relative;
}
.cid-qGSEXF9DkQ .map {
  padding: 0;
  height: inherit !important;
}
.cid-qGSEXF9DkQ .social-list {
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  -webkit-justify-content: flex-start;
}
.cid-qGSEXF9DkQ .social-list .soc-item {
  padding: 0;
  margin: 2rem 2rem 0 0;
}
.cid-qGSEXF9DkQ .social-list a {
  margin: 0;
  opacity: 0.5;
  -webkit-transition: 0.2s linear;
  transition: 0.2s linear;
}
.cid-qGSEXF9DkQ .social-list a:hover {
  opacity: 1;
}
.cid-qGSEXF9DkQ .row-element,
.cid-qGSEXF9DkQ .image-element {
  padding: 0;
}
.cid-qGSEXF9DkQ .underline {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.cid-qGSEXF9DkQ .underline .line {
  width: 5rem;
  height: 3px;
  background: linear-gradient(90deg, #a38dfd, #16a16c);
  display: inline-block;
}
@media (min-width: 1500px) {
  .cid-qGSEXF9DkQ .text-content {
    padding: 5rem;
  }
}
@media (min-width: 768px) and (max-width: 1499px) {
  .cid-qGSEXF9DkQ .text-content {
    padding: 3rem;
  }
}
@media (max-width: 767px) {
  .cid-qGSEXF9DkQ .text-content {
    padding: 2rem 1rem;
  }
  .cid-qGSEXF9DkQ .underline .line {
    height: 2px;
  }
  .cid-qGSEXF9DkQ .mbr-title,
  .cid-qGSEXF9DkQ .underline,
  .cid-qGSEXF9DkQ .mbr-text,
  .cid-qGSEXF9DkQ .mbr-section-btn {
    text-align: center !important;
  }
}
.cid-qGSEXF9DkQ .mbr-title,
.cid-qGSEXF9DkQ .underline {
  color: #ffffff;
}
.cid-qGSEXF9DkQ .mbr-text,
.cid-qGSEXF9DkQ .mbr-section-btn {
  color: #ffffff;
}
.cid-qGSEXF9DkQ mbr-list LI {
  color: #ffffff;
}
.cid-qGSEXF9DkQ mbr-list {
  text-align: center;
}
.cid-qGSEXF9DkQ .mbr-list,
.cid-qGSEXF9DkQ .social-list mbr-list {
  color: #ffffff;
}
.cid-qGS5P16Ent {
  padding-top: 90px;
  padding-bottom: 90px;
  background-color: #ffffff;
}
.cid-qGS5P16Ent .container-fluid {
  padding: 0 3rem;
}
.cid-qGS5P16Ent .mbr-section-title {
  margin-bottom: 1.5em;
}
.cid-qGS5P16Ent .input-main {
  width: 99.6%;
  margin-left: 0.1em;
  justify-content: center;
}
.cid-qGS5P16Ent .input-main label {
  color: #000000;
}
.cid-qGS5P16Ent .form-1 {
  margin: 0 1em;
  padding: 0;
}
.cid-qGS5P16Ent .input-wrap {
  padding: 0;
  margin-bottom: 1.3em;
}
.cid-qGS5P16Ent .input-wrap input {
  border: 1px solid #ddd;
  border-radius: 22px;
  background-color: #efefef;
  padding: 18px 25px;
  width: 96%;
}
.cid-qGS5P16Ent .form-group {
  padding: 0;
}
.cid-qGS5P16Ent .form-group textarea {
  background-color: #efefef;
  border-radius: 22px;
  padding: 1rem;
  width: 98%;
}
.cid-qGS5P16Ent .btn-row {
  padding-left: 0;
}
.cid-qGS5P16Ent .btn {
  padding: 1rem 4rem;
}
@media (max-width: 767px) {
  .cid-qGS5P16Ent .container-fluid {
    padding: 0 1rem;
  }
  .cid-qGS5P16Ent .btn {
    padding: 1rem 2rem;
    font-size: 16px !important;
  }
}
.cid-qGRlVNVoZS {
  padding-top: 30px;
  padding-bottom: 30px;
  background-color: #048e53;
}
.cid-qGRlVNVoZS .mbr-section-title span {
  display: block;
}
.cid-qGRlVNVoZS .first-column,
.cid-qGRlVNVoZS .second-column {
  margin-bottom: 1rem;
}
.cid-qGRlVNVoZS .first-column ul,
.cid-qGRlVNVoZS .second-column ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
.cid-qGRlVNVoZS .first-column ul li,
.cid-qGRlVNVoZS .second-column ul li {
  margin-bottom: 1rem;
}
.cid-qGRlVNVoZS .card-support {
  margin-top: 1.5rem;
  padding: 0;
  list-style: none;
}
.cid-qGRlVNVoZS .card-support li {
  margin-bottom: 0.4rem;
  margin-right: 0.4rem;
  display: inline-block;
}
.cid-qGRlVNVoZS .mbr-iconfont {
  padding: 0 0.5rem;
}
@media (max-width: 767px) {
  .cid-qGRlVNVoZS .card-support {
    text-align: center;
  }
}
.cid-qGRlVNVoZS .first-column-item {
  color: #767676;
}
.cid-qGQPqWmbAg .dropdown-item:before {
  font-family: MobiriseIcons !important;
  content: '\e966';
  display: inline-block;
  width: 0;
  position: absolute;
  left: 1rem;
  top: 0.5rem;
  margin-right: 0.5rem;
  line-height: 1;
  font-size: inherit;
  vertical-align: middle;
  text-align: center;
  overflow: hidden;
  transform: scale(0, 1);
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
}
.cid-qGQPqWmbAg .nav-item:focus,
.cid-qGQPqWmbAg .nav-link:focus {
  outline: none;
}
@media (min-width: 992px) {
  .cid-qGQPqWmbAg .dropdown-item:hover:before {
    transform: scale(1, 1);
    width: 16px;
  }
  .cid-qGQPqWmbAg .nav-item .nav-link {
    position: relative;
  }
  .cid-qGQPqWmbAg .nav-item .nav-link::before {
    position: absolute;
    content: '';
    width: 0;
    height: 3px;
    bottom: -0.5rem;
    left: 50%;
    background: linear-gradient(90deg, #a38dfd, #16a16c);
    transition: width 200ms linear, left 200ms linear;
  }
  .cid-qGQPqWmbAg .nav-item.open .nav-link::before {
    bottom: 0.2rem;
    width: 80% !important;
    left: 10% !important;
  }
  .cid-qGQPqWmbAg .nav-item .nav-link:hover::before {
    width: calc(100% + 2rem);
    left: -1rem;
  }
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item {
  width: auto;
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item::after {
  right: 0.5rem;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item:hover {
  padding-right: 2.5385em;
  padding-left: 3.5385em;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item:hover .mbr-iconfont:before {
  transform: scale(0, 1);
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item .mbr-iconfont {
  margin-left: -1.8rem;
  padding-right: 1rem;
  font-size: inherit;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item .mbr-iconfont:before {
  display: inline-block;
  transform: scale(1, 1);
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
}
.cid-qGQPqWmbAg .collapsed .dropdown-menu .dropdown-item:before {
  display: none;
}
.cid-qGQPqWmbAg .collapsed .dropdown .dropdown-menu .dropdown-item {
  padding: 0.235em 1.5em 0.235em 1.5em !important;
  transition: none;
  margin: 0 !important;
}
.cid-qGQPqWmbAg .navbar {
  min-height: 77px;
  transition: all 0.3s;
  background: #ffffff;
}
.cid-qGQPqWmbAg .navbar.opened {
  transition: all 0.3s;
  background: #ffffff !important;
}
.cid-qGQPqWmbAg .navbar .dropdown-item {
  padding: 0.235rem 1.5rem;
}
.cid-qGQPqWmbAg .navbar .navbar-collapse {
  justify-content: flex-end;
  z-index: 1;
}
.cid-qGQPqWmbAg .navbar.collapsed.opened .dropdown-menu {
  top: 0;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu {
  background: transparent !important;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-submenu {
  left: 0 !important;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-item:after {
  right: auto;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-toggle[data-toggle='dropdown-submenu']:after {
  margin-left: 0.25rem;
  border-top: 0.35em solid;
  border-right: 0.35em solid transparent;
  border-left: 0.35em solid transparent;
  border-bottom: 0;
  top: 55%;
}
.cid-qGQPqWmbAg .navbar.collapsed ul.navbar-nav li {
  margin: auto;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-item {
  padding: 0.25rem 1.5rem;
  text-align: center;
}
.cid-qGQPqWmbAg .navbar.collapsed .icons-menu {
  padding-left: 0;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
@media (max-width: 991px) {
  .cid-qGQPqWmbAg .navbar.opened .dropdown-menu {
    top: 0;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu {
    background: transparent !important;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-submenu {
    left: 0 !important;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-item:after {
    right: auto;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-toggle[data-toggle='dropdown-submenu']:after {
    margin-left: 0.25rem;
    border-top: 0.35em solid;
    border-right: 0.35em solid transparent;
    border-left: 0.35em solid transparent;
    border-bottom: 0;
    top: 55%;
  }
  .cid-qGQPqWmbAg .navbar .navbar-logo img {
    height: 3.8rem !important;
  }
  .cid-qGQPqWmbAg .navbar ul.navbar-nav li {
    margin: auto;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-item {
    padding: 0.25rem 1.5rem !important;
    text-align: center;
  }
  .cid-qGQPqWmbAg .navbar .navbar-brand {
    flex-shrink: initial;
    flex-basis: 70%;
    word-break: break-word;
  }
  .cid-qGQPqWmbAg .navbar .navbar-toggler {
    flex-basis: 30%;
  }
  .cid-qGQPqWmbAg .navbar .icons-menu {
    padding-left: 0;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
}
.cid-qGQPqWmbAg .navbar.navbar-short {
  background: #ffffff !important;
  min-height: 60px;
}
.cid-qGQPqWmbAg .navbar.navbar-short .navbar-logo img {
  height: 3rem !important;
}
.cid-qGQPqWmbAg .navbar.navbar-short .navbar-brand {
  padding: 0;
}
.cid-qGQPqWmbAg .navbar-brand {
  flex-shrink: 0;
  align-items: center;
  margin-right: 0;
  padding: 0;
  transition: all 0.3s;
  word-break: break-word;
  z-index: 1;
}
.cid-qGQPqWmbAg .navbar-brand .navbar-caption {
  line-height: inherit !important;
}
.cid-qGQPqWmbAg .navbar-brand .navbar-logo a {
  outline: none;
}
.cid-qGQPqWmbAg .dropdown-item.active,
.cid-qGQPqWmbAg .dropdown-item:active {
  background-color: transparent;
}
.cid-qGQPqWmbAg .navbar-expand-lg .navbar-nav .nav-link {
  padding: 0;
}
.cid-qGQPqWmbAg .nav-dropdown .link.dropdown-toggle {
  margin-right: 1.667em;
}
.cid-qGQPqWmbAg .nav-dropdown .link.dropdown-toggle[aria-expanded='true'] {
  margin-right: 0;
  padding: 0.667em 1.667em;
}
.cid-qGQPqWmbAg .navbar.navbar-expand-lg .dropdown .dropdown-menu {
  background: #ffffff;
}
.cid-qGQPqWmbAg .navbar.navbar-expand-lg .dropdown .dropdown-menu .dropdown-submenu {
  margin: 0;
  left: 100%;
}
.cid-qGQPqWmbAg .navbar .dropdown.open > .dropdown-menu {
  display: block;
}
.cid-qGQPqWmbAg ul.navbar-nav {
  flex-wrap: wrap;
}
.cid-qGQPqWmbAg .navbar-buttons {
  text-align: center;
}
.cid-qGQPqWmbAg button.navbar-toggler {
  outline: none;
  width: 31px;
  height: 20px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  align-self: center;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span {
  position: absolute;
  right: 0;
  width: 30px;
  height: 2px;
  border-right: 5px;
  background-color: #232323;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(1) {
  top: 0;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(2) {
  top: 8px;
  transition: all 0.15s;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(3) {
  top: 8px;
  transition: all 0.15s;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(4) {
  top: 16px;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(1) {
  top: 8px;
  width: 0;
  opacity: 0;
  right: 50%;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(2) {
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  transition: all 0.25s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(3) {
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  transition: all 0.25s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(4) {
  top: 8px;
  width: 0;
  opacity: 0;
  right: 50%;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg .navbar-dropdown {
  padding: 0.5rem 1rem;
  position: fixed;
}
.cid-qGQPqWmbAg a.nav-link {
  justify-content: center;
  display: flex;
  align-items: center;
}
.cid-qGQPqWmbAg .mbr-iconfont {
  font-size: 1.5rem;
  padding-right: 0.5rem;
}
.cid-qGQPqWmbAg .icons-menu {
  flex-wrap: wrap;
  display: flex;
  justify-content: center;
  padding-left: 1rem;
  text-align: center;
}
.cid-qGQPqWmbAg .icons-menu span {
  font-size: 20px;
  color: #232323;
}
.cid-qGSREmHyjI {
  padding-top: 60px;
  padding-bottom: 60px;
  background-image: url('../../../assets/images/mbr-1-1618x1080.jpg');
}
.cid-qGSREmHyjI .container-fluid {
  padding: 0 3rem;
}
.cid-qGSREmHyjI .card .card-wrapper .card-img {
  overflow: hidden;
  margin-bottom: 1rem;
  z-index: 1;
}
.cid-qGSREmHyjI .card .card-wrapper .card-img img {
  transition: all 0.5s;
}
.cid-qGSREmHyjI .card .card-wrapper .card-img:hover img {
  -ms-transform: scale3d(1.05, 1.05, 1.05);
  -webkit-transform: scale3d(1.05, 1.05, 1.05);
  -o-transform: scale3d(1.05, 1.05, 1.05);
  -moz-transform: scale3d(1.05, 1.05, 1.05);
  transform: scale3d(1.05, 1.05, 1.05);
  transition: all 0.5s;
}
.cid-qGSREmHyjI .text-row {
  align-self: center;
}
@media (max-width: 767px) {
  .cid-qGSREmHyjI .container-fluid {
    padding: 0 1rem;
  }
  .cid-qGSREmHyjI .text-row {
    padding-bottom: 1rem;
  }
}
.cid-qGSUU1eG2K {
  padding-top: 0px;
  padding-bottom: 0px;
  background-color: #ffffff;
}
.cid-qGSUU1eG2K .img-wrap {
  width: 100% !important;
  height: 100% !important;
}
.cid-qGSUU1eG2K .row-element,
.cid-qGSUU1eG2K .image-element {
  padding: 0;
}
.cid-qGSUU1eG2K .image-element {
  display: flex;
  justify-content: center;
}
.cid-qGSUU1eG2K .image-element img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center center;
}
.cid-qGSUU1eG2K .underline {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.cid-qGSUU1eG2K .underline .line {
  width: 5rem;
  height: 3px;
  background: linear-gradient(90deg, #a38dfd, #16a16c);
  display: inline-block;
}
@media (min-width: 1500px) {
  .cid-qGSUU1eG2K .text-content {
    padding: 5rem;
  }
}
@media (min-width: 768px) and (max-width: 1499px) {
  .cid-qGSUU1eG2K .text-content {
    padding: 3rem;
  }
}
@media (max-width: 767px) {
  .cid-qGSUU1eG2K .text-content {
    padding: 2rem 1rem;
  }
  .cid-qGSUU1eG2K .underline .line {
    height: 2px;
  }
  .cid-qGSUU1eG2K .mbr-title,
  .cid-qGSUU1eG2K .underline,
  .cid-qGSUU1eG2K .mbr-text,
  .cid-qGSUU1eG2K .mbr-section-btn {
    text-align: center !important;
  }
}
.cid-qGRlVNVoZS {
  padding-top: 30px;
  padding-bottom: 30px;
  background-color: #048e53;
}
.cid-qGRlVNVoZS .mbr-section-title span {
  display: block;
}
.cid-qGRlVNVoZS .first-column,
.cid-qGRlVNVoZS .second-column {
  margin-bottom: 1rem;
}
.cid-qGRlVNVoZS .first-column ul,
.cid-qGRlVNVoZS .second-column ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
.cid-qGRlVNVoZS .first-column ul li,
.cid-qGRlVNVoZS .second-column ul li {
  margin-bottom: 1rem;
}
.cid-qGRlVNVoZS .card-support {
  margin-top: 1.5rem;
  padding: 0;
  list-style: none;
}
.cid-qGRlVNVoZS .card-support li {
  margin-bottom: 0.4rem;
  margin-right: 0.4rem;
  display: inline-block;
}
.cid-qGRlVNVoZS .mbr-iconfont {
  padding: 0 0.5rem;
}
@media (max-width: 767px) {
  .cid-qGRlVNVoZS .card-support {
    text-align: center;
  }
}
.cid-qGRlVNVoZS .first-column-item {
  color: #767676;
}
.cid-qGQPqWmbAg .dropdown-item:before {
  font-family: MobiriseIcons !important;
  content: '\e966';
  display: inline-block;
  width: 0;
  position: absolute;
  left: 1rem;
  top: 0.5rem;
  margin-right: 0.5rem;
  line-height: 1;
  font-size: inherit;
  vertical-align: middle;
  text-align: center;
  overflow: hidden;
  transform: scale(0, 1);
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
}
.cid-qGQPqWmbAg .nav-item:focus,
.cid-qGQPqWmbAg .nav-link:focus {
  outline: none;
}
@media (min-width: 992px) {
  .cid-qGQPqWmbAg .dropdown-item:hover:before {
    transform: scale(1, 1);
    width: 16px;
  }
  .cid-qGQPqWmbAg .nav-item .nav-link {
    position: relative;
  }
  .cid-qGQPqWmbAg .nav-item .nav-link::before {
    position: absolute;
    content: '';
    width: 0;
    height: 3px;
    bottom: -0.5rem;
    left: 50%;
    background: linear-gradient(90deg, #a38dfd, #16a16c);
    transition: width 200ms linear, left 200ms linear;
  }
  .cid-qGQPqWmbAg .nav-item.open .nav-link::before {
    bottom: 0.2rem;
    width: 80% !important;
    left: 10% !important;
  }
  .cid-qGQPqWmbAg .nav-item .nav-link:hover::before {
    width: calc(100% + 2rem);
    left: -1rem;
  }
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item {
  width: auto;
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item::after {
  right: 0.5rem;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item:hover {
  padding-right: 2.5385em;
  padding-left: 3.5385em;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item:hover .mbr-iconfont:before {
  transform: scale(0, 1);
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item .mbr-iconfont {
  margin-left: -1.8rem;
  padding-right: 1rem;
  font-size: inherit;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item .mbr-iconfont:before {
  display: inline-block;
  transform: scale(1, 1);
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
}
.cid-qGQPqWmbAg .collapsed .dropdown-menu .dropdown-item:before {
  display: none;
}
.cid-qGQPqWmbAg .collapsed .dropdown .dropdown-menu .dropdown-item {
  padding: 0.235em 1.5em 0.235em 1.5em !important;
  transition: none;
  margin: 0 !important;
}
.cid-qGQPqWmbAg .navbar {
  min-height: 77px;
  transition: all 0.3s;
  background: #ffffff;
}
.cid-qGQPqWmbAg .navbar.opened {
  transition: all 0.3s;
  background: #ffffff !important;
}
.cid-qGQPqWmbAg .navbar .dropdown-item {
  padding: 0.235rem 1.5rem;
}
.cid-qGQPqWmbAg .navbar .navbar-collapse {
  justify-content: flex-end;
  z-index: 1;
}
.cid-qGQPqWmbAg .navbar.collapsed.opened .dropdown-menu {
  top: 0;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu {
  background: transparent !important;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-submenu {
  left: 0 !important;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-item:after {
  right: auto;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-toggle[data-toggle='dropdown-submenu']:after {
  margin-left: 0.25rem;
  border-top: 0.35em solid;
  border-right: 0.35em solid transparent;
  border-left: 0.35em solid transparent;
  border-bottom: 0;
  top: 55%;
}
.cid-qGQPqWmbAg .navbar.collapsed ul.navbar-nav li {
  margin: auto;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-item {
  padding: 0.25rem 1.5rem;
  text-align: center;
}
.cid-qGQPqWmbAg .navbar.collapsed .icons-menu {
  padding-left: 0;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
@media (max-width: 991px) {
  .cid-qGQPqWmbAg .navbar.opened .dropdown-menu {
    top: 0;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu {
    background: transparent !important;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-submenu {
    left: 0 !important;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-item:after {
    right: auto;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-toggle[data-toggle='dropdown-submenu']:after {
    margin-left: 0.25rem;
    border-top: 0.35em solid;
    border-right: 0.35em solid transparent;
    border-left: 0.35em solid transparent;
    border-bottom: 0;
    top: 55%;
  }
  .cid-qGQPqWmbAg .navbar .navbar-logo img {
    height: 3.8rem !important;
  }
  .cid-qGQPqWmbAg .navbar ul.navbar-nav li {
    margin: auto;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-item {
    padding: 0.25rem 1.5rem !important;
    text-align: center;
  }
  .cid-qGQPqWmbAg .navbar .navbar-brand {
    flex-shrink: initial;
    flex-basis: 70%;
    word-break: break-word;
  }
  .cid-qGQPqWmbAg .navbar .navbar-toggler {
    flex-basis: 30%;
  }
  .cid-qGQPqWmbAg .navbar .icons-menu {
    padding-left: 0;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
}
.cid-qGQPqWmbAg .navbar.navbar-short {
  background: #ffffff !important;
  min-height: 60px;
}
.cid-qGQPqWmbAg .navbar.navbar-short .navbar-logo img {
  height: 3rem !important;
}
.cid-qGQPqWmbAg .navbar.navbar-short .navbar-brand {
  padding: 0;
}
.cid-qGQPqWmbAg .navbar-brand {
  flex-shrink: 0;
  align-items: center;
  margin-right: 0;
  padding: 0;
  transition: all 0.3s;
  word-break: break-word;
  z-index: 1;
}
.cid-qGQPqWmbAg .navbar-brand .navbar-caption {
  line-height: inherit !important;
}
.cid-qGQPqWmbAg .navbar-brand .navbar-logo a {
  outline: none;
}
.cid-qGQPqWmbAg .dropdown-item.active,
.cid-qGQPqWmbAg .dropdown-item:active {
  background-color: transparent;
}
.cid-qGQPqWmbAg .navbar-expand-lg .navbar-nav .nav-link {
  padding: 0;
}
.cid-qGQPqWmbAg .nav-dropdown .link.dropdown-toggle {
  margin-right: 1.667em;
}
.cid-qGQPqWmbAg .nav-dropdown .link.dropdown-toggle[aria-expanded='true'] {
  margin-right: 0;
  padding: 0.667em 1.667em;
}
.cid-qGQPqWmbAg .navbar.navbar-expand-lg .dropdown .dropdown-menu {
  background: #ffffff;
}
.cid-qGQPqWmbAg .navbar.navbar-expand-lg .dropdown .dropdown-menu .dropdown-submenu {
  margin: 0;
  left: 100%;
}
.cid-qGQPqWmbAg .navbar .dropdown.open > .dropdown-menu {
  display: block;
}
.cid-qGQPqWmbAg ul.navbar-nav {
  flex-wrap: wrap;
}
.cid-qGQPqWmbAg .navbar-buttons {
  text-align: center;
}
.cid-qGQPqWmbAg button.navbar-toggler {
  outline: none;
  width: 31px;
  height: 20px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  align-self: center;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span {
  position: absolute;
  right: 0;
  width: 30px;
  height: 2px;
  border-right: 5px;
  background-color: #232323;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(1) {
  top: 0;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(2) {
  top: 8px;
  transition: all 0.15s;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(3) {
  top: 8px;
  transition: all 0.15s;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(4) {
  top: 16px;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(1) {
  top: 8px;
  width: 0;
  opacity: 0;
  right: 50%;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(2) {
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  transition: all 0.25s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(3) {
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  transition: all 0.25s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(4) {
  top: 8px;
  width: 0;
  opacity: 0;
  right: 50%;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg .navbar-dropdown {
  padding: 0.5rem 1rem;
  position: fixed;
}
.cid-qGQPqWmbAg a.nav-link {
  justify-content: center;
  display: flex;
  align-items: center;
}
.cid-qGQPqWmbAg .mbr-iconfont {
  font-size: 1.5rem;
  padding-right: 0.5rem;
}
.cid-qGQPqWmbAg .icons-menu {
  flex-wrap: wrap;
  display: flex;
  justify-content: center;
  padding-left: 1rem;
  text-align: center;
}
.cid-qGQPqWmbAg .icons-menu span {
  font-size: 20px;
  color: #232323;
}
.cid-qGRlVNVoZS {
  padding-top: 30px;
  padding-bottom: 30px;
  background-color: #048e53;
}
.cid-qGRlVNVoZS .mbr-section-title span {
  display: block;
}
.cid-qGRlVNVoZS .first-column,
.cid-qGRlVNVoZS .second-column {
  margin-bottom: 1rem;
}
.cid-qGRlVNVoZS .first-column ul,
.cid-qGRlVNVoZS .second-column ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
.cid-qGRlVNVoZS .first-column ul li,
.cid-qGRlVNVoZS .second-column ul li {
  margin-bottom: 1rem;
}
.cid-qGRlVNVoZS .card-support {
  margin-top: 1.5rem;
  padding: 0;
  list-style: none;
}
.cid-qGRlVNVoZS .card-support li {
  margin-bottom: 0.4rem;
  margin-right: 0.4rem;
  display: inline-block;
}
.cid-qGRlVNVoZS .mbr-iconfont {
  padding: 0 0.5rem;
}
@media (max-width: 767px) {
  .cid-qGRlVNVoZS .card-support {
    text-align: center;
  }
}
.cid-qGRlVNVoZS .first-column-item {
  color: #767676;
}
.cid-qGQPqWmbAg .dropdown-item:before {
  font-family: MobiriseIcons !important;
  content: '\e966';
  display: inline-block;
  width: 0;
  position: absolute;
  left: 1rem;
  top: 0.5rem;
  margin-right: 0.5rem;
  line-height: 1;
  font-size: inherit;
  vertical-align: middle;
  text-align: center;
  overflow: hidden;
  transform: scale(0, 1);
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
}
.cid-qGQPqWmbAg .nav-item:focus,
.cid-qGQPqWmbAg .nav-link:focus {
  outline: none;
}
@media (min-width: 992px) {
  .cid-qGQPqWmbAg .dropdown-item:hover:before {
    transform: scale(1, 1);
    width: 16px;
  }
  .cid-qGQPqWmbAg .nav-item .nav-link {
    position: relative;
  }
  .cid-qGQPqWmbAg .nav-item .nav-link::before {
    position: absolute;
    content: '';
    width: 0;
    height: 3px;
    bottom: -0.5rem;
    left: 50%;
    background: linear-gradient(90deg, #a38dfd, #16a16c);
    transition: width 200ms linear, left 200ms linear;
  }
  .cid-qGQPqWmbAg .nav-item.open .nav-link::before {
    bottom: 0.2rem;
    width: 80% !important;
    left: 10% !important;
  }
  .cid-qGQPqWmbAg .nav-item .nav-link:hover::before {
    width: calc(100% + 2rem);
    left: -1rem;
  }
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item {
  width: auto;
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item::after {
  right: 0.5rem;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item:hover {
  padding-right: 2.5385em;
  padding-left: 3.5385em;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item:hover .mbr-iconfont:before {
  transform: scale(0, 1);
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item .mbr-iconfont {
  margin-left: -1.8rem;
  padding-right: 1rem;
  font-size: inherit;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item .mbr-iconfont:before {
  display: inline-block;
  transform: scale(1, 1);
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
}
.cid-qGQPqWmbAg .collapsed .dropdown-menu .dropdown-item:before {
  display: none;
}
.cid-qGQPqWmbAg .collapsed .dropdown .dropdown-menu .dropdown-item {
  padding: 0.235em 1.5em 0.235em 1.5em !important;
  transition: none;
  margin: 0 !important;
}
.cid-qGQPqWmbAg .navbar {
  min-height: 77px;
  transition: all 0.3s;
  background: #ffffff;
}
.cid-qGQPqWmbAg .navbar.opened {
  transition: all 0.3s;
  background: #ffffff !important;
}
.cid-qGQPqWmbAg .navbar .dropdown-item {
  padding: 0.235rem 1.5rem;
}
.cid-qGQPqWmbAg .navbar .navbar-collapse {
  justify-content: flex-end;
  z-index: 1;
}
.cid-qGQPqWmbAg .navbar.collapsed.opened .dropdown-menu {
  top: 0;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu {
  background: transparent !important;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-submenu {
  left: 0 !important;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-item:after {
  right: auto;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-toggle[data-toggle='dropdown-submenu']:after {
  margin-left: 0.25rem;
  border-top: 0.35em solid;
  border-right: 0.35em solid transparent;
  border-left: 0.35em solid transparent;
  border-bottom: 0;
  top: 55%;
}
.cid-qGQPqWmbAg .navbar.collapsed ul.navbar-nav li {
  margin: auto;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-item {
  padding: 0.25rem 1.5rem;
  text-align: center;
}
.cid-qGQPqWmbAg .navbar.collapsed .icons-menu {
  padding-left: 0;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
@media (max-width: 991px) {
  .cid-qGQPqWmbAg .navbar.opened .dropdown-menu {
    top: 0;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu {
    background: transparent !important;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-submenu {
    left: 0 !important;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-item:after {
    right: auto;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-toggle[data-toggle='dropdown-submenu']:after {
    margin-left: 0.25rem;
    border-top: 0.35em solid;
    border-right: 0.35em solid transparent;
    border-left: 0.35em solid transparent;
    border-bottom: 0;
    top: 55%;
  }
  .cid-qGQPqWmbAg .navbar .navbar-logo img {
    height: 3.8rem !important;
  }
  .cid-qGQPqWmbAg .navbar ul.navbar-nav li {
    margin: auto;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-item {
    padding: 0.25rem 1.5rem !important;
    text-align: center;
  }
  .cid-qGQPqWmbAg .navbar .navbar-brand {
    flex-shrink: initial;
    flex-basis: 70%;
    word-break: break-word;
  }
  .cid-qGQPqWmbAg .navbar .navbar-toggler {
    flex-basis: 30%;
  }
  .cid-qGQPqWmbAg .navbar .icons-menu {
    padding-left: 0;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
}
.cid-qGQPqWmbAg .navbar.navbar-short {
  background: #ffffff !important;
  min-height: 60px;
}
.cid-qGQPqWmbAg .navbar.navbar-short .navbar-logo img {
  height: 3rem !important;
}
.cid-qGQPqWmbAg .navbar.navbar-short .navbar-brand {
  padding: 0;
}
.cid-qGQPqWmbAg .navbar-brand {
  flex-shrink: 0;
  align-items: center;
  margin-right: 0;
  padding: 0;
  transition: all 0.3s;
  word-break: break-word;
  z-index: 1;
}
.cid-qGQPqWmbAg .navbar-brand .navbar-caption {
  line-height: inherit !important;
}
.cid-qGQPqWmbAg .navbar-brand .navbar-logo a {
  outline: none;
}
.cid-qGQPqWmbAg .dropdown-item.active,
.cid-qGQPqWmbAg .dropdown-item:active {
  background-color: transparent;
}
.cid-qGQPqWmbAg .navbar-expand-lg .navbar-nav .nav-link {
  padding: 0;
}
.cid-qGQPqWmbAg .nav-dropdown .link.dropdown-toggle {
  margin-right: 1.667em;
}
.cid-qGQPqWmbAg .nav-dropdown .link.dropdown-toggle[aria-expanded='true'] {
  margin-right: 0;
  padding: 0.667em 1.667em;
}
.cid-qGQPqWmbAg .navbar.navbar-expand-lg .dropdown .dropdown-menu {
  background: #ffffff;
}
.cid-qGQPqWmbAg .navbar.navbar-expand-lg .dropdown .dropdown-menu .dropdown-submenu {
  margin: 0;
  left: 100%;
}
.cid-qGQPqWmbAg .navbar .dropdown.open > .dropdown-menu {
  display: block;
}
.cid-qGQPqWmbAg ul.navbar-nav {
  flex-wrap: wrap;
}
.cid-qGQPqWmbAg .navbar-buttons {
  text-align: center;
}
.cid-qGQPqWmbAg button.navbar-toggler {
  outline: none;
  width: 31px;
  height: 20px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  align-self: center;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span {
  position: absolute;
  right: 0;
  width: 30px;
  height: 2px;
  border-right: 5px;
  background-color: #232323;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(1) {
  top: 0;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(2) {
  top: 8px;
  transition: all 0.15s;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(3) {
  top: 8px;
  transition: all 0.15s;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(4) {
  top: 16px;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(1) {
  top: 8px;
  width: 0;
  opacity: 0;
  right: 50%;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(2) {
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  transition: all 0.25s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(3) {
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  transition: all 0.25s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(4) {
  top: 8px;
  width: 0;
  opacity: 0;
  right: 50%;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg .navbar-dropdown {
  padding: 0.5rem 1rem;
  position: fixed;
}
.cid-qGQPqWmbAg a.nav-link {
  justify-content: center;
  display: flex;
  align-items: center;
}
.cid-qGQPqWmbAg .mbr-iconfont {
  font-size: 1.5rem;
  padding-right: 0.5rem;
}
.cid-qGQPqWmbAg .icons-menu {
  flex-wrap: wrap;
  display: flex;
  justify-content: center;
  padding-left: 1rem;
  text-align: center;
}
.cid-qGQPqWmbAg .icons-menu span {
  font-size: 20px;
  color: #232323;
}
.cid-qGSQ4G0pCw {
  padding-top: 150px;
  padding-bottom: 150px;
  background-image: url('../../../assets/images/10-1200x800.jpg');
}
.cid-qGSQ4G0pCw .card-wrapper {
  z-index: 3;
}
.cid-qGSQ4G0pCw .card-wrapper .mbr-section-title {
  letter-spacing: 1px;
  transition: all 2.5s cubic-bezier(0, 0.74, 0.52, 1.2);
}
.cid-qGSQ4G0pCw:hover .mbr-section-title {
  letter-spacing: 10px;
}
.cid-qGSQ4G0pCw .full-link {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  font-size: 0;
  z-index: 2;
}
.cid-qGSQ4G0pCw .popup-btn.card-wrapper {
  z-index: 1 !important;
}
.cid-qGSQ4G0pCw .text-block {
  text-align: right !important;
}
.cid-qGSQfGVLMv {
  padding-top: 60px;
  padding-bottom: 60px;
  background-color: #ffffff;
}
.cid-qGSQfGVLMv .counter-container {
  color: #767676;
}
.cid-qGSQfGVLMv .counter-container ul {
  margin-bottom: 0;
}
.cid-qGSQfGVLMv .counter-container ul li {
  margin-bottom: 1rem;
  list-style: none;
}
.cid-qGSQfGVLMv .counter-container ul li:before {
  position: absolute;
  left: 0px;
  margin-top: -10px;
  padding-top: 3px;
  content: '';
  display: inline-block;
  text-align: center;
  margin: 5px 10px;
  line-height: 20px;
  transition: all 0.2s;
  color: #ffffff;
  background: #16a16c;
  background: linear-gradient(135deg, #16a16c, #ffef00);
  width: 25px;
  height: 25px;
  border-radius: 50%;
}
.cid-qGSQchBmqn {
  padding-top: 150px;
  padding-bottom: 150px;
  background-image: url('../../../assets/images/background6.jpg');
}
.cid-qGSQchBmqn .row {
  justify-content: flex-start;
}
.cid-qGSQchBmqn .row {
  justify-content: flex-end;
}
.cid-qGSQchBmqn .form-container {
  background: rgba(255, 255, 255, 0.8) none repeat scroll 0 0;
  display: inline-block;
  padding: 3rem;
  position: relative;
}
.cid-qGSQchBmqn .mbr-section-title {
  margin-left: -4px;
}
.cid-qGSQchBmqn .media-container-column {
  width: 100%;
}
.cid-qGSQchBmqn .form {
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: column;
}
.cid-qGSQchBmqn .form-group {
  margin: 0;
  width: 100%;
}
.cid-qGSQchBmqn .form-group .form-control {
  padding-left: 1rem;
}
@media (min-width: 1600px) {
  .cid-qGSQchBmqn .my-col {
    flex: 0 0 50%;
    max-width: 50%;
  }
}
@media (min-width: 992px) {
  .cid-qGSQchBmqn .form-container {
    margin-left: 5rem;
    margin-right: 5rem;
  }
}
@media (min-width: 768px) {
  .cid-qGSQchBmqn .form-group {
    flex-direction: row;
  }
  .cid-qGSQchBmqn .mbr-section-btn {
    padding-right: 0.5rem;
    position: absolute;
    align-self: center;
    right: 0;
    height: 100%;
    display: flex;
    align-items: center;
  }
  .cid-qGSQchBmqn .mbr-section-btn .btn {
    margin: 0;
  }
}
.cid-qGRlVNVoZS {
  padding-top: 30px;
  padding-bottom: 30px;
  background-color: #048e53;
}
.cid-qGRlVNVoZS .mbr-section-title span {
  display: block;
}
.cid-qGRlVNVoZS .first-column,
.cid-qGRlVNVoZS .second-column {
  margin-bottom: 1rem;
}
.cid-qGRlVNVoZS .first-column ul,
.cid-qGRlVNVoZS .second-column ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
.cid-qGRlVNVoZS .first-column ul li,
.cid-qGRlVNVoZS .second-column ul li {
  margin-bottom: 1rem;
}
.cid-qGRlVNVoZS .card-support {
  margin-top: 1.5rem;
  padding: 0;
  list-style: none;
}
.cid-qGRlVNVoZS .card-support li {
  margin-bottom: 0.4rem;
  margin-right: 0.4rem;
  display: inline-block;
}
.cid-qGRlVNVoZS .mbr-iconfont {
  padding: 0 0.5rem;
}
@media (max-width: 767px) {
  .cid-qGRlVNVoZS .card-support {
    text-align: center;
  }
}
.cid-qGRlVNVoZS .first-column-item {
  color: #767676;
}
.cid-qGQPqWmbAg .dropdown-item:before {
  font-family: MobiriseIcons !important;
  content: '\e966';
  display: inline-block;
  width: 0;
  position: absolute;
  left: 1rem;
  top: 0.5rem;
  margin-right: 0.5rem;
  line-height: 1;
  font-size: inherit;
  vertical-align: middle;
  text-align: center;
  overflow: hidden;
  transform: scale(0, 1);
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
}
.cid-qGQPqWmbAg .nav-item:focus,
.cid-qGQPqWmbAg .nav-link:focus {
  outline: none;
}
@media (min-width: 992px) {
  .cid-qGQPqWmbAg .dropdown-item:hover:before {
    transform: scale(1, 1);
    width: 16px;
  }
  .cid-qGQPqWmbAg .nav-item .nav-link {
    position: relative;
  }
  .cid-qGQPqWmbAg .nav-item .nav-link::before {
    position: absolute;
    content: '';
    width: 0;
    height: 3px;
    bottom: -0.5rem;
    left: 50%;
    background: linear-gradient(90deg, #a38dfd, #16a16c);
    transition: width 200ms linear, left 200ms linear;
  }
  .cid-qGQPqWmbAg .nav-item.open .nav-link::before {
    bottom: 0.2rem;
    width: 80% !important;
    left: 10% !important;
  }
  .cid-qGQPqWmbAg .nav-item .nav-link:hover::before {
    width: calc(100% + 2rem);
    left: -1rem;
  }
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item {
  width: auto;
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item::after {
  right: 0.5rem;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item:hover {
  padding-right: 2.5385em;
  padding-left: 3.5385em;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item:hover .mbr-iconfont:before {
  transform: scale(0, 1);
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item .mbr-iconfont {
  margin-left: -1.8rem;
  padding-right: 1rem;
  font-size: inherit;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item .mbr-iconfont:before {
  display: inline-block;
  transform: scale(1, 1);
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
}
.cid-qGQPqWmbAg .collapsed .dropdown-menu .dropdown-item:before {
  display: none;
}
.cid-qGQPqWmbAg .collapsed .dropdown .dropdown-menu .dropdown-item {
  padding: 0.235em 1.5em 0.235em 1.5em !important;
  transition: none;
  margin: 0 !important;
}
.cid-qGQPqWmbAg .navbar {
  min-height: 77px;
  transition: all 0.3s;
  background: #ffffff;
}
.cid-qGQPqWmbAg .navbar.opened {
  transition: all 0.3s;
  background: #ffffff !important;
}
.cid-qGQPqWmbAg .navbar .dropdown-item {
  padding: 0.235rem 1.5rem;
}
.cid-qGQPqWmbAg .navbar .navbar-collapse {
  justify-content: flex-end;
  z-index: 1;
}
.cid-qGQPqWmbAg .navbar.collapsed.opened .dropdown-menu {
  top: 0;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu {
  background: transparent !important;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-submenu {
  left: 0 !important;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-item:after {
  right: auto;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-toggle[data-toggle='dropdown-submenu']:after {
  margin-left: 0.25rem;
  border-top: 0.35em solid;
  border-right: 0.35em solid transparent;
  border-left: 0.35em solid transparent;
  border-bottom: 0;
  top: 55%;
}
.cid-qGQPqWmbAg .navbar.collapsed ul.navbar-nav li {
  margin: auto;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-item {
  padding: 0.25rem 1.5rem;
  text-align: center;
}
.cid-qGQPqWmbAg .navbar.collapsed .icons-menu {
  padding-left: 0;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
@media (max-width: 991px) {
  .cid-qGQPqWmbAg .navbar.opened .dropdown-menu {
    top: 0;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu {
    background: transparent !important;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-submenu {
    left: 0 !important;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-item:after {
    right: auto;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-toggle[data-toggle='dropdown-submenu']:after {
    margin-left: 0.25rem;
    border-top: 0.35em solid;
    border-right: 0.35em solid transparent;
    border-left: 0.35em solid transparent;
    border-bottom: 0;
    top: 55%;
  }
  .cid-qGQPqWmbAg .navbar .navbar-logo img {
    height: 3.8rem !important;
  }
  .cid-qGQPqWmbAg .navbar ul.navbar-nav li {
    margin: auto;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-item {
    padding: 0.25rem 1.5rem !important;
    text-align: center;
  }
  .cid-qGQPqWmbAg .navbar .navbar-brand {
    flex-shrink: initial;
    flex-basis: 70%;
    word-break: break-word;
  }
  .cid-qGQPqWmbAg .navbar .navbar-toggler {
    flex-basis: 30%;
  }
  .cid-qGQPqWmbAg .navbar .icons-menu {
    padding-left: 0;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
}
.cid-qGQPqWmbAg .navbar.navbar-short {
  background: #ffffff !important;
  min-height: 60px;
}
.cid-qGQPqWmbAg .navbar.navbar-short .navbar-logo img {
  height: 3rem !important;
}
.cid-qGQPqWmbAg .navbar.navbar-short .navbar-brand {
  padding: 0;
}
.cid-qGQPqWmbAg .navbar-brand {
  flex-shrink: 0;
  align-items: center;
  margin-right: 0;
  padding: 0;
  transition: all 0.3s;
  word-break: break-word;
  z-index: 1;
}
.cid-qGQPqWmbAg .navbar-brand .navbar-caption {
  line-height: inherit !important;
}
.cid-qGQPqWmbAg .navbar-brand .navbar-logo a {
  outline: none;
}
.cid-qGQPqWmbAg .dropdown-item.active,
.cid-qGQPqWmbAg .dropdown-item:active {
  background-color: transparent;
}
.cid-qGQPqWmbAg .navbar-expand-lg .navbar-nav .nav-link {
  padding: 0;
}
.cid-qGQPqWmbAg .nav-dropdown .link.dropdown-toggle {
  margin-right: 1.667em;
}
.cid-qGQPqWmbAg .nav-dropdown .link.dropdown-toggle[aria-expanded='true'] {
  margin-right: 0;
  padding: 0.667em 1.667em;
}
.cid-qGQPqWmbAg .navbar.navbar-expand-lg .dropdown .dropdown-menu {
  background: #ffffff;
}
.cid-qGQPqWmbAg .navbar.navbar-expand-lg .dropdown .dropdown-menu .dropdown-submenu {
  margin: 0;
  left: 100%;
}
.cid-qGQPqWmbAg .navbar .dropdown.open > .dropdown-menu {
  display: block;
}
.cid-qGQPqWmbAg ul.navbar-nav {
  flex-wrap: wrap;
}
.cid-qGQPqWmbAg .navbar-buttons {
  text-align: center;
}
.cid-qGQPqWmbAg button.navbar-toggler {
  outline: none;
  width: 31px;
  height: 20px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  align-self: center;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span {
  position: absolute;
  right: 0;
  width: 30px;
  height: 2px;
  border-right: 5px;
  background-color: #232323;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(1) {
  top: 0;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(2) {
  top: 8px;
  transition: all 0.15s;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(3) {
  top: 8px;
  transition: all 0.15s;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(4) {
  top: 16px;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(1) {
  top: 8px;
  width: 0;
  opacity: 0;
  right: 50%;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(2) {
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  transition: all 0.25s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(3) {
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  transition: all 0.25s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(4) {
  top: 8px;
  width: 0;
  opacity: 0;
  right: 50%;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg .navbar-dropdown {
  padding: 0.5rem 1rem;
  position: fixed;
}
.cid-qGQPqWmbAg a.nav-link {
  justify-content: center;
  display: flex;
  align-items: center;
}
.cid-qGQPqWmbAg .mbr-iconfont {
  font-size: 1.5rem;
  padding-right: 0.5rem;
}
.cid-qGQPqWmbAg .icons-menu {
  flex-wrap: wrap;
  display: flex;
  justify-content: center;
  padding-left: 1rem;
  text-align: center;
}
.cid-qGQPqWmbAg .icons-menu span {
  font-size: 20px;
  color: #232323;
}
.cid-qGRlVNVoZS {
  padding-top: 30px;
  padding-bottom: 30px;
  background-color: #048e53;
}
.cid-qGRlVNVoZS .mbr-section-title span {
  display: block;
}
.cid-qGRlVNVoZS .first-column,
.cid-qGRlVNVoZS .second-column {
  margin-bottom: 1rem;
}
.cid-qGRlVNVoZS .first-column ul,
.cid-qGRlVNVoZS .second-column ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
.cid-qGRlVNVoZS .first-column ul li,
.cid-qGRlVNVoZS .second-column ul li {
  margin-bottom: 1rem;
}
.cid-qGRlVNVoZS .card-support {
  margin-top: 1.5rem;
  padding: 0;
  list-style: none;
}
.cid-qGRlVNVoZS .card-support li {
  margin-bottom: 0.4rem;
  margin-right: 0.4rem;
  display: inline-block;
}
.cid-qGRlVNVoZS .mbr-iconfont {
  padding: 0 0.5rem;
}
@media (max-width: 767px) {
  .cid-qGRlVNVoZS .card-support {
    text-align: center;
  }
}
.cid-qGRlVNVoZS .first-column-item {
  color: #767676;
}
.cid-qGQPqWmbAg .dropdown-item:before {
  font-family: MobiriseIcons !important;
  content: '\e966';
  display: inline-block;
  width: 0;
  position: absolute;
  left: 1rem;
  top: 0.5rem;
  margin-right: 0.5rem;
  line-height: 1;
  font-size: inherit;
  vertical-align: middle;
  text-align: center;
  overflow: hidden;
  transform: scale(0, 1);
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
}
.cid-qGQPqWmbAg .nav-item:focus,
.cid-qGQPqWmbAg .nav-link:focus {
  outline: none;
}
@media (min-width: 992px) {
  .cid-qGQPqWmbAg .dropdown-item:hover:before {
    transform: scale(1, 1);
    width: 16px;
  }
  .cid-qGQPqWmbAg .nav-item .nav-link {
    position: relative;
  }
  .cid-qGQPqWmbAg .nav-item .nav-link::before {
    position: absolute;
    content: '';
    width: 0;
    height: 3px;
    bottom: -0.5rem;
    left: 50%;
    background: linear-gradient(90deg, #f0f12e, #16a16c);
    transition: width 200ms linear, left 200ms linear;
  }
  .cid-qGQPqWmbAg .nav-item.open .nav-link::before {
    bottom: 0.2rem;
    width: 80% !important;
    left: 10% !important;
  }
  .cid-qGQPqWmbAg .nav-item .nav-link:hover::before {
    width: calc(100% + 2rem);
    left: -1rem;
  }
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item {
  width: auto;
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item::after {
  right: 0.5rem;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item:hover {
  padding-right: 2.5385em;
  padding-left: 3.5385em;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item:hover .mbr-iconfont:before {
  transform: scale(0, 1);
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item .mbr-iconfont {
  margin-left: -1.8rem;
  padding-right: 1rem;
  font-size: inherit;
}
.cid-qGQPqWmbAg .dropdown .dropdown-menu .dropdown-item .mbr-iconfont:before {
  display: inline-block;
  transform: scale(1, 1);
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
}
.cid-qGQPqWmbAg .collapsed .dropdown-menu .dropdown-item:before {
  display: none;
}
.cid-qGQPqWmbAg .collapsed .dropdown .dropdown-menu .dropdown-item {
  padding: 0.235em 1.5em 0.235em 1.5em !important;
  transition: none;
  margin: 0 !important;
}
.cid-qGQPqWmbAg .navbar {
  min-height: 77px;
  transition: all 0.3s;
  background: transparent;
}
.cid-qGQPqWmbAg .navbar.opened {
  transition: all 0.3s;
  background: #ffffff !important;
}
.cid-qGQPqWmbAg .navbar .dropdown-item {
  padding: 0.235rem 1.5rem;
}
.cid-qGQPqWmbAg .navbar .navbar-collapse {
  justify-content: flex-end;
  z-index: 1;
}
.cid-qGQPqWmbAg .navbar.collapsed.opened .dropdown-menu {
  top: 0;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu {
  background: transparent !important;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-submenu {
  left: 0 !important;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-item:after {
  right: auto;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-toggle[data-toggle='dropdown-submenu']:after {
  margin-left: 0.25rem;
  border-top: 0.35em solid;
  border-right: 0.35em solid transparent;
  border-left: 0.35em solid transparent;
  border-bottom: 0;
  top: 55%;
}
.cid-qGQPqWmbAg .navbar.collapsed ul.navbar-nav li {
  margin: auto;
}
.cid-qGQPqWmbAg .navbar.collapsed .dropdown-menu .dropdown-item {
  padding: 0.25rem 1.5rem;
  text-align: center;
}
.cid-qGQPqWmbAg .navbar.collapsed .icons-menu {
  padding-left: 0;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
@media (max-width: 991px) {
  .cid-qGQPqWmbAg .navbar.opened .dropdown-menu {
    top: 0;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu {
    background: transparent !important;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-submenu {
    left: 0 !important;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-item:after {
    right: auto;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-toggle[data-toggle='dropdown-submenu']:after {
    margin-left: 0.25rem;
    border-top: 0.35em solid;
    border-right: 0.35em solid transparent;
    border-left: 0.35em solid transparent;
    border-bottom: 0;
    top: 55%;
  }
  .cid-qGQPqWmbAg .navbar .navbar-logo img {
    height: 3.8rem !important;
  }
  .cid-qGQPqWmbAg .navbar ul.navbar-nav li {
    margin: auto;
  }
  .cid-qGQPqWmbAg .navbar .dropdown-menu .dropdown-item {
    padding: 0.25rem 1.5rem !important;
    text-align: center;
  }
  .cid-qGQPqWmbAg .navbar .navbar-brand {
    flex-shrink: initial;
    flex-basis: 70%;
    word-break: break-word;
  }
  .cid-qGQPqWmbAg .navbar .navbar-toggler {
    flex-basis: 30%;
  }
  .cid-qGQPqWmbAg .navbar .icons-menu {
    padding-left: 0;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
}
.cid-qGQPqWmbAg .navbar.navbar-short {
  background: #ffffff !important;
  min-height: 60px;
}
.cid-qGQPqWmbAg .navbar.navbar-short .navbar-logo img {
  height: 3rem !important;
}
.cid-qGQPqWmbAg .navbar.navbar-short .navbar-brand {
  padding: 0;
}
.cid-qGQPqWmbAg .navbar-brand {
  flex-shrink: 0;
  align-items: center;
  margin-right: 0;
  padding: 0;
  transition: all 0.3s;
  word-break: break-word;
  z-index: 1;
}
.cid-qGQPqWmbAg .navbar-brand .navbar-caption {
  line-height: inherit !important;
}
.cid-qGQPqWmbAg .navbar-brand .navbar-logo a {
  outline: none;
}
.cid-qGQPqWmbAg .dropdown-item.active,
.cid-qGQPqWmbAg .dropdown-item:active {
  background-color: transparent;
}
.cid-qGQPqWmbAg .navbar-expand-lg .navbar-nav .nav-link {
  padding: 0;
}
.cid-qGQPqWmbAg .nav-dropdown .link.dropdown-toggle {
  margin-right: 1.667em;
}
.cid-qGQPqWmbAg .nav-dropdown .link.dropdown-toggle[aria-expanded='true'] {
  margin-right: 0;
  padding: 0.667em 1.667em;
}
.cid-qGQPqWmbAg .navbar.navbar-expand-lg .dropdown .dropdown-menu {
  background: #ffffff;
}
.cid-qGQPqWmbAg .navbar.navbar-expand-lg .dropdown .dropdown-menu .dropdown-submenu {
  margin: 0;
  left: 100%;
}
.cid-qGQPqWmbAg .navbar .dropdown.open > .dropdown-menu {
  display: block;
}
.cid-qGQPqWmbAg ul.navbar-nav {
  flex-wrap: wrap;
}
.cid-qGQPqWmbAg .navbar-buttons {
  text-align: center;
}
.cid-qGQPqWmbAg button.navbar-toggler {
  outline: none;
  width: 31px;
  height: 20px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  align-self: center;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span {
  position: absolute;
  right: 0;
  width: 30px;
  height: 2px;
  border-right: 5px;
  background-color: #232323;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(1) {
  top: 0;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(2) {
  top: 8px;
  transition: all 0.15s;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(3) {
  top: 8px;
  transition: all 0.15s;
}
.cid-qGQPqWmbAg button.navbar-toggler .hamburger span:nth-child(4) {
  top: 16px;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(1) {
  top: 8px;
  width: 0;
  opacity: 0;
  right: 50%;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(2) {
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  transition: all 0.25s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(3) {
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  transition: all 0.25s;
}
.cid-qGQPqWmbAg nav.opened .hamburger span:nth-child(4) {
  top: 8px;
  width: 0;
  opacity: 0;
  right: 50%;
  transition: all 0.2s;
}
.cid-qGQPqWmbAg .navbar-dropdown {
  padding: 0.5rem 1rem;
  position: fixed;
}
.cid-qGQPqWmbAg a.nav-link {
  justify-content: center;
  display: flex;
  align-items: center;
}
.cid-qGQPqWmbAg .mbr-iconfont {
  font-size: 1.5rem;
  padding-right: 0.5rem;
}
.cid-qGQPqWmbAg .icons-menu {
  flex-wrap: wrap;
  display: flex;
  justify-content: center;
  padding-left: 1rem;
  text-align: center;
}
.cid-qGQPqWmbAg .icons-menu span {
  font-size: 20px;
  color: #232323;
}
.cid-qGRlVNVoZS {
  padding-top: 45px;
  padding-bottom: 25px;
  background-color: #048e53;
}
.cid-qGRlVNVoZS .mbr-section-title span {
  display: inline;
}
.cid-qGRlVNVoZS .first-column,
.cid-qGRlVNVoZS .second-column {
  margin-bottom: 1rem;
}
.cid-qGRlVNVoZS .first-column ul,
.cid-qGRlVNVoZS .second-column ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
.cid-qGRlVNVoZS .first-column ul li,
.cid-qGRlVNVoZS .second-column ul li {
  margin-bottom: 1rem;
}
.cid-qGRlVNVoZS .first-column ul li a,
.cid-qGRlVNVoZS .second-column ul li a {
  font-weight: 300;
}
.cid-qGRlVNVoZS .card-support {
  margin-top: 1.5rem;
  padding: 0;
  list-style: none;
}
.cid-qGRlVNVoZS .card-support li {
  margin-bottom: 0.4rem;
  margin-right: 0.4rem;
  display: inline-block;
}
.cid-qGRlVNVoZS .mbr-iconfont {
  padding: 0 0.5rem;
}
@media (max-width: 767px) {
  .cid-qGRlVNVoZS .card-support {
    text-align: center;
  }
}
.cid-qGRlVNVoZS .first-column-item {
  color: #767676;
}
@media (max-width: 412px) {
  .display-2 {
    font-size: 2.4rem;
    font-size: calc(1.25rem + (3 - 1.7) * ((100vw - 20rem) / (48 - 20)));
    line-height: calc(1.25rem + (3 - 1.7) * ((100vw - 20rem) / (48 - 20)));
  }
  .display-5 {
    font-size: 1.2rem;
    font-size: calc(1rem + (1.5 - 1.175) * ((100vw - 20rem) / (48 - 20)));
    line-height: calc(1 * (1.175rem + (1.5 - 1.175) * ((100vw - 20rem) / (48 - 20))));
  }
  .table td,
  .table th {
    padding: 5px;
  }
  h2.recommend,
  h2.clinicallyProven {
    font-size: 1.25rem;
  }
  .benefits-title.display-2 {
    font-size: calc(1.75rem + (3 - 1.7) * ((100vw - 20rem) / (48 - 20)));
  }
  .benefits-contents ul {
    font-size: 1rem;
    line-height: 1.4rem;
  }
}
