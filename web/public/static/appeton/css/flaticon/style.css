  /*
    Flaticon icon font: Flaticon
    Creation date: 08/01/2021 01:30
    */

@font-face {
  font-family: "Flaticon";
  src: url("fonts/Flaticon.eot");
  src: url("fonts/Flaticon.eot#iefix") format("embedded-opentype"),
       url("fonts/Flaticon.woff2") format("woff2"),
       url("fonts/Flaticon.woff") format("woff"),
       url("fonts/Flaticon.ttf") format("truetype"),
       url("fonts/Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("fonts/Flaticon.svg#Flaticon") format("svg");
  }
}

[class^="flaticon-"]:before, [class*=" flaticon-"]:before,
[class^="flaticon-"]:after, [class*=" flaticon-"]:after {   
  font-family: "Flaticon";
    font-size: 1.6rem;
    font-style: normal
}

.flaticon-vitamins:before { content: "\f100"; }
.flaticon-couple:before { content: "\f101"; }
.flaticon-shield:before { content: "\f102"; }
.flaticon-baby:before { content: "\f103"; }
.flaticon-hospital:before { content: "\f104"; }
.flaticon-scale:before { content: "\f105"; }
.flaticon-vitamins-1:before { content: "\f106"; }