.bootstrap-touchspin .input-group-btn-vertical {
  position: relative;
  white-space: nowrap;
  width: 1%;
  vertical-align: middle;
  display: table-cell;
}

.bootstrap-touchspin .input-group-btn-vertical > .btn {
  display: block;
  float: none;
  width: 100%;
  max-width: 100%;
  padding: 8px 10px;
  margin-left: -1px;
  position: relative;
}

.bootstrap-touchspin .input-group-btn-vertical .bootstrap-touchspin-up {
  border-radius: 0;
  border-top-right-radius: 4px;
}

.bootstrap-touchspin .input-group-btn-vertical .bootstrap-touchspin-down {
  margin-top: -2px;
  border-radius: 0;
  border-bottom-right-radius: 4px;
}

.bootstrap-touchspin .input-group-btn-vertical i {
  position: absolute;
  top: 3px;
  left: 5px;
  font-size: 9px;
  font-weight: 400;
}

html {
  font-family: sans-serif;
  line-height: 1.15;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
main,
menu,
nav,
section,
summary {
  display: block;
}

audio,
canvas,
progress,
video {
  display: inline-block;
}

audio:not([controls]) {
  display: none;
  height: 0;
}

progress {
  vertical-align: baseline;
}

[hidden],
template {
  display: none;
}

a {
  background-color: transparent;
  -webkit-text-decoration-skip: objects;
}

a:active,
a:hover {
  outline-width: 0;
}

abbr[title] {
  border-bottom: none;
  text-decoration: underline;
  text-decoration: underline dotted;
}

b,
strong {
  font-weight: inherit;
  font-weight: bolder;
}

dfn {
  font-style: italic;
}

h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

mark {
  background-color: #ff0;
  color: #000;
}

small {
  font-size: 80%;
}

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

img {
  border-style: none;
}

svg:not(:root) {
  overflow: hidden;
}

code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}

figure {
  margin: 1em 40px;
}

hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}

button,
input,
optgroup,
select,
textarea {
  font: inherit;
  margin: 0;
}

optgroup {
  font-weight: 700;
}

button,
input {
  overflow: visible;
}

button,
select {
  text-transform: none;
}

[type='reset'],
[type='submit'],
button,
html [type='button'] {
  -webkit-appearance: button;
}

[type='button']::-moz-focus-inner,
[type='reset']::-moz-focus-inner,
[type='submit']::-moz-focus-inner,
button::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

[type='button']:-moz-focusring,
[type='reset']:-moz-focusring,
[type='submit']:-moz-focusring,
button:-moz-focusring {
  outline: 1px dotted ButtonText;
}

fieldset {
  border: 1px solid silver;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}

legend {
  box-sizing: border-box;
  color: inherit;
  display: table;
  max-width: 100%;
  white-space: normal;
}

textarea {
  overflow: auto;
}

[type='checkbox'],
[type='radio'] {
  box-sizing: border-box;
  padding: 0;
}

[type='number']::-webkit-inner-spin-button,
[type='number']::-webkit-outer-spin-button {
  height: auto;
}

[type='search'] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

[type='search']::-webkit-search-cancel-button,
[type='search']::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-input-placeholder {
  color: inherit;
  opacity: 0.54;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

@media print {
  *,
  :after,
  :before,
  :first-letter,
  blockquote:first-line,
  div:first-line,
  li:first-line,
  p:first-line {
    text-shadow: none !important;
    box-shadow: none !important;
  }

  a,
  a:visited {
    text-decoration: underline;
  }

  abbr[title]:after {
    content: ' (' attr(title) ')';
  }

  pre {
    white-space: pre-wrap !important;
  }

  blockquote,
  pre {
    border: 1px solid #999;
    page-break-inside: avoid;
  }

  thead {
    display: table-header-group;
  }

  img,
  tr {
    page-break-inside: avoid;
  }

  h2,
  h3,
  p {
    orphans: 3;
    widows: 3;
  }

  h2,
  h3 {
    page-break-after: avoid;
  }

  .navbar {
    display: none;
  }

  .btn > .caret,
  .dropup > .btn > .caret {
    border-top-color: #000 !important;
  }

  .tag {
    border: 1px solid #000;
  }

  .table {
    border-collapse: collapse !important;
  }

  .table td,
  .table th {
    background-color: #fff !important;
  }

  .table-bordered td,
  .table-bordered th {
    border: 1px solid #ddd !important;
  }
}

html {
  box-sizing: border-box;
}

*,
:after,
:before {
  box-sizing: inherit;
}

@-ms-viewport {
  width: device-width;
}

html {
  font-size: 16px;
  -ms-overflow-style: scrollbar;
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif;
  line-height: 1.5;
  color: #373a3c;
  background-color: #fff;
}

[tabindex='-1']:focus {
  outline: none !important;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

abbr[data-original-title],
abbr[title] {
  cursor: help;
  border-bottom: 1px dotted #f1f1f1;
}

address {
  font-style: normal;
  line-height: inherit;
}

address,
dl,
ol,
ul {
  margin-bottom: 1rem;
}

dl,
ol,
ul {
  margin-top: 0;
}

ol ol,
ol ul,
ul ol,
ul ul {
  margin-bottom: 0;
}

dt {
  font-weight: 700;
}

dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}

blockquote {
  margin: 0 0 1rem;
}

a {
  color: #2fb5d2;
  text-decoration: none;
}

a:focus,
a:hover {
  color: #208094;
  text-decoration: underline;
}

a:focus {
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}

a:not([href]):not([tabindex]),
a:not([href]):not([tabindex]):focus,
a:not([href]):not([tabindex]):hover {
  color: inherit;
  text-decoration: none;
}

a:not([href]):not([tabindex]):focus {
  outline: none;
}

pre {
  overflow: auto;
}

figure {
  margin: 0 0 1rem;
}

img {
  vertical-align: middle;
}

[role='button'] {
  cursor: pointer;
}

[role='button'],
a,
area,
button,
input,
label,
select,
summary,
textarea {
  -ms-touch-action: manipulation;
  touch-action: manipulation;
}

table {
  border-collapse: collapse;
  background-color: transparent;
}

caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: #7a7a7a;
  caption-side: bottom;
}

caption,
th {
  text-align: left;
}

label {
  display: inline-block;
  margin-bottom: 0.5rem;
}

button:focus {
  outline: 1px dotted;
  outline: 5px auto -webkit-focus-ring-color;
}

button,
input,
select,
textarea {
  line-height: inherit;
}

input[type='checkbox']:disabled,
input[type='radio']:disabled {
  cursor: not-allowed;
}

input[type='date'],
input[type='datetime-local'],
input[type='month'],
input[type='time'] {
  -webkit-appearance: listbox;
}

textarea {
  resize: vertical;
}

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}

legend {
  display: block;
  width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  line-height: inherit;
}

input[type='search'] {
  -webkit-appearance: none;
}

output {
  display: inline-block;
}

[hidden] {
  display: none !important;
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-bottom: 0.5rem;
  font-family: inherit;
  font-weight: 500;
  color: inherit;
}

.h1,
h1 {
  font-size: 1.375rem;
}

.h2,
h2 {
  font-size: 2rem;
}

.h3,
.h4,
h3,
h4 {
  font-size: 1.125rem;
}

.h5,
h5 {
  font-size: 1rem;
}

.h6,
h6 {
  font-size: 0.9375rem;
}

.lead {
  font-size: 1.25rem;
  font-weight: 300;
}

.display-1 {
  font-size: 3.125rem;
  font-weight: 600;
}

.display-2 {
  font-size: 2.188rem;
  font-weight: 400;
}

.display-3 {
  font-size: 1.563rem;
  font-weight: 400;
}

.display-4 {
  font-size: 1.25rem;
  font-weight: 400;
}

hr {
  margin-top: 1rem;
  margin-bottom: 1rem;
  border: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.small,
small {
  font-size: 80%;
  font-weight: 400;
}

.mark,
mark {
  padding: 0.2em;
  background-color: #fcf8e3;
}

.list-inline,
.list-unstyled {
  padding-left: 0;
  list-style: none;
}

.list-inline-item {
  display: inline-block;
}

.list-inline-item:not(:last-child) {
  margin-right: 5px;
}

.initialism {
  font-size: 90%;
  text-transform: uppercase;
}

.blockquote {
  padding: 0.5rem 1rem;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  border-left: 0.25rem solid #f6f6f6;
}

.blockquote-footer {
  display: block;
  font-size: 80%;
  color: #f1f1f1;
}

.blockquote-footer:before {
  content: '\2014   \A0';
}

.blockquote-reverse {
  padding-right: 1rem;
  padding-left: 0;
  text-align: right;
  border-right: 0.25rem solid #f6f6f6;
  border-left: 0;
}

.blockquote-reverse .blockquote-footer:before {
  content: '';
}

.blockquote-reverse .blockquote-footer:after {
  content: '\A0   \2014';
}

dl.row > dd + dt {
  clear: left;
}

.carousel-inner > .carousel-item > a > img,
.carousel-inner > .carousel-item > img,
.img-fluid,
.img-thumbnail {
  max-width: 100%;
  height: auto;
}

.img-thumbnail {
  padding: 0.25rem;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 0;
  transition: all 0.2s ease-in-out;
}

.figure {
  display: inline-block;
}

.figure-img {
  margin-bottom: 0.5rem;
  line-height: 1;
}

.figure-caption {
  font-size: 90%;
  color: #f1f1f1;
}

code,
kbd,
pre,
samp {
  font-family: Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
}

code {
  color: #bd4147;
  background-color: #f7f7f9;
  border-radius: 0;
}

code,
kbd {
  padding: 0.2rem 0.4rem;
  font-size: 90%;
}

kbd {
  color: #fff;
  background-color: #333;
  border-radius: 0.2rem;
}

kbd kbd {
  padding: 0;
  font-size: 100%;
  font-weight: 700;
}

pre {
  display: block;
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 90%;
  color: #373a3c;
}

pre code {
  padding: 0;
  font-size: inherit;
  color: inherit;
  background-color: transparent;
  border-radius: 0;
}

.pre-scrollable {
  max-height: 340px;
  overflow-y: scroll;
}

.container {
  margin-left: auto;
  margin-right: auto;
  padding-left: 15px;
  padding-right: 15px;
}

.container:after {
  content: '';
  display: table;
  clear: both;
}

@media (min-width: 576px) {
  .container {
    width: 540px;
    max-width: 100%;
  }
}

@media (min-width: 768px) {
  .container {
    width: 720px;
    max-width: 100%;
  }

  #header #mobileNav {
    display: none !important;
  }
}

@media (min-width: 992px) {
  .container {
    width: 960px;
    max-width: 100%;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 100%;
    width: 100%;
    padding: 0;
  }
}

.container-fluid {
  margin-left: auto;
  margin-right: auto;
  padding-left: 15px;
  padding-right: 15px;
}

.container-fluid:after {
  content: '';
  display: table;
  clear: both;
}

.container .row {
  padding: 5%;
  width: 100%;
}

#main .row {
  padding: 0 10%;
  width: 100%;
}

.footer-container .container .row {
  padding: 0 10%;
  width: unset;
}

.row:after {
  content: '';
  display: table;
  clear: both;
}

@media (min-width: 576px) {
  .row {
    margin-right: -15px;
    margin-left: -15px;
  }
}

@media (min-width: 768px) {
  .row {
    margin-right: -15px;
    margin-left: -15px;
  }
}

@media (min-width: 992px) {
  .row {
    margin-right: -15px;
    margin-left: -15px;
  }
}

@media (min-width: 1200px) {
  .row {
    margin-right: -15px;
    margin-left: -15px;
  }
}

.col-lg,
.col-lg-1,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-md,
.col-md-1,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-md-10,
.col-md-11,
.col-md-12,
.col-sm,
.col-sm-1,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-xl,
.col-xl-1,
.col-xl-2,
.col-xl-3,
.col-xl-4,
.col-xl-5,
.col-xl-6,
.col-xl-7,
.col-xl-8,
.col-xl-9,
.col-xl-10,
.col-xl-11,
.col-xl-12,
.col-xs,
.col-xs-1,
.col-xs-2,
.col-xs-3,
.col-xs-4,
.col-xs-5,
.col-xs-6,
.col-xs-7,
.col-xs-8,
.col-xs-9,
.col-xs-10,
.col-xs-11,
.col-xs-12 {
  position: relative;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
}

@media (min-width: 576px) {
  .col-lg,
  .col-lg-1,
  .col-lg-2,
  .col-lg-3,
  .col-lg-4,
  .col-lg-5,
  .col-lg-6,
  .col-lg-7,
  .col-lg-8,
  .col-lg-9,
  .col-lg-10,
  .col-lg-11,
  .col-lg-12,
  .col-md,
  .col-md-1,
  .col-md-2,
  .col-md-3,
  .col-md-4,
  .col-md-5,
  .col-md-6,
  .col-md-7,
  .col-md-8,
  .col-md-9,
  .col-md-10,
  .col-md-11,
  .col-md-12,
  .col-sm,
  .col-sm-1,
  .col-sm-2,
  .col-sm-3,
  .col-sm-4,
  .col-sm-5,
  .col-sm-6,
  .col-sm-7,
  .col-sm-8,
  .col-sm-9,
  .col-sm-10,
  .col-sm-11,
  .col-sm-12,
  .col-xl,
  .col-xl-1,
  .col-xl-2,
  .col-xl-3,
  .col-xl-4,
  .col-xl-5,
  .col-xl-6,
  .col-xl-7,
  .col-xl-8,
  .col-xl-9,
  .col-xl-10,
  .col-xl-11,
  .col-xl-12,
  .col-xs,
  .col-xs-1,
  .col-xs-2,
  .col-xs-3,
  .col-xs-4,
  .col-xs-5,
  .col-xs-6,
  .col-xs-7,
  .col-xs-8,
  .col-xs-9,
  .col-xs-10,
  .col-xs-11,
  .col-xs-12 {
    padding-right: 15px;
    padding-left: 15px;
  }
}

@media (min-width: 768px) {
  .col-lg,
  .col-lg-1,
  .col-lg-2,
  .col-lg-3,
  .col-lg-4,
  .col-lg-5,
  .col-lg-6,
  .col-lg-7,
  .col-lg-8,
  .col-lg-9,
  .col-lg-10,
  .col-lg-11,
  .col-lg-12,
  .col-md,
  .col-md-1,
  .col-md-2,
  .col-md-3,
  .col-md-4,
  .col-md-5,
  .col-md-6,
  .col-md-7,
  .col-md-8,
  .col-md-9,
  .col-md-10,
  .col-md-11,
  .col-md-12,
  .col-sm,
  .col-sm-1,
  .col-sm-2,
  .col-sm-3,
  .col-sm-4,
  .col-sm-5,
  .col-sm-6,
  .col-sm-7,
  .col-sm-8,
  .col-sm-9,
  .col-sm-10,
  .col-sm-11,
  .col-sm-12,
  .col-xl,
  .col-xl-1,
  .col-xl-2,
  .col-xl-3,
  .col-xl-4,
  .col-xl-5,
  .col-xl-6,
  .col-xl-7,
  .col-xl-8,
  .col-xl-9,
  .col-xl-10,
  .col-xl-11,
  .col-xl-12,
  .col-xs,
  .col-xs-1,
  .col-xs-2,
  .col-xs-3,
  .col-xs-4,
  .col-xs-5,
  .col-xs-6,
  .col-xs-7,
  .col-xs-8,
  .col-xs-9,
  .col-xs-10,
  .col-xs-11,
  .col-xs-12 {
    padding-right: 15px;
    padding-left: 15px;
  }
}

@media (min-width: 992px) {
  .col-lg,
  .col-lg-1,
  .col-lg-2,
  .col-lg-3,
  .col-lg-4,
  .col-lg-5,
  .col-lg-6,
  .col-lg-7,
  .col-lg-8,
  .col-lg-9,
  .col-lg-10,
  .col-lg-11,
  .col-lg-12,
  .col-md,
  .col-md-1,
  .col-md-2,
  .col-md-3,
  .col-md-4,
  .col-md-5,
  .col-md-6,
  .col-md-7,
  .col-md-8,
  .col-md-9,
  .col-md-10,
  .col-md-11,
  .col-md-12,
  .col-sm,
  .col-sm-1,
  .col-sm-2,
  .col-sm-3,
  .col-sm-4,
  .col-sm-5,
  .col-sm-6,
  .col-sm-7,
  .col-sm-8,
  .col-sm-9,
  .col-sm-10,
  .col-sm-11,
  .col-sm-12,
  .col-xl,
  .col-xl-1,
  .col-xl-2,
  .col-xl-3,
  .col-xl-4,
  .col-xl-5,
  .col-xl-6,
  .col-xl-7,
  .col-xl-8,
  .col-xl-9,
  .col-xl-10,
  .col-xl-11,
  .col-xl-12,
  .col-xs,
  .col-xs-1,
  .col-xs-2,
  .col-xs-3,
  .col-xs-4,
  .col-xs-5,
  .col-xs-6,
  .col-xs-7,
  .col-xs-8,
  .col-xs-9,
  .col-xs-10,
  .col-xs-11,
  .col-xs-12 {
    padding-right: 15px;
    padding-left: 15px;
  }
}

@media (min-width: 1200px) {
  .col-lg,
  .col-lg-1,
  .col-lg-2,
  .col-lg-3,
  .col-lg-4,
  .col-lg-5,
  .col-lg-6,
  .col-lg-7,
  .col-lg-8,
  .col-lg-9,
  .col-lg-10,
  .col-lg-11,
  .col-lg-12,
  .col-md,
  .col-md-1,
  .col-md-2,
  .col-md-3,
  .col-md-4,
  .col-md-5,
  .col-md-6,
  .col-md-7,
  .col-md-8,
  .col-md-9,
  .col-md-10,
  .col-md-11,
  .col-md-12,
  .col-sm,
  .col-sm-1,
  .col-sm-2,
  .col-sm-3,
  .col-sm-4,
  .col-sm-5,
  .col-sm-6,
  .col-sm-7,
  .col-sm-8,
  .col-sm-9,
  .col-sm-10,
  .col-sm-11,
  .col-sm-12,
  .col-xl,
  .col-xl-1,
  .col-xl-2,
  .col-xl-3,
  .col-xl-4,
  .col-xl-5,
  .col-xl-6,
  .col-xl-7,
  .col-xl-8,
  .col-xl-9,
  .col-xl-10,
  .col-xl-11,
  .col-xl-12,
  .col-xs,
  .col-xs-1,
  .col-xs-2,
  .col-xs-3,
  .col-xs-4,
  .col-xs-5,
  .col-xs-6,
  .col-xs-7,
  .col-xs-8,
  .col-xs-9,
  .col-xs-10,
  .col-xs-11,
  .col-xs-12 {
    padding-right: 15px;
    padding-left: 15px;
  }
}

.col-xs-1 {
  float: left;
  width: 8.33333%;
}

.col-xs-2 {
  float: left;
  width: 16.66667%;
}

.col-xs-3 {
  float: left;
  width: 25%;
}

.col-xs-4 {
  float: left;
  width: 33.33333%;
}

.col-xs-5 {
  float: left;
  width: 41.66667%;
}

.col-xs-6 {
  float: left;
  width: 50%;
}

.col-xs-7 {
  float: left;
  width: 58.33333%;
}

.col-xs-8 {
  float: left;
  width: 66.66667%;
}

.col-xs-9 {
  float: left;
  width: 75%;
}

.col-xs-10 {
  float: left;
  width: 83.33333%;
}

.col-xs-11 {
  float: left;
  width: 91.66667%;
}

.col-xs-12 {
  float: left;
  width: 100%;
}

.pull-xs-0 {
  right: auto;
}

.pull-xs-1 {
  right: 8.33333%;
}

.pull-xs-2 {
  right: 16.66667%;
}

.pull-xs-3 {
  right: 25%;
}

.pull-xs-4 {
  right: 33.33333%;
}

.pull-xs-5 {
  right: 41.66667%;
}

.pull-xs-6 {
  right: 50%;
}

.pull-xs-7 {
  right: 58.33333%;
}

.pull-xs-8 {
  right: 66.66667%;
}

.pull-xs-9 {
  right: 75%;
}

.pull-xs-10 {
  right: 83.33333%;
}

.pull-xs-11 {
  right: 91.66667%;
}

.pull-xs-12 {
  right: 100%;
}

.push-xs-0 {
  left: auto;
}

.push-xs-1 {
  left: 8.33333%;
}

.push-xs-2 {
  left: 16.66667%;
}

.push-xs-3 {
  left: 25%;
}

.push-xs-4 {
  left: 33.33333%;
}

.push-xs-5 {
  left: 41.66667%;
}

.push-xs-6 {
  left: 50%;
}

.push-xs-7 {
  left: 58.33333%;
}

.push-xs-8 {
  left: 66.66667%;
}

.push-xs-9 {
  left: 75%;
}

.push-xs-10 {
  left: 83.33333%;
}

.push-xs-11 {
  left: 91.66667%;
}

.push-xs-12 {
  left: 100%;
}

.offset-xs-1 {
  margin-left: 8.33333%;
}

.offset-xs-2 {
  margin-left: 16.66667%;
}

.offset-xs-3 {
  margin-left: 25%;
}

.offset-xs-4 {
  margin-left: 33.33333%;
}

.offset-xs-5 {
  margin-left: 41.66667%;
}

.offset-xs-6 {
  margin-left: 50%;
}

.offset-xs-7 {
  margin-left: 58.33333%;
}

.offset-xs-8 {
  margin-left: 66.66667%;
}

.offset-xs-9 {
  margin-left: 75%;
}

.offset-xs-10 {
  margin-left: 83.33333%;
}

.offset-xs-11 {
  margin-left: 91.66667%;
}

@media (min-width: 576px) {
  .col-sm-1 {
    float: left;
    width: 8.33333%;
  }

  .col-sm-2 {
    float: left;
    width: 16.66667%;
  }

  .col-sm-3 {
    float: left;
    width: 25%;
  }

  .col-sm-4 {
    float: left;
    width: 33.33333%;
  }

  .col-sm-5 {
    float: left;
    width: 41.66667%;
  }

  .col-sm-6 {
    float: left;
    width: 50%;
  }

  .col-sm-7 {
    float: left;
    width: 58.33333%;
  }

  .col-sm-8 {
    float: left;
    width: 66.66667%;
  }

  .col-sm-9 {
    float: left;
    width: 75%;
  }

  .col-sm-10 {
    float: left;
    width: 83.33333%;
  }

  .col-sm-11 {
    float: left;
    width: 91.66667%;
  }

  .col-sm-12 {
    float: left;
    width: 100%;
  }

  .pull-sm-0 {
    right: auto;
  }

  .pull-sm-1 {
    right: 8.33333%;
  }

  .pull-sm-2 {
    right: 16.66667%;
  }

  .pull-sm-3 {
    right: 25%;
  }

  .pull-sm-4 {
    right: 33.33333%;
  }

  .pull-sm-5 {
    right: 41.66667%;
  }

  .pull-sm-6 {
    right: 50%;
  }

  .pull-sm-7 {
    right: 58.33333%;
  }

  .pull-sm-8 {
    right: 66.66667%;
  }

  .pull-sm-9 {
    right: 75%;
  }

  .pull-sm-10 {
    right: 83.33333%;
  }

  .pull-sm-11 {
    right: 91.66667%;
  }

  .pull-sm-12 {
    right: 100%;
  }

  .push-sm-0 {
    left: auto;
  }

  .push-sm-1 {
    left: 8.33333%;
  }

  .push-sm-2 {
    left: 16.66667%;
  }

  .push-sm-3 {
    left: 25%;
  }

  .push-sm-4 {
    left: 33.33333%;
  }

  .push-sm-5 {
    left: 41.66667%;
  }

  .push-sm-6 {
    left: 50%;
  }

  .push-sm-7 {
    left: 58.33333%;
  }

  .push-sm-8 {
    left: 66.66667%;
  }

  .push-sm-9 {
    left: 75%;
  }

  .push-sm-10 {
    left: 83.33333%;
  }

  .push-sm-11 {
    left: 91.66667%;
  }

  .push-sm-12 {
    left: 100%;
  }

  .offset-sm-0 {
    margin-left: 0;
  }

  .offset-sm-1 {
    margin-left: 8.33333%;
  }

  .offset-sm-2 {
    margin-left: 16.66667%;
  }

  .offset-sm-3 {
    margin-left: 25%;
  }

  .offset-sm-4 {
    margin-left: 33.33333%;
  }

  .offset-sm-5 {
    margin-left: 41.66667%;
  }

  .offset-sm-6 {
    margin-left: 50%;
  }

  .offset-sm-7 {
    margin-left: 58.33333%;
  }

  .offset-sm-8 {
    margin-left: 66.66667%;
  }

  .offset-sm-9 {
    margin-left: 75%;
  }

  .offset-sm-10 {
    margin-left: 83.33333%;
  }

  .offset-sm-11 {
    margin-left: 91.66667%;
  }
}

@media (min-width: 768px) {
  .col-md-1 {
    float: left;
    width: 8.33333%;
  }

  .col-md-2 {
    float: left;
    width: 16.66667%;
  }

  .col-md-3 {
    float: left;
    width: 25%;
  }

  .col-md-4 {
    float: left;
    width: 33.33333%;
  }

  .col-md-5 {
    float: left;
    width: 41.66667%;
  }

  .col-md-6 {
    float: left;
    width: 50%;
  }

  .col-md-7 {
    float: left;
    width: 58.33333%;
  }

  .col-md-8 {
    float: left;
    width: 66.66667%;
  }

  .col-md-9 {
    float: left;
    width: 75%;
  }

  .col-md-10 {
    float: left;
    width: 83.33333%;
  }

  .col-md-11 {
    float: left;
    width: 91.66667%;
  }

  .col-md-12 {
    float: left;
    width: 100%;
  }

  .pull-md-0 {
    right: auto;
  }

  .pull-md-1 {
    right: 8.33333%;
  }

  .pull-md-2 {
    right: 16.66667%;
  }

  .pull-md-3 {
    right: 25%;
  }

  .pull-md-4 {
    right: 33.33333%;
  }

  .pull-md-5 {
    right: 41.66667%;
  }

  .pull-md-6 {
    right: 50%;
  }

  .pull-md-7 {
    right: 58.33333%;
  }

  .pull-md-8 {
    right: 66.66667%;
  }

  .pull-md-9 {
    right: 75%;
  }

  .pull-md-10 {
    right: 83.33333%;
  }

  .pull-md-11 {
    right: 91.66667%;
  }

  .pull-md-12 {
    right: 100%;
  }

  .push-md-0 {
    left: auto;
  }

  .push-md-1 {
    left: 8.33333%;
  }

  .push-md-2 {
    left: 16.66667%;
  }

  .push-md-3 {
    left: 25%;
  }

  .push-md-4 {
    left: 33.33333%;
  }

  .push-md-5 {
    left: 41.66667%;
  }

  .push-md-6 {
    left: 50%;
  }

  .push-md-7 {
    left: 58.33333%;
  }

  .push-md-8 {
    left: 66.66667%;
  }

  .push-md-9 {
    left: 75%;
  }

  .push-md-10 {
    left: 83.33333%;
  }

  .push-md-11 {
    left: 91.66667%;
  }

  .push-md-12 {
    left: 100%;
  }

  .offset-md-0 {
    margin-left: 0;
  }

  .offset-md-1 {
    margin-left: 8.33333%;
  }

  .offset-md-2 {
    margin-left: 16.66667%;
  }

  .offset-md-3 {
    margin-left: 25%;
  }

  .offset-md-4 {
    margin-left: 33.33333%;
  }

  .offset-md-5 {
    margin-left: 41.66667%;
  }

  .offset-md-6 {
    margin-left: 50%;
  }

  .offset-md-7 {
    margin-left: 58.33333%;
  }

  .offset-md-8 {
    margin-left: 66.66667%;
  }

  .offset-md-9 {
    margin-left: 75%;
  }

  .offset-md-10 {
    margin-left: 83.33333%;
  }

  .offset-md-11 {
    margin-left: 91.66667%;
  }
}

@media (min-width: 992px) {
  .col-lg-1 {
    float: left;
    width: 8.33333%;
  }

  .col-lg-2 {
    float: left;
    width: 16.66667%;
  }

  .col-lg-3 {
    float: left;
    width: 25%;
  }

  .col-lg-4 {
    float: left;
    width: 33.33333%;
  }

  .col-lg-5 {
    float: left;
    width: 41.66667%;
  }

  .col-lg-6 {
    float: left;
    width: 50%;
  }

  .col-lg-7 {
    float: left;
    width: 58.33333%;
  }

  .col-lg-8 {
    float: left;
    width: 66.66667%;
  }

  .col-lg-9 {
    float: left;
    width: 75%;
  }

  .col-lg-10 {
    float: left;
    width: 83.33333%;
  }

  .col-lg-11 {
    float: left;
    width: 91.66667%;
  }

  .col-lg-12 {
    float: left;
    width: 100%;
  }

  .pull-lg-0 {
    right: auto;
  }

  .pull-lg-1 {
    right: 8.33333%;
  }

  .pull-lg-2 {
    right: 16.66667%;
  }

  .pull-lg-3 {
    right: 25%;
  }

  .pull-lg-4 {
    right: 33.33333%;
  }

  .pull-lg-5 {
    right: 41.66667%;
  }

  .pull-lg-6 {
    right: 50%;
  }

  .pull-lg-7 {
    right: 58.33333%;
  }

  .pull-lg-8 {
    right: 66.66667%;
  }

  .pull-lg-9 {
    right: 75%;
  }

  .pull-lg-10 {
    right: 83.33333%;
  }

  .pull-lg-11 {
    right: 91.66667%;
  }

  .pull-lg-12 {
    right: 100%;
  }

  .push-lg-0 {
    left: auto;
  }

  .push-lg-1 {
    left: 8.33333%;
  }

  .push-lg-2 {
    left: 16.66667%;
  }

  .push-lg-3 {
    left: 25%;
  }

  .push-lg-4 {
    left: 33.33333%;
  }

  .push-lg-5 {
    left: 41.66667%;
  }

  .push-lg-6 {
    left: 50%;
  }

  .push-lg-7 {
    left: 58.33333%;
  }

  .push-lg-8 {
    left: 66.66667%;
  }

  .push-lg-9 {
    left: 75%;
  }

  .push-lg-10 {
    left: 83.33333%;
  }

  .push-lg-11 {
    left: 91.66667%;
  }

  .push-lg-12 {
    left: 100%;
  }

  .offset-lg-0 {
    margin-left: 0;
  }

  .offset-lg-1 {
    margin-left: 8.33333%;
  }

  .offset-lg-2 {
    margin-left: 16.66667%;
  }

  .offset-lg-3 {
    margin-left: 25%;
  }

  .offset-lg-4 {
    margin-left: 33.33333%;
  }

  .offset-lg-5 {
    margin-left: 41.66667%;
  }

  .offset-lg-6 {
    margin-left: 50%;
  }

  .offset-lg-7 {
    margin-left: 58.33333%;
  }

  .offset-lg-8 {
    margin-left: 66.66667%;
  }

  .offset-lg-9 {
    margin-left: 75%;
  }

  .offset-lg-10 {
    margin-left: 83.33333%;
  }

  .offset-lg-11 {
    margin-left: 91.66667%;
  }
}

@media (min-width: 1200px) {
  .col-xl-1 {
    float: left;
    width: 8.33333%;
  }

  .col-xl-2 {
    float: left;
    width: 16.66667%;
  }

  .col-xl-3 {
    float: left;
    width: 25%;
  }

  .col-xl-4 {
    float: left;
    width: 33.33333%;
  }

  .col-xl-5 {
    float: left;
    width: 41.66667%;
  }

  .col-xl-6 {
    float: left;
    width: 50%;
  }

  .col-xl-7 {
    float: left;
    width: 58.33333%;
  }

  .col-xl-8 {
    float: left;
    width: 66.66667%;
  }

  .col-xl-9 {
    float: left;
    width: 75%;
  }

  .col-xl-10 {
    float: left;
    width: 83.33333%;
  }

  .col-xl-11 {
    float: left;
    width: 91.66667%;
  }

  .col-xl-12 {
    float: left;
    width: 100%;
  }

  .pull-xl-0 {
    right: auto;
  }

  .pull-xl-1 {
    right: 8.33333%;
  }

  .pull-xl-2 {
    right: 16.66667%;
  }

  .pull-xl-3 {
    right: 25%;
  }

  .pull-xl-4 {
    right: 33.33333%;
  }

  .pull-xl-5 {
    right: 41.66667%;
  }

  .pull-xl-6 {
    right: 50%;
  }

  .pull-xl-7 {
    right: 58.33333%;
  }

  .pull-xl-8 {
    right: 66.66667%;
  }

  .pull-xl-9 {
    right: 75%;
  }

  .pull-xl-10 {
    right: 83.33333%;
  }

  .pull-xl-11 {
    right: 91.66667%;
  }

  .pull-xl-12 {
    right: 100%;
  }

  .push-xl-0 {
    left: auto;
  }

  .push-xl-1 {
    left: 8.33333%;
  }

  .push-xl-2 {
    left: 16.66667%;
  }

  .push-xl-3 {
    left: 25%;
  }

  .push-xl-4 {
    left: 33.33333%;
  }

  .push-xl-5 {
    left: 41.66667%;
  }

  .push-xl-6 {
    left: 50%;
  }

  .push-xl-7 {
    left: 58.33333%;
  }

  .push-xl-8 {
    left: 66.66667%;
  }

  .push-xl-9 {
    left: 75%;
  }

  .push-xl-10 {
    left: 83.33333%;
  }

  .push-xl-11 {
    left: 91.66667%;
  }

  .push-xl-12 {
    left: 100%;
  }

  .offset-xl-0 {
    margin-left: 0;
  }

  .offset-xl-1 {
    margin-left: 8.33333%;
  }

  .offset-xl-2 {
    margin-left: 16.66667%;
  }

  .offset-xl-3 {
    margin-left: 25%;
  }

  .offset-xl-4 {
    margin-left: 33.33333%;
  }

  .offset-xl-5 {
    margin-left: 41.66667%;
  }

  .offset-xl-6 {
    margin-left: 50%;
  }

  .offset-xl-7 {
    margin-left: 58.33333%;
  }

  .offset-xl-8 {
    margin-left: 66.66667%;
  }

  .offset-xl-9 {
    margin-left: 75%;
  }

  .offset-xl-10 {
    margin-left: 83.33333%;
  }

  .offset-xl-11 {
    margin-left: 91.66667%;
  }
}

.table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 1rem;
}

.table td,
.table th {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid #f6f6f6;
}

.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid #f6f6f6;
}

.table tbody + tbody {
  border-top: 2px solid #f6f6f6;
}

.table .table {
  background-color: #fff;
}

.table-sm td,
.table-sm th {
  padding: 0.3rem;
}

.table-bordered,
.table-bordered td,
.table-bordered th {
  border: 1px solid #f6f6f6;
}

.table-bordered thead td,
.table-bordered thead th {
  border-bottom-width: 2px;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.05);
}

.table-active,
.table-active > td,
.table-active > th,
.table-hover .table-active:hover,
.table-hover .table-active:hover > td,
.table-hover .table-active:hover > th,
.table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.075);
}

.table-success,
.table-success > td,
.table-success > th {
  background-color: #dff0d8;
}

.table-hover .table-success:hover,
.table-hover .table-success:hover > td,
.table-hover .table-success:hover > th {
  background-color: #d0e9c6;
}

.table-info,
.table-info > td,
.table-info > th {
  background-color: #d9edf7;
}

.table-hover .table-info:hover,
.table-hover .table-info:hover > td,
.table-hover .table-info:hover > th {
  background-color: #c4e3f3;
}

.table-warning,
.table-warning > td,
.table-warning > th {
  background-color: #fcf8e3;
}

.table-hover .table-warning:hover,
.table-hover .table-warning:hover > td,
.table-hover .table-warning:hover > th {
  background-color: #faf2cc;
}

.table-danger,
.table-danger > td,
.table-danger > th {
  background-color: #f2dede;
}

.table-hover .table-danger:hover,
.table-hover .table-danger:hover > td,
.table-hover .table-danger:hover > th {
  background-color: #ebcccc;
}

.thead-inverse th {
  color: #fff;
  background-color: #373a3c;
}

.thead-default th {
  color: #7a7a7a;
  background-color: #f6f6f6;
}

.table-inverse {
  color: #f6f6f6;
  background-color: #373a3c;
}

.table-inverse td,
.table-inverse th,
.table-inverse thead th {
  border-color: #7a7a7a;
}

.table-inverse.table-bordered {
  border: 0;
}

.table-responsive {
  display: block;
  width: 100%;
  min-height: 0;
  overflow-x: auto;
}

.table-reflow thead {
  float: left;
}

.table-reflow tbody {
  display: block;
  white-space: nowrap;
}

.table-reflow td,
.table-reflow th {
  border-top: 1px solid #f6f6f6;
  border-left: 1px solid #f6f6f6;
}

.table-reflow td:last-child,
.table-reflow th:last-child {
  border-right: 1px solid #f6f6f6;
}

.table-reflow tbody:last-child tr:last-child td,
.table-reflow tbody:last-child tr:last-child th,
.table-reflow tfoot:last-child tr:last-child td,
.table-reflow tfoot:last-child tr:last-child th,
.table-reflow thead:last-child tr:last-child td,
.table-reflow thead:last-child tr:last-child th {
  border-bottom: 1px solid #f6f6f6;
}

.table-reflow tr {
  float: left;
}

.table-reflow tr td,
.table-reflow tr th {
  display: block !important;
  border: 1px solid #f6f6f6;
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  line-height: 1.25;
  background-color: #fff;
  background-image: none;
  background-clip: padding-box;
  border-radius: 0;
}

.form-control::-ms-expand {
  background-color: transparent;
  border: 0;
}

.form-control:focus {
  color: #7a7a7a;
  border-color: #66afe9;
  outline: none;
}

.form-control::-webkit-input-placeholder {
  color: #999;
  opacity: 1;
}

.form-control:-ms-input-placeholder {
  color: #999;
  opacity: 1;
}

.form-control::placeholder {
  color: #999;
  opacity: 1;
}

.form-control:disabled,
.form-control[readonly] {
  background-color: #f6f6f6;
  opacity: 1;
}

.form-control:disabled {
  cursor: not-allowed;
}

select.form-control:not([size]):not([multiple]) {
  height: calc(2.5rem - 2px);
}

select.form-control:focus::-ms-value {
  color: #7a7a7a;
  background-color: #fff;
}

.form-control-file,
.form-control-range {
  display: block;
}

.col-form-label {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  margin-bottom: 0;
}

.col-form-label-lg {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-size: 0.9375rem;
}

.col-form-label-sm {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  font-size: 0.875rem;
}

.col-form-legend {
  margin-bottom: 0;
  font-size: 1rem;
}

.col-form-legend,
.form-control-static {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.form-control-static {
  line-height: 1.25;
  border: solid transparent;
  border-width: 1px 0;
}

.form-control-static.form-control-lg,
.form-control-static.form-control-sm,
.input-group-lg > .form-control-static.form-control,
.input-group-lg > .form-control-static.input-group-addon,
.input-group-lg > .input-group-btn > .form-control-static.btn,
.input-group-sm > .form-control-static.form-control,
.input-group-sm > .form-control-static.input-group-addon,
.input-group-sm > .input-group-btn > .form-control-static.btn {
  padding-right: 0;
  padding-left: 0;
}

.form-control-sm,
.input-group-sm > .form-control,
.input-group-sm > .input-group-addon,
.input-group-sm > .input-group-btn > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.2rem;
}

.input-group-sm > .input-group-btn > select.btn:not([size]):not([multiple]),
.input-group-sm > select.form-control:not([size]):not([multiple]),
.input-group-sm > select.input-group-addon:not([size]):not([multiple]),
select.form-control-sm:not([size]):not([multiple]) {
  height: 1.8125rem;
}

.form-control-lg,
.input-group-lg > .form-control,
.input-group-lg > .input-group-addon,
.input-group-lg > .input-group-btn > .btn {
  padding: 0.75rem 1.5rem;
  font-size: 0.9375rem;
  border-radius: 0.3rem;
}

.input-group-lg > .input-group-btn > select.btn:not([size]):not([multiple]),
.input-group-lg > select.form-control:not([size]):not([multiple]),
.input-group-lg > select.input-group-addon:not([size]):not([multiple]),
select.form-control-lg:not([size]):not([multiple]) {
  height: 2.75rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-text {
  display: block;
  margin-top: 0.25rem;
}

.form-check {
  position: relative;
  display: block;
  margin-bottom: 0.75rem;
}

.form-check + .form-check {
  margin-top: -0.25rem;
}

.form-check.disabled .form-check-label {
  color: #7a7a7a;
  cursor: not-allowed;
}

.form-check-label {
  padding-left: 1.25rem;
  margin-bottom: 0;
  cursor: pointer;
}

.form-check-input {
  position: absolute;
  margin-top: 0.25rem;
  margin-left: -1.25rem;
}

.form-check-input:only-child {
  position: static;
}

.form-check-inline {
  position: relative;
  display: inline-block;
  padding-left: 1.25rem;
  margin-bottom: 0;
  vertical-align: middle;
  cursor: pointer;
}

.form-check-inline + .form-check-inline {
  margin-left: 0.75rem;
}

.form-check-inline.disabled {
  color: #7a7a7a;
  cursor: not-allowed;
}

.form-control-feedback {
  margin-top: 0.25rem;
}

.form-control-danger,
.form-control-success,
.form-control-warning {
  padding-right: 2.25rem;
  background-repeat: no-repeat;
  background-position: center right 0.625rem;
  background-size: 1.25rem 1.25rem;
}

.has-success .custom-control,
.has-success .form-check-inline,
.has-success .form-check-label,
.has-success .form-control-feedback,
.has-success .form-control-label {
  color: #4cbb6c;
}

.has-success .form-control {
  border-color: #4cbb6c;
}

.has-success .form-control:focus {
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #96d7a9;
}

.has-success .input-group-addon {
  color: #4cbb6c;
  border-color: #4cbb6c;
  background-color: #e0f3e5;
}

.has-success .form-control-success {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='#4cbb6c' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3E%3C/svg%3E");
}

.has-warning .custom-control,
.has-warning .form-check-inline,
.has-warning .form-check-label,
.has-warning .form-control-feedback,
.has-warning .form-control-label {
  color: #ff9a52;
}

.has-warning .form-control {
  border-color: #ff9a52;
}

.has-warning .form-control:focus {
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ffd6b8;
}

.has-warning .input-group-addon {
  color: #ff9a52;
  border-color: #ff9a52;
  background-color: #fff;
}

.has-warning .form-control-warning {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='#ff9a52' d='M4.4 5.324h-.8v-2.46h.8zm0 1.42h-.8V5.89h.8zM3.76.63L.04 7.075c-.115.2.016.425.26.426h7.397c.242 0 .372-.226.258-.426C6.726 4.924 5.47 2.79 4.253.63c-.113-.174-.39-.174-.494 0z'/%3E%3C/svg%3E");
}

.has-danger .custom-control,
.has-danger .form-check-inline,
.has-danger .form-check-label,
.has-danger .form-control-feedback,
.has-danger .form-control-label {
  color: #ff4c4c;
}

.has-danger .form-control {
  border-color: #ff4c4c;
}

.has-danger .form-control:focus {
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ffb2b2;
}

.has-danger .input-group-addon {
  color: #ff4c4c;
  border-color: #ff4c4c;
  background-color: #fff;
}

.has-danger .form-control-danger {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='#ff4c4c' viewBox='-2 -2 7 7'%3E%3Cpath stroke='%23d9534f' d='M0 0l3 3m0-3L0 3'/%3E%3Ccircle r='.5'/%3E%3Ccircle cx='3' r='.5'/%3E%3Ccircle cy='3' r='.5'/%3E%3Ccircle cx='3' cy='3' r='.5'/%3E%3C/svg%3E");
}

@media (min-width: 576px) {
  .form-inline .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
  }

  .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }

  .form-inline .form-control-static {
    display: inline-block;
  }

  .form-inline .input-group {
    display: inline-table;
    width: auto;
    vertical-align: middle;
  }

  .form-inline .input-group .form-control,
  .form-inline .input-group .input-group-addon,
  .form-inline .input-group .input-group-btn {
    width: auto;
  }

  .form-inline .input-group > .form-control {
    width: 100%;
  }

  .form-inline .form-check,
  .form-inline .form-control-label {
    margin-bottom: 0;
    vertical-align: middle;
  }

  .form-inline .form-check {
    display: inline-block;
    margin-top: 0;
  }

  .form-inline .form-check-label {
    padding-left: 0;
  }

  .form-inline .form-check-input {
    position: relative;
    margin-left: 0;
  }

  .form-inline .has-feedback .form-control-feedback {
    top: 0;
  }
}

.btn {
  display: inline-block;
  font-weight: 400;
  line-height: 1.25;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  border-radius: 0;
}

.btn.active.focus,
.btn.active:focus,
.btn.focus,
.btn:active.focus,
.btn:active:focus,
.btn:focus {
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}

.btn.focus,
.btn:focus,
.btn:hover {
  text-decoration: none;
}

.btn.active,
.btn:active {
  background-image: none;
  outline: 0;
}

.btn.disabled,
.btn:disabled {
  cursor: not-allowed;
  opacity: 0.65;
}

a.btn.disabled,
fieldset[disabled] a.btn {
  pointer-events: none;
}

.btn-primary {
  color: #fff;
  background-color: #2fb5d2;
  border-color: transparent;
}

.btn-primary.focus,
.btn-primary:focus,
.btn-primary:hover {
  color: #fff;
  background-color: #2592a9;
  border-color: transparent;
}

.btn-primary.active,
.btn-primary:active,
.open > .btn-primary.dropdown-toggle {
  color: #fff;
  background-color: #2592a9;
  border-color: transparent;
  background-image: none;
}

.btn-primary.active.focus,
.btn-primary.active:focus,
.btn-primary.active:hover,
.btn-primary:active.focus,
.btn-primary:active:focus,
.btn-primary:active:hover,
.open > .btn-primary.dropdown-toggle.focus,
.open > .btn-primary.dropdown-toggle:focus,
.open > .btn-primary.dropdown-toggle:hover {
  color: #fff;
  background-color: #1e788c;
  border-color: transparent;
}

.btn-primary.disabled.focus,
.btn-primary.disabled:focus,
.btn-primary.disabled:hover,
.btn-primary:disabled.focus,
.btn-primary:disabled:focus,
.btn-primary:disabled:hover {
  background-color: #2fb5d2;
  border-color: transparent;
}

.btn-secondary,
.btn-tertiary {
  color: #232323;
  background-color: #f6f6f6;
  border-color: transparent;
}

.btn-secondary.focus,
.btn-secondary:focus,
.btn-secondary:hover,
.btn-tertiary:focus,
.btn-tertiary:hover,
.focus.btn-tertiary {
  color: #232323;
  background-color: #ddd;
  border-color: transparent;
}

.active.btn-tertiary,
.btn-secondary.active,
.btn-secondary:active,
.btn-tertiary:active,
.open > .btn-secondary.dropdown-toggle,
.open > .dropdown-toggle.btn-tertiary {
  color: #232323;
  background-color: #ddd;
  border-color: transparent;
  background-image: none;
}

.active.btn-tertiary:focus,
.active.btn-tertiary:hover,
.active.focus.btn-tertiary,
.btn-secondary.active.focus,
.btn-secondary.active:focus,
.btn-secondary.active:hover,
.btn-secondary:active.focus,
.btn-secondary:active:focus,
.btn-secondary:active:hover,
.btn-tertiary:active.focus,
.btn-tertiary:active:focus,
.btn-tertiary:active:hover,
.open > .btn-secondary.dropdown-toggle.focus,
.open > .btn-secondary.dropdown-toggle:focus,
.open > .btn-secondary.dropdown-toggle:hover,
.open > .dropdown-toggle.btn-tertiary:focus,
.open > .dropdown-toggle.btn-tertiary:hover,
.open > .dropdown-toggle.focus.btn-tertiary {
  color: #232323;
  background-color: #cbcbcb;
  border-color: transparent;
}

.btn-secondary.disabled.focus,
.btn-secondary.disabled:focus,
.btn-secondary.disabled:hover,
.btn-secondary:disabled.focus,
.btn-secondary:disabled:focus,
.btn-secondary:disabled:hover,
.btn-tertiary:disabled.focus,
.btn-tertiary:disabled:focus,
.btn-tertiary:disabled:hover,
.disabled.btn-tertiary:focus,
.disabled.btn-tertiary:hover,
.disabled.focus.btn-tertiary {
  background-color: #f6f6f6;
  border-color: transparent;
}

.btn-info {
  color: #fff;
  background-color: #5bc0de;
  border-color: #5bc0de;
}

.btn-info.focus,
.btn-info:focus,
.btn-info:hover {
  color: #fff;
  background-color: #31b0d5;
  border-color: #2aabd2;
}

.btn-info.active,
.btn-info:active,
.open > .btn-info.dropdown-toggle {
  color: #fff;
  background-color: #31b0d5;
  border-color: #2aabd2;
  background-image: none;
}

.btn-info.active.focus,
.btn-info.active:focus,
.btn-info.active:hover,
.btn-info:active.focus,
.btn-info:active:focus,
.btn-info:active:hover,
.open > .btn-info.dropdown-toggle.focus,
.open > .btn-info.dropdown-toggle:focus,
.open > .btn-info.dropdown-toggle:hover {
  color: #fff;
  background-color: #269abc;
  border-color: #1f7e9a;
}

.btn-info.disabled.focus,
.btn-info.disabled:focus,
.btn-info.disabled:hover,
.btn-info:disabled.focus,
.btn-info:disabled:focus,
.btn-info:disabled:hover {
  background-color: #5bc0de;
  border-color: #5bc0de;
}

.btn-success {
  color: #fff;
  background-color: #4cbb6c;
  border-color: #4cbb6c;
}

.btn-success.focus,
.btn-success:focus,
.btn-success:hover {
  color: #fff;
  background-color: #3a9a56;
  border-color: #389252;
}

.btn-success.active,
.btn-success:active,
.open > .btn-success.dropdown-toggle {
  color: #fff;
  background-color: #3a9a56;
  border-color: #389252;
  background-image: none;
}

.btn-success.active.focus,
.btn-success.active:focus,
.btn-success.active:hover,
.btn-success:active.focus,
.btn-success:active:focus,
.btn-success:active:hover,
.open > .btn-success.dropdown-toggle.focus,
.open > .btn-success.dropdown-toggle:focus,
.open > .btn-success.dropdown-toggle:hover {
  color: #fff;
  background-color: #318047;
  border-color: #256237;
}

.btn-success.disabled.focus,
.btn-success.disabled:focus,
.btn-success.disabled:hover,
.btn-success:disabled.focus,
.btn-success:disabled:focus,
.btn-success:disabled:hover {
  background-color: #4cbb6c;
  border-color: #4cbb6c;
}

.btn-warning {
  color: #fff;
  background-color: #ff9a52;
  border-color: #ff9a52;
}

.btn-warning.focus,
.btn-warning:focus,
.btn-warning:hover {
  color: #fff;
  background-color: #ff7c1f;
  border-color: #ff7615;
}

.btn-warning.active,
.btn-warning:active,
.open > .btn-warning.dropdown-toggle {
  color: #fff;
  background-color: #ff7c1f;
  border-color: #ff7615;
  background-image: none;
}

.btn-warning.active.focus,
.btn-warning.active:focus,
.btn-warning.active:hover,
.btn-warning:active.focus,
.btn-warning:active:focus,
.btn-warning:active:hover,
.open > .btn-warning.dropdown-toggle.focus,
.open > .btn-warning.dropdown-toggle:focus,
.open > .btn-warning.dropdown-toggle:hover {
  color: #fff;
  background-color: #fa6800;
  border-color: #d25700;
}

.btn-warning.disabled.focus,
.btn-warning.disabled:focus,
.btn-warning.disabled:hover,
.btn-warning:disabled.focus,
.btn-warning:disabled:focus,
.btn-warning:disabled:hover {
  background-color: #ff9a52;
  border-color: #ff9a52;
}

.btn-danger {
  color: #fff;
  background-color: #ff4c4c;
  border-color: #ff4c4c;
}

.btn-danger.focus,
.btn-danger:focus,
.btn-danger:hover {
  color: #fff;
  background-color: #ff1919;
  border-color: #ff0f0f;
}

.btn-danger.active,
.btn-danger:active,
.open > .btn-danger.dropdown-toggle {
  color: #fff;
  background-color: #ff1919;
  border-color: #ff0f0f;
  background-image: none;
}

.btn-danger.active.focus,
.btn-danger.active:focus,
.btn-danger.active:hover,
.btn-danger:active.focus,
.btn-danger:active:focus,
.btn-danger:active:hover,
.open > .btn-danger.dropdown-toggle.focus,
.open > .btn-danger.dropdown-toggle:focus,
.open > .btn-danger.dropdown-toggle:hover {
  color: #fff;
  background-color: #f40000;
  border-color: #c00;
}

.btn-danger.disabled.focus,
.btn-danger.disabled:focus,
.btn-danger.disabled:hover,
.btn-danger:disabled.focus,
.btn-danger:disabled:focus,
.btn-danger:disabled:hover {
  background-color: #ff4c4c;
  border-color: #ff4c4c;
}

.btn-outline-primary {
  color: #2fb5d2;
  background-image: none;
  background-color: transparent;
  border-color: #2fb5d2;
}

.btn-outline-primary.active,
.btn-outline-primary.focus,
.btn-outline-primary:active,
.btn-outline-primary:focus,
.btn-outline-primary:hover,
.open > .btn-outline-primary.dropdown-toggle {
  color: #fff;
  background-color: #2fb5d2;
  border-color: #2fb5d2;
}

.btn-outline-primary.active.focus,
.btn-outline-primary.active:focus,
.btn-outline-primary.active:hover,
.btn-outline-primary:active.focus,
.btn-outline-primary:active:focus,
.btn-outline-primary:active:hover,
.open > .btn-outline-primary.dropdown-toggle.focus,
.open > .btn-outline-primary.dropdown-toggle:focus,
.open > .btn-outline-primary.dropdown-toggle:hover {
  color: #fff;
  background-color: #1e788c;
  border-color: #175c6a;
}

.btn-outline-primary.disabled.focus,
.btn-outline-primary.disabled:focus,
.btn-outline-primary.disabled:hover,
.btn-outline-primary:disabled.focus,
.btn-outline-primary:disabled:focus,
.btn-outline-primary:disabled:hover {
  border-color: #83d3e4;
}

.btn-outline-secondary {
  color: transparent;
  background-image: none;
  background-color: transparent;
  border-color: transparent;
}

.btn-outline-secondary.active,
.btn-outline-secondary.active.focus,
.btn-outline-secondary.active:focus,
.btn-outline-secondary.active:hover,
.btn-outline-secondary.focus,
.btn-outline-secondary:active,
.btn-outline-secondary:active.focus,
.btn-outline-secondary:active:focus,
.btn-outline-secondary:active:hover,
.btn-outline-secondary:focus,
.btn-outline-secondary:hover,
.open > .btn-outline-secondary.dropdown-toggle,
.open > .btn-outline-secondary.dropdown-toggle.focus,
.open > .btn-outline-secondary.dropdown-toggle:focus,
.open > .btn-outline-secondary.dropdown-toggle:hover {
  color: #fff;
  background-color: transparent;
  border-color: transparent;
}

.btn-outline-secondary.disabled.focus,
.btn-outline-secondary.disabled:focus,
.btn-outline-secondary.disabled:hover,
.btn-outline-secondary:disabled.focus,
.btn-outline-secondary:disabled:focus,
.btn-outline-secondary:disabled:hover {
  border-color: rgba(51, 51, 51, 0);
}

.btn-outline-info {
  color: #5bc0de;
  background-image: none;
  background-color: transparent;
  border-color: #5bc0de;
}

.btn-outline-info.active,
.btn-outline-info.focus,
.btn-outline-info:active,
.btn-outline-info:focus,
.btn-outline-info:hover,
.open > .btn-outline-info.dropdown-toggle {
  color: #fff;
  background-color: #5bc0de;
  border-color: #5bc0de;
}

.btn-outline-info.active.focus,
.btn-outline-info.active:focus,
.btn-outline-info.active:hover,
.btn-outline-info:active.focus,
.btn-outline-info:active:focus,
.btn-outline-info:active:hover,
.open > .btn-outline-info.dropdown-toggle.focus,
.open > .btn-outline-info.dropdown-toggle:focus,
.open > .btn-outline-info.dropdown-toggle:hover {
  color: #fff;
  background-color: #269abc;
  border-color: #1f7e9a;
}

.btn-outline-info.disabled.focus,
.btn-outline-info.disabled:focus,
.btn-outline-info.disabled:hover,
.btn-outline-info:disabled.focus,
.btn-outline-info:disabled:focus,
.btn-outline-info:disabled:hover {
  border-color: #b0e1ef;
}

.btn-outline-success {
  color: #4cbb6c;
  background-image: none;
  background-color: transparent;
  border-color: #4cbb6c;
}

.btn-outline-success.active,
.btn-outline-success.focus,
.btn-outline-success:active,
.btn-outline-success:focus,
.btn-outline-success:hover,
.open > .btn-outline-success.dropdown-toggle {
  color: #fff;
  background-color: #4cbb6c;
  border-color: #4cbb6c;
}

.btn-outline-success.active.focus,
.btn-outline-success.active:focus,
.btn-outline-success.active:hover,
.btn-outline-success:active.focus,
.btn-outline-success:active:focus,
.btn-outline-success:active:hover,
.open > .btn-outline-success.dropdown-toggle.focus,
.open > .btn-outline-success.dropdown-toggle:focus,
.open > .btn-outline-success.dropdown-toggle:hover {
  color: #fff;
  background-color: #318047;
  border-color: #256237;
}

.btn-outline-success.disabled.focus,
.btn-outline-success.disabled:focus,
.btn-outline-success.disabled:hover,
.btn-outline-success:disabled.focus,
.btn-outline-success:disabled:focus,
.btn-outline-success:disabled:hover {
  border-color: #96d7a9;
}

.btn-outline-warning {
  color: #ff9a52;
  background-image: none;
  background-color: transparent;
  border-color: #ff9a52;
}

.btn-outline-warning.active,
.btn-outline-warning.focus,
.btn-outline-warning:active,
.btn-outline-warning:focus,
.btn-outline-warning:hover,
.open > .btn-outline-warning.dropdown-toggle {
  color: #fff;
  background-color: #ff9a52;
  border-color: #ff9a52;
}

.btn-outline-warning.active.focus,
.btn-outline-warning.active:focus,
.btn-outline-warning.active:hover,
.btn-outline-warning:active.focus,
.btn-outline-warning:active:focus,
.btn-outline-warning:active:hover,
.open > .btn-outline-warning.dropdown-toggle.focus,
.open > .btn-outline-warning.dropdown-toggle:focus,
.open > .btn-outline-warning.dropdown-toggle:hover {
  color: #fff;
  background-color: #fa6800;
  border-color: #d25700;
}

.btn-outline-warning.disabled.focus,
.btn-outline-warning.disabled:focus,
.btn-outline-warning.disabled:hover,
.btn-outline-warning:disabled.focus,
.btn-outline-warning:disabled:focus,
.btn-outline-warning:disabled:hover {
  border-color: #ffd6b8;
}

.btn-outline-danger {
  color: #ff4c4c;
  background-image: none;
  background-color: transparent;
  border-color: #ff4c4c;
}

.btn-outline-danger.active,
.btn-outline-danger.focus,
.btn-outline-danger:active,
.btn-outline-danger:focus,
.btn-outline-danger:hover,
.open > .btn-outline-danger.dropdown-toggle {
  color: #fff;
  background-color: #ff4c4c;
  border-color: #ff4c4c;
}

.btn-outline-danger.active.focus,
.btn-outline-danger.active:focus,
.btn-outline-danger.active:hover,
.btn-outline-danger:active.focus,
.btn-outline-danger:active:focus,
.btn-outline-danger:active:hover,
.open > .btn-outline-danger.dropdown-toggle.focus,
.open > .btn-outline-danger.dropdown-toggle:focus,
.open > .btn-outline-danger.dropdown-toggle:hover {
  color: #fff;
  background-color: #f40000;
  border-color: #c00;
}

.btn-outline-danger.disabled.focus,
.btn-outline-danger.disabled:focus,
.btn-outline-danger.disabled:hover,
.btn-outline-danger:disabled.focus,
.btn-outline-danger:disabled:focus,
.btn-outline-danger:disabled:hover {
  border-color: #ffb2b2;
}

.btn-link {
  font-weight: 400;
  color: #2fb5d2;
  border-radius: 0;
}

.btn-link,
.btn-link.active,
.btn-link:active,
.btn-link:disabled {
  background-color: transparent;
}

.btn-link,
.btn-link:active,
.btn-link:focus,
.btn-link:hover {
  border-color: transparent;
}

.btn-link:focus,
.btn-link:hover {
  color: #208094;
  text-decoration: underline;
  background-color: transparent;
}

.btn-link:disabled:focus,
.btn-link:disabled:hover {
  color: #f1f1f1;
  text-decoration: none;
}

.btn-group-lg > .btn,
.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 0.9375rem;
  border-radius: 0.3rem;
}

.btn-group-sm > .btn,
.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.2rem;
}

.btn-block {
  display: block;
  width: 100%;
}

.btn-block + .btn-block {
  margin-top: 0.5rem;
}

input[type='button'].btn-block,
input[type='reset'].btn-block,
input[type='submit'].btn-block {
  width: 100%;
}

.fade {
  opacity: 0;
  transition: opacity 0.15s linear;
}

.fade.in {
  opacity: 1;
}

.collapse {
  display: none;
}

.collapse.in {
  display: block;
}

tr.collapse.in {
  display: table-row;
}

tbody.collapse.in {
  display: table-row-group;
}

.collapsing {
  height: 0;
  overflow: hidden;
  transition-timing-function: ease;
  transition-duration: 0.35s;
  transition-property: height;
}

.collapsing,
.dropdown,
.dropup {
  position: relative;
}

.dropdown-toggle:after {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 0.3em;
  vertical-align: middle;
  content: '';
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-left: 0.3em solid transparent;
}

.dropdown-toggle:focus {
  outline: 0;
}

.dropup .dropdown-toggle:after {
  border-top: 0;
  border-bottom: 0.3em solid;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  font-size: 1rem;
  color: #373a3c;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0;
}

.dropdown-divider {
  height: 1px;
  margin: 0.5rem 0;
  overflow: hidden;
  background-color: #e5e5e5;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 3px 1.5rem;
  clear: both;
  font-weight: 400;
  color: #373a3c;
  text-align: inherit;
  white-space: nowrap;
  background: none;
  border: 0;
}

.dropdown-item:focus,
.dropdown-item:hover {
  color: #2b2d2f;
  background-color: #f5f5f5;
}

.dropdown-item.active,
.dropdown-item.active:focus,
.dropdown-item.active:hover {
  color: #fff;
  text-decoration: none;
  background-color: #2fb5d2;
  outline: 0;
}

.dropdown-item.disabled,
.dropdown-item.disabled:focus,
.dropdown-item.disabled:hover {
  color: #f1f1f1;
}

.dropdown-item.disabled:focus,
.dropdown-item.disabled:hover {
  text-decoration: none;
  cursor: not-allowed;
  background-color: transparent;
  background-image: none;
  filter: 'progid:DXImageTransform.Microsoft.gradient(enabled = false)';
}

.open > .dropdown-menu {
  display: block;
}

.open > a {
  outline: 0;
}

.dropdown-menu-right {
  right: 0;
  left: auto;
}

.dropdown-menu-left {
  right: auto;
  left: 0;
}

.dropdown-header {
  display: block;
  padding: 0.5rem 1.5rem;
  margin-bottom: 0;
  font-size: 0.875rem;
  color: #f1f1f1;
  white-space: nowrap;
}

.dropdown-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 990;
}

.dropup .caret,
.navbar-fixed-bottom .dropdown .caret {
  content: '';
  border-top: 0;
  border-bottom: 0.3em solid;
}

.dropup .dropdown-menu,
.navbar-fixed-bottom .dropdown .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-bottom: 0.125rem;
}

.btn-group,
.btn-group-vertical {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}

.btn-group-vertical > .btn,
.btn-group > .btn {
  position: relative;
  float: left;
  margin-bottom: 0;
}

.btn-group-vertical > .btn.active,
.btn-group-vertical > .btn:active,
.btn-group-vertical > .btn:focus,
.btn-group-vertical > .btn:hover,
.btn-group > .btn.active,
.btn-group > .btn:active,
.btn-group > .btn:focus,
.btn-group > .btn:hover {
  z-index: 2;
}

.btn-group .btn + .btn,
.btn-group .btn + .btn-group,
.btn-group .btn-group + .btn,
.btn-group .btn-group + .btn-group {
  margin-left: -1px;
}

.btn-toolbar {
  margin-left: -0.5rem;
}

.btn-toolbar:after {
  content: '';
  display: table;
  clear: both;
}

.btn-toolbar .btn-group,
.btn-toolbar .input-group {
  float: left;
}

.btn-toolbar > .btn,
.btn-toolbar > .btn-group,
.btn-toolbar > .input-group {
  margin-left: 0.5rem;
}

.btn-group > .btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
  border-radius: 0;
}

.btn-group > .btn:first-child {
  margin-left: 0;
}

.btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle) {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}

.btn-group > .btn:last-child:not(:first-child),
.btn-group > .dropdown-toggle:not(:first-child) {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}

.btn-group > .btn-group {
  float: left;
}

.btn-group > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
}

.btn-group > .btn-group:first-child:not(:last-child) > .btn:last-child,
.btn-group > .btn-group:first-child:not(:last-child) > .dropdown-toggle {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}

.btn-group > .btn-group:last-child:not(:first-child) > .btn:first-child {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}

.btn-group .dropdown-toggle:active,
.btn-group.open .dropdown-toggle {
  outline: 0;
}

.btn + .dropdown-toggle-split {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}

.btn + .dropdown-toggle-split:after {
  margin-left: 0;
}

.btn-group-sm > .btn + .dropdown-toggle-split,
.btn-sm + .dropdown-toggle-split {
  padding-right: 0.375rem;
  padding-left: 0.375rem;
}

.btn-group-lg > .btn + .dropdown-toggle-split,
.btn-lg + .dropdown-toggle-split {
  padding-right: 1.125rem;
  padding-left: 1.125rem;
}

.btn .caret {
  margin-left: 0;
}

.btn-group-lg > .btn .caret,
.btn-lg .caret {
  border-width: 0.3em 0.3em 0;
  border-bottom-width: 0;
}

.dropup .btn-group-lg > .btn .caret,
.dropup .btn-lg .caret {
  border-width: 0 0.3em 0.3em;
}

.btn-group-vertical > .btn,
.btn-group-vertical > .btn-group,
.btn-group-vertical > .btn-group > .btn {
  display: block;
  float: none;
  width: 100%;
  max-width: 100%;
}

.btn-group-vertical > .btn-group:after {
  content: '';
  display: table;
  clear: both;
}

.btn-group-vertical > .btn-group > .btn {
  float: none;
}

.btn-group-vertical > .btn + .btn,
.btn-group-vertical > .btn + .btn-group,
.btn-group-vertical > .btn-group + .btn,
.btn-group-vertical > .btn-group + .btn-group {
  margin-top: -1px;
  margin-left: 0;
}

.btn-group-vertical > .btn:not(:first-child):not(:last-child) {
  border-radius: 0;
}

.btn-group-vertical > .btn:first-child:not(:last-child) {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.btn-group-vertical > .btn:last-child:not(:first-child) {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}

.btn-group-vertical > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
}

.btn-group-vertical > .btn-group:first-child:not(:last-child) > .btn:last-child,
.btn-group-vertical > .btn-group:first-child:not(:last-child) > .dropdown-toggle {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.btn-group-vertical > .btn-group:last-child:not(:first-child) > .btn:first-child {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}

[data-toggle='buttons'] > .btn-group > .btn input[type='checkbox'],
[data-toggle='buttons'] > .btn-group > .btn input[type='radio'],
[data-toggle='buttons'] > .btn input[type='checkbox'],
[data-toggle='buttons'] > .btn input[type='radio'] {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}

.input-group {
  position: relative;
  width: 100%;
  display: table;
  border-collapse: separate;
}

.input-group .form-control {
  position: relative;
  z-index: 2;
  float: left;
  width: 100%;
  margin-bottom: 0;
}

.input-group .form-control:active,
.input-group .form-control:focus,
.input-group .form-control:hover {
  z-index: 3;
}

.input-group-addon,
.input-group-btn,
.input-group .form-control {
  display: table-cell;
}

.input-group-addon:not(:first-child):not(:last-child),
.input-group-btn:not(:first-child):not(:last-child),
.input-group .form-control:not(:first-child):not(:last-child) {
  border-radius: 0;
}

.input-group-addon,
.input-group-btn {
  width: 1%;
  white-space: nowrap;
  vertical-align: middle;
}

.input-group-addon {
  padding: 0.5rem 0.75rem;
  margin-bottom: 0;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.25;
  color: #7a7a7a;
  text-align: center;
  background-color: #f6f6f6;
  border: 1px solid rgba(0, 0, 0, 0.25);
  border-radius: 0;
}

.input-group-addon.form-control-sm,
.input-group-sm > .input-group-addon,
.input-group-sm > .input-group-btn > .input-group-addon.btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.2rem;
}

.input-group-addon.form-control-lg,
.input-group-lg > .input-group-addon,
.input-group-lg > .input-group-btn > .input-group-addon.btn {
  padding: 0.75rem 1.5rem;
  font-size: 0.9375rem;
  border-radius: 0.3rem;
}

.input-group-addon input[type='checkbox'],
.input-group-addon input[type='radio'] {
  margin-top: 0;
}

.input-group-addon:not(:last-child),
.input-group-btn:not(:first-child) > .btn-group:not(:last-child) > .btn,
.input-group-btn:not(:first-child) > .btn:not(:last-child):not(.dropdown-toggle),
.input-group-btn:not(:last-child) > .btn,
.input-group-btn:not(:last-child) > .btn-group > .btn,
.input-group-btn:not(:last-child) > .dropdown-toggle,
.input-group .form-control:not(:last-child) {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}

.input-group-addon:not(:last-child) {
  border-right: 0;
}

.input-group-addon:not(:first-child),
.input-group-btn:not(:first-child) > .btn,
.input-group-btn:not(:first-child) > .btn-group > .btn,
.input-group-btn:not(:first-child) > .dropdown-toggle,
.input-group-btn:not(:last-child) > .btn-group:not(:first-child) > .btn,
.input-group-btn:not(:last-child) > .btn:not(:first-child),
.input-group .form-control:not(:first-child) {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}

.form-control + .input-group-addon:not(:first-child) {
  border-left: 0;
}

.input-group-btn {
  font-size: 0;
  white-space: nowrap;
}

.input-group-btn,
.input-group-btn > .btn {
  position: relative;
}

.input-group-btn > .btn + .btn {
  margin-left: -1px;
}

.input-group-btn > .btn:active,
.input-group-btn > .btn:focus,
.input-group-btn > .btn:hover {
  z-index: 3;
}

.input-group-btn:not(:last-child) > .btn,
.input-group-btn:not(:last-child) > .btn-group {
  margin-right: -1px;
}

.input-group-btn:not(:first-child) > .btn,
.input-group-btn:not(:first-child) > .btn-group {
  z-index: 2;
  margin-left: -1px;
}

.input-group-btn:not(:first-child) > .btn-group:active,
.input-group-btn:not(:first-child) > .btn-group:focus,
.input-group-btn:not(:first-child) > .btn-group:hover,
.input-group-btn:not(:first-child) > .btn:active,
.input-group-btn:not(:first-child) > .btn:focus,
.input-group-btn:not(:first-child) > .btn:hover {
  z-index: 3;
}

.custom-control {
  position: relative;
  display: inline-block;
  padding-left: 1.5rem;
  cursor: pointer;
}

.custom-control + .custom-control {
  margin-left: 1rem;
}

.custom-control-input {
  position: absolute;
  z-index: -1;
  opacity: 0;
}

.custom-control-input:checked ~ .custom-control-indicator {
  color: #fff;
  background-color: #0074d9;
}

.custom-control-input:focus ~ .custom-control-indicator {
  box-shadow: 0 0 0 0.075rem #fff, 0 0 0 0.2rem #0074d9;
}

.custom-control-input:active ~ .custom-control-indicator {
  color: #fff;
  background-color: #84c6ff;
}

.custom-control-input:disabled ~ .custom-control-indicator {
  cursor: not-allowed;
  background-color: #eee;
}

.custom-control-input:disabled ~ .custom-control-description {
  color: #767676;
  cursor: not-allowed;
}

.custom-control-indicator {
  position: absolute;
  top: 0.25rem;
  left: 0;
  display: block;
  width: 1rem;
  height: 1rem;
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: #ddd;
  background-repeat: no-repeat;
  background-position: 50%;
  background-size: 50% 50%;
}

.custom-checkbox .custom-control-indicator {
  border-radius: 0;
}

.custom-checkbox .custom-control-input:checked ~ .custom-control-indicator {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='#fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E");
}

.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-indicator {
  background-color: #0074d9;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath stroke='#fff' d='M0 2h4'/%3E%3C/svg%3E");
}

.custom-radio .custom-control-indicator {
  border-radius: 50%;
}

.custom-radio .custom-control-input:checked ~ .custom-control-indicator {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='#fff'/%3E%3C/svg%3E");
}

.custom-controls-stacked .custom-control {
  float: left;
  clear: left;
}

.custom-controls-stacked .custom-control + .custom-control {
  margin-left: 0;
}

.custom-select {
  display: inline-block;
  max-width: 100%;
  height: calc(2.5rem - 2px);
  padding: 0.375rem 1.75rem 0.375rem 0.75rem;
  padding-right: 0.75rem\9;
  color: #7a7a7a;
  vertical-align: middle;
  background: #fff url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='#333' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E") no-repeat right
    0.75rem center;
  background-image: none\9;
  background-size: 8px 10px;
  border: 1px solid rgba(0, 0, 0, 0.25);
  border-radius: 0;
  -moz-appearance: none;
  -webkit-appearance: none;
}

.custom-select:focus {
  border-color: #51a7e8;
  outline: none;
}

.custom-select:focus::-ms-value {
  color: #7a7a7a;
  background-color: #fff;
}

.custom-select:disabled {
  color: #f1f1f1;
  cursor: not-allowed;
  background-color: #f6f6f6;
}

.custom-select::-ms-expand {
  opacity: 0;
}

.custom-select-sm {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  font-size: 75%;
}

.custom-file {
  position: relative;
  display: inline-block;
  max-width: 100%;
  height: 2.5rem;
  cursor: pointer;
}

.custom-file-input {
  min-width: 14rem;
  max-width: 100%;
  margin: 0;
  filter: alpha(opacity=0);
  opacity: 0;
}

.custom-file-control {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  z-index: 5;
  height: 2.5rem;
  padding: 0.5rem 1rem;
  line-height: 1.5;
  color: #555;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 0;
}

.custom-file-control:lang(en):after {
  content: 'Choose file...';
}

.custom-file-control:before {
  position: absolute;
  top: -1px;
  right: -1px;
  bottom: -1px;
  z-index: 6;
  display: block;
  height: 2.5rem;
  padding: 0.5rem 1rem;
  line-height: 1.5;
  color: #555;
  background-color: #eee;
  border: 1px solid #ddd;
  border-radius: 0 0 0 0;
}

.custom-file-control:lang(en):before {
  content: 'Browse';
}

.nav {
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.nav-link {
  display: inline-block;
}

.nav-link:focus,
.nav-link:hover {
  text-decoration: none;
}

.nav-link.disabled {
  color: #f1f1f1;
}

.nav-link.disabled,
.nav-link.disabled:focus,
.nav-link.disabled:hover {
  color: #f1f1f1;
  cursor: not-allowed;
  background-color: transparent;
}

.nav-inline .nav-item {
  display: inline-block;
}

.nav-inline .nav-item + .nav-item,
.nav-inline .nav-link + .nav-link {
  margin-left: 1rem;
}

.nav-tabs {
  border-bottom: 1px solid #ddd;
}

.nav-tabs:after {
  content: '';
  display: table;
  clear: both;
}

.nav-tabs .nav-item {
  float: left;
  margin-bottom: -1px;
}

.nav-tabs .nav-item + .nav-item {
  margin-left: 0.2rem;
}

.nav-tabs .nav-link {
  display: block;
  padding: 0.5em 1em;
  border: 1px solid transparent;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}

.nav-tabs .nav-link:focus,
.nav-tabs .nav-link:hover {
  border-color: #f6f6f6 #f6f6f6 #ddd;
}

.nav-tabs .nav-link.disabled,
.nav-tabs .nav-link.disabled:focus,
.nav-tabs .nav-link.disabled:hover {
  color: #f1f1f1;
  background-color: transparent;
  border-color: transparent;
}

.nav-tabs .nav-item.open .nav-link,
.nav-tabs .nav-item.open .nav-link:focus,
.nav-tabs .nav-item.open .nav-link:hover,
.nav-tabs .nav-link.active,
.nav-tabs .nav-link.active:focus,
.nav-tabs .nav-link.active:hover {
  color: #7a7a7a;
  background-color: #fff;
  border-color: #ddd #ddd transparent;
}

.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}

.nav-pills:after {
  content: '';
  display: table;
  clear: both;
}

.nav-pills .nav-item {
  float: left;
}

.nav-pills .nav-item + .nav-item {
  margin-left: 0.2rem;
}

.nav-pills .nav-link {
  display: block;
  padding: 0.5em 1em;
  border-radius: 0;
}

.nav-pills .nav-item.open .nav-link,
.nav-pills .nav-item.open .nav-link:focus,
.nav-pills .nav-item.open .nav-link:hover,
.nav-pills .nav-link.active,
.nav-pills .nav-link.active:focus,
.nav-pills .nav-link.active:hover {
  color: #fff;
  cursor: default;
  background-color: #2fb5d2;
}

.nav-stacked .nav-item {
  display: block;
  float: none;
}

.nav-stacked .nav-item + .nav-item {
  margin-top: 0.2rem;
  margin-left: 0;
}

.tab-content > .tab-pane {
  display: none;
}

.tab-content > .active {
  display: block;
}

.navbar {
  position: relative;
  padding: 0.5rem 1rem;
}

.navbar:after {
  content: '';
  display: table;
  clear: both;
}

@media (min-width: 576px) {
  .navbar {
    border-radius: 0;
  }
}

.navbar-full {
  z-index: 1000;
}

@media (min-width: 576px) {
  .navbar-full {
    border-radius: 0;
  }
}

.navbar-fixed-bottom,
.navbar-fixed-top {
  position: fixed;
  right: 0;
  left: 0;
  z-index: 1030;
}

@media (min-width: 576px) {
  .navbar-fixed-bottom,
  .navbar-fixed-top {
    border-radius: 0;
  }
}

.navbar-fixed-top {
  top: 0;
}

.navbar-fixed-bottom {
  bottom: 0;
}

.navbar-sticky-top {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 1030;
  width: 100%;
}

@media (min-width: 576px) {
  .navbar-sticky-top {
    border-radius: 0;
  }
}

.navbar-brand {
  float: left;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  margin-right: 1rem;
  font-size: 0.9375rem;
  line-height: inherit;
}

.navbar-brand:focus,
.navbar-brand:hover {
  text-decoration: none;
}

.navbar-divider {
  float: left;
  width: 1px;
  padding-top: 0.425rem;
  padding-bottom: 0.425rem;
  margin-right: 1rem;
  margin-left: 1rem;
  overflow: hidden;
}

.navbar-divider:before {
  content: '\A0';
}

.navbar-text {
  display: inline-block;
  padding-top: 0.425rem;
  padding-bottom: 0.425rem;
}

.navbar-toggler {
  width: 2.5em;
  height: 2em;
  padding: 0.5rem 0.75rem;
  font-size: 0.9375rem;
  line-height: 1;
  background: transparent no-repeat 50%;
  background-size: 24px 24px;
  border: 1px solid transparent;
  border-radius: 0;
}

.navbar-toggler:focus,
.navbar-toggler:hover {
  text-decoration: none;
}

@media (max-width: 575px) {
  .navbar-toggleable-xs .navbar-brand {
    display: block;
    float: none;
    margin-top: 0.5rem;
    margin-right: 0;
  }

  .navbar-toggleable-xs .navbar-nav {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .navbar-toggleable-xs .navbar-nav .dropdown-menu {
    position: static;
    float: none;
  }
}

@media (min-width: 576px) {
  .navbar-toggleable-xs {
    display: block;
  }
}

@media (max-width: 767px) {
  .navbar-toggleable-sm .navbar-brand {
    display: block;
    float: none;
    margin-top: 0.5rem;
    margin-right: 0;
  }

  .navbar-toggleable-sm .navbar-nav {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .navbar-toggleable-sm .navbar-nav .dropdown-menu {
    position: static;
    float: none;
  }
}

@media (min-width: 768px) {
  .navbar-toggleable-sm {
    display: block;
  }
}

@media (max-width: 991px) {
  .navbar-toggleable-md .navbar-brand {
    display: block;
    float: none;
    margin-top: 0.5rem;
    margin-right: 0;
  }

  .navbar-toggleable-md .navbar-nav {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .navbar-toggleable-md .navbar-nav .dropdown-menu {
    position: static;
    float: none;
  }
}

@media (min-width: 992px) {
  .navbar-toggleable-md {
    display: block;
  }
}

.navbar-toggleable-lg:after {
  content: '';
  display: table;
  clear: both;
}

@media (max-width: 1199px) {
  .navbar-toggleable-lg .navbar-brand {
    display: block;
    float: none;
    margin-top: 0.5rem;
    margin-right: 0;
  }

  .navbar-toggleable-lg .navbar-nav {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .navbar-toggleable-lg .navbar-nav .dropdown-menu {
    position: static;
    float: none;
  }
}

@media (min-width: 1200px) {
  .navbar-toggleable-lg {
    display: block;
  }
}

.navbar-toggleable-xl {
  display: block;
}

.navbar-toggleable-xl:after {
  content: '';
  display: table;
  clear: both;
}

.navbar-toggleable-xl .navbar-brand {
  display: block;
  float: none;
  margin-top: 0.5rem;
  margin-right: 0;
}

.navbar-toggleable-xl .navbar-nav {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.navbar-toggleable-xl .navbar-nav .dropdown-menu {
  position: static;
  float: none;
}

.navbar-nav .nav-item {
  float: left;
}

.navbar-nav .nav-link {
  display: block;
  padding-top: 0.425rem;
  padding-bottom: 0.425rem;
}

.navbar-nav .nav-item + .nav-item,
.navbar-nav .nav-link + .nav-link {
  margin-left: 1rem;
}

.navbar-light .navbar-brand,
.navbar-light .navbar-brand:focus,
.navbar-light .navbar-brand:hover,
.navbar-light .navbar-toggler,
.navbar-light .navbar-toggler:focus,
.navbar-light .navbar-toggler:hover {
  color: rgba(0, 0, 0, 0.9);
}

.navbar-light .navbar-nav .nav-link {
  color: rgba(0, 0, 0, 0.5);
}

.navbar-light .navbar-nav .nav-link:focus,
.navbar-light .navbar-nav .nav-link:hover {
  color: rgba(0, 0, 0, 0.7);
}

.navbar-light .navbar-nav .active > .nav-link,
.navbar-light .navbar-nav .active > .nav-link:focus,
.navbar-light .navbar-nav .active > .nav-link:hover,
.navbar-light .navbar-nav .nav-link.active,
.navbar-light .navbar-nav .nav-link.active:focus,
.navbar-light .navbar-nav .nav-link.active:hover,
.navbar-light .navbar-nav .nav-link.open,
.navbar-light .navbar-nav .nav-link.open:focus,
.navbar-light .navbar-nav .nav-link.open:hover,
.navbar-light .navbar-nav .open > .nav-link,
.navbar-light .navbar-nav .open > .nav-link:focus,
.navbar-light .navbar-nav .open > .nav-link:hover {
  color: rgba(0, 0, 0, 0.9);
}

.navbar-light .navbar-toggler {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(0, 0, 0, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 8h24M4 16h24M4 24h24'/%3E%3C/svg%3E");
  border-color: rgba(0, 0, 0, 0.1);
}

.navbar-light .navbar-divider {
  background-color: rgba(0, 0, 0, 0.075);
}

.navbar-dark .navbar-brand,
.navbar-dark .navbar-brand:focus,
.navbar-dark .navbar-brand:hover,
.navbar-dark .navbar-toggler,
.navbar-dark .navbar-toggler:focus,
.navbar-dark .navbar-toggler:hover {
  color: #fff;
}

.navbar-dark .navbar-nav .nav-link {
  color: hsla(0, 0%, 100%, 0.5);
}

.navbar-dark .navbar-nav .nav-link:focus,
.navbar-dark .navbar-nav .nav-link:hover {
  color: hsla(0, 0%, 100%, 0.75);
}

.navbar-dark .navbar-nav .active > .nav-link,
.navbar-dark .navbar-nav .active > .nav-link:focus,
.navbar-dark .navbar-nav .active > .nav-link:hover,
.navbar-dark .navbar-nav .nav-link.active,
.navbar-dark .navbar-nav .nav-link.active:focus,
.navbar-dark .navbar-nav .nav-link.active:hover,
.navbar-dark .navbar-nav .nav-link.open,
.navbar-dark .navbar-nav .nav-link.open:focus,
.navbar-dark .navbar-nav .nav-link.open:hover,
.navbar-dark .navbar-nav .open > .nav-link,
.navbar-dark .navbar-nav .open > .nav-link:focus,
.navbar-dark .navbar-nav .open > .nav-link:hover {
  color: #fff;
}

.navbar-dark .navbar-toggler {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(255, 255, 255, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 8h24M4 16h24M4 24h24'/%3E%3C/svg%3E");
  border-color: hsla(0, 0%, 100%, 0.1);
}

.navbar-dark .navbar-divider {
  background-color: hsla(0, 0%, 100%, 0.075);
}

.navbar-toggleable-xs:after {
  content: '';
  display: table;
  clear: both;
}

@media (max-width: 575px) {
  .navbar-toggleable-xs .navbar-nav .nav-item {
    float: none;
    margin-left: 0;
  }
}

@media (min-width: 576px) {
  .navbar-toggleable-xs {
    display: block !important;
  }
}

.navbar-toggleable-sm:after {
  content: '';
  display: table;
  clear: both;
}

@media (max-width: 767px) {
  .navbar-toggleable-sm .navbar-nav .nav-item {
    float: none;
    margin-left: 0;
  }
}

@media (min-width: 768px) {
  .navbar-toggleable-sm {
    display: block !important;
  }
}

.navbar-toggleable-md:after {
  content: '';
  display: table;
  clear: both;
}

@media (max-width: 991px) {
  .navbar-toggleable-md .navbar-nav .nav-item {
    float: none;
    margin-left: 0;
  }
}

@media (min-width: 992px) {
  .navbar-toggleable-md {
    display: block !important;
  }
}

.card {
  position: relative;
  display: block;
  margin-bottom: 0.75rem;
  background-color: #fff;
  border-radius: 0;
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-block {
  padding: 1.25rem;
}

.card-block:after {
  content: '';
  display: table;
  clear: both;
}

.card-title {
  margin-bottom: 0.75rem;
}

.card-subtitle {
  margin-top: -0.375rem;
}

.card-subtitle,
.card-text:last-child {
  margin-bottom: 0;
}

.card-link:hover {
  text-decoration: none;
}

.card-link + .card-link {
  margin-left: 1.25rem;
}

.card > .list-group:first-child .list-group-item:first-child {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}

.card > .list-group:last-child .list-group-item:last-child {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.card-header {
  padding: 0.75rem 1.25rem;
  margin-bottom: 0;
  background-color: #f5f5f5;
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header:after {
  content: '';
  display: table;
  clear: both;
}

.card-header:first-child {
  border-radius: -1px -1px 0 0;
}

.card-footer {
  padding: 0.75rem 1.25rem;
  background-color: #f5f5f5;
  border-top: 1px solid rgba(0, 0, 0, 0.125);
}

.card-footer:after {
  content: '';
  display: table;
  clear: both;
}

.card-footer:last-child {
  border-radius: 0 0 -1px -1px;
}

.card-header-tabs {
  margin-bottom: -0.75rem;
  border-bottom: 0;
}

.card-header-pills,
.card-header-tabs {
  margin-right: -0.625rem;
  margin-left: -0.625rem;
}

.card-primary {
  background-color: #2fb5d2;
  border-color: #2fb5d2;
}

.card-primary .card-footer,
.card-primary .card-header {
  background-color: transparent;
}

.card-success {
  background-color: #4cbb6c;
  border-color: #4cbb6c;
}

.card-success .card-footer,
.card-success .card-header {
  background-color: transparent;
}

.card-info {
  background-color: #5bc0de;
  border-color: #5bc0de;
}

.card-info .card-footer,
.card-info .card-header {
  background-color: transparent;
}

.card-warning {
  background-color: #ff9a52;
  border-color: #ff9a52;
}

.card-warning .card-footer,
.card-warning .card-header {
  background-color: transparent;
}

.card-danger {
  background-color: #ff4c4c;
  border-color: #ff4c4c;
}

.card-danger .card-footer,
.card-danger .card-header,
.card-outline-primary {
  background-color: transparent;
}

.card-outline-primary {
  border-color: #2fb5d2;
}

.card-outline-secondary {
  background-color: transparent;
  border-color: transparent;
}

.card-outline-info {
  background-color: transparent;
  border-color: #5bc0de;
}

.card-outline-success {
  background-color: transparent;
  border-color: #4cbb6c;
}

.card-outline-warning {
  background-color: transparent;
  border-color: #ff9a52;
}

.card-outline-danger {
  background-color: transparent;
  border-color: #ff4c4c;
}

.card-inverse .card-footer,
.card-inverse .card-header {
  border-color: hsla(0, 0%, 100%, 0.2);
}

.card-inverse .card-blockquote,
.card-inverse .card-footer,
.card-inverse .card-header,
.card-inverse .card-title {
  color: #fff;
}

.card-inverse .card-blockquote .blockquote-footer,
.card-inverse .card-link,
.card-inverse .card-subtitle,
.card-inverse .card-text {
  color: hsla(0, 0%, 100%, 0.65);
}

.card-inverse .card-link:focus,
.card-inverse .card-link:hover {
  color: #fff;
}

.card-blockquote {
  padding: 0;
  margin-bottom: 0;
  border-left: 0;
}

.card-img {
  border-radius: -1px;
}

.card-img-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 1.25rem;
}

.card-img-top {
  border-top-right-radius: -1px;
  border-top-left-radius: -1px;
}

.card-img-bottom {
  border-bottom-right-radius: -1px;
  border-bottom-left-radius: -1px;
}

@media (min-width: 576px) {
  .card-deck {
    display: table;
    width: 100%;
    margin-bottom: 0.75rem;
    table-layout: fixed;
    border-spacing: 1.25rem 0;
  }

  .card-deck .card {
    display: table-cell;
    margin-bottom: 0;
    vertical-align: top;
  }

  .card-deck-wrapper {
    margin-right: -1.25rem;
    margin-left: -1.25rem;
  }
}

@media (min-width: 576px) {
  .card-group {
    display: table;
    width: 100%;
    table-layout: fixed;
  }

  .card-group .card {
    display: table-cell;
    vertical-align: top;
  }

  .card-group .card + .card {
    margin-left: 0;
    border-left: 0;
  }

  .card-group .card:first-child {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
  }

  .card-group .card:first-child .card-img-top {
    border-top-right-radius: 0;
  }

  .card-group .card:first-child .card-img-bottom {
    border-bottom-right-radius: 0;
  }

  .card-group .card:last-child {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
  }

  .card-group .card:last-child .card-img-top {
    border-top-left-radius: 0;
  }

  .card-group .card:last-child .card-img-bottom {
    border-bottom-left-radius: 0;
  }

  .card-group .card:not(:first-child):not(:last-child),
  .card-group .card:not(:first-child):not(:last-child) .card-img-bottom,
  .card-group .card:not(:first-child):not(:last-child) .card-img-top {
    border-radius: 0;
  }
}

@media (min-width: 576px) {
  .card-columns {
    -webkit-column-count: 3;
    column-count: 3;
    -webkit-column-gap: 1.25rem;
    column-gap: 1.25rem;
  }

  .card-columns .card {
    display: inline-block;
    width: 100%;
  }
}

.breadcrumb {
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  list-style: none;
  background-color: #f6f6f6;
  border-radius: 0;
}

.breadcrumb:after {
  content: '';
  display: table;
  clear: both;
}

.breadcrumb-item {
  float: left;
}

.breadcrumb-item + .breadcrumb-item:before {
  display: inline-block;
  padding-right: 0.5rem;
  padding-left: 0.5rem;
  color: #f1f1f1;
  content: '/';
}

.breadcrumb-item + .breadcrumb-item:hover:before {
  text-decoration: underline;
  text-decoration: none;
}

.breadcrumb-item.active {
  color: #f1f1f1;
}

.pagination {
  display: inline-block;
  padding-left: 0;
  margin-top: 1rem;
  margin-bottom: 1rem;
  border-radius: 0;
}

.page-item {
  display: inline;
}

.page-item:first-child .page-link {
  margin-left: 0;
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}

.page-item:last-child .page-link {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}

.page-item.active .page-link,
.page-item.active .page-link:focus,
.page-item.active .page-link:hover {
  z-index: 2;
  color: #fff;
  cursor: default;
  background-color: #2fb5d2;
  border-color: #2fb5d2;
}

.page-item.disabled .page-link,
.page-item.disabled .page-link:focus,
.page-item.disabled .page-link:hover {
  color: #f1f1f1;
  pointer-events: none;
  cursor: not-allowed;
  background-color: #fff;
  border-color: #ddd;
}

.page-link {
  position: relative;
  float: left;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  color: #2fb5d2;
  text-decoration: none;
  background-color: #fff;
  border: 1px solid #ddd;
}

.page-link:focus,
.page-link:hover {
  color: #208094;
  background-color: #f6f6f6;
  border-color: #ddd;
}

.pagination-lg .page-link {
  padding: 0.75rem 1.5rem;
  font-size: 0.9375rem;
}

.pagination-lg .page-item:first-child .page-link {
  border-bottom-left-radius: 0.3rem;
  border-top-left-radius: 0.3rem;
}

.pagination-lg .page-item:last-child .page-link {
  border-bottom-right-radius: 0.3rem;
  border-top-right-radius: 0.3rem;
}

.pagination-sm .page-link {
  padding: 0.275rem 0.75rem;
  font-size: 0.875rem;
}

.pagination-sm .page-item:first-child .page-link {
  border-bottom-left-radius: 0.2rem;
  border-top-left-radius: 0.2rem;
}

.pagination-sm .page-item:last-child .page-link {
  border-bottom-right-radius: 0.2rem;
  border-top-right-radius: 0.2rem;
}

.tag {
  display: inline-block;
  padding: 0.25em 0.4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0;
}

.tag:empty {
  display: none;
}

.btn .tag {
  position: relative;
  top: -1px;
}

a.tag:focus,
a.tag:hover {
  color: #fff;
  text-decoration: none;
  cursor: pointer;
}

.tag-pill {
  padding-right: 0.6em;
  padding-left: 0.6em;
  border-radius: 10rem;
}

.tag-default {
  background-color: #f1f1f1;
}

.tag-default[href]:focus,
.tag-default[href]:hover {
  background-color: #d8d8d8;
}

.tag-primary {
  background-color: #2fb5d2;
}

.tag-primary[href]:focus,
.tag-primary[href]:hover {
  background-color: #2592a9;
}

.tag-success {
  background-color: #4cbb6c;
}

.tag-success[href]:focus,
.tag-success[href]:hover {
  background-color: #3a9a56;
}

.tag-info {
  background-color: #5bc0de;
}

.tag-info[href]:focus,
.tag-info[href]:hover {
  background-color: #31b0d5;
}

.tag-warning {
  background-color: #ff9a52;
}

.tag-warning[href]:focus,
.tag-warning[href]:hover {
  background-color: #ff7c1f;
}

.tag-danger {
  background-color: #ff4c4c;
}

.tag-danger[href]:focus,
.tag-danger[href]:hover {
  background-color: #ff1919;
}

.jumbotron {
  padding: 2rem 1rem;
  margin-bottom: 2rem;
  background-color: #f6f6f6;
  border-radius: 0.3rem;
}

@media (min-width: 576px) {
  .jumbotron {
    padding: 4rem 2rem;
  }
}

.jumbotron-hr {
  border-top-color: #ddd;
}

.jumbotron-fluid {
  padding-right: 0;
  padding-left: 0;
  border-radius: 0;
}

.alert {
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0;
}

.alert-heading {
  color: inherit;
}

.alert-link {
  font-weight: 700;
}

.alert-dismissible {
  padding-right: 2.5rem;
}

.alert-dismissible .close {
  position: relative;
  top: -0.125rem;
  right: -1.25rem;
  color: inherit;
}

.alert-success {
  background-color: #dff0d8;
  border-color: #d0e9c6;
  color: #3c763d;
}

.alert-success hr {
  border-top-color: #c1e2b3;
}

.alert-success .alert-link {
  color: #2b542c;
}

.alert-info {
  background-color: #d9edf7;
  border-color: #bcdff1;
  color: #31708f;
}

.alert-info hr {
  border-top-color: #a6d5ec;
}

.alert-info .alert-link {
  color: #245269;
}

.alert-warning {
  background-color: rgba(255, 154, 82, 0.3);
  border-color: #ff9a52;
  color: #232323;
}

.alert-warning hr {
  border-top-color: #ff8b39;
}

.alert-warning .alert-link {
  color: #0a0a0a;
}

.alert-danger {
  background-color: #f2dede;
  border-color: #ebcccc;
  color: #a94442;
}

.alert-danger hr {
  border-top-color: #e4b9b9;
}

.alert-danger .alert-link {
  color: #843534;
}

@-webkit-keyframes progress-bar-stripes {
  0% {
    background-position: 1rem 0;
  }

  to {
    background-position: 0 0;
  }
}

@keyframes progress-bar-stripes {
  0% {
    background-position: 1rem 0;
  }

  to {
    background-position: 0 0;
  }
}

.progress {
  display: block;
  width: 100%;
  height: 1rem;
  margin-bottom: 1rem;
}

.progress[value] {
  background-color: #eee;
  border: 0;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: 0;
}

.progress[value]::-ms-fill {
  background-color: #0074d9;
  border: 0;
}

.progress[value]::-moz-progress-bar {
  background-color: #0074d9;
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}

.progress[value]::-webkit-progress-value {
  background-color: #0074d9;
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}

.progress[value='100']::-moz-progress-bar {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}

.progress[value='100']::-webkit-progress-value {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}

.progress[value]::-webkit-progress-bar {
  background-color: #eee;
  border-radius: 0;
}

.progress[value],
base::-moz-progress-bar {
  background-color: #eee;
  border-radius: 0;
}

@media screen and (min-width: 0\0) {
  .progress {
    background-color: #eee;
    border-radius: 0;
  }

  .progress-bar {
    display: inline-block;
    height: 1rem;
    text-indent: -999rem;
    background-color: #0074d9;
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
  }

  .progress[width='100%'] {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
  }
}

.progress-striped[value]::-webkit-progress-value {
  background-image: linear-gradient(45deg, hsla(0, 0%, 100%, 0.15) 25%, transparent 0, transparent 50%, hsla(0, 0%, 100%, 0.15) 0, hsla(0, 0%, 100%, 0.15) 75%, transparent 0, transparent);
  background-size: 1rem 1rem;
}

.progress-striped[value]::-moz-progress-bar {
  background-image: linear-gradient(45deg, hsla(0, 0%, 100%, 0.15) 25%, transparent 0, transparent 50%, hsla(0, 0%, 100%, 0.15) 0, hsla(0, 0%, 100%, 0.15) 75%, transparent 0, transparent);
  background-size: 1rem 1rem;
}

.progress-striped[value]::-ms-fill {
  background-image: linear-gradient(45deg, hsla(0, 0%, 100%, 0.15) 25%, transparent 0, transparent 50%, hsla(0, 0%, 100%, 0.15) 0, hsla(0, 0%, 100%, 0.15) 75%, transparent 0, transparent);
  background-size: 1rem 1rem;
}

@media screen and (min-width: 0\0) {
  .progress-bar-striped {
    background-image: linear-gradient(45deg, hsla(0, 0%, 100%, 0.15) 25%, transparent 0, transparent 50%, hsla(0, 0%, 100%, 0.15) 0, hsla(0, 0%, 100%, 0.15) 75%, transparent 0, transparent);
    background-size: 1rem 1rem;
  }
}

.progress-animated[value]::-webkit-progress-value {
  -webkit-animation: progress-bar-stripes 2s linear infinite;
  animation: progress-bar-stripes 2s linear infinite;
}

.progress-animated[value]::-moz-progress-bar {
  animation: progress-bar-stripes 2s linear infinite;
}

@media screen and (min-width: 0\0) {
  .progress-animated .progress-bar-striped {
    -webkit-animation: progress-bar-stripes 2s linear infinite;
    animation: progress-bar-stripes 2s linear infinite;
  }
}

.progress-success[value]::-webkit-progress-value {
  background-color: #4cbb6c;
}

.progress-success[value]::-moz-progress-bar {
  background-color: #4cbb6c;
}

.progress-success[value]::-ms-fill {
  background-color: #4cbb6c;
}

@media screen and (min-width: 0\0) {
  .progress-success .progress-bar {
    background-color: #4cbb6c;
  }
}

.progress-info[value]::-webkit-progress-value {
  background-color: #5bc0de;
}

.progress-info[value]::-moz-progress-bar {
  background-color: #5bc0de;
}

.progress-info[value]::-ms-fill {
  background-color: #5bc0de;
}

@media screen and (min-width: 0\0) {
  .progress-info .progress-bar {
    background-color: #5bc0de;
  }
}

.progress-warning[value]::-webkit-progress-value {
  background-color: #ff9a52;
}

.progress-warning[value]::-moz-progress-bar {
  background-color: #ff9a52;
}

.progress-warning[value]::-ms-fill {
  background-color: #ff9a52;
}

@media screen and (min-width: 0\0) {
  .progress-warning .progress-bar {
    background-color: #ff9a52;
  }
}

.progress-danger[value]::-webkit-progress-value {
  background-color: #ff4c4c;
}

.progress-danger[value]::-moz-progress-bar {
  background-color: #ff4c4c;
}

.progress-danger[value]::-ms-fill {
  background-color: #ff4c4c;
}

@media screen and (min-width: 0\0) {
  .progress-danger .progress-bar {
    background-color: #ff4c4c;
  }
}

.media,
.media-body {
  overflow: hidden;
}

.media-body {
  width: 10000px;
}

.media-body,
.media-left,
.media-right {
  display: table-cell;
  vertical-align: top;
}

.media-middle {
  vertical-align: middle;
}

.media-bottom {
  vertical-align: bottom;
}

.media-object {
  display: block;
}

.media-object.img-thumbnail {
  max-width: none;
}

.media-right {
  padding-left: 10px;
}

.media-left {
  padding-right: 10px;
}

.media-heading {
  margin-top: 0;
  margin-bottom: 5px;
}

.media-list {
  padding-left: 0;
  list-style: none;
}

.list-group {
  padding-left: 0;
  margin-bottom: 0;
}

.list-group-item {
  position: relative;
  display: block;
  padding: 0.75rem 1.25rem;
  margin-bottom: -1px;
  background-color: #fff;
  border: 1px solid #ddd;
}

.list-group-item:first-child {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}

.list-group-item:last-child {
  margin-bottom: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.list-group-item.disabled,
.list-group-item.disabled:focus,
.list-group-item.disabled:hover {
  color: #f1f1f1;
  cursor: not-allowed;
  background-color: #f6f6f6;
}

.list-group-item.disabled .list-group-item-heading,
.list-group-item.disabled:focus .list-group-item-heading,
.list-group-item.disabled:hover .list-group-item-heading {
  color: inherit;
}

.list-group-item.disabled .list-group-item-text,
.list-group-item.disabled:focus .list-group-item-text,
.list-group-item.disabled:hover .list-group-item-text {
  color: #f1f1f1;
}

.list-group-item.active,
.list-group-item.active:focus,
.list-group-item.active:hover {
  z-index: 2;
  color: #fff;
  text-decoration: none;
  background-color: #2fb5d2;
  border-color: #2fb5d2;
}

.list-group-item.active .list-group-item-heading,
.list-group-item.active .list-group-item-heading > .small,
.list-group-item.active .list-group-item-heading > small,
.list-group-item.active:focus .list-group-item-heading,
.list-group-item.active:focus .list-group-item-heading > .small,
.list-group-item.active:focus .list-group-item-heading > small,
.list-group-item.active:hover .list-group-item-heading,
.list-group-item.active:hover .list-group-item-heading > .small,
.list-group-item.active:hover .list-group-item-heading > small {
  color: inherit;
}

.list-group-item.active .list-group-item-text,
.list-group-item.active:focus .list-group-item-text,
.list-group-item.active:hover .list-group-item-text {
  color: #d7f1f6;
}

.list-group-flush .list-group-item {
  border-right: 0;
  border-left: 0;
  border-radius: 0;
}

.list-group-item-action {
  width: 100%;
  color: #555;
  text-align: inherit;
}

.list-group-item-action .list-group-item-heading {
  color: #333;
}

.list-group-item-action:focus,
.list-group-item-action:hover {
  color: #555;
  text-decoration: none;
  background-color: #f5f5f5;
}

.list-group-item-success {
  color: #3c763d;
  background-color: #dff0d8;
}

a.list-group-item-success,
button.list-group-item-success {
  color: #3c763d;
}

a.list-group-item-success .list-group-item-heading,
button.list-group-item-success .list-group-item-heading {
  color: inherit;
}

a.list-group-item-success:focus,
a.list-group-item-success:hover,
button.list-group-item-success:focus,
button.list-group-item-success:hover {
  color: #3c763d;
  background-color: #d0e9c6;
}

a.list-group-item-success.active,
a.list-group-item-success.active:focus,
a.list-group-item-success.active:hover,
button.list-group-item-success.active,
button.list-group-item-success.active:focus,
button.list-group-item-success.active:hover {
  color: #fff;
  background-color: #3c763d;
  border-color: #3c763d;
}

.list-group-item-info {
  color: #31708f;
  background-color: #d9edf7;
}

a.list-group-item-info,
button.list-group-item-info {
  color: #31708f;
}

a.list-group-item-info .list-group-item-heading,
button.list-group-item-info .list-group-item-heading {
  color: inherit;
}

a.list-group-item-info:focus,
a.list-group-item-info:hover,
button.list-group-item-info:focus,
button.list-group-item-info:hover {
  color: #31708f;
  background-color: #c4e3f3;
}

a.list-group-item-info.active,
a.list-group-item-info.active:focus,
a.list-group-item-info.active:hover,
button.list-group-item-info.active,
button.list-group-item-info.active:focus,
button.list-group-item-info.active:hover {
  color: #fff;
  background-color: #31708f;
  border-color: #31708f;
}

.list-group-item-warning {
  color: #8a6d3b;
  background-color: #fcf8e3;
}

a.list-group-item-warning,
button.list-group-item-warning {
  color: #8a6d3b;
}

a.list-group-item-warning .list-group-item-heading,
button.list-group-item-warning .list-group-item-heading {
  color: inherit;
}

a.list-group-item-warning:focus,
a.list-group-item-warning:hover,
button.list-group-item-warning:focus,
button.list-group-item-warning:hover {
  color: #8a6d3b;
  background-color: #faf2cc;
}

a.list-group-item-warning.active,
a.list-group-item-warning.active:focus,
a.list-group-item-warning.active:hover,
button.list-group-item-warning.active,
button.list-group-item-warning.active:focus,
button.list-group-item-warning.active:hover {
  color: #fff;
  background-color: #8a6d3b;
  border-color: #8a6d3b;
}

.list-group-item-danger {
  color: #a94442;
  background-color: #f2dede;
}

a.list-group-item-danger,
button.list-group-item-danger {
  color: #a94442;
}

a.list-group-item-danger .list-group-item-heading,
button.list-group-item-danger .list-group-item-heading {
  color: inherit;
}

a.list-group-item-danger:focus,
a.list-group-item-danger:hover,
button.list-group-item-danger:focus,
button.list-group-item-danger:hover {
  color: #a94442;
  background-color: #ebcccc;
}

a.list-group-item-danger.active,
a.list-group-item-danger.active:focus,
a.list-group-item-danger.active:hover,
button.list-group-item-danger.active,
button.list-group-item-danger.active:focus,
button.list-group-item-danger.active:hover {
  color: #fff;
  background-color: #a94442;
  border-color: #a94442;
}

.list-group-item-heading {
  margin-top: 0;
  margin-bottom: 5px;
}

.list-group-item-text {
  margin-bottom: 0;
  line-height: 1.3;
}

.embed-responsive {
  position: relative;
  display: block;
  height: 0;
  padding: 0;
  overflow: hidden;
}

.embed-responsive .embed-responsive-item,
.embed-responsive embed,
.embed-responsive iframe,
.embed-responsive object,
.embed-responsive video {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

.embed-responsive-21by9 {
  padding-bottom: 42.85714%;
}

.embed-responsive-16by9 {
  padding-bottom: 56.25%;
}

.embed-responsive-4by3 {
  padding-bottom: 75%;
}

.embed-responsive-1by1 {
  padding-bottom: 100%;
}

.close {
  float: right;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.2;
}

.close:focus,
.close:hover {
  color: #000;
  text-decoration: none;
  cursor: pointer;
  opacity: 0.5;
}

button.close {
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: 0;
  -webkit-appearance: none;
}

.modal,
.modal-open {
  overflow: hidden;
}

.modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1050;
  display: none;
  outline: 0;
}

.modal.fade .modal-dialog {
  transition: -webkit-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
  transition: transform 0.3s ease-out, -webkit-transform 0.3s ease-out;
  -webkit-transform: translateY(-25%);
  transform: translateY(-25%);
}

.modal.in .modal-dialog {
  -webkit-transform: translate(0);
  transform: translate(0);
}

.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: 10px;
}

.modal-content {
  position: relative;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
  outline: 0;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040;
  background-color: #000;
}

.modal-backdrop.fade {
  opacity: 0;
}

.modal-backdrop.in {
  opacity: 0.5;
}

.modal-header {
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
}

.modal-header:after {
  content: '';
  display: table;
  clear: both;
}

.modal-header .close {
  margin-top: -2px;
}

.modal-title {
  margin: 0;
  line-height: 1.5;
}

.modal-body {
  position: relative;
  padding: 15px;
}

.modal-footer {
  padding: 15px;
  text-align: right;
  border-top: 1px solid #e5e5e5;
}

.modal-footer:after {
  content: '';
  display: table;
  clear: both;
}

.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}

@media (min-width: 576px) {
  .modal-dialog {
    max-width: 600px;
    margin: 30px auto;
  }

  .modal-sm {
    max-width: 300px;
  }
}

@media (min-width: 992px) {
  .modal-lg {
    max-width: 900px;
  }
}

.tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  letter-spacing: normal;
  line-break: auto;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  white-space: normal;
  word-break: normal;
  word-spacing: normal;
  font-size: 0.875rem;
  word-wrap: break-word;
  opacity: 0;
}

.tooltip.in {
  opacity: 0.9;
}

.tooltip.bs-tether-element-attached-bottom,
.tooltip.tooltip-top {
  padding: 5px 0;
  margin-top: -3px;
}

.tooltip.bs-tether-element-attached-bottom .tooltip-inner:before,
.tooltip.tooltip-top .tooltip-inner:before {
  bottom: 0;
  left: 50%;
  margin-left: -5px;
  content: '';
  border-width: 5px 5px 0;
  border-top-color: #ff4c4c;
}

.tooltip.bs-tether-element-attached-left,
.tooltip.tooltip-right {
  padding: 0 5px;
  margin-left: 3px;
}

.tooltip.bs-tether-element-attached-left .tooltip-inner:before,
.tooltip.tooltip-right .tooltip-inner:before {
  top: 50%;
  left: 0;
  margin-top: -5px;
  content: '';
  border-width: 5px 5px 5px 0;
  border-right-color: #ff4c4c;
}

.tooltip.bs-tether-element-attached-top,
.tooltip.tooltip-bottom {
  padding: 5px 0;
  margin-top: 3px;
}

.tooltip.bs-tether-element-attached-top .tooltip-inner:before,
.tooltip.tooltip-bottom .tooltip-inner:before {
  top: 0;
  left: 50%;
  margin-left: -5px;
  content: '';
  border-width: 0 5px 5px;
  border-bottom-color: #ff4c4c;
}

.tooltip.bs-tether-element-attached-right,
.tooltip.tooltip-left {
  padding: 0 5px;
  margin-left: -3px;
}

.tooltip.bs-tether-element-attached-right .tooltip-inner:before,
.tooltip.tooltip-left .tooltip-inner:before {
  top: 50%;
  right: 0;
  margin-top: -5px;
  content: '';
  border-width: 5px 0 5px 5px;
  border-left-color: #ff4c4c;
}

.tooltip-inner {
  max-width: 200px;
  padding: 3px 8px;
  color: #fff;
  text-align: center;
  background-color: #ff4c4c;
  border-radius: 0;
}

.tooltip-inner:before {
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}

.popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1060;
  display: block;
  max-width: 276px;
  padding: 1px;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  letter-spacing: normal;
  line-break: auto;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  white-space: normal;
  word-break: normal;
  word-spacing: normal;
  font-size: 0.875rem;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
}

.popover.bs-tether-element-attached-bottom,
.popover.popover-top {
  margin-top: -10px;
}

.popover.bs-tether-element-attached-bottom:after,
.popover.bs-tether-element-attached-bottom:before,
.popover.popover-top:after,
.popover.popover-top:before {
  left: 50%;
  border-bottom-width: 0;
}

.popover.bs-tether-element-attached-bottom:before,
.popover.popover-top:before {
  bottom: -11px;
  margin-left: -11px;
  border-top-color: rgba(0, 0, 0, 0.25);
}

.popover.bs-tether-element-attached-bottom:after,
.popover.popover-top:after {
  bottom: -10px;
  margin-left: -10px;
  border-top-color: #fff;
}

.popover.bs-tether-element-attached-left,
.popover.popover-right {
  margin-left: 10px;
}

.popover.bs-tether-element-attached-left:after,
.popover.bs-tether-element-attached-left:before,
.popover.popover-right:after,
.popover.popover-right:before {
  top: 50%;
  border-left-width: 0;
}

.popover.bs-tether-element-attached-left:before,
.popover.popover-right:before {
  left: -11px;
  margin-top: -11px;
  border-right-color: rgba(0, 0, 0, 0.25);
}

.popover.bs-tether-element-attached-left:after,
.popover.popover-right:after {
  left: -10px;
  margin-top: -10px;
  border-right-color: #fff;
}

.popover.bs-tether-element-attached-top,
.popover.popover-bottom {
  margin-top: 10px;
}

.popover.bs-tether-element-attached-top:after,
.popover.bs-tether-element-attached-top:before,
.popover.popover-bottom:after,
.popover.popover-bottom:before {
  left: 50%;
  border-top-width: 0;
}

.popover.bs-tether-element-attached-top:before,
.popover.popover-bottom:before {
  top: -11px;
  margin-left: -11px;
  border-bottom-color: rgba(0, 0, 0, 0.25);
}

.popover.bs-tether-element-attached-top:after,
.popover.popover-bottom:after {
  top: -10px;
  margin-left: -10px;
  border-bottom-color: #f7f7f7;
}

.popover.bs-tether-element-attached-top .popover-title:before,
.popover.popover-bottom .popover-title:before {
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: 20px;
  margin-left: -10px;
  content: '';
  border-bottom: 1px solid #f7f7f7;
}

.popover.bs-tether-element-attached-right,
.popover.popover-left {
  margin-left: -10px;
}

.popover.bs-tether-element-attached-right:after,
.popover.bs-tether-element-attached-right:before,
.popover.popover-left:after,
.popover.popover-left:before {
  top: 50%;
  border-right-width: 0;
}

.popover.bs-tether-element-attached-right:before,
.popover.popover-left:before {
  right: -11px;
  margin-top: -11px;
  border-left-color: rgba(0, 0, 0, 0.25);
}

.popover.bs-tether-element-attached-right:after,
.popover.popover-left:after {
  right: -10px;
  margin-top: -10px;
  border-left-color: #fff;
}

.popover-title {
  padding: 8px 14px;
  margin: 0;
  font-size: 1rem;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-radius: 0.2375rem 0.2375rem 0 0;
}

.popover-title:empty {
  display: none;
}

.popover-content {
  padding: 9px 14px;
}

.popover:after,
.popover:before {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}

.popover:before {
  content: '';
  border-width: 11px;
}

.popover:after {
  content: '';
  border-width: 10px;
}

.carousel,
.carousel-inner {
  position: relative;
}

.carousel-inner {
  width: 100%;
  overflow: hidden;
}

.carousel-inner > .carousel-item {
  position: relative;
  display: none;
  transition: left 0.6s ease-in-out;
}

.carousel-inner > .carousel-item > a > img,
.carousel-inner > .carousel-item > img {
  line-height: 1;
}

@media (-webkit-transform-3d), (transform-3d) {
  .carousel-inner > .carousel-item {
    transition: -webkit-transform 0.6s ease-in-out;
    transition: transform 0.6s ease-in-out;
    transition: transform 0.6s ease-in-out, -webkit-transform 0.6s ease-in-out;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-perspective: 1000px;
    perspective: 1000px;
  }

  .carousel-inner > .carousel-item.active.right,
  .carousel-inner > .carousel-item.next {
    left: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }

  .carousel-inner > .carousel-item.active.left,
  .carousel-inner > .carousel-item.prev {
    left: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }

  .carousel-inner > .carousel-item.active,
  .carousel-inner > .carousel-item.next.left,
  .carousel-inner > .carousel-item.prev.right {
    left: 0;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }
}

.carousel-inner > .active,
.carousel-inner > .next,
.carousel-inner > .prev {
  display: block;
}

.carousel-inner > .active {
  left: 0;
}

.carousel-inner > .next,
.carousel-inner > .prev {
  position: absolute;
  top: 0;
  width: 100%;
}

.carousel-inner > .next {
  left: 100%;
}

.carousel-inner > .prev {
  left: -100%;
}

.carousel-inner > .next.left,
.carousel-inner > .prev.right {
  left: 0;
}

.carousel-inner > .active.left {
  left: -100%;
}

.carousel-inner > .active.right {
  left: 100%;
}

.carousel-control {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 15%;
  font-size: 20px;
  color: #fff;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
  opacity: 0.5;
}

.carousel-control.left {
  background-image: linear-gradient(90deg, rgba(0, 0, 0, 0.5) 0, rgba(0, 0, 0, 0.0001));
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#80000000", endColorstr="#00000000", GradientType=1);
}

.carousel-control.right {
  right: 0;
  left: auto;
  background-image: linear-gradient(90deg, rgba(0, 0, 0, 0.0001) 0, rgba(0, 0, 0, 0.5));
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#00000000", endColorstr="#80000000", GradientType=1);
}

.carousel-control:focus,
.carousel-control:hover {
  color: #fff;
  text-decoration: none;
  outline: 0;
  opacity: 0.9;
}

.carousel-control .icon-next,
.carousel-control .icon-prev {
  position: absolute;
  top: 50%;
  z-index: 5;
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-top: -10px;
  font-family: serif;
  line-height: 1;
}

.carousel-control .icon-prev {
  left: 50%;
  margin-left: -10px;
}

.carousel-control .icon-next {
  right: 50%;
  margin-right: -10px;
}

.carousel-control .icon-prev:before {
  content: '\2039';
}

.carousel-control .icon-next:before {
  content: '\203A';
}

.carousel-indicators {
  position: absolute;
  bottom: 10px;
  left: 50%;
  z-index: 15;
  width: 60%;
  padding-left: 0;
  margin-left: -30%;
  text-align: center;
  list-style: none;
}

.carousel-indicators li {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin: 1px;
  text-indent: -999px;
  cursor: pointer;
  background-color: transparent;
  border: 1px solid #fff;
  border-radius: 10px;
}

.carousel-indicators .active {
  width: 12px;
  height: 12px;
  margin: 0;
  background-color: #fff;
}

.carousel-caption {
  position: absolute;
  right: 15%;
  bottom: 20px;
  left: 15%;
  z-index: 10;
  padding-top: 20px;
  padding-bottom: 20px;
  color: #fff;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
}

.carousel-caption .btn {
  text-shadow: none;
}

@media (min-width: 576px) {
  .carousel-control .icon-next,
  .carousel-control .icon-prev {
    width: 30px;
    height: 30px;
    margin-top: -15px;
    font-size: 30px;
  }

  .carousel-control .icon-prev {
    margin-left: -15px;
  }

  .carousel-control .icon-next {
    margin-right: -15px;
  }

  .carousel-caption {
    right: 20%;
    left: 20%;
    padding-bottom: 30px;
  }

  .carousel-indicators {
    bottom: 20px;
  }
}

.align-baseline {
  vertical-align: baseline !important;
}

.align-top {
  vertical-align: top !important;
}

.align-middle {
  vertical-align: middle !important;
}

.align-bottom {
  vertical-align: bottom !important;
}

.align-text-bottom {
  vertical-align: text-bottom !important;
}

.align-text-top {
  vertical-align: text-top !important;
}

.bg-faded {
  background-color: #f7f7f9;
}

.bg-primary {
  background-color: #2fb5d2 !important;
}

a.bg-primary:focus,
a.bg-primary:hover {
  background-color: #2592a9 !important;
}

.bg-success {
  background-color: #4cbb6c !important;
}

a.bg-success:focus,
a.bg-success:hover {
  background-color: #3a9a56 !important;
}

.bg-info {
  background-color: #5bc0de !important;
}

a.bg-info:focus,
a.bg-info:hover {
  background-color: #31b0d5 !important;
}

.bg-warning {
  background-color: #ff9a52 !important;
}

a.bg-warning:focus,
a.bg-warning:hover {
  background-color: #ff7c1f !important;
}

.bg-danger {
  background-color: #ff4c4c !important;
}

a.bg-danger:focus,
a.bg-danger:hover {
  background-color: #ff1919 !important;
}

.bg-inverse {
  background-color: #373a3c !important;
}

a.bg-inverse:focus,
a.bg-inverse:hover {
  background-color: #1f2021 !important;
}

.rounded {
  border-radius: 0;
}

.rounded-top {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}

.rounded-right {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}

.rounded-bottom {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.rounded-left {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}

.rounded-circle {
  border-radius: 50%;
}

.clearfix:after {
  content: '';
  display: table;
  clear: both;
}

.d-block {
  display: block !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-inline {
  display: inline !important;
}

.float-xs-left {
  float: left !important;
}

.float-xs-right {
  float: right !important;
}

.float-xs-none {
  float: none !important;
}

@media (min-width: 576px) {
  .float-sm-left {
    float: left !important;
  }

  .float-sm-right {
    float: right !important;
  }

  .float-sm-none {
    float: none !important;
  }
}

@media (min-width: 768px) {
  .float-md-left {
    float: left !important;
  }

  .float-md-right {
    float: right !important;
  }

  .float-md-none {
    float: none !important;
  }
}

@media (min-width: 992px) {
  .float-lg-left {
    float: left !important;
  }

  .float-lg-right {
    float: right !important;
  }

  .float-lg-none {
    float: none !important;
  }
}

@media (min-width: 1200px) {
  .float-xl-left {
    float: left !important;
  }

  .float-xl-right {
    float: right !important;
  }

  .float-xl-none {
    float: none !important;
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.sr-only-focusable:active,
.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}

.w-100 {
  width: 100% !important;
}

.h-100 {
  height: 100% !important;
}

.mx-auto {
  margin-right: auto !important;
  margin-left: auto !important;
}

.m-0 {
  margin: 0 !important;
}

.mt-0 {
  margin-top: 0 !important;
}

.mr-0 {
  margin-right: 0 !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.ml-0,
.mx-0 {
  margin-left: 0 !important;
}

.mx-0 {
  margin-right: 0 !important;
}

.my-0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.m-1 {
  margin: 1rem !important;
}

.mt-1 {
  margin-top: 1rem !important;
}

.mr-1 {
  margin-right: 1rem !important;
}

.mb-1 {
  margin-bottom: 1rem !important;
}

.ml-1,
.mx-1 {
  margin-left: 1rem !important;
}

.mx-1 {
  margin-right: 1rem !important;
}

.my-1 {
  margin-top: 1rem !important;
  margin-bottom: 1rem !important;
}

.m-2 {
  margin: 1.5rem !important;
}

.mt-2 {
  margin-top: 1.5rem !important;
}

.mr-2 {
  margin-right: 1.5rem !important;
}

.mb-2 {
  margin-bottom: 1.5rem !important;
}

.ml-2,
.mx-2 {
  margin-left: 1.5rem !important;
}

.mx-2 {
  margin-right: 1.5rem !important;
}

.my-2 {
  margin-top: 1.5rem !important;
  margin-bottom: 1.5rem !important;
}

.m-3 {
  margin: 3rem !important;
}

.mt-3 {
  margin-top: 3rem !important;
}

.mr-3 {
  margin-right: 3rem !important;
}

.mb-3 {
  margin-bottom: 3rem !important;
}

.ml-3,
.mx-3 {
  margin-left: 3rem !important;
}

.mx-3 {
  margin-right: 3rem !important;
}

.my-3 {
  margin-top: 3rem !important;
  margin-bottom: 3rem !important;
}

.p-0 {
  padding: 0 !important;
}

.pt-0 {
  padding-top: 0 !important;
}

.pr-0 {
  padding-right: 0 !important;
}

.pb-0 {
  padding-bottom: 0 !important;
}

.pl-0,
.px-0 {
  padding-left: 0 !important;
}

.px-0 {
  padding-right: 0 !important;
}

.py-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.p-1 {
  padding: 1rem !important;
}

.pt-1 {
  padding-top: 1rem !important;
}

.pr-1 {
  padding-right: 1rem !important;
}

.pb-1 {
  padding-bottom: 1rem !important;
}

.pl-1,
.px-1 {
  padding-left: 1rem !important;
}

.px-1 {
  padding-right: 1rem !important;
}

.py-1 {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important;
}

.p-2 {
  padding: 1.5rem !important;
}

.pt-2 {
  padding-top: 1.5rem !important;
}

.pr-2 {
  padding-right: 1.5rem !important;
}

.pb-2 {
  padding-bottom: 1.5rem !important;
}

.pl-2,
.px-2 {
  padding-left: 1.5rem !important;
}

.px-2 {
  padding-right: 1.5rem !important;
}

.py-2 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important;
}

.p-3 {
  padding: 3rem !important;
}

.pt-3 {
  padding-top: 3rem !important;
}

.pr-3 {
  padding-right: 3rem !important;
}

.pb-3 {
  padding-bottom: 3rem !important;
}

.pl-3,
.px-3 {
  padding-left: 3rem !important;
}

.px-3 {
  padding-right: 3rem !important;
}

.py-3 {
  padding-top: 3rem !important;
  padding-bottom: 3rem !important;
}

.pos-f-t {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}

.text-justify {
  text-align: justify !important;
}

.text-nowrap {
  white-space: nowrap !important;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-xs-left {
  text-align: left !important;
}

.text-xs-right {
  text-align: right !important;
}

.text-xs-center {
  text-align: center !important;
}

@media (min-width: 576px) {
  .text-sm-left {
    text-align: left !important;
  }

  .text-sm-right {
    text-align: right !important;
  }

  .text-sm-center {
    text-align: center !important;
  }
}

@media (min-width: 768px) {
  .text-md-left {
    text-align: left !important;
  }

  .text-md-right {
    text-align: right !important;
  }

  .text-md-center {
    text-align: center !important;
  }
}

@media (min-width: 992px) {
  .text-lg-left {
    text-align: left !important;
  }

  .text-lg-right {
    text-align: right !important;
  }

  .text-lg-center {
    text-align: center !important;
  }
}

@media (min-width: 1200px) {
  .text-xl-left {
    text-align: left !important;
  }

  .text-xl-right {
    text-align: right !important;
  }

  .text-xl-center {
    text-align: center !important;
  }
}

.text-lowercase {
  text-transform: lowercase !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.font-weight-normal {
  font-weight: 400;
}

.font-weight-bold {
  font-weight: 700;
}

.font-italic {
  font-style: italic;
}

.text-white {
  color: #fff !important;
}

.text-muted {
  color: #7a7a7a !important;
}

a.text-muted:focus,
a.text-muted:hover {
  color: #616161 !important;
}

.text-primary {
  color: #2fb5d2 !important;
}

a.text-primary:focus,
a.text-primary:hover {
  color: #2592a9 !important;
}

.text-success {
  color: #4cbb6c !important;
}

a.text-success:focus,
a.text-success:hover {
  color: #3a9a56 !important;
}

.text-info {
  color: #5bc0de !important;
}

a.text-info:focus,
a.text-info:hover {
  color: #31b0d5 !important;
}

.text-warning {
  color: #ff9a52 !important;
}

a.text-warning:focus,
a.text-warning:hover {
  color: #ff7c1f !important;
}

.text-danger {
  color: #ff4c4c !important;
}

a.text-danger:focus,
a.text-danger:hover {
  color: #ff1919 !important;
}

.text-gray-dark {
  color: #373a3c !important;
}

a.text-gray-dark:focus,
a.text-gray-dark:hover {
  color: #1f2021 !important;
}

.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}

.invisible {
  visibility: hidden !important;
}

.hidden-xs-up {
  display: none !important;
}

@media (max-width: 575px) {
  .hidden-xs-down {
    display: none !important;
  }
}

@media (min-width: 576px) {
  .hidden-sm-up {
    display: none !important;
  }
}

@media (max-width: 767px) {
  .hidden-sm-down {
    display: none !important;
  }
}

@media (min-width: 768px) {
  .hidden-md-up {
    display: none !important;
  }
}

@media (max-width: 991px) {
  .hidden-md-down {
    display: none !important;
  }
}

@media (min-width: 992px) {
  .hidden-lg-up {
    display: none !important;
  }
}

@media (max-width: 1199px) {
  .hidden-lg-down {
    display: none !important;
  }
}

@media (min-width: 1200px) {
  .hidden-xl-up {
    display: none !important;
  }
}

.hidden-xl-down,
.visible-print-block {
  display: none !important;
}

@media print {
  .visible-print-block {
    display: block !important;
  }
}

.visible-print-inline {
  display: none !important;
}

@media print {
  .visible-print-inline {
    display: inline !important;
  }
}

.visible-print-inline-block {
  display: none !important;
}

@media print {
  .visible-print-inline-block {
    display: inline-block !important;
  }
}

@media print {
  .hidden-print {
    display: none !important;
  }
}

@font-face {
  font-family: Material Icons;
  font-style: normal;
  font-weight: 400;
  src: url(../css/e79bfd88537def476913f3ed52f4f4b3.eot);
  src: local('Material Icons'), local('MaterialIcons-Regular'), url(../css/570eb83859dc23dd0eec423a49e147fe.woff2) format('woff2'), url(../css/012cf6a10129e2275d79d6adac7f3b02.woff) format('woff'),
    url(../css/a37b0c01c0baf1888ca812cc0508f6e2.ttf) format('truetype');
}

.material-icons {
  font-family: Material Icons;
  font-weight: 400;
  font-style: normal;
  font-size: 35px;
  display: inline-block;
  vertical-align: middle;
  width: 1em;
  height: 1em;
  line-height: 1;
  text-transform: none;
  letter-spacing: normal;
  word-wrap: normal;
  white-space: nowrap;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-feature-settings: 'liga';
  font-feature-settings: 'liga';
  color: #000;
}

body,
html {
  height: 100%;
}

body {
  direction: ltr;
  font-size: 1rem;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #232323;
  line-height: 1.25em;
}

ul {
  list-style: none;
  padding-left: 0;
}

a:hover {
  color: #2fb5d2;
  text-decoration: none;
}

.color,
.custom-checkbox input[type='checkbox'] + span.color {
  width: 1.25rem;
  height: 1.25rem;
  display: inline-block;
  margin: 0.3125rem;
  box-shadow: 2px 2px 4px 0 rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(0, 0, 0, 0.05);
  cursor: pointer;
  background-size: contain;
}

.color.active,
.color:hover,
.custom-checkbox input[type='checkbox'] + span.color.active,
.custom-checkbox input[type='checkbox'] + span.color:hover,
.facet-label.active .custom-checkbox span.color,
.facet-label:hover .custom-checkbox span.color {
  border: 2px solid #232323;
}

.h1,
.h2,
.h3 {
  text-transform: uppercase;
  color: #232323;
}

.h4 {
  font-weight: 700;
  color: #232323;
}

.btn-primary,
.btn-secondary,
.btn-tertiary {
  box-shadow: 2px 2px 4px 0 rgba(0, 0, 0, 0.2);
  text-transform: uppercase;
  font-weight: 600;
  padding: 0.5rem 1.25rem;
}

.btn-primary .material-icons,
.btn-secondary .material-icons,
.btn-tertiary .material-icons {
  margin-right: 0.625rem;
}

.btn-tertiary {
  background-color: #f7f7f7;
  text-transform: lowercase;
  color: #7a7a7a;
  box-shadow: 0.0625rem 0.0625rem 0.0625rem 0 rgba(0, 0, 0, 0.1);
  padding: 0.25rem;
  margin: 0.25rem 0;
  font-weight: 400;
  font-size: 0.875rem;
}

.btn-tertiary .material-icons {
  font-size: 1rem;
}

.btn-tertiary:hover {
  box-shadow: 0.0625rem 0.0625rem 0.0625rem 0 rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(35, 35, 35, 0.2);
}

.btn-unstyle {
  background-color: transparent;
  border: none;
  padding: 0;
  text-align: inherit;
}

.btn-unstyle:focus {
  outline: 0;
}

.btn-unstyle:focus .expand-more {
  color: #2fb5d2;
}

.card {
  box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, 0.2);
}

.label,
label {
  color: #232323;
  text-align: right;
  font-size: 0.875rem;
}

small.label,
small.value {
  font-size: 0.8125rem;
}

.form-control-label {
  padding-top: 0.625rem;
}

.form-control {
  background: #f1f1f1;
  color: #7a7a7a;
  border: 1px solid rgba(0, 0, 0, 0.25);
  padding: 0.5rem 1rem;
}

.form-control:focus {
  background-color: #fff;
  color: #232323;
}

.form-control:focus,
.input-group.focus {
  outline: 0.1875rem solid #2fb5d2;
}

.input-group .form-control:focus {
  outline: none;
}

.input-group .input-group-btn {
  height: 100%;
}

.input-group .input-group-btn > .btn {
  border: 0;
  box-shadow: none;
  color: #fff;
  font-size: 0.6875rem;
  font-weight: 400;
  margin-left: 0;
  padding: 0.625rem 1rem;
  text-transform: uppercase;
}

.input-group .input-group-btn > .btn[data-action='show-password'] {
  background: #7a7a7a;
  padding: 0.78rem 1rem;
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #7a7a7a;
}

input::-moz-placeholder,
textarea::-moz-placeholder {
  color: #7a7a7a;
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: #7a7a7a;
}

input:-moz-placeholder,
textarea:-moz-placeholder {
  color: #7a7a7a;
}

input:active::-webkit-input-placeholder,
textarea:active::-webkit-input-placeholder {
  color: #232323;
}

input:active::-moz-placeholder,
textarea:active::-moz-placeholder {
  color: #232323;
}

input:active:-ms-input-placeholder,
textarea:active:-ms-input-placeholder {
  color: #232323;
}

input:active:-moz-placeholder,
textarea:active:-moz-placeholder {
  color: #232323;
}

.form-control-select {
  height: 2.625rem;
  -moz-appearance: none;
  -webkit-appearance: none;
  background: #f1f1f1
    url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAQAAAD9CzEMAAAAPklEQVR4Ae3TwREAEBQD0V/6do4SXPZg7EsBhsQ8IEmSMOsiuEfg3gL3oXC7wK0bd1G4o8X9F4yIkyQfSrIByQBjp7QuND8AAAAASUVORK5CYII=)
    no-repeat scroll right 0.5rem center/1.25rem 1.25rem;
  padding: 0 0.5rem;
}

.form-control-valign {
  padding-top: 0.5rem;
}

.form-control-comment {
  font-size: 0.875rem;
  padding-top: 0.5rem;
  color: #7a7a7a;
}

.form-control-submit.disabled {
  background: #5bc0de;
  color: #fff;
}

.form-group.has-error input,
.form-group.has-error select {
  outline: 0.1875rem solid #ff4c4c;
}

.form-group.has-error .help-block {
  color: #ff4c4c;
}

.group-span-filestyle label {
  margin: 0;
}

.bootstrap-touchspin .group-span-filestyle .btn-touchspin,
.group-span-filestyle .bootstrap-touchspin .btn-touchspin,
.group-span-filestyle .btn-default {
  background: #2fb5d2;
  color: #fff;
  text-transform: uppercase;
  border-radius: 0;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
}

.bootstrap-touchspin {
  width: auto;
  float: left;
  display: inline-block;
  box-shadow: 2px 2px 4px 0 rgba(0, 0, 0, 0.2);
}

.bootstrap-touchspin input:focus {
  outline: none;
}

.bootstrap-touchspin input.form-control,
.bootstrap-touchspin input.input-group {
  color: #232323;
  background-color: #fff;
  height: 2.5rem;
  padding: 0.175rem 0.5rem;
  width: 3rem;
  border: 1px solid rgba(0, 0, 0, 0.25);
  float: left;
}

.bootstrap-touchspin .btn-touchspin {
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.25);
  height: 1.3125rem;
}

.bootstrap-touchspin .btn-touchspin:hover {
  background-color: #f1f1f1;
}

.bootstrap-touchspin .input-group-btn-vertical {
  color: #232323;
  width: auto;
  float: left;
}

.bootstrap-touchspin .input-group-btn-vertical .bootstrap-touchspin-down,
.bootstrap-touchspin .input-group-btn-vertical .bootstrap-touchspin-up {
  border-radius: 0;
}

.bootstrap-touchspin .input-group-btn-vertical .touchspin-up:after {
  content: '\E5CE';
}

.bootstrap-touchspin .input-group-btn-vertical .touchspin-down:after {
  content: '\E5CF';
}

.bootstrap-touchspin .input-group-btn-vertical i {
  top: 0.0625rem;
  left: 0.1875rem;
  font-size: 0.9375rem;
}

.custom-radio {
  display: inline-block;
  position: relative;
  width: 20px;
  height: 20px;
  vertical-align: middle;
  cursor: pointer;
  border-radius: 50%;
  border: 2px solid #7a7a7a;
  background: #fff;
  margin-right: 1.25rem;
}

.custom-radio input[type='radio'] {
  opacity: 0;
  cursor: pointer;
}

.custom-radio input[type='radio']:checked + span {
  display: block;
  background-color: #2fb5d2;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  position: absolute;
  left: 0.125rem;
  top: 0.125rem;
}

.custom-radio input[type='radio']:focus + span {
  border-color: #7a7a7a;
}

.custom-checkbox {
  position: relative;
}

.custom-checkbox input[type='checkbox'] {
  margin-top: 0.25rem;
  opacity: 0;
  cursor: pointer;
  position: absolute;
}

.custom-checkbox input[type='checkbox'] + span {
  margin-right: 3px;
  display: inline-block;
  width: 0.9375rem;
  height: 0.9375rem;
  vertical-align: middle;
  cursor: pointer;
  border: 2px solid #232323;
}

.custom-checkbox input[type='checkbox'] + span .checkbox-checked {
  display: none;
  margin: -0.25rem -0.125rem;
  font-size: 1.1rem;
  color: #232323;
}

.custom-checkbox input[type='checkbox']:checked + span .checkbox-checked {
  display: block;
}

.custom-checkbox input[type='checkbox']:focus + span {
  border-color: #7a7a7a;
}

.custom-checkbox label {
  text-align: left;
}

.text-muted {
  font-size: 0.875rem;
}

.done {
  color: #4cbb6c;
  display: inline-block;
  padding: 0 0.8125rem;
  margin-right: 1.563rem;
}

.thumb-mask > .mask {
  position: relative;
  width: 3.438rem;
  height: 3.438rem;
  overflow: hidden;
  border: 1px solid #f1f1f1;
  margin: 0.625rem 0;
}

.thumb-mask > .mask img {
  width: 55px;
  height: 55px;
}

.definition-list dl {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.definition-list dl dt {
  font-weight: 400;
}

.definition-list dl dd,
.definition-list dl dt {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 45%;
  flex: 0 0 45%;
  background: #f1f1f1;
  padding: 0.625rem;
  margin: 0.125rem;
}

.definition-list dl dd:nth-of-type(2n),
.definition-list dl dt:nth-of-type(2n) {
  background: #f6f6f6;
}

.help-block {
  margin-top: 0.625rem;
}

.btn.disabled,
.btn.disabled:hover {
  background: #7a7a7a;
}

.alert-warning .material-icons {
  color: #ff9a52;
  font-size: 2rem;
  margin-right: 0.625rem;
  padding-top: 0.3125rem;
}

.alert-warning .alert-text {
  font-size: 0.9375rem;
  padding-top: 0.625rem;
}

.alert-warning .alert-link {
  border-radius: 2px;
  border-width: 2px;
  margin-left: 0.625rem;
  padding: 0.3125rem 1.25rem;
  font-weight: 600;
  font-size: 0.8125rem;
  color: #6c868e;
}

.alert-warning ul li:last-child .alert-link {
  color: #fff;
}

.alert-warning .warning-buttons {
  margin-top: 0.3125rem;
}

.btn-tertiary-outline {
  color: #6c868e;
  background-image: none;
  background-color: transparent;
  border-color: #6c868e;
  border: 0.15rem solid #6c868e;
}

.btn-tertiary-outline:hover {
  border-color: #bbcdd2;
  color: #bbcdd2;
}

.alert {
  font-size: 0.8125rem;
}

.nav-item .nav-link,
.nav-item .nav-separtor {
  color: #7a7a7a;
  font-weight: 700;
}

.nav-item .nav-link.active,
.nav-item .nav-separtor.active {
  color: #232323;
}

.separator {
  margin: 0;
  border-color: rgba(0, 0, 0, 0.25);
}

.ps-alert-error {
  margin-bottom: 0;
}

.ps-alert-error .item,
.ps-alert-success .item {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: 2px solid #ff4c4c;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  background-color: #ff4c4c;
  margin-bottom: 1rem;
}

.ps-alert-error .item i,
.ps-alert-success .item i {
  border: 15px solid #ff4c4c;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.ps-alert-error .item i svg,
.ps-alert-success .item i svg {
  background-color: #ff4c4c;
  width: 24px;
  height: 24px;
}

.ps-alert-error .item p,
.ps-alert-success .item p {
  background-color: #fff;
  margin: 0;
  padding: 18px 20px;
  width: 100%;
}

.ps-alert-success {
  padding: 0.25rem 0.25rem 2.75rem;
}

.ps-alert-success .item {
  border-color: #4cbb6c;
  background-color: #4cbb6c;
}

.ps-alert-success .item i {
  border-color: #4cbb6c;
}

.ps-alert-success .item i svg {
  background-color: #4cbb6c;
}

.dropdown {
  color: #7a7a7a;
}

.dropdown:hover .expand-more {
  color: #2fb5d2;
}

.dropdown .expand-more {
  color: #232323;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.dropdown .active {
  max-height: 200px;
  overflow-y: hidden;
  visibility: visible;
}

.dropdown select {
  -moz-appearance: none;
  border: 0 none;
  outline: 0 none;
  color: #232323;
  background: #fff;
}

.dropdown-item:focus,
.dropdown-item:hover {
  background: none;
  text-decoration: none;
  color: #2fb5d2;
}

.search-widget {
  display: inline-block;
}

.search-widget form {
  position: relative;
}

.search-widget form input[type='text'] {
  border: none;
  padding: 10px;
  min-width: 255px;
  color: #7a7a7a;
  border: 1px solid rgba(0, 0, 0, 0.25);
}

.search-widget form input[type='text']:focus {
  outline: 3px solid #2fb5d2;
  color: #232323;
  background: #fff;
}

.search-widget form input[type='text']:focus + button .search {
  color: #2fb5d2;
}

.search-widget form button[type='submit'] {
  position: absolute;
  background: none;
  border: none;
  bottom: 0.3125rem;
  right: 0.125rem;
  color: #7a7a7a;
}

.search-widget form button[type='submit'] .search:hover {
  color: #2fb5d2;
}

.header-top .search-widget form input[type='text'] {
  min-width: inherit;
  width: 100%;
}

#checkout #search_widget {
  display: none;
}

#pagenotfound .page-content #search_widget {
  width: 100%;
}

.page-not-found .search-widget form {
  display: inline-block;
}

@media (max-width: 767px) {
  .header-top .search-widget {
    float: none;
  }

  .header-top .search-widget form {
    margin: 0 auto;
  }

  .header-top .search-widget form input[type='text'] {
    min-width: inherit;
    background: #fff;
  }
}

@media (min-width: 768px) {
  .search-widget {
    min-width: 15.63rem;
  }
}

.top-menu[data-depth='1'] {
  margin: 0.625rem;
}

.top-menu a:not([data-depth='0']) {
  display: block;
  padding: 0.625rem;
  color: #7a7a7a;
  font-weight: 400;
}

.top-menu a.dropdown-submenu {
  color: #232323;
  text-transform: uppercase;
  font-weight: 600;
}

.top-menu a[data-depth='0'] {
  font-weight: 600;
  padding: 0.1875rem 0.625rem 0.375rem;
}

.top-menu a[data-depth='1'],
.top-menu a[data-depth='2'] {
  padding: 0 0.625rem 0.625rem 0;
}

.top-menu .collapse {
  display: inherit;
}

.top-menu .sub-menu {
  border: none;
  z-index: 18;
}

.top-menu .sub-menu.collapse {
  display: none;
}

.top-menu .sub-menu ul[data-depth='1'] > li {
  float: left;
  margin: 0 4px;
}

.top-menu .sub-menu a:hover {
  color: #2fb5d2;
}

.top-menu .popover {
  max-width: inherit;
  border-radius: 0;
}

.popover.bs-tether-element-attached-top {
  margin-top: 0;
}

#_desktop_top_menu .top-menu[data-depth='0'] li:hover .sub-menu {
  display: block !important;
  top: 37px !important;
}

#mobile_top_menu_wrapper {
  margin: 0.625rem 0 0;
  padding-bottom: 0.625rem;
  background: #fff;
}

#mobile_top_menu_wrapper #top-menu {
  margin-bottom: 0.625rem;
}

#mobile_top_menu_wrapper .top-menu {
  color: #232323;
}

#mobile_top_menu_wrapper .top-menu .collapse-icons[aria-expanded='true'] .add {
  display: none;
}

#mobile_top_menu_wrapper .top-menu .collapse-icons[aria-expanded='true'] .remove {
  display: block;
}

#mobile_top_menu_wrapper .top-menu .collapse-icons .remove {
  display: none;
}

#mobile_top_menu_wrapper .top-menu .navbar-toggler {
  display: inline-block;
  padding: 0;
}

#mobile_top_menu_wrapper .top-menu a[data-depth='0'] {
  padding: 0.625rem;
  border-bottom: 1px solid #f6f6f6;
}

#mobile_top_menu_wrapper .top-menu .collapse {
  display: none;
}

#mobile_top_menu_wrapper .top-menu .collapse.in {
  display: block;
}

#mobile_top_menu_wrapper .top-menu .sub-menu {
  box-shadow: none;
  z-index: inherit;
  display: block;
  position: static;
  overflow: hidden;
  margin-left: 0;
  width: 100%;
  min-width: 100%;
  background: #f6f6f6;
}

#mobile_top_menu_wrapper .top-menu .sub-menu.collapse {
  display: none;
}

#mobile_top_menu_wrapper .top-menu .sub-menu.collapse.in {
  display: block;
}

#mobile_top_menu_wrapper .top-menu .sub-menu ul[data-depth='0'] > li {
  border-bottom: 1px solid #7a7a7a;
}

#mobile_top_menu_wrapper .top-menu .sub-menu ul[data-depth='1'] {
  margin: 0;
}

#mobile_top_menu_wrapper .top-menu .sub-menu ul[data-depth='1'] > li {
  float: none;
  margin: 0;
}

#mobile_top_menu_wrapper .top-menu .sub-menu ul[data-depth='1'] > li a {
  text-transform: none;
}

#mobile_top_menu_wrapper .top-menu .sub-menu ul {
  padding: 0;
}

#mobile_top_menu_wrapper .top-menu .sub-menu li > a {
  padding: 0.625rem;
  border-bottom: 1px solid #fff;
  font-weight: 700;
}

#mobile_top_menu_wrapper .top-menu .sub-menu ul[data-depth='2'] li a {
  padding-left: 1.25rem;
}

#mobile_top_menu_wrapper .top-menu .sub-menu ul[data-depth='3'] li a {
  padding-left: 2.5rem;
}

#mobile_top_menu_wrapper .top-menu .sub-menu ul[data-depth='4'] li a {
  padding-left: 3.75rem;
}

#mobile_top_menu_wrapper .js-top-menu-bottom a {
  color: #7a7a7a;
}

#mobile_top_menu_wrapper .js-top-menu-bottom .language-selector-wrapper {
  padding: 0.625rem;
}

#mobile_top_menu_wrapper .js-top-menu-bottom .language-selector-wrapper .language-selector {
  display: inline;
}

#mobile_top_menu_wrapper .js-top-menu-bottom #contact-link,
#mobile_top_menu_wrapper .js-top-menu-bottom .currency-selector {
  padding: 0.625rem;
}

#mobile_top_menu_wrapper .js-top-menu-bottom .user-info {
  padding: 0 0.625rem;
}

#mobile_top_menu_wrapper .js-top-menu-bottom .user-info a {
  padding: 0.625rem 0;
  display: block;
  width: 100%;
}

body#checkout {
  color: #232323;
}

body#checkout #header .header-nav {
  max-height: none;
  padding: 5rem 0 0.9375rem;
  border: none;
  margin-bottom: 0;
  box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, 0.2);
}

body#checkout #header .header-nav .logo {
  max-height: 4.375rem;
  width: auto;
}

body#checkout .custom-checkbox {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

body#checkout .custom-checkbox span {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 15px;
  flex: 0 0 15px;
  margin-top: 0.1875rem;
}

body#checkout a:hover {
  color: #1a8196;
}

body#checkout section#content {
  margin-bottom: 1.563rem;
}

body#checkout .container {
  min-height: 100%;
}

body#checkout section.checkout-step {
  box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, 0.2);
  background-color: #fff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.25);
  padding: 0.9375rem;
}

body#checkout section.checkout-step:last-child {
  border: 0;
}

body#checkout section.checkout-step .step-title {
  text-transform: uppercase;
  cursor: pointer;
  margin-bottom: 0;
}

body#checkout section.checkout-step .content {
  padding: 0 2.313rem;
}

body#checkout section.checkout-step .step-edit {
  text-transform: lowercase;
  font-weight: 400;
}

body#checkout section.checkout-step .step-edit .edit {
  font-size: 1rem;
}

body#checkout section.checkout-step .not-allowed {
  cursor: not-allowed;
  opacity: 0.5;
}

body#checkout section.checkout-step .content,
body#checkout section.checkout-step .done,
body#checkout section.checkout-step .step-edit {
  display: none;
}

body#checkout section.checkout-step.-current .content {
  display: block;
}

body#checkout section.checkout-step.-current.-reachable.-complete .done,
body#checkout section.checkout-step.-current.-reachable.-complete .step-edit {
  display: none;
}

body#checkout section.checkout-step.-current.-reachable.-complete .step-number {
  display: inline-block;
}

body#checkout section.checkout-step.-current.-reachable.-complete .content {
  display: block;
}

body#checkout section.checkout-step.-reachable.-complete h1 .done {
  display: inline-block;
}

body#checkout section.checkout-step.-reachable.-complete h1 .step-number {
  display: none;
}

body#checkout section.checkout-step.-reachable.-complete h1 .step-edit {
  cursor: pointer;
  display: block;
  float: right;
  margin-right: 0.125rem;
  color: #7a7a7a;
}

body#checkout section.checkout-step.-reachable.-complete .content {
  display: none;
}

body#checkout section.checkout-step small {
  color: #7a7a7a;
}

body#checkout section.checkout-step .default-input {
  min-width: 40%;
}

body#checkout section.checkout-step .default-input[name='address1'],
body#checkout section.checkout-step .default-input[name='address2'] {
  min-width: 60%;
}

body#checkout section.checkout-step .radio-field {
  margin-top: 1.875rem;
}

body#checkout section.checkout-step .radio-field label {
  display: inline;
}

body#checkout section.checkout-step .checkbox-field div {
  margin-top: 3.75rem;
}

body#checkout section.checkout-step .checkbox-field + .checkbox-field div {
  margin-top: 0;
}

body#checkout section.checkout-step .select-field div {
  background: #f6f6f6;
  padding: 0.625rem 3.125rem;
}

body#checkout section.checkout-step .form-footer {
  text-align: center;
}

body#checkout section.checkout-step #conditions-to-approve {
  padding-top: 1rem;
}

body#checkout section.checkout-step .payment-options label {
  display: table-cell;
}

body#checkout section.checkout-step .payment-options .custom-radio {
  margin-right: 1.25rem;
}

body#checkout section.checkout-step .payment-options .payment-option {
  margin-bottom: 0.5rem;
}

body#checkout section.checkout-step .step-number {
  display: inline-block;
  padding: 0.625rem;
}

body#checkout section.checkout-step .address-selector {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

body#checkout section.checkout-step .address-item {
  background: #f6f6f6;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 49%;
  flex: 0 0 49%;
  margin-bottom: 0.625rem;
  border: 3px solid transparent;
}

body#checkout section.checkout-step .address-item.selected {
  background: #fff;
  border: 3px solid #2fb5d2;
}

body#checkout section.checkout-step .address-alias {
  display: inline-block;
  font-weight: 600;
  margin-bottom: 0.625rem;
}

body#checkout section.checkout-step .address {
  margin-left: 1.563rem;
  font-weight: 400;
}

body#checkout section.checkout-step .radio-block {
  padding: 0.9375rem;
  text-align: left;
  cursor: pointer;
}

body#checkout section.checkout-step .custom-radio {
  margin-right: 0;
}

body#checkout section.checkout-step .custom-radio input[type='radio'] {
  height: 1.25rem;
  width: 1.25rem;
}

body#checkout section.checkout-step .delete-address,
body#checkout section.checkout-step .edit-address {
  color: #7a7a7a;
  display: inline-block;
  margin: 0 0.3125rem;
}

body#checkout section.checkout-step .delete-address .delete,
body#checkout section.checkout-step .delete-address .edit,
body#checkout section.checkout-step .edit-address .delete,
body#checkout section.checkout-step .edit-address .edit {
  font-size: 1rem;
}

body#checkout section.checkout-step hr {
  margin: 0;
}

body#checkout section.checkout-step .address-footer {
  text-align: center;
  padding: 0.625rem;
}

body#checkout section.checkout-step #delivery-addresses,
body#checkout section.checkout-step #invoice-addresses,
body#checkout section.checkout-step .add-address {
  margin-top: 1.25rem;
}

body#checkout section.checkout-step .add-address a {
  color: #232323;
}

body#checkout section.checkout-step .add-address a i {
  font-size: 0.9375rem;
}

body#checkout section.checkout-step .delivery-option {
  background: #f6f6f6;
  padding: 0.9375rem 0;
  margin-bottom: 0.9375rem;
}

body#checkout section.checkout-step .delivery-option label {
  text-align: inherit;
}

body#checkout section.checkout-step .carrier-delay,
body#checkout section.checkout-step .carrier-name {
  display: inline-block;
  word-break: break-word;
  text-align: left;
}

body#checkout section.checkout-step #customer-form,
body#checkout section.checkout-step #delivery-address,
body#checkout section.checkout-step #invoice-address,
body#checkout section.checkout-step #login-form {
  margin-left: 0.3125rem;
  margin-top: 1.563rem;
}

body#checkout section.checkout-step #customer-form .form-control-label,
body#checkout section.checkout-step #delivery-address .form-control-label,
body#checkout section.checkout-step #invoice-address .form-control-label,
body#checkout section.checkout-step #login-form .form-control-label {
  text-align: left;
}

body#checkout section.checkout-step #customer-form .radio-inline,
body#checkout section.checkout-step #delivery-address .radio-inline,
body#checkout section.checkout-step #invoice-address .radio-inline,
body#checkout section.checkout-step #login-form .radio-inline {
  padding: 0;
}

body#checkout section.checkout-step .sign-in {
  font-size: 0.875rem;
}

body#checkout section.checkout-step .forgot-password {
  margin-left: 14.38rem;
}

body#checkout .additional-information {
  font-size: 0.875rem;
  margin-left: 2.875rem;
  margin-top: 1.25rem;
}

body#checkout .condition-label {
  margin-left: 2.5rem;
  margin-top: 0.625rem;
}

body#checkout .condition-label label {
  text-align: inherit;
}

body#checkout .cancel-address {
  margin: 0.625rem;
  display: block;
  color: #7a7a7a;
  text-decoration: underline;
}

body#checkout .modal-content {
  padding: 1.25rem;
  background-color: #f1f1f1;
}

body#checkout #cart-summary-product-list {
  font-size: 0.875rem;
}

body#checkout #cart-summary-product-list img {
  border: 1px solid #f1f1f1;
  width: 3.125rem;
}

body#checkout #cart-summary-product-list .media-body {
  vertical-align: middle;
}

body#checkout #order-summary-content {
  padding-top: 0.9375rem;
}

body#checkout #order-summary-content h4.h4 {
  margin-top: 0.625rem;
  margin-bottom: 1.25rem;
  color: #232323;
}

body#checkout #order-summary-content h4.black {
  color: #000;
}

body#checkout #order-summary-content h4.addresshead {
  margin-top: 0.1875rem;
}

body#checkout #order-summary-content .noshadow {
  box-shadow: none;
}

body#checkout #order-summary-content #order-items {
  border-right: 0;
}

body#checkout #order-summary-content #order-items h3.h3 {
  color: #232323;
  margin-top: 1.25rem;
}

body#checkout #order-summary-content #order-items table tr:first-child td {
  border-top: 0;
}

body#checkout #order-summary-content .order-confirmation-table {
  padding: 1rem;
  margin-bottom: 2rem;
  background-color: #fff;
  border: 3px solid #e5e5e5;
  border-radius: 0;
}

body#checkout #order-summary-content .summary-selected-carrier {
  margin-bottom: 0.75rem;
  background-color: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 0;
  padding: 1rem;
}

body#checkout #order-summary-content .step-edit {
  display: inline;
  color: #7a7a7a;
}

body#checkout #order-summary-content .step-edit:hover {
  cursor: pointer;
}

body#checkout #order-summary-content a .step-edit {
  color: #7a7a7a;
}

body#checkout #delivery,
body#checkout #gift_message {
  max-width: 100%;
  border-color: #232323;
}

body#checkout #delivery textarea,
body#checkout #gift_message textarea {
  max-width: 100%;
  margin-bottom: 10px;
}

body#checkout #footer {
  box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, 0.2);
  padding: 0.9375rem;
  background: #fff;
  color: #7a7a7a;
}

#order-details {
  padding-left: 1.875rem;
}

#order-details > .card-title {
  margin-bottom: 1.875rem;
}

#order-details ul {
  margin-bottom: 1.25rem;
}

#order-details ul li {
  margin-bottom: 0.625rem;
}

#order-items {
  border-right: 1px solid #f1f1f1;
}

#order-items hr {
  border-top-color: #232323;
}

#order-items table {
  width: 100%;
}

#order-items table tr {
  height: 1.875rem;
}

#order-items table tr td:last-child {
  text-align: right;
}

#order-items .order-line {
  margin-top: 1rem;
}

#order-items .image img {
  width: 100%;
  border: 1px solid gray-lighter;
  margin-bottom: 1rem;
}

#order-items .details {
  margin-bottom: 1rem;
}

#order-items .details .customizations {
  margin-top: 0.625rem;
}

#order-items .qty {
  margin-bottom: 1rem;
}

#order-confirmation #registration-form {
  width: 50%;
  margin: 0 auto 1rem;
}

@media (max-width: 991px) {
  .done {
    margin: 0;
    padding: 0;
  }

  body#checkout section.checkout-step .address-item {
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
  }

  body#checkout section.checkout-step .delivery-option-2 {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
  }

  .delivery-option {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin: auto;
  }

  .delivery-option .custom-radio {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
  }

  .condition-label label[for='conditions_to_approve[terms-and-conditions]'] {
    text-align: left;
  }

  #order-confirmation #registration-form {
    width: 100%;
  }
}

@media (max-width: 767px) {
  body#checkout section.checkout-step.-reachable.-complete h1 .step-edit {
    float: none;
    margin-top: 0.25rem;
    margin-left: 1.25rem;
  }

  body#checkout #header .header-nav {
    max-height: none;
    padding: 0;
  }

  body#checkout section.checkout-step .content {
    padding: 0.9375rem;
  }

  body#checkout .form-group {
    margin-bottom: 0.5rem;
  }

  #order-items {
    border-right: 0;
    margin-bottom: 2.5rem;
  }

  #order-items .card-title {
    border-bottom: 1px solid #f1f1f1;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
  }

  #order-items hr {
    border-top-color: #f1f1f1;
  }

  .bold {
    font-weight: 700;
  }

  #order-details {
    padding-left: 0.9375rem;
  }

  #order-details .card-title {
    border-bottom: 1px solid #f1f1f1;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
  }
}

@media (max-width: 575px) {
  body#checkout section.checkout-step .content {
    padding: 0.9375rem 0;
  }

  #payment-confirmation button {
    font-size: 0.875rem;
  }

  #payment-confirmation button.btn {
    white-space: normal;
  }
}

.js-payment-binary,
.js-payment-binary .accept-cgv {
  display: none;
}

.js-payment-binary.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.js-payment-binary.disabled:before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  pointer-events: none;
}

.js-payment-binary.disabled .accept-cgv {
  display: block;
}

.table-labeled td,
.table-labeled th {
  vertical-align: middle;
}

.table-labeled .label {
  font-weight: 400;
  border-radius: 3px;
  font-size: inherit;
  padding: 0.25rem 0.375rem;
  margin: 0.125rem;
  color: #fff;
  white-space: nowrap;
}

.page-order .table {
  margin-bottom: 0;
}

.page-order table td,
.page-order table th {
  padding: 0.5rem;
}

.page-order table thead th {
  text-align: center;
}

#authentication .tooltip.tooltip-bottom {
  padding: 0;
  margin: 0;
}

#authentication .custom-checkbox {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

#authentication .custom-checkbox span {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 0.9375rem;
  flex: 0 0 0.9375rem;
}

#authentication .custom-checkbox label {
  padding-left: 0.625rem;
}

#authentication .radio-inline,
#identity .radio-inline {
  padding: 0;
}

#authentication .radio-inline .custom-radio,
#identity .radio-inline .custom-radio {
  margin-right: 0;
}

.page-customer-account #content {
  box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, 0.2);
  background: #fff;
  padding: 1rem;
  font-size: 0.875rem;
  color: #7a7a7a;
}

.page-customer-account #content .order-actions a {
  padding: 0 0.125rem;
}

.page-customer-account #content .forgot-password {
  text-align: center;
  font-size: 0.875rem;
  margin-top: 1rem;
  padding-bottom: 0.9375rem;
}

.page-customer-account #content .no-account {
  text-align: center;
  font-size: 1rem;
}

.page-authentication #content {
  padding: 1rem;
  max-width: 640px;
  margin: 0 auto;
}

.page-addresses .address,
.page-authentication #content {
  box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, 0.2);
  background: #fff;
}

.page-addresses .address {
  margin-bottom: 1.875rem;
  font-size: 0.875rem;
  color: #232323;
}

.page-addresses .address .address-body {
  padding: 1rem;
}

.page-addresses .address .address-body h4 {
  font-size: 1rem;
  font-weight: 700;
}

.page-addresses .address .address-body address {
  min-height: 9rem;
}

.page-addresses .address .address-footer {
  border-top: 1px solid #7a7a7a;
  padding: 0.5rem 1rem;
}

.page-addresses .address .address-footer a {
  color: #7a7a7a;
  margin-right: 0.5rem;
}

.page-addresses .address .address-footer a:hover {
  color: #232323;
}

.page-addresses .address .address-footer a i {
  font-size: 1rem;
}

.page-addresses .address .address-footer a span {
  font-size: 0.9375rem;
  vertical-align: middle;
}

.page-addresses .addresses-footer {
  margin: 0 0.9375rem;
}

.page-addresses .addresses-footer a,
.page-addresses .addresses-footer a:hover {
  color: #232323;
}

.page-addresses .addresses-footer a i {
  font-size: 1rem;
}

.page-addresses .addresses-footer a span {
  font-size: 1rem;
  vertical-align: middle;
  margin-top: 0.625rem;
}

.page-order-detail {
  font-size: 0.875rem;
  color: #7a7a7a;
}

.page-order-detail .box {
  box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, 0.2);
  background: #fff;
  padding: 1rem;
  margin-bottom: 1rem;
}

.page-order-detail h3 {
  font-size: 1rem;
  font-weight: 700;
  text-transform: uppercase;
  color: #232323;
  margin-bottom: 1rem;
}

.page-order-detail #order-infos ul {
  margin: 0;
}

.page-order-detail #order-history .history-lines .history-line {
  padding: 0.5rem 0;
  border-bottom: 1px solid #f6f6f6;
}

.page-order-detail #order-history .history-lines .history-line:last-child {
  border-bottom: 0;
}

.page-order-detail #order-history .history-lines .history-line .label {
  display: inline-block;
  margin: 0.25rem 0;
  padding: 0.25rem 0.375rem;
  color: #fff;
  border-radius: 3px;
}

.page-order-detail .addresses {
  margin: 0 -0.9375rem;
}

.page-order-detail .addresses h4 {
  font-size: 1rem;
  font-weight: 700;
}

.page-order-detail #order-products.return {
  margin-bottom: 1rem;
}

.page-order-detail #order-products.return th.head-checkbox {
  width: 30px;
}

.page-order-detail #order-products.return td {
  padding: 1.375rem 0.75rem;
}

.page-order-detail #order-products.return td.qty {
  min-width: 125px;
}

.page-order-detail #order-products.return td.qty .current {
  width: 30%;
  float: left;
  text-align: right;
  padding-right: 0.5rem;
}

.page-order-detail #order-products.return td.qty .select {
  width: 70%;
  float: left;
  margin: -0.625rem 0;
  padding-left: 0.25rem;
}

.page-order-detail #order-products.return td.qty .select select {
  text-align: center;
}

.page-order-detail .order-items {
  padding: 0 !important;
}

.page-order-detail .order-items .order-item {
  padding: 1rem 1rem 0;
  border-bottom: 1px solid #f6f6f6;
}

.page-order-detail .order-items .order-item .checkbox {
  width: 30px;
  float: left;
  padding: 0 0.9375rem;
}

.page-order-detail .order-items .order-item .content {
  width: calc(100% - 30px);
  float: left;
  padding: 0 0.9375rem;
}

.page-order-detail .order-items .order-item .desc {
  margin-bottom: 1rem;
}

.page-order-detail .order-items .order-item .desc .name {
  font-weight: 700;
}

.page-order-detail .order-items .order-item .qty {
  margin-bottom: 1rem;
}

.page-order-detail .order-items .order-item .qty .q,
.page-order-detail .order-items .order-item .qty .s {
  margin-bottom: 0.25rem;
}

.page-order-detail .messages .message {
  margin-top: 0.5rem;
  border-bottom: 1px solid #f6f6f6;
}

.page-order-detail .messages .message:last-child {
  border-bottom: 0;
}

.page-order-detail .messages .message > div {
  margin-bottom: 0.5rem;
}

.page-order-detail .customization {
  margin-top: 0.75rem;
}

#order-return-infos .thead-default th {
  color: #232323;
}

#order-return-infos .customization {
  margin-top: 0.75rem;
}

.page-my-account #content .links {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.page-my-account #content .links a {
  text-align: center;
  display: inline-block;
  font-size: 1rem;
  font-weight: 700;
  text-transform: uppercase;
  color: #7a7a7a;
  padding: 0 0.9375rem;
  margin-bottom: 1.875rem;
}

.page-my-account #content .links a span.link-item {
  display: block;
  height: 100%;
  box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, 0.2);
  background: #fff;
  padding: 1rem;
}

.page-my-account #content .links a i {
  display: block;
  font-size: 2.6rem;
  width: 100%;
  color: #232323;
  padding-bottom: 3.4rem;
}

.page-my-account #content .links a:hover {
  color: #232323;
}

.page-my-account #content .links a:hover i {
  color: #2fb5d2;
}

#history .orders {
  margin: 0 -1rem;
}

#history .orders .order {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f6f6f6;
}

#history .orders .order a h3 {
  color: #7a7a7a;
}

#history .orders .order .label {
  display: inline-block;
  margin: 0.25rem 0;
  padding: 0.25rem 0.375rem;
  color: #fff;
  border-radius: 3px;
}

#history .orders .order:last-child {
  border-bottom: 0;
}

.page-footer .account-link {
  margin-right: 1rem;
}

.page-footer .account-link i {
  font-size: 1rem;
}

.page-footer .account-link span {
  font-size: 0.875rem;
  vertical-align: middle;
}

.login-form {
  margin-top: 15px;
}

.forgotten-password {
  padding: 4px;
}

.forgotten-password .form-fields .center-email-fields {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

@media (max-width: 767px) {
  .forgotten-password .form-fields .center-email-fields {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
  }

  .forgotten-password .form-fields .center-email-fields button {
    margin: 10px;
    width: calc(100% - 20px);
  }
}

.forgotten-password .form-fields .center-email-fields button {
  height: 38px;
}

.forgotten-password .form-fields .email {
  padding-left: 0;
  padding-right: 0;
  width: 430px;
}

@media (max-width: 767px) {
  .forgotten-password .form-fields .email {
    padding-left: 10px;
    padding-right: 10px;
    width: 100%;
  }
}

.forgotten-password .form-fields .email input {
  height: 38px;
}

.forgotten-password .form-fields label.required {
  width: 130px;
}

.send-renew-password-link {
  padding-left: 10px;
  padding-right: 10px;
}

.renew-password {
  margin-left: 10px;
}

.renew-password .email {
  padding-bottom: 30px;
}

.renew-password [type='submit'] {
  margin-left: 50px;
}

.carousel {
  box-shadow: 1px 1px 7px 0 rgba(0, 0, 0, 0.15);
  margin-bottom: 1.5rem;
}

.carousel .direction {
  z-index: auto;
}

.carousel .carousel-inner {
  height: 340px;
}

@media (max-width: 767px) {
  .carousel .carousel-inner {
    height: auto;
  }
}

.carousel .carousel-item {
  height: 100%;
}

@media (max-width: 767px) {
  .carousel .carousel-item img {
    max-width: 100%;
    height: auto;
  }
}

@media (min-width: 768px) {
  .carousel .carousel-item img {
    width: 100%;
    margin-left: 0;
  }
}

.carousel .carousel-item .caption {
  position: absolute;
  color: #fff;
  max-width: 340px;
}

@media (min-width: 768px) {
  .carousel .carousel-item .caption {
    bottom: 28px;
    left: 90px;
  }
}

@media (max-width: 767px) {
  .carousel .carousel-item .caption {
    bottom: 5px;
    left: 40px;
  }
}

.carousel .carousel-item .caption .caption-description p {
  color: #fff;
}

@media (max-width: 767px) {
  .carousel .carousel-item figure {
    margin: 0;
  }
}

.carousel .carousel-control {
  opacity: 1;
}

.carousel .carousel-control .icon-next:before,
.carousel .carousel-control .icon-prev:before {
  content: '';
}

.carousel .carousel-control .icon-next i,
.carousel .carousel-control .icon-prev i {
  font-size: 3.125rem;
  color: #fff;
}

.carousel .carousel-control .icon-next:hover i,
.carousel .carousel-control .icon-prev:hover i {
  color: #2fb5d2;
}

.carousel .carousel-control .icon-prev {
  left: 1rem;
}

.carousel .carousel-control .icon-next {
  right: 2rem;
}

.carousel .carousel-control.left,
.carousel .carousel-control.right {
  background: none;
}

#products .products,
.featured-products .products,
.product-accessories .products,
.product-miniature .products {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

#products .product-thumbnail,
.featured-products .product-thumbnail,
.product-accessories .product-thumbnail,
.product-miniature .product-thumbnail {
  display: block;
}

#products .product-title a,
.featured-products .product-title a,
.product-accessories .product-title a,
.product-miniature .product-title a {
  color: #7a7a7a;
  font-size: 0.875rem;
  text-decoration: none;
  text-align: center;
  font-weight: 400;
}

#products .thumbnail-container,
.featured-products .thumbnail-container,
.product-accessories .thumbnail-container,
.product-miniature .thumbnail-container {
  position: relative;
  margin-bottom: 1.563rem;
  height: 318px;
  width: 257px;
  background: #fff;
  box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, 0.2);
}

#products .thumbnail-container:focus .highlighted-informations,
#products .thumbnail-container:hover .highlighted-informations,
.featured-products .thumbnail-container:focus .highlighted-informations,
.featured-products .thumbnail-container:hover .highlighted-informations,
.product-accessories .thumbnail-container:focus .highlighted-informations,
.product-accessories .thumbnail-container:hover .highlighted-informations,
.product-miniature .thumbnail-container:focus .highlighted-informations,
.product-miniature .thumbnail-container:hover .highlighted-informations {
  bottom: 5.5rem;
}

#products .thumbnail-container:focus .highlighted-informations:after,
#products .thumbnail-container:hover .highlighted-informations:after,
.featured-products .thumbnail-container:focus .highlighted-informations:after,
.featured-products .thumbnail-container:hover .highlighted-informations:after,
.product-accessories .thumbnail-container:focus .highlighted-informations:after,
.product-accessories .thumbnail-container:hover .highlighted-informations:after,
.product-miniature .thumbnail-container:focus .highlighted-informations:after,
.product-miniature .thumbnail-container:hover .highlighted-informations:after {
  opacity: 1;
}

#products .thumbnail-container:focus .highlighted-informations.no-variants,
#products .thumbnail-container:hover .highlighted-informations.no-variants,
.featured-products .thumbnail-container:focus .highlighted-informations.no-variants,
.featured-products .thumbnail-container:hover .highlighted-informations.no-variants,
.product-accessories .thumbnail-container:focus .highlighted-informations.no-variants,
.product-accessories .thumbnail-container:hover .highlighted-informations.no-variants,
.product-miniature .thumbnail-container:focus .highlighted-informations.no-variants,
.product-miniature .thumbnail-container:hover .highlighted-informations.no-variants {
  bottom: 4.2rem;
}

#products .products-section-title,
.featured-products .products-section-title,
.product-accessories .products-section-title,
.product-miniature .products-section-title {
  text-align: center;
  margin-bottom: 1.5rem;
}

#products .product-title,
.featured-products .product-title,
.product-accessories .product-title,
.product-miniature .product-title {
  text-align: center;
  text-transform: capitalize;
  margin-top: 1rem;
}

#products .product-price-and-shipping,
.featured-products .product-price-and-shipping,
.product-accessories .product-price-and-shipping,
.product-miniature .product-price-and-shipping {
  color: #232323;
  font-weight: 700;
  text-align: center;
}

#products .variant-links,
.featured-products .variant-links,
.product-accessories .variant-links,
.product-miniature .variant-links {
  position: relative;
  text-align: center;
  width: 100%;
  top: -0.25em;
  padding-top: 0.1875rem;
  min-height: 2.5rem;
  background: #fff;
}

#products .highlighted-informations,
.featured-products .highlighted-informations,
.product-accessories .highlighted-informations,
.product-miniature .highlighted-informations {
  position: absolute;
  bottom: 1.25rem;
  padding-top: 0.625rem;
  z-index: 0;
  background: #fff;
  text-align: center;
  width: 257px;
  height: 3.125rem;
  box-shadow: 0 -5px 10px -5px rgba(0, 0, 0, 0.2);
  transition: bottom 0.3s;
}

#products .highlighted-informations .quick-view,
.featured-products .highlighted-informations .quick-view,
.product-accessories .highlighted-informations .quick-view,
.product-miniature .highlighted-informations .quick-view {
  color: #7a7a7a;
  font-size: 1rem;
}

#products .highlighted-informations .quick-view:hover,
.featured-products .highlighted-informations .quick-view:hover,
.product-accessories .highlighted-informations .quick-view:hover,
.product-miniature .highlighted-informations .quick-view:hover {
  color: #2fb5d2;
}

#products .product-description,
.featured-products .product-description,
.product-accessories .product-description,
.product-miniature .product-description {
  position: absolute;
  z-index: 1;
  background: #fff;
  width: 257px;
  bottom: 0;
  height: 70px;
}

#products img,
.featured-products img,
.product-accessories img,
.product-miniature img {
  margin-left: 4px;
}

#products .product-miniature,
.featured-products .product-miniature,
.product-accessories .product-miniature,
.product-miniature .product-miniature {
  margin: 0 0.625rem;
}

#products .product-miniature .discount,
.featured-products .product-miniature .discount,
.product-accessories .product-miniature .discount,
.product-miniature .product-miniature .discount {
  display: none;
}

#products .product-miniature .discount-amount,
#products .product-miniature .discount-percentage,
#products .product-miniature .on-sale,
#products .product-miniature .online-only,
#products .product-miniature .pack,
#products .product-miniature .product-flags .new,
.featured-products .product-miniature .discount-amount,
.featured-products .product-miniature .discount-percentage,
.featured-products .product-miniature .on-sale,
.featured-products .product-miniature .online-only,
.featured-products .product-miniature .pack,
.featured-products .product-miniature .product-flags .new,
.product-accessories .product-miniature .discount-amount,
.product-accessories .product-miniature .discount-percentage,
.product-accessories .product-miniature .on-sale,
.product-accessories .product-miniature .online-only,
.product-accessories .product-miniature .pack,
.product-accessories .product-miniature .product-flags .new,
.product-miniature .product-miniature .discount-amount,
.product-miniature .product-miniature .discount-percentage,
.product-miniature .product-miniature .on-sale,
.product-miniature .product-miniature .online-only,
.product-miniature .product-miniature .pack,
.product-miniature .product-miniature .product-flags .new {
  display: block;
  position: absolute;
  left: -0.4375rem;
  padding: 0.3125rem 0.4375rem;
  color: #fff;
  background: #2fb5d2;
  text-transform: uppercase;
  min-width: 3.125rem;
  min-height: 1.875rem;
  font-size: 1rem;
  font-weight: 600;
  box-shadow: 2px 2px 4px 0 rgba(0, 0, 0, 0.2);
}

#products .product-miniature .discount-amount.discount-amount,
#products .product-miniature .discount-amount.discount-percentage,
#products .product-miniature .discount-percentage.discount-amount,
#products .product-miniature .discount-percentage.discount-percentage,
#products .product-miniature .on-sale.discount-amount,
#products .product-miniature .on-sale.discount-percentage,
#products .product-miniature .online-only.discount-amount,
#products .product-miniature .online-only.discount-percentage,
#products .product-miniature .pack.discount-amount,
#products .product-miniature .pack.discount-percentage,
#products .product-miniature .product-flags .new.discount-amount,
#products .product-miniature .product-flags .new.discount-percentage,
.featured-products .product-miniature .discount-amount.discount-amount,
.featured-products .product-miniature .discount-amount.discount-percentage,
.featured-products .product-miniature .discount-percentage.discount-amount,
.featured-products .product-miniature .discount-percentage.discount-percentage,
.featured-products .product-miniature .on-sale.discount-amount,
.featured-products .product-miniature .on-sale.discount-percentage,
.featured-products .product-miniature .online-only.discount-amount,
.featured-products .product-miniature .online-only.discount-percentage,
.featured-products .product-miniature .pack.discount-amount,
.featured-products .product-miniature .pack.discount-percentage,
.featured-products .product-miniature .product-flags .new.discount-amount,
.featured-products .product-miniature .product-flags .new.discount-percentage,
.product-accessories .product-miniature .discount-amount.discount-amount,
.product-accessories .product-miniature .discount-amount.discount-percentage,
.product-accessories .product-miniature .discount-percentage.discount-amount,
.product-accessories .product-miniature .discount-percentage.discount-percentage,
.product-accessories .product-miniature .on-sale.discount-amount,
.product-accessories .product-miniature .on-sale.discount-percentage,
.product-accessories .product-miniature .online-only.discount-amount,
.product-accessories .product-miniature .online-only.discount-percentage,
.product-accessories .product-miniature .pack.discount-amount,
.product-accessories .product-miniature .pack.discount-percentage,
.product-accessories .product-miniature .product-flags .new.discount-amount,
.product-accessories .product-miniature .product-flags .new.discount-percentage,
.product-miniature .product-miniature .discount-amount.discount-amount,
.product-miniature .product-miniature .discount-amount.discount-percentage,
.product-miniature .product-miniature .discount-percentage.discount-amount,
.product-miniature .product-miniature .discount-percentage.discount-percentage,
.product-miniature .product-miniature .on-sale.discount-amount,
.product-miniature .product-miniature .on-sale.discount-percentage,
.product-miniature .product-miniature .online-only.discount-amount,
.product-miniature .product-miniature .online-only.discount-percentage,
.product-miniature .product-miniature .pack.discount-amount,
.product-miniature .product-miniature .pack.discount-percentage,
.product-miniature .product-miniature .product-flags .new.discount-amount,
.product-miniature .product-miniature .product-flags .new.discount-percentage {
  z-index: 2;
  background: #f39d72;
}

#products .product-miniature .discount-amount.on-sale,
#products .product-miniature .discount-percentage.on-sale,
#products .product-miniature .on-sale.on-sale,
#products .product-miniature .online-only.on-sale,
#products .product-miniature .pack.on-sale,
#products .product-miniature .product-flags .new.on-sale,
.featured-products .product-miniature .discount-amount.on-sale,
.featured-products .product-miniature .discount-percentage.on-sale,
.featured-products .product-miniature .on-sale.on-sale,
.featured-products .product-miniature .online-only.on-sale,
.featured-products .product-miniature .pack.on-sale,
.featured-products .product-miniature .product-flags .new.on-sale,
.product-accessories .product-miniature .discount-amount.on-sale,
.product-accessories .product-miniature .discount-percentage.on-sale,
.product-accessories .product-miniature .on-sale.on-sale,
.product-accessories .product-miniature .online-only.on-sale,
.product-accessories .product-miniature .pack.on-sale,
.product-accessories .product-miniature .product-flags .new.on-sale,
.product-miniature .product-miniature .discount-amount.on-sale,
.product-miniature .product-miniature .discount-percentage.on-sale,
.product-miniature .product-miniature .on-sale.on-sale,
.product-miniature .product-miniature .online-only.on-sale,
.product-miniature .product-miniature .pack.on-sale,
.product-miniature .product-miniature .product-flags .new.on-sale {
  background: #f39d72;
  width: 100%;
  text-align: center;
  left: 0;
  top: 0;
}

#products .product-miniature .discount-amount.online-only,
#products .product-miniature .discount-percentage.online-only,
#products .product-miniature .on-sale.online-only,
#products .product-miniature .online-only.online-only,
#products .product-miniature .pack.online-only,
#products .product-miniature .product-flags .new.online-only,
.featured-products .product-miniature .discount-amount.online-only,
.featured-products .product-miniature .discount-percentage.online-only,
.featured-products .product-miniature .on-sale.online-only,
.featured-products .product-miniature .online-only.online-only,
.featured-products .product-miniature .pack.online-only,
.featured-products .product-miniature .product-flags .new.online-only,
.product-accessories .product-miniature .discount-amount.online-only,
.product-accessories .product-miniature .discount-percentage.online-only,
.product-accessories .product-miniature .on-sale.online-only,
.product-accessories .product-miniature .online-only.online-only,
.product-accessories .product-miniature .pack.online-only,
.product-accessories .product-miniature .product-flags .new.online-only,
.product-miniature .product-miniature .discount-amount.online-only,
.product-miniature .product-miniature .discount-percentage.online-only,
.product-miniature .product-miniature .on-sale.online-only,
.product-miniature .product-miniature .online-only.online-only,
.product-miniature .product-miniature .pack.online-only,
.product-miniature .product-miniature .product-flags .new.online-only {
  font-size: 0.8125rem;
  margin-top: 13rem;
  margin-left: 8.688rem;
}

#products .product-miniature .discount-amount.online-only:before,
#products .product-miniature .discount-percentage.online-only:before,
#products .product-miniature .on-sale.online-only:before,
#products .product-miniature .online-only.online-only:before,
#products .product-miniature .pack.online-only:before,
#products .product-miniature .product-flags .new.online-only:before,
.featured-products .product-miniature .discount-amount.online-only:before,
.featured-products .product-miniature .discount-percentage.online-only:before,
.featured-products .product-miniature .on-sale.online-only:before,
.featured-products .product-miniature .online-only.online-only:before,
.featured-products .product-miniature .pack.online-only:before,
.featured-products .product-miniature .product-flags .new.online-only:before,
.product-accessories .product-miniature .discount-amount.online-only:before,
.product-accessories .product-miniature .discount-percentage.online-only:before,
.product-accessories .product-miniature .on-sale.online-only:before,
.product-accessories .product-miniature .online-only.online-only:before,
.product-accessories .product-miniature .pack.online-only:before,
.product-accessories .product-miniature .product-flags .new.online-only:before,
.product-miniature .product-miniature .discount-amount.online-only:before,
.product-miniature .product-miniature .discount-percentage.online-only:before,
.product-miniature .product-miniature .on-sale.online-only:before,
.product-miniature .product-miniature .online-only.online-only:before,
.product-miniature .product-miniature .pack.online-only:before,
.product-miniature .product-miniature .product-flags .new.online-only:before {
  content: '\E30A';
  font-family: Material Icons;
  vertical-align: middle;
  margin: 0.3125rem;
}

#products .comments_note,
.featured-products .comments_note,
.product-accessories .comments_note,
.product-miniature .comments_note {
  text-align: center;
  color: #7a7a7a;
}

#products .regular-price,
.featured-products .regular-price,
.product-accessories .regular-price,
.product-miniature .regular-price {
  color: #7a7a7a;
  text-decoration: line-through;
  font-size: 0.875rem;
}

#products .count,
.featured-products .count,
.product-accessories .count,
.product-miniature .count {
  color: #7a7a7a;
  font-weight: 700;
  position: relative;
  bottom: 0.5rem;
}

#products .all-product-link,
.featured-products .all-product-link,
.product-accessories .all-product-link,
.product-miniature .all-product-link {
  clear: both;
  color: #7a7a7a;
  font-weight: 700;
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

@media (max-width: 767px) {
  #products .thumbnail-container,
  .featured-products .thumbnail-container,
  .product-accessories .thumbnail-container {
    box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, 0.2);
  }

  #products .thumbnail-container .product-description,
  .featured-products .thumbnail-container .product-description,
  .product-accessories .thumbnail-container .product-description {
    box-shadow: 0 -5px 10px -5px rgba(0, 0, 0, 0.2);
  }
}

#custom-text {
  background: #fff;
  border-radius: 2px;
  margin-bottom: 1.5rem;
  padding: 3.125rem;
  text-align: center;
}

#custom-text h3 {
  text-transform: uppercase;
  color: #232323;
  font-size: 1.563rem;
  font-weight: 700;
}

#custom-text p {
  color: #232323;
  font-weight: 400;
  font-size: 1.1em;
}

#custom-text p .dark {
  color: #7a7a7a;
  font-weight: 400;
}

.page-content.page-cms {
  background: #fff;
  text-align: justify;
}

.page-content.page-cms .cms-box img {
  max-width: 100%;
}

@media (max-width: 991px) {
  #block-cmsinfo {
    padding: 1.25rem 1.875rem;
  }
}

#products {
  color: #7a7a7a;
}

#products .products-select {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

#products .up {
  margin-bottom: 1rem;
}

#products .up .btn-secondary,
#products .up .btn-tertiary {
  color: #7a7a7a;
  text-transform: inherit;
}

#products .up .btn-secondary .material-icons,
#products .up .btn-tertiary .material-icons {
  margin-right: 0;
}

.block-category {
  min-height: 13.75rem;
  margin-bottom: 1.563rem;
  margin-top: 1.563rem;
}

.block-category #category-description p,
.block-category #category-description strong {
  font-weight: 400;
  color: #7a7a7a;
}

.block-category #category-description p {
  color: #232323;
  margin-bottom: 0;
}

.block-category #category-description p:first-child {
  margin-bottom: 1.25rem;
}

.block-category .category-cover {
  position: absolute;
  right: 0.75rem;
  bottom: 0;
}

.block-category .category-cover img {
  width: 141px;
  height: 180px;
}

.products-selection .sort-by-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.products-selection .sort-by {
  white-space: normal;
  word-break: break-word;
  margin-right: -0.9375rem;
  margin-left: 0.9375rem;
  text-align: right;
}

.products-selection .total-products,
.products-selection h1 {
  padding-top: 0.625rem;
}

.products-sort-order {
  color: #7a7a7a;
}

.products-sort-order .select-title {
  display: inline-block;
  width: 100%;
  color: #232323;
  background: #fff;
  padding: 0.625rem;
  cursor: pointer;
  border: 1px solid rgba(0, 0, 0, 0.25);
  box-shadow: 2px 2px 4px 0 rgba(0, 0, 0, 0.2);
}

.products-sort-order .select-list {
  display: block;
  color: #232323;
  padding: 0.625rem 1.25rem;
}

.products-sort-order .select-list:hover {
  background: #2fb5d2;
  color: #fff;
  text-decoration: none;
}

.products-sort-order .dropdown-menu {
  left: auto;
  width: 16.88rem;
  background: #f6f6f6;
  border: none;
  border-radius: 0;
  box-shadow: 2px 2px 4px 0 rgba(0, 0, 0, 0.1);
  margin: 0;
}

.facet-dropdown {
  color: #7a7a7a;
  padding-left: 0;
  padding-right: 0;
  background: #f1f1f1;
  border: 3px solid transparent;
  box-sizing: border-box;
  box-shadow: 1px 1px 1px 1px #f1f1f1;
}

.facet-dropdown.open {
  border: 0;
}

.facet-dropdown.open > .select-title {
  border: 3px solid #2fb5d2;
  background: #f6f6f6;
}

.facet-dropdown .select-title {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-left: 0;
  width: 100%;
  color: #232323;
  background: #f1f1f1;
  padding: 0.625rem 0.625rem 0.625rem 1.25rem;
  cursor: pointer;
}

.facet-dropdown .select-title > i {
  margin-left: auto;
}

.facet-dropdown .select-list {
  display: block;
  color: #232323;
  background: #f6f6f6;
  padding: 0.625rem 1.25rem;
}

.facet-dropdown .select-list:hover {
  background: #2fb5d2;
  color: #fff;
  text-decoration: none;
}

.facet-dropdown .dropdown-menu {
  padding: 0;
  margin-top: 3px;
  left: auto;
  width: 100%;
  background: #f6f6f6;
  border: none;
  box-shadow: 1px 1px 1px 1px #f1f1f1;
}

#search_filters,
#search_filters_brands,
#search_filters_suppliers {
  box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, 0.2);
  margin-bottom: 1.563rem;
  background: #fff;
  padding: 1.563rem 1.25rem;
}

#search_filters .facet,
#search_filters_brands .facet,
#search_filters_suppliers .facet {
  padding-top: 0.625rem;
}

#search_filters .facet .collapse,
#search_filters_brands .facet .collapse,
#search_filters_suppliers .facet .collapse {
  display: block;
}

#search_filters .facet .facet-title,
#search_filters_brands .facet .facet-title,
#search_filters_suppliers .facet .facet-title {
  color: #7a7a7a;
}

#search_filters .facet .facet-label,
#search_filters_brands .facet .facet-label,
#search_filters_suppliers .facet .facet-label {
  margin-bottom: 0;
}

#search_filters .facet .facet-label a,
#search_filters_brands .facet .facet-label a,
#search_filters_suppliers .facet .facet-label a {
  margin-top: 0.4375rem;
  color: #232323;
  display: inline-block;
  font-size: 0.9375rem;
}

#search_filters_brands .facet,
#search_filters_suppliers .facet {
  padding-top: 0;
}

#search_filters_brands .facet .facet-label,
#search_filters_suppliers .facet .facet-label {
  margin-bottom: 0.3125rem;
}

.pagination {
  width: 100%;
}

.pagination > div:first-child {
  line-height: 2.5rem;
}

.pagination .page-list {
  background: #fff;
  padding: 0.375rem;
  margin-bottom: 0;
  box-shadow: 2px 2px 4px 0 rgba(0, 0, 0, 0.2);
}

.pagination .page-list li {
  display: inline;
}

.pagination a {
  color: #000;
  font-weight: 600;
}

.pagination a:not(.previous):not(.next) {
  letter-spacing: 0.125rem;
}

.pagination .previous {
  float: left;
}

.pagination .next {
  float: right;
}

.pagination .disabled {
  color: #7a7a7a;
}

.pagination .current a {
  color: #2fb5d2;
  text-decoration: none;
  font-size: 1.25rem;
}

.active_filters {
  background: #dededd;
  padding: 0.625rem 1.875rem 0;
  margin-bottom: 1.25rem;
}

.active_filters .active-filter-title {
  display: inline;
  margin-right: 0.625rem;
  font-weight: 600;
}

.active_filters ul {
  display: inline;
}

.active_filters .filter-block {
  box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, 0.2);
  color: #232323;
  margin-right: 0.625rem;
  margin-bottom: 0.625rem;
  background: #fff;
  padding: 0.625rem;
  display: inline-block;
  font-size: 0.8125rem;
}

.active_filters .filter-block .close {
  color: #232323;
  font-size: 0.9375rem;
  opacity: 1;
  margin-top: 0.1875rem;
  margin-left: 0.3125rem;
}

.block-categories {
  box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, 0.2);
  background: #fff;
  padding: 1.563rem 1.25rem;
  margin-bottom: 1.563rem;
}

.block-categories .category-sub-menu {
  margin-top: 0.625rem;
}

.block-categories .category-sub-menu .category-sub-link {
  font-size: 0.875rem;
}

.block-categories .category-sub-menu li {
  position: relative;
}

.block-categories .category-sub-menu li[data-depth='1'] {
  margin-bottom: 0.625rem;
}

.block-categories .category-sub-menu li[data-depth='0'] > a {
  font-weight: 600;
  border-bottom: 1px solid rgba(0, 0, 0, 0.25);
  width: 100%;
  display: inline-block;
  margin: 0.3125rem 0 0;
  padding-bottom: 0.1875rem;
}

.block-categories .category-sub-menu li:not([data-depth='0']):not([data-depth='1']) {
  padding-left: 0.3125rem;
}

.block-categories .category-sub-menu li:not([data-depth='0']):not([data-depth='1']):before {
  content: '-';
  margin-right: 0.3125rem;
}

.block-categories a {
  color: #232323;
}

.block-categories .collapse-icons {
  position: absolute;
  right: 0;
  top: 0;
  padding: 0;
  cursor: pointer;
}

.block-categories .collapse-icons[aria-expanded='true'] .add {
  display: none;
}

.block-categories .collapse-icons[aria-expanded='true'] .remove {
  display: block;
}

.block-categories .collapse-icons .add:hover,
.block-categories .collapse-icons .remove:hover {
  color: #2fb5d2;
}

.block-categories .collapse-icons .remove {
  display: none;
}

.block-categories .arrows .arrow-down,
.block-categories .arrows .arrow-right {
  font-size: 0.875rem;
  cursor: pointer;
  margin-left: 2px;
}

.block-categories .arrows .arrow-down:hover,
.block-categories .arrows .arrow-right:hover {
  color: #2fb5d2;
}

.block-categories .arrows .arrow-down,
.block-categories .arrows[aria-expanded='true'] .arrow-right {
  display: none;
}

.block-categories .arrows[aria-expanded='true'] .arrow-down {
  display: inline-block;
}

.facets-title {
  color: #232323;
}

.products-selection .filter-button .btn-secondary,
.products-selection .filter-button .btn-tertiary {
  padding: 0.75rem 0.5rem 0.6875rem;
}

.advertising-block {
  margin-bottom: 1.563rem;
}

.advertising-block img {
  width: 100%;
}

@media (max-width: 767px) {
  #category #left-column {
    width: 100%;
  }

  #category #left-column #search_filters_wrapper {
    margin-left: -30px;
    margin-right: -30px;
  }

  #category #left-column #search_filter_controls {
    text-align: center;
    margin-bottom: 1rem;
  }

  #category #left-column #search_filter_controls button {
    margin: 0 0.5rem;
  }

  #category #left-column #search_filters {
    margin-bottom: 0;
    box-shadow: none;
    padding: 0;
    border-top: 1px solid #f6f6f6;
  }

  #category #left-column #search_filters .facet {
    padding-top: 0;
    border-bottom: 1px solid #f6f6f6;
  }

  #category #left-column #search_filters .facet .title {
    cursor: pointer;
  }

  #category #left-column #search_filters .facet .title .collapse-icons .remove,
  #category #left-column #search_filters .facet .title[aria-expanded='true'] .collapse-icons .add {
    display: none;
  }

  #category #left-column #search_filters .facet .title[aria-expanded='true'] .collapse-icons .remove {
    display: block;
  }

  #category #left-column #search_filters .facet .facet-title {
    color: #232323;
    text-transform: uppercase;
  }

  #category #left-column #search_filters .facet .h6 {
    margin-bottom: 0;
    padding: 0.625rem;
    display: inline-block;
  }

  #category #left-column #search_filters .facet .navbar-toggler {
    display: inline-block;
    padding: 0.625rem 0.625rem 0 0;
  }

  #category #left-column #search_filters .facet .collapse {
    display: none;
  }

  #category #left-column #search_filters .facet .collapse.in {
    display: block;
  }

  #category #left-column #search_filters .facet .facet-label a {
    margin-top: 0;
  }

  #category #left-column #search_filters .facet ul {
    margin-bottom: 0;
  }

  #category #left-column #search_filters .facet ul li {
    border-top: 1px solid #f6f6f6;
    padding: 0.625rem;
  }

  #category #content-wrapper,
  #category #search_filter_toggler {
    width: 100%;
  }

  .products-sort-order .select-title {
    margin-left: 0;
  }

  .products-selection h1 {
    padding-top: 0;
    text-align: center;
    margin-bottom: 1rem;
  }

  .products-selection .showing {
    padding-top: 1rem;
  }

  #best-sales #content-wrapper,
  #new-products #content-wrapper,
  #prices-drop #content-wrapper {
    width: 100%;
  }
}

@media (max-width: 575px) {
  .products-selection .filter-button {
    padding-left: 0;
  }

  #category #left-column #search_filters_wrapper {
    margin-left: -15px;
    margin-right: -15px;
  }
}

#product #content {
  position: relative;
  max-width: 452px;
  margin: 0 auto;
}

.product-price {
  color: #2fb5d2;
  display: inline-block;
}

#product-description-short {
  color: #232323;
}

.product-information {
  font-size: 0.9375rem;
  color: #232323;
}

.product-information .manufacturer-logo {
  height: 35px;
}

.product-information .product-description img {
  max-width: 100%;
  height: auto;
}

.input-color {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 1.25rem;
  width: 1.25rem;
}

.input-container {
  position: relative;
}

.input-radio {
  position: absolute;
  top: 0;
  cursor: pointer;
  opacity: 0;
  width: 100%;
  height: 100%;
}

.input-color:checked + span,
.input-color:hover + span,
.input-radio:checked + span,
.input-radio:hover + span {
  border: 2px solid #232323;
}

.radio-label {
  box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, 0.2);
  background: #fff;
  display: inline-block;
  padding: 0.125rem 0.625rem;
  font-weight: 600;
  border: 2px solid #fff;
}

.product-actions .control-label {
  margin-bottom: 0.375rem;
  display: block;
  width: 100%;
}

.product-actions .add-to-cart {
  height: 2.75rem;
  line-height: inherit;
  padding-top: 0.625rem;
}

.product-actions .add-to-cart .material-icons {
  line-height: inherit;
}

.product-quantity {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.product-quantity .add,
.product-quantity .qty {
  float: left;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  margin-bottom: 0.5rem;
}

.product-quantity .qty {
  margin-right: 0.4rem;
}

.product-quantity #quantity_wanted {
  color: #232323;
  background-color: #fff;
  height: 2.75rem;
  padding: 0.175rem 0.5rem;
  width: 3rem;
}

.product-quantity .input-group-btn-vertical {
  width: auto;
}

.product-quantity .input-group-btn-vertical .btn {
  padding: 0.5rem 0.6875rem;
}

.product-quantity .input-group-btn-vertical .btn i {
  font-size: 1rem;
  top: 0.125rem;
  left: 0.1875rem;
}

.product-quantity .btn-touchspin {
  height: 1.438rem;
}

.product-discounts {
  margin-bottom: 1.5rem;
}

.product-discounts > .product-discounts-title {
  font-weight: 400;
  font-size: 0.875rem;
}

.product-discounts > .table-product-discounts thead tr th {
  width: 33%;
  padding: 0.625rem 1.25rem;
  background: #fff;
  border: 0.3125rem solid #f1f1f1;
  text-align: center;
}

.product-discounts > .table-product-discounts tbody tr {
  background: #f6f6f6;
}

.product-discounts > .table-product-discounts tbody tr:nth-of-type(2n) {
  background: #fff;
}

.product-discounts > .table-product-discounts tbody tr td {
  padding: 0.625rem 1.25rem;
  text-align: center;
  border: 0.3125rem solid #f1f1f1;
}

.product-prices {
  margin-top: 1.25rem;
}

.product-prices div {
  margin-bottom: 0.625rem;
}

.product-prices .tax-shipping-delivery-label {
  font-size: 0.8125rem;
  color: #7a7a7a;
}

.product-prices .tax-shipping-delivery-label .delivery-information {
  padding: 0 0 0 2px;
}

.product-prices .tax-shipping-delivery-label .delivery-information:before {
  content: '-';
  padding: 0 2px 0 0;
}

.product-discount {
  color: #7a7a7a;
}

.product-discount .regular-price {
  text-decoration: line-through;
  font-weight: 400;
  margin-right: 0.625rem;
}

.has-discount.product-price,
.has-discount p {
  color: #f39d72;
}

.has-discount .discount {
  background: #f39d72;
  color: #fff;
  font-weight: 600;
  padding: 0.3125rem 0.625rem;
  font-size: 1rem;
  margin-left: 0.625rem;
  text-transform: uppercase;
  display: inline-block;
}

.product-unit-price {
  font-size: 0.8125rem;
  margin-bottom: 0;
}

.tabs {
  box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, 0.2);
  margin-top: 2rem;
  background: #fff;
  padding: 1.25rem 1.875rem;
}

.tabs .tab-pane {
  padding-top: 1.25rem;
}

.tabs .nav-tabs {
  border: none;
  border-bottom: 2px solid #f1f1f1;
}

.tabs .nav-tabs .nav-link {
  color: #7a7a7a;
  border: 0 solid transparent;
}

.tabs .nav-tabs .nav-link.active {
  color: #2fb5d2;
}

.tabs .nav-tabs .nav-link.active,
.tabs .nav-tabs .nav-link:hover {
  border: none;
  border-bottom: 3px solid #2fb5d2;
}

.tabs .nav-tabs .nav-item {
  float: left;
  margin-bottom: -0.125rem;
}

.product-cover {
  margin-bottom: 1.25rem;
  position: relative;
}

.product-cover img {
  box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, 0.2);
  background: #fff;
}

.product-cover .layer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #fff;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  background: hsla(0, 0%, 100%, 0.7);
  text-align: center;
  cursor: pointer;
}

.product-cover .layer:hover {
  opacity: 1;
}

.product-cover .layer .zoom-in {
  font-size: 6.25rem;
  color: #7a7a7a;
}

#product-modal .modal-content {
  background: transparent;
  border: none;
  padding: 0;
}

#product-modal .modal-content .modal-body {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-left: -30%;
}

#product-modal .modal-content .modal-body .product-cover-modal {
  background: #fff;
}

#product-modal .modal-content .modal-body .image-caption {
  background: #fff;
  width: 800px;
  padding: 0.625rem 1.25rem;
  border-top: 1px solid #f1f1f1;
}

#product-modal .modal-content .modal-body .image-caption p {
  margin-bottom: 0;
}

#product-modal .modal-content .modal-body .thumbnails {
  position: relative;
}

#product-modal .modal-content .modal-body .mask {
  position: relative;
  overflow: hidden;
  max-height: 49.38rem;
  margin-top: 2.188rem;
  z-index: 1;
}

#product-modal .modal-content .modal-body .mask.nomargin {
  margin-top: 0;
}

#product-modal .modal-content .modal-body .product-images {
  margin-left: 2.5rem;
}

#product-modal .modal-content .modal-body .product-images img {
  width: 9.25rem;
  cursor: pointer;
  background: #fff;
}

#product-modal .modal-content .modal-body .product-images img:hover {
  border: 3px solid #2fb5d2;
}

#product-modal .modal-content .modal-body .arrows {
  height: 100%;
  width: 100%;
  text-align: center;
  position: absolute;
  top: 0;
  color: #fff;
  right: 1.875rem;
  z-index: 0;
  cursor: pointer;
}

#product-modal .modal-content .modal-body .arrows .arrow-up {
  position: absolute;
  top: -2rem;
  opacity: 0.2;
}

#product-modal .modal-content .modal-body .arrows .arrow-down {
  position: absolute;
  bottom: -2rem;
}

#product-modal .modal-content .modal-body .arrows i {
  font-size: 6.25rem;
  display: inline;
}

#blockcart-modal {
  color: #232323;
}

#blockcart-modal .modal-header {
  background: #4cbb6c;
}

#blockcart-modal .modal-body {
  background: #f1f1f1;
  padding: 3.125rem 1.875rem;
}

#blockcart-modal .modal-body .divide-right span {
  display: inline-block;
  margin-bottom: 0.3125rem;
}

#blockcart-modal .modal-dialog {
  max-width: 1140px;
  width: 100%;
}

#blockcart-modal .product-image {
  width: 100%;
}

#blockcart-modal .modal-title {
  font-weight: 600;
  color: #fff;
  font-size: 1.125rem;
}

#blockcart-modal .modal-title i.material-icons {
  margin-right: 1.875rem;
}

#blockcart-modal .product-name {
  color: #2fb5d2;
  font-size: 1.125rem;
}

#blockcart-modal .cart-products-count {
  font-size: 1.125rem;
}

#blockcart-modal .cart-content {
  padding-left: 2.5rem;
}

#blockcart-modal .cart-content .cart-content-btn {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

#blockcart-modal .cart-content .cart-content-btn button {
  margin-right: 0.3125rem;
}

#blockcart-modal .cart-content .cart-content-btn .btn {
  white-space: inherit;
}

#blockcart-modal .divide-right {
  border-right: 1px solid #7a7a7a;
}

.product-images > li.thumb-container {
  display: inline;
}

.product-images > li.thumb-container > .thumb {
  box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, 0.2);
  cursor: pointer;
  margin-bottom: 0.625rem;
}

.images-container .product-images > li.thumb-container > .thumb {
  margin-right: 0.8125rem;
}

.product-images > li.thumb-container > .thumb.selected,
.product-images > li.thumb-container > .thumb:hover {
  border: 3px solid #2fb5d2;
}

#main .images-container .js-qv-mask {
  white-space: nowrap;
  overflow: hidden;
}

#main .images-container .js-qv-mask.scroll {
  width: calc(100% - 60px);
  margin: 0 auto;
}

.scroll-box-arrows {
  display: none;
}

.scroll-box-arrows.scroll {
  display: block;
}

.scroll-box-arrows i {
  position: absolute;
  bottom: 1.625rem;
  height: 100px;
  line-height: 100px;
  cursor: pointer;
}

.scroll-box-arrows .left {
  left: 0;
}

.scroll-box-arrows .right {
  right: 0;
}

#product-availability {
  margin-top: 0.625rem;
  display: inline-block;
  font-weight: 700;
}

#product-availability .material-icons {
  line-height: inherit;
}

#product-availability .product-available {
  color: #4cbb6c;
}

#product-availability .product-last-items,
#product-availability .product-unavailable {
  color: #ff9a52;
}

#product-details .label {
  font-size: 1rem;
  color: #232323;
  font-weight: 700;
}

.product-features {
  margin-top: 1.25rem;
  margin-left: 0.3125rem;
}

.product-features > dl.data-sheet {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
}

.product-features > dl.data-sheet dd.value,
.product-features > dl.data-sheet dt.name {
  -webkit-box-flex: 1;
  -ms-flex: 1 0 40%;
  flex: 1 0 40%;
  font-weight: 400;
  background: #f1f1f1;
  padding: 0.625rem;
  margin-right: 0.625rem;
  min-height: 2.5rem;
  word-break: normal;
  text-transform: capitalize;
  margin-bottom: 0.5rem;
}

.product-features > dl.data-sheet dd.value:nth-of-type(2n),
.product-features > dl.data-sheet dt.name:nth-of-type(2n) {
  background: #f6f6f6;
}

.product-variants > .product-variants-item {
  margin: 1.25rem 0;
}

.product-variants > .product-variants-item select {
  background-color: #fff;
  width: auto;
  padding-right: 1.875rem;
  box-shadow: 2px 2px 4px 0 rgba(0, 0, 0, 0.2);
}

.product-variants > .product-variants-item ul li {
  margin-right: 0.625rem;
}

.product-variants > .product-variants-item .color {
  margin-left: 0;
  margin-top: 0;
}

.product-flags {
  position: absolute;
  top: 0;
  width: 100%;
}

li.product-flag {
  display: block;
  position: absolute;
  background: #2fb5d2;
  font-weight: 700;
  padding: 0.3125rem 0.4375rem;
  text-transform: uppercase;
  color: #fff;
  margin-top: 0.625rem;
  z-index: 2;
  box-shadow: 2px 2px 4px 0 rgba(0, 0, 0, 0.2);
}

li.product-flag.online-only {
  top: 25rem;
  right: 0;
  font-size: 0.8125rem;
}

li.product-flag.online-only:before {
  content: '\E30A';
  font-family: Material Icons;
  vertical-align: middle;
  margin: 0.3125rem;
}

li.product-flag.discount {
  display: none;
}

li.product-flag.on-sale {
  background: #f39d72;
  width: 100%;
  text-align: center;
  margin: 0;
  left: 0;
  top: 0;
}

li.product-flag:not(.discount):not(.on-sale) ~ li.product-flag {
  margin-top: 3.75rem;
}

.product-customization {
  margin: 2.5rem 0;
}

.product-customization .product-customization-item {
  margin: 1.25rem 0;
}

.product-customization .product-message {
  background: #f1f1f1;
  border: none;
  width: 100%;
  height: 3.125rem;
  resize: none;
  padding: 0.625rem;
}

.product-customization .product-message:focus {
  background-color: #fff;
  outline: 0.1875rem solid #2fb5d2;
}

.product-customization .file-input {
  width: 100%;
  opacity: 0;
  left: 0;
  z-index: 1;
  cursor: pointer;
  height: 2.625rem;
  overflow: hidden;
  position: absolute;
}

.product-customization .customization-message {
  margin-top: 20px;
}

.product-customization .custom-file {
  position: relative;
  background: #f1f1f1;
  width: 100%;
  height: 2.625rem;
  line-height: 2.625rem;
  text-indent: 0.625rem;
  display: block;
  color: #7a7a7a;
  margin-top: 1.25rem;
}

.product-customization .custom-file button {
  z-index: 0;
  position: absolute;
  right: 0;
  top: 0;
}

.product-customization small {
  color: #7a7a7a;
}

.product-pack {
  margin-top: 2.5rem;
}

.product-pack .pack-product-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: distribute;
  justify-content: space-around;
}

.product-pack .pack-product-container .pack-product-name {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 50%;
  flex: 0 0 50%;
  font-size: 0.875rem;
  color: #7a7a7a;
}

.product-pack .pack-product-container .pack-product-quantity {
  border-left: 2px solid #f1f1f1;
  padding-left: 0.625rem;
}

.product-pack .pack-product-container .pack-product-name,
.product-pack .pack-product-container .pack-product-price,
.product-pack .pack-product-container .pack-product-quantity {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.product-refresh {
  margin-top: 1.25rem;
}

.social-sharing {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-top: 1.5rem;
}

.social-sharing ul {
  margin-bottom: 0;
}

.social-sharing li {
  box-shadow: 2px 2px 4px 0 rgba(0, 0, 0, 0.2);
  height: 2.5rem;
  width: 2.5rem;
  border-radius: 50%;
  display: inline-block;
  background-color: #fff;
  background-repeat: no-repeat;
  background-size: 2rem 2rem;
  background-position: 0.25rem 0.25rem;
  cursor: pointer;
  margin-left: 0.5rem;
}

.social-sharing li a {
  display: block;
  width: 100%;
  height: 100%;
  white-space: nowrap;
  text-indent: 100%;
  overflow: hidden;
}

.social-sharing li a:hover {
  color: transparent;
}

.products-selection {
  margin-bottom: 1.25rem;
}

.products-selection .title {
  color: #7a7a7a;
}

#blockcart-modal .cart-content .btn {
  margin-bottom: 0.625rem;
}

@media (max-width: 991px) {
  .product-cover img {
    width: 100%;
  }

  #product-modal .modal-content .modal-body {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    margin-left: 0;
  }

  #product-modal .modal-content .modal-body img.product-cover-modal {
    width: 100%;
  }

  #product-modal .modal-content .modal-body .arrows {
    display: none;
  }

  #product-modal .modal-content .modal-body .image-caption {
    width: 100%;
  }

  #blockcart-modal .modal-dialog {
    width: calc(100% - 20px);
  }

  #blockcart-modal .modal-body {
    padding: 1.875rem;
  }
}

@media (max-width: 767px) {
  #blockcart-modal .divide-right {
    border-right: none;
  }

  #blockcart-modal .modal-body {
    padding: 1rem;
  }
}

.cart-grid {
  margin-bottom: 1rem;
}

.cart-items {
  margin-bottom: 0;
}

.cart-item {
  padding: 1rem 0;
}

.cart-summary-line {
  clear: both;
}

.cart-summary-line:after {
  content: '';
  display: table;
  clear: both;
}

.cart-summary-line .label {
  padding-left: 0;
  font-weight: 400;
  white-space: inherit;
}

.cart-summary-line .value {
  color: #232323;
  float: right;
}

.cart-summary-line.cart-summary-subtotals .label,
.cart-summary-line.cart-summary-subtotals .value {
  font-weight: 400;
}

.cart-grid-body {
  margin-bottom: 0.75rem;
}

.cart-grid-body a.label:hover {
  color: #2fb5d2;
}

.cart-grid-body .card-block {
  padding: 1rem;
}

.cart-grid-body .card-block h1 {
  margin-bottom: 0;
}

.cart-grid-body .cart-overview {
  padding: 1rem;
}

.cart-grid-right .promo-discounts {
  margin-bottom: 0;
}

.cart-grid-right .promo-discounts .cart-summary-line .label {
  color: #7a7a7a;
}

.cart-grid-right .promo-discounts .cart-summary-line .label .code {
  text-decoration: underline;
  cursor: pointer;
}

.block-promo .promo-code {
  padding: 1.6rem;
  background: #f1f1f1;
}

.block-promo .promo-code .alert-danger {
  position: relative;
  margin-top: 1.25rem;
  background: #ff4c4c;
  color: #fff;
  display: none;
}

.block-promo .promo-code .alert-danger:after {
  bottom: 100%;
  left: 10%;
  border: solid transparent;
  content: ' ';
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-bottom-color: #ff4c4c;
  border-width: 10px;
  margin-left: -10px;
}

.block-promo .promo-input {
  color: #232323;
  border: 1px solid #7a7a7a;
  height: 2.5rem;
  text-indent: 0.625rem;
  width: 60%;
}

.block-promo .promo-input + button {
  margin-top: -4px;
  text-transform: capitalize;
}

.block-promo .cart-summary-line .label,
.block-promo .promo-name {
  color: #ff9a52;
  font-weight: 600;
}

.block-promo .cart-summary-line .label a,
.block-promo .promo-name a {
  font-weight: 400;
  color: #232323;
  display: inline-block;
}

.block-promo .promo-code-button {
  padding-left: 1.25rem;
  margin-bottom: 1.25rem;
  display: inline-block;
}

.block-promo.promo-highlighted {
  padding: 1.25rem;
  padding-bottom: 0;
  margin-bottom: 0;
}

.product-line-grid-left img {
  max-width: 100%;
}

.product-line-grid-body > .product-line-info > .label {
  padding: 0;
  line-height: inherit;
  text-align: left;
  white-space: inherit;
}

.product-line-grid-body > .product-line-info > .out-of-stock {
  color: red;
}

.product-line-grid-body > .product-line-info > .available {
  color: #4cbb6c;
}

.product-line-grid-body > .product-line-info > .unit-price-cart {
  padding-left: 0.3125rem;
  font-size: 0.875rem;
  color: #7a7a7a;
}

.product-line-grid-right .cart-line-product-actions,
.product-line-grid-right .product-price {
  color: #232323;
  line-height: 36px;
}

.product-line-grid-right .cart-line-product-actions .remove-from-cart,
.product-line-grid-right .product-price .remove-from-cart {
  color: #232323;
  display: inline-block;
  margin-top: 0.3125rem;
}

@media (max-width: 767px) {
  .product-line-grid-body {
    margin-bottom: 1rem;
  }
}

@media (max-width: 575px) {
  .cart-items {
    padding: 1rem 0;
  }

  .cart-item {
    border-bottom: 1px solid #f1f1f1;
  }

  .cart-item:last-child {
    border-bottom: 0;
  }

  .cart-grid-body .cart-overview {
    padding: 0;
  }

  .cart-grid-body .no-items {
    padding: 1rem;
    display: inline-block;
  }

  .product-line-grid-left {
    padding-right: 0 !important;
  }
}

@media (max-width: 360px) {
  .product-line-grid-right .price,
  .product-line-grid-right .qty {
    width: 100%;
  }
}

#block-reassurance {
  margin-top: 2rem;
}

#block-reassurance img {
  width: 1.563rem;
  margin-right: 0.625rem;
}

#block-reassurance li {
  border-bottom: 1px solid rgba(0, 0, 0, 0.25);
}

#block-reassurance li:last-child {
  border: 0;
}

#block-reassurance li .block-reassurance-item {
  padding: 1rem 1.5rem;
}

#product #block-reassurance {
  box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, 0.2);
  background: #fff;
}

#product #block-reassurance span {
  font-weight: 700;
}

.quickview .modal-dialog {
  width: calc(100% - 30px);
  max-width: 64rem;
}

.quickview .modal-content {
  background: #f1f1f1;
  min-height: 28.13rem;
}

.quickview .modal-header {
  border: none;
  padding: 0.625rem;
}

.quickview .modal-body {
  min-height: 23.75rem;
}

.quickview .modal-footer {
  border-top: 1px solid hsla(0, 0%, 48%, 0.3);
}

.quickview .layer {
  display: none;
}

.quickview .product-cover img {
  width: 95%;
}

.quickview .images-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  z-index: 1;
  min-height: 21.88rem;
}

.quickview .images-container .product-images > li.thumb-container > .thumb {
  width: 100%;
  max-width: 4.938rem;
  margin-bottom: 0.8125rem;
  background: #fff;
}

.quickview .mask {
  width: 35%;
  max-height: 22.5rem;
  overflow: hidden;
  margin-left: 0.625rem;
}

.quickview .arrows {
  position: absolute;
  top: 0;
  bottom: 0;
  max-height: 22.5rem;
  right: 5rem;
  z-index: 0;
}

.quickview .arrows .arrow-up {
  margin-top: -3.125rem;
  cursor: pointer;
  opacity: 0.2;
}

.quickview .arrows .arrow-down {
  position: absolute;
  bottom: -1.875rem;
  cursor: pointer;
}

.quickview .social-sharing {
  margin-top: 0;
  margin-left: 2.5rem;
}

#stores .page-stores {
  width: 85%;
  margin: 0 auto;
}

#stores .page-stores .store-item {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

#stores .page-stores .store-picture img {
  max-width: 100%;
}

#stores .page-stores .store-item-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: distribute;
  justify-content: space-around;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 1.875rem 0;
}

#stores .page-stores .store-item-container ul {
  margin-bottom: 0;
  font-size: 0.9375rem;
}

#stores .page-stores .store-item-container .divide-left {
  border-left: 1px solid #f1f1f1;
}

#stores .page-stores .store-item-container .divide-left tr {
  height: 1.563rem;
}

#stores .page-stores .store-item-container .divide-left td {
  padding-left: 0.375rem;
}

#stores .page-stores .store-item-container .divide-left th {
  text-align: right;
}

#stores .page-stores .store-item-container .store-description {
  font-size: 1rem;
}

#stores .page-stores .store-item-footer {
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: distribute;
  justify-content: space-around;
}

#stores .page-stores .store-item-footer.divide-top {
  border-top: 1px solid #f1f1f1;
}

#stores .page-stores .store-item-footer div:first-child {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 65%;
  flex: 0 0 65%;
}

#stores .page-stores .store-item-footer i.material-icons {
  margin-right: 0.625rem;
  color: #7a7a7a;
  font-size: 1rem;
}

#stores .page-stores .store-item-footer li {
  margin-bottom: 0.625rem;
}

@media (max-width: 767px) {
  #stores .page-stores {
    width: 100%;
  }

  #stores .page-stores .store-item-container {
    padding: 1rem 0;
  }
}

@media (max-width: 575px) {
  #stores .page-stores .store-item-container {
    display: block;
  }

  #stores .page-stores .store-item-container .divide-left {
    border-left: none;
  }

  #stores .page-stores .store-item-container .store-description a,
  #stores .page-stores .store-item-container .store-description address {
    margin-bottom: 0.5rem;
  }

  #stores .page-stores .store-item-footer {
    display: block;
  }

  #stores .page-stores .store-item-footer.divide-top {
    border-top: 1px solid #f1f1f1;
  }

  #stores .page-stores .store-item-footer li {
    margin-bottom: 0.625rem;
  }

  #stores .page-stores .store-item-footer .card-block {
    padding: 0.75rem 0.75rem 0;
  }
}

.block_newsletter {
  font-size: 0.875rem;
  margin-bottom: 0.625rem;
}

.block_newsletter form {
  position: relative;
}

.block_newsletter form input[type='text'] {
  border: none;
  padding: 10px;
  min-width: 255px;
  color: #7a7a7a;
  border: 1px solid rgba(0, 0, 0, 0.25);
}

.block_newsletter form input[type='text']:focus {
  outline: 3px solid #2fb5d2;
  color: #232323;
  background: #fff;
}

.block_newsletter form input[type='text']:focus + button .search {
  color: #2fb5d2;
}

.block_newsletter form button[type='submit'] {
  position: absolute;
  background: none;
  border: none;
  bottom: 0.3125rem;
  right: 0.125rem;
  color: #7a7a7a;
}

.block_newsletter form button[type='submit'] .search:hover {
  color: #2fb5d2;
}

.block_newsletter p {
  padding-top: 0.625rem;
}

.block_newsletter form .input-wrapper {
  overflow: hidden;
}

.block_newsletter form input[type='text'] {
  padding: 11px;
  width: 100%;
}

.block_newsletter form input[type='text']:focus {
  border: 3px solid #2fb5d2;
  padding: 8px 8px 9px;
  outline: 0;
}

.block_newsletter form input {
  height: 42px;
  box-shadow: none;
}

.block-contact {
  border-left: 2px solid #f1f1f1;
  padding-left: 3rem;
  color: #7a7a7a;
}

.block-contact .block-contact-title,
.linklist .blockcms-title a {
  color: #232323;
}

.account-list a {
  color: #7a7a7a;
}

.account-list a:hover {
  color: #2fb5d2;
}

.block-contact-title,
.blockcms-title,
.myaccount-title,
.myaccount-title a {
  font-weight: 700;
  font-size: 1rem;
}

.block-social {
  text-align: right;
}

.block-social li {
  height: 2.5rem;
  width: 2.5rem;
  background-color: #f1f1f1;
  background-repeat: no-repeat;
  display: inline-block;
  margin: 0.125rem;
  cursor: pointer;
}

.block-social li:hover {
  background-color: #2fb5d2;
}

.block-social li a {
  display: block;
  height: 100%;
  white-space: nowrap;
  text-indent: 100%;
  overflow: hidden;
}

.block-social li a:hover {
  color: transparent;
}

.facebook {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDE5LjEuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkNhbHF1ZV8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB3aWR0aD0iNDBweCIgaGVpZ2h0PSI0MHB4IiB2aWV3Qm94PSIwIDAgNDAgNDAiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDQwIDQwIiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPGc+Cgk8aW1hZ2Ugb3ZlcmZsb3c9InZpc2libGUiIG9wYWNpdHk9IjAuMSIgd2lkdGg9IjI2IiBoZWlnaHQ9IjQyIiB4bGluazpocmVmPSJENzk1Q0EyOS5wbmciICB0cmFuc2Zvcm09Im1hdHJpeCgxIDAgMCAxIDggMCkiPgoJPC9pbWFnZT4KCTxnPgoJCTxwYXRoIGZpbGw9IiNGRkZGRkYiIGQ9Ik0yMi4yLDI3LjJ2LTcuMmgyYzEuNSwwLDIsMCwyLTAuMWMwLTAuMSwwLjEtMSwwLjItMi4xYzAuMS0xLjEsMC4yLTIuMiwwLjItMi40bDAtMC40bC0yLjIsMGwtMi4yLDAKCQkJbDAtMS42YzAtMC45LDAuMS0xLjgsMC4yLTEuOWMwLjItMC41LDAuNy0wLjcsMi42LTAuN2gxLjdWOC4zVjUuOEgyNGMtMywwLTMuOCwwLjEtNSwwLjdjLTAuOCwwLjQtMS42LDEuMi0yLDEuOQoJCQljLTAuNSwxLjEtMC43LDEuOC0wLjcsNC4zTDE2LjIsMTVoLTEuNWgtMS41djIuNXYyLjVoMS41aDEuNXY3LjJ2Ny4yaDNoM1YyNy4yeiIvPgoJPC9nPgo8L2c+Cjwvc3ZnPgo=);
}

.facebook:before {
  content: '';
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDE5LjEuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkNhbHF1ZV8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB3aWR0aD0iNDBweCIgaGVpZ2h0PSI0MHB4IiB2aWV3Qm94PSIwIDAgNDAgNDAiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDQwIDQwIiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPGc+Cgk8aW1hZ2Ugb3ZlcmZsb3c9InZpc2libGUiIG9wYWNpdHk9IjAuMSIgd2lkdGg9IjI2IiBoZWlnaHQ9IjQyIiB4bGluazpocmVmPSJENzk1Q0EyOS5wbmciICB0cmFuc2Zvcm09Im1hdHJpeCgxIDAgMCAxIDggMCkiPgoJPC9pbWFnZT4KCTxnPgoJCTxwYXRoIGZpbGw9IiMyZmI1ZDIiIGQ9Ik0yMi4yLDI3LjJ2LTcuMmgyYzEuNSwwLDIsMCwyLTAuMWMwLTAuMSwwLjEtMSwwLjItMi4xYzAuMS0xLjEsMC4yLTIuMiwwLjItMi40bDAtMC40bC0yLjIsMGwtMi4yLDAKCQkJbDAtMS42YzAtMC45LDAuMS0xLjgsMC4yLTEuOWMwLjItMC41LDAuNy0wLjcsMi42LTAuN2gxLjdWOC4zVjUuOEgyNGMtMywwLTMuOCwwLjEtNSwwLjdjLTAuOCwwLjQtMS42LDEuMi0yLDEuOQoJCQljLTAuNSwxLjEtMC43LDEuOC0wLjcsNC4zTDE2LjIsMTVoLTEuNWgtMS41djIuNXYyLjVoMS41aDEuNXY3LjJ2Ny4yaDNoM1YyNy4yeiIvPgoJPC9nPgo8L2c+Cjwvc3ZnPgo=);
}

.facebook.icon-gray {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iNDVweCIgaGVpZ2h0PSI0NXB4IiB2aWV3Qm94PSIwIDAgNDUgNDUiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDQyICgzNjc4MSkgLSBodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2ggLS0+CiAgICA8dGl0bGU+QXJ0Ym9hcmQgMzwvdGl0bGU+CiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4KICAgIDxkZWZzPjwvZGVmcz4KICAgIDxnIGlkPSJQYWdlLTEiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxnIGlkPSJBcnRib2FyZC0zIiBmaWxsLXJ1bGU9Im5vbnplcm8iIGZpbGw9IiM3QTdBN0EiPgogICAgICAgICAgICA8ZyBpZD0ic3ZnK3htbCIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMTYuMDAwMDAwLCA4LjAwMDAwMCkiPgogICAgICAgICAgICAgICAgPGcgaWQ9IkNhbHF1ZV8xIj4KICAgICAgICAgICAgICAgICAgICA8ZyBpZD0iR3JvdXAiPgogICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPSJNOS4yLDIyLjIgTDkuMiwxNSBMMTEuMiwxNSBDMTIuNywxNSAxMy4yLDE1IDEzLjIsMTQuOSBDMTMuMiwxNC44IDEzLjMsMTMuOSAxMy40LDEyLjggQzEzLjUsMTEuNyAxMy42LDEwLjYgMTMuNiwxMC40IEwxMy42LDEwIEwxMS40LDEwIEw5LjIsMTAgTDkuMiw4LjQgQzkuMiw3LjUgOS4zLDYuNiA5LjQsNi41IEM5LjYsNiAxMC4xLDUuOCAxMiw1LjggTDEzLjcsNS44IEwxMy43LDMuMyBMMTMuNywwLjggTDExLDAuOCBDOCwwLjggNy4yLDAuOSA2LDEuNSBDNS4yLDEuOSA0LjQsMi43IDQsMy40IEMzLjUsNC41IDMuMyw1LjIgMy4zLDcuNyBMMy4yLDEwIEwxLjcsMTAgTDAuMiwxMCBMMC4yLDEyLjUgTDAuMiwxNSBMMS43LDE1IEwzLjIsMTUgTDMuMiwyMi4yIEwzLjIsMjkuNCBMNi4yLDI5LjQgTDkuMiwyOS40IEw5LjIsMjIuMiBaIiBpZD0iU2hhcGUiPjwvcGF0aD4KICAgICAgICAgICAgICAgICAgICA8L2c+CiAgICAgICAgICAgICAgICA8L2c+CiAgICAgICAgICAgIDwvZz4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==);
}

.facebook.icon-gray:hover {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDE5LjEuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkNhbHF1ZV8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB3aWR0aD0iNDBweCIgaGVpZ2h0PSI0MHB4IiB2aWV3Qm94PSIwIDAgNDAgNDAiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDQwIDQwIiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPGc+Cgk8aW1hZ2Ugb3ZlcmZsb3c9InZpc2libGUiIG9wYWNpdHk9IjAuMSIgd2lkdGg9IjI2IiBoZWlnaHQ9IjQyIiB4bGluazpocmVmPSJENzk1Q0EyOS5wbmciICB0cmFuc2Zvcm09Im1hdHJpeCgxIDAgMCAxIDggMCkiPgoJPC9pbWFnZT4KCTxnPgoJCTxwYXRoIGZpbGw9IiMyZmI1ZDIiIGQ9Ik0yMi4yLDI3LjJ2LTcuMmgyYzEuNSwwLDIsMCwyLTAuMWMwLTAuMSwwLjEtMSwwLjItMi4xYzAuMS0xLjEsMC4yLTIuMiwwLjItMi40bDAtMC40bC0yLjIsMGwtMi4yLDAKCQkJbDAtMS42YzAtMC45LDAuMS0xLjgsMC4yLTEuOWMwLjItMC41LDAuNy0wLjcsMi42LTAuN2gxLjdWOC4zVjUuOEgyNGMtMywwLTMuOCwwLjEtNSwwLjdjLTAuOCwwLjQtMS42LDEuMi0yLDEuOQoJCQljLTAuNSwxLjEtMC43LDEuOC0wLjcsNC4zTDE2LjIsMTVoLTEuNWgtMS41djIuNXYyLjVoMS41aDEuNXY3LjJ2Ny4yaDNoM1YyNy4yeiIvPgoJPC9nPgo8L2c+Cjwvc3ZnPgo=);
}

.twitter {
  background-image: url(data:image/svg+xml;base64,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);
}

.twitter:before {
  content: '';
  background-image: url(data:image/svg+xml;base64,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);
}

.twitter.icon-gray {
  background-image: url(../css/e049aeb07a2ae1627933e8e58d3886d2.svg);
}

.twitter.icon-gray:hover {
  background-image: url(data:image/svg+xml;base64,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);
}

.rss {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDE5LjEuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkNhbHF1ZV8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB3aWR0aD0iNDBweCIgaGVpZ2h0PSI0MHB4IiB2aWV3Qm94PSIwIDAgNDAgNDAiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDQwIDQwIiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPGc+Cgk8aW1hZ2Ugb3ZlcmZsb3c9InZpc2libGUiIG9wYWNpdHk9IjAuMSIgd2lkdGg9IjQyIiBoZWlnaHQ9IjQyIiB4bGluazpocmVmPSI5NzBCMDdEMC5wbmciID4KCTwvaW1hZ2U+Cgk8Zz4KCQk8cGF0aCBmaWxsPSIjRkZGRkZGIiBkPSJNNS45LDMwLjRjMC0xLDAuNC0xLjksMS4xLTIuN2MwLjctMC43LDEuNi0xLjEsMi43LTEuMXMxLjksMC40LDIuNywxLjFjMC43LDAuNywxLjEsMS42LDEuMSwyLjcKCQkJYzAsMS4xLTAuNCwyLTEuMSwyLjdzLTEuNiwxLjEtMi43LDEuMVM3LjgsMzMuOCw3LDMzLjFDNi4zLDMyLjMsNS45LDMxLjQsNS45LDMwLjR6IE01LjksMjAuOXYtNS40YzMuNCwwLDYuNSwwLjgsOS40LDIuNQoJCQlzNS4yLDQsNi44LDYuOGMxLjcsMi45LDIuNSw2LDIuNSw5LjRoLTUuNWMwLTMuNy0xLjMtNi44LTMuOS05LjRDMTIuNywyMi4yLDkuNiwyMC45LDUuOSwyMC45eiBNNS45LDExLjJWNS44CgkJCWMzLjgsMCw3LjUsMC44LDExLDIuM3M2LjUsMy41LDkuMSw2LjFzNC42LDUuNiw2LjEsOS4xYzEuNSwzLjUsMi4zLDcuMiwyLjMsMTFoLTUuNWMwLTMuMS0wLjYtNi4xLTEuOC04LjkKCQkJYy0xLjItMi44LTIuOC01LjMtNC45LTcuM3MtNC41LTMuNy03LjMtNC45UzksMTEuMiw1LjksMTEuMnoiLz4KCTwvZz4KPC9nPgo8L3N2Zz4K);
}

.youtube {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDE5LjEuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkNhbHF1ZV8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB3aWR0aD0iNDBweCIgaGVpZ2h0PSI0MHB4IiB2aWV3Qm94PSIwIDAgNDAgNDAiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDQwIDQwIiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPGc+Cgk8aW1hZ2Ugb3ZlcmZsb3c9InZpc2libGUiIG9wYWNpdHk9IjAuMSIgd2lkdGg9IjM5IiBoZWlnaHQ9IjM5IiB4bGluazpocmVmPSI1Q0JGNDQ5Qy5wbmciICB0cmFuc2Zvcm09Im1hdHJpeCgxIDAgMCAxIDIgMSkiPgoJPC9pbWFnZT4KCTxnPgoJCTxnPgoJCQk8Zz4KCQkJCTxwYXRoIGZpbGw9IiNGRkZGRkYiIGQ9Ik0yMi45LDIyYy0wLjQsMC0wLjgsMC4yLTEuMiwwLjdWMjBoLTEuMXY4LjRoMS4xdi0wLjZjMC40LDAuNSwwLjgsMC43LDEuMiwwLjdjMC41LDAsMC44LTAuMiwwLjktMC43CgkJCQkJYzAuMS0wLjIsMC4xLTAuNywwLjEtMS4yVjI0YzAtMC42LDAtMS0wLjEtMS4zQzIzLjcsMjIuMywyMy40LDIyLDIyLjksMjJ6IE0yMi44LDI2LjZjMCwwLjYtMC4yLDAuOC0wLjUsMC44CgkJCQkJYy0wLjIsMC0wLjQtMC4xLTAuNi0wLjN2LTMuOGMwLjItMC4yLDAuNC0wLjMsMC42LTAuM2MwLjMsMCwwLjUsMC4zLDAuNSwwLjlWMjYuNnoiLz4KCQkJCTxwYXRoIGZpbGw9IiNGRkZGRkYiIGQ9Ik0yNi41LDIyYy0wLjYsMC0xLDAuMi0xLjQsMC42Yy0wLjIsMC4zLTAuMywwLjgtMC4zLDEuNXYyLjJjMCwwLjcsMC4xLDEuMiwwLjQsMS41CgkJCQkJYzAuMywwLjQsMC44LDAuNiwxLjQsMC42YzAuNiwwLDEuMS0wLjIsMS40LTAuN2MwLjEtMC4yLDAuMi0wLjQsMC4zLTAuN2MwLTAuMiwwLTAuNCwwLTAuN3YtMC4yaC0xLjJjMCwwLjEsMCwwLjIsMCwwLjQKCQkJCQlzMCwwLjIsMCwwLjNjMCwwLjEsMCwwLjEsMCwwLjFjLTAuMSwwLjMtMC4yLDAuNC0wLjUsMC40Yy0wLjQsMC0wLjYtMC4zLTAuNi0wLjl2LTEuMWgyLjN2LTEuM2MwLTAuNy0wLjEtMS4xLTAuNC0xLjUKCQkJCQlDMjcuNiwyMi4yLDI3LjEsMjIsMjYuNSwyMnogTTI3LjEsMjQuNUgyNnYtMC42YzAtMC42LDAuMi0wLjksMC42LTAuOWMwLjQsMCwwLjYsMC4zLDAuNiwwLjlWMjQuNUwyNy4xLDI0LjV6Ii8+CgkJCQk8cGF0aCBmaWxsPSIjRkZGRkZGIiBkPSJNMTguNSwyNi45Yy0wLjIsMC40LTAuNSwwLjUtMC43LDAuNWMtMC4yLDAtMC4yLTAuMS0wLjMtMC4zYzAsMCwwLTAuMiwwLTAuNHYtNC42aC0xLjFWMjcKCQkJCQljMCwwLjQsMCwwLjcsMC4xLDAuOWMwLjEsMC4zLDAuMywwLjUsMC43LDAuNWMwLjQsMCwwLjktMC4zLDEuMy0wLjh2MC43aDEuMXYtNi4zaC0xLjFMMTguNSwyNi45TDE4LjUsMjYuOUwxOC41LDI2Ljl6Ii8+CgkJCQk8cGF0aCBmaWxsPSIjRkZGRkZGIiBkPSJNMjAsMTUuN2MwLjQsMCwwLjUtMC4zLDAuNS0wLjl2LTIuN2MwLTAuNi0wLjItMC45LTAuNS0wLjljLTAuNCwwLTAuNSwwLjMtMC41LDAuOXYyLjcKCQkJCQlDMTkuNCwxNS40LDE5LjYsMTUuNywyMCwxNS43eiIvPgoJCQkJPHBvbHlnb24gZmlsbD0iI0ZGRkZGRiIgcG9pbnRzPSIxMi4zLDIxLjEgMTMuNiwyMS4xIDEzLjYsMjguNCAxNC45LDI4LjQgMTQuOSwyMS4xIDE2LjIsMjEuMSAxNi4yLDIwIDEyLjMsMjAgCQkJCSIvPgoJCQkJPHBhdGggZmlsbD0iI0ZGRkZGRiIgZD0iTTI4LjQsNi41SDEyLjFDMTAuNyw2LjUsOS42LDcsOC42LDhjLTEsMS0xLjQsMi4xLTEuNCwzLjV2MTYuNGMwLDEuNCwwLjUsMi41LDEuNCwzLjUKCQkJCQljMSwxLDIuMSwxLjQsMy41LDEuNGgxNi40YzEuNCwwLDIuNS0wLjUsMy41LTEuNGMxLTEsMS40LTIuMSwxLjQtMy41VjExLjVjMC0xLjQtMC41LTIuNS0xLjQtMy41QzMwLjksNywyOS44LDYuNSwyOC40LDYuNXoKCQkJCQkgTTIyLjYsMTAuM2gxLjFWMTVjMCwwLjMsMCwwLjQsMCwwLjRjMCwwLjIsMC4xLDAuMywwLjMsMC4zYzAuMiwwLDAuNS0wLjIsMC43LTAuNXYtNC44aDEuMXY2LjNoLTEuMVYxNgoJCQkJCWMtMC41LDAuNS0wLjksMC44LTEuMywwLjhjLTAuNCwwLTAuNi0wLjItMC43LTAuNWMtMC4xLTAuMi0wLjEtMC41LTAuMS0wLjlMMjIuNiwxMC4zTDIyLjYsMTAuM3ogTTE4LjMsMTIuNAoJCQkJCWMwLTAuNywwLjEtMS4yLDAuNC0xLjVjMC4zLTAuNCwwLjctMC42LDEuMy0wLjZjMC42LDAsMSwwLjIsMS4zLDAuNmMwLjIsMC4zLDAuNCwwLjgsMC40LDEuNXYyLjJjMCwwLjctMC4xLDEuMi0wLjQsMS41CgkJCQkJYy0wLjMsMC40LTAuOCwwLjYtMS4zLDAuNmMtMC42LDAtMS0wLjItMS4zLTAuNmMtMC4yLTAuMy0wLjQtMC44LTAuNC0xLjVMMTguMywxMi40TDE4LjMsMTIuNHogTTE1LjIsOC4ybDAuOSwzLjNMMTcsOC4yaDEuMwoJCQkJCWwtMS41LDV2My40aC0xLjN2LTMuNGMtMC4xLTAuNi0wLjQtMS41LTAuOC0yLjdjLTAuMS0wLjMtMC4yLTAuNy0wLjQtMS4yYy0wLjItMC41LTAuMy0wLjktMC40LTEuMkwxNS4yLDguMkwxNS4yLDguMnoKCQkJCQkgTTI5LjMsMjguN2MtMC4xLDAuNS0wLjQsMC45LTAuNywxLjJjLTAuNCwwLjMtMC44LDAuNS0xLjMsMC42Yy0xLjYsMC4yLTMuOSwwLjMtNywwLjNjLTMuMSwwLTUuNS0wLjEtNy0wLjMKCQkJCQljLTAuNS0wLjEtMC45LTAuMy0xLjMtMC42Yy0wLjQtMC4zLTAuNi0wLjgtMC43LTEuMmMtMC4yLTEtMC4zLTIuNC0wLjMtNC40YzAtMiwwLjEtMy40LDAuMy00LjRjMC4xLTAuNSwwLjQtMC45LDAuNy0xLjMKCQkJCQljMC40LTAuMywwLjgtMC41LDEuMy0wLjZjMS41LTAuMiwzLjktMC4zLDctMC4zYzMuMSwwLDUuNSwwLjEsNywwLjNjMC41LDAuMSwwLjksMC4zLDEuMywwLjZjMC40LDAuMywwLjYsMC44LDAuNywxLjMKCQkJCQljMC4yLDAuOSwwLjMsMi40LDAuMyw0LjRDMjkuNiwyNi4zLDI5LjUsMjcuOCwyOS4zLDI4Ljd6Ii8+CgkJCTwvZz4KCQk8L2c+Cgk8L2c+CjwvZz4KPC9zdmc+Cg==);
}

.googleplus {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDE5LjEuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkNhbHF1ZV8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB3aWR0aD0iNDBweCIgaGVpZ2h0PSI0MHB4IiB2aWV3Qm94PSIwIDAgNDAgNDAiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDQwIDQwIiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPGc+Cgk8aW1hZ2Ugb3ZlcmZsb3c9InZpc2libGUiIG9wYWNpdHk9IjAuMSIgd2lkdGg9IjQ2IiBoZWlnaHQ9IjM0IiB4bGluazpocmVmPSJDRTYxRDA0Qi5wbmciICB0cmFuc2Zvcm09Im1hdHJpeCgxIDAgMCAxIC0yIDQpIj4KCTwvaW1hZ2U+Cgk8Zz4KCQk8Zz4KCQkJPHBhdGggZmlsbD0iI0ZGRkZGRiIgZD0iTTE0LDE4LjF2NC4yYzAsMCw0LDAsNS43LDBjLTAuOSwyLjctMi4zLDQuMi01LjcsNC4yYy0zLjQsMC02LjEtMi44LTYuMS02LjJTMTAuNSwxNCwxNCwxNAoJCQkJYzEuOCwwLDMsMC42LDQuMSwxLjVjMC45LTAuOSwwLjgtMSwzLTMuMWMtMS45LTEuNy00LjMtMi43LTcuMS0yLjdjLTUuOCwwLTEwLjUsNC43LTEwLjUsMTAuNUMzLjUsMjYsOC4yLDMwLjcsMTQsMzAuNwoJCQkJYzguNywwLDEwLjgtNy41LDEwLjEtMTIuNkMyMiwxOC4xLDE0LDE4LjEsMTQsMTguMXogTTMyLjksMTguNHYtMy42aC0yLjZ2My42aC0zLjd2Mi42aDMuN3YzLjdoMi42di0zLjdoMy42di0yLjZIMzIuOXoiLz4KCQk8L2c+Cgk8L2c+CjwvZz4KPC9zdmc+Cg==);
}

.googleplus:before {
  content: '';
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDE5LjEuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkNhbHF1ZV8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB3aWR0aD0iNDBweCIgaGVpZ2h0PSI0MHB4IiB2aWV3Qm94PSIwIDAgNDAgNDAiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDQwIDQwIiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPGc+Cgk8aW1hZ2Ugb3ZlcmZsb3c9InZpc2libGUiIG9wYWNpdHk9IjAuMSIgd2lkdGg9IjQ2IiBoZWlnaHQ9IjM0IiB4bGluazpocmVmPSJDRTYxRDA0Qi5wbmciICB0cmFuc2Zvcm09Im1hdHJpeCgxIDAgMCAxIC0yIDQpIj4KCTwvaW1hZ2U+Cgk8Zz4KCQk8Zz4KCQkJPHBhdGggZmlsbD0iIzJmYjVkMiIgZD0iTTE0LDE4LjF2NC4yYzAsMCw0LDAsNS43LDBjLTAuOSwyLjctMi4zLDQuMi01LjcsNC4yYy0zLjQsMC02LjEtMi44LTYuMS02LjJTMTAuNSwxNCwxNCwxNAoJCQkJYzEuOCwwLDMsMC42LDQuMSwxLjVjMC45LTAuOSwwLjgtMSwzLTMuMWMtMS45LTEuNy00LjMtMi43LTcuMS0yLjdjLTUuOCwwLTEwLjUsNC43LTEwLjUsMTAuNUMzLjUsMjYsOC4yLDMwLjcsMTQsMzAuNwoJCQkJYzguNywwLDEwLjgtNy41LDEwLjEtMTIuNkMyMiwxOC4xLDE0LDE4LjEsMTQsMTguMXogTTMyLjksMTguNHYtMy42aC0yLjZ2My42aC0zLjd2Mi42aDMuN3YzLjdoMi42di0zLjdoMy42di0yLjZIMzIuOXoiLz4KCQk8L2c+Cgk8L2c+CjwvZz4KPC9zdmc+Cg==);
}

.googleplus.icon-gray {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iNDVweCIgaGVpZ2h0PSI0NXB4IiB2aWV3Qm94PSIwIDAgNDUgNDUiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDQyICgzNjc4MSkgLSBodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2ggLS0+CiAgICA8dGl0bGU+QXJ0Ym9hcmQgMyBDb3B5PC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPgogICAgPGRlZnM+PC9kZWZzPgogICAgPGcgaWQ9IlBhZ2UtMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9IkFydGJvYXJkLTMtQ29weSIgZmlsbC1ydWxlPSJub256ZXJvIiBmaWxsPSIjN0E3QTdBIj4KICAgICAgICAgICAgPGcgaWQ9InN2Zyt4bWwiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDYuMDAwMDAwLCAxMi4wMDAwMDApIj4KICAgICAgICAgICAgICAgIDxnIGlkPSJDYWxxdWVfMSI+CiAgICAgICAgICAgICAgICAgICAgPGcgaWQ9Ikdyb3VwIj4KICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZD0iTTExLDkuMSBMMTEsMTMuMyBMMTYuNywxMy4zIEMxNS44LDE2IDE0LjQsMTcuNSAxMSwxNy41IEM3LjYsMTcuNSA0LjksMTQuNyA0LjksMTEuMyBDNC45LDcuOSA3LjUsNSAxMSw1IEMxMi44LDUgMTQsNS42IDE1LjEsNi41IEMxNiw1LjYgMTUuOSw1LjUgMTguMSwzLjQgQzE2LjIsMS43IDEzLjgsMC43IDExLDAuNyBDNS4yLDAuNyAwLjUsNS40IDAuNSwxMS4yIEMwLjUsMTcgNS4yLDIxLjcgMTEsMjEuNyBDMTkuNywyMS43IDIxLjgsMTQuMiAyMS4xLDkuMSBMMTEsOS4xIFogTTI5LjksOS40IEwyOS45LDUuOCBMMjcuMyw1LjggTDI3LjMsOS40IEwyMy42LDkuNCBMMjMuNiwxMiBMMjcuMywxMiBMMjcuMywxNS43IEwyOS45LDE1LjcgTDI5LjksMTIgTDMzLjUsMTIgTDMzLjUsOS40IEwyOS45LDkuNCBaIiBpZD0iU2hhcGUiPjwvcGF0aD4KICAgICAgICAgICAgICAgICAgICA8L2c+CiAgICAgICAgICAgICAgICA8L2c+CiAgICAgICAgICAgIDwvZz4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==);
}

.googleplus.icon-gray:hover {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDE5LjEuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkNhbHF1ZV8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB3aWR0aD0iNDBweCIgaGVpZ2h0PSI0MHB4IiB2aWV3Qm94PSIwIDAgNDAgNDAiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDQwIDQwIiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPGc+Cgk8aW1hZ2Ugb3ZlcmZsb3c9InZpc2libGUiIG9wYWNpdHk9IjAuMSIgd2lkdGg9IjQ2IiBoZWlnaHQ9IjM0IiB4bGluazpocmVmPSJDRTYxRDA0Qi5wbmciICB0cmFuc2Zvcm09Im1hdHJpeCgxIDAgMCAxIC0yIDQpIj4KCTwvaW1hZ2U+Cgk8Zz4KCQk8Zz4KCQkJPHBhdGggZmlsbD0iIzJmYjVkMiIgZD0iTTE0LDE4LjF2NC4yYzAsMCw0LDAsNS43LDBjLTAuOSwyLjctMi4zLDQuMi01LjcsNC4yYy0zLjQsMC02LjEtMi44LTYuMS02LjJTMTAuNSwxNCwxNCwxNAoJCQkJYzEuOCwwLDMsMC42LDQuMSwxLjVjMC45LTAuOSwwLjgtMSwzLTMuMWMtMS45LTEuNy00LjMtMi43LTcuMS0yLjdjLTUuOCwwLTEwLjUsNC43LTEwLjUsMTAuNUMzLjUsMjYsOC4yLDMwLjcsMTQsMzAuNwoJCQkJYzguNywwLDEwLjgtNy41LDEwLjEtMTIuNkMyMiwxOC4xLDE0LDE4LjEsMTQsMTguMXogTTMyLjksMTguNHYtMy42aC0yLjZ2My42aC0zLjd2Mi42aDMuN3YzLjdoMi42di0zLjdoMy42di0yLjZIMzIuOXoiLz4KCQk8L2c+Cgk8L2c+CjwvZz4KPC9zdmc+Cg==);
}

#block_myaccount_infos .myaccount-title a {
  color: #232323;
}

.pinterest {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDE5LjEuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkNhbHF1ZV8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB3aWR0aD0iNDBweCIgaGVpZ2h0PSI0MHB4IiB2aWV3Qm94PSIwIDAgNDAgNDAiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDQwIDQwIiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPGc+Cgk8aW1hZ2Ugb3ZlcmZsb3c9InZpc2libGUiIG9wYWNpdHk9IjAuMSIgd2lkdGg9IjM4IiBoZWlnaHQ9IjQ2IiB4bGluazpocmVmPSI4REY2NkQ0Qi5wbmciICB0cmFuc2Zvcm09Im1hdHJpeCgxIDAgMCAxIDIgLTEpIj4KCTwvaW1hZ2U+Cgk8Zz4KCQk8Zz4KCQkJPHBhdGggZmlsbD0iI0ZGRkZGRiIgZD0iTTE4LjcsNS4xQzEzLjQsNS42LDguMSwxMCw3LjgsMTYuMWMtMC4xLDMuOCwwLjksNi42LDQuNSw3LjRjMS42LTIuNy0wLjUtMy4zLTAuOC01LjMKCQkJCWMtMS4zLTguMSw5LjQtMTMuNywxNS04YzMuOSwzLjksMS4zLDE2LTQuOSwxNC44Yy02LTEuMiwyLjktMTAuOC0xLjgtMTIuN2MtMy45LTEuNS01LjksNC43LTQuMSw3LjhjLTEuMSw1LjMtMy40LDEwLjMtMi41LDE3CgkJCQljMy4xLTIuMiw0LjEtNi41LDQuOS0xMC45YzEuNSwwLjksMi40LDEuOSw0LjMsMi4xYzcuMiwwLjYsMTEuMi03LjIsMTAuMy0xNC40QzMxLjgsNy41LDI1LjUsNC4zLDE4LjcsNS4xeiIvPgoJCTwvZz4KCTwvZz4KPC9nPgo8L3N2Zz4K);
}

.pinterest:before {
  content: '';
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDE5LjEuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkNhbHF1ZV8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB3aWR0aD0iNDBweCIgaGVpZ2h0PSI0MHB4IiB2aWV3Qm94PSIwIDAgNDAgNDAiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDQwIDQwIiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPGc+Cgk8aW1hZ2Ugb3ZlcmZsb3c9InZpc2libGUiIG9wYWNpdHk9IjAuMSIgd2lkdGg9IjM4IiBoZWlnaHQ9IjQ2IiB4bGluazpocmVmPSI4REY2NkQ0Qi5wbmciICB0cmFuc2Zvcm09Im1hdHJpeCgxIDAgMCAxIDIgLTEpIj4KCTwvaW1hZ2U+Cgk8Zz4KCQk8Zz4KCQkJPHBhdGggZmlsbD0iIzJmYjVkMiIgZD0iTTE4LjcsNS4xQzEzLjQsNS42LDguMSwxMCw3LjgsMTYuMWMtMC4xLDMuOCwwLjksNi42LDQuNSw3LjRjMS42LTIuNy0wLjUtMy4zLTAuOC01LjMKCQkJCWMtMS4zLTguMSw5LjQtMTMuNywxNS04YzMuOSwzLjksMS4zLDE2LTQuOSwxNC44Yy02LTEuMiwyLjktMTAuOC0xLjgtMTIuN2MtMy45LTEuNS01LjksNC43LTQuMSw3LjhjLTEuMSw1LjMtMy40LDEwLjMtMi41LDE3CgkJCQljMy4xLTIuMiw0LjEtNi41LDQuOS0xMC45YzEuNSwwLjksMi40LDEuOSw0LjMsMi4xYzcuMiwwLjYsMTEuMi03LjIsMTAuMy0xNC40QzMxLjgsNy41LDI1LjUsNC4zLDE4LjcsNS4xeiIvPgoJCTwvZz4KCTwvZz4KPC9nPgo8L3N2Zz4K);
}

.pinterest.icon-gray {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iNDVweCIgaGVpZ2h0PSI0NXB4IiB2aWV3Qm94PSIwIDAgNDUgNDUiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDQyICgzNjc4MSkgLSBodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2ggLS0+CiAgICA8dGl0bGU+QXJ0Ym9hcmQgMyBDb3B5IDI8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZGVmcz48L2RlZnM+CiAgICA8ZyBpZD0iUGFnZS0xIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8ZyBpZD0iQXJ0Ym9hcmQtMy1Db3B5LTIiIGZpbGwtcnVsZT0ibm9uemVybyIgZmlsbD0iIzdBN0E3QSI+CiAgICAgICAgICAgIDxnIGlkPSJzdmcreG1sIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgxMC4wMDAwMDAsIDYuMDAwMDAwKSI+CiAgICAgICAgICAgICAgICA8ZyBpZD0iQ2FscXVlXzEiPgogICAgICAgICAgICAgICAgICAgIDxnIGlkPSJHcm91cCI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9Ik0xMS43LDEuMSBDNi40LDEuNiAxLjEsNiAwLjgsMTIuMSBDMC43LDE1LjkgMS43LDE4LjcgNS4zLDE5LjUgQzYuOSwxNi44IDQuOCwxNi4yIDQuNSwxNC4yIEMzLjIsNi4xIDEzLjksMC41IDE5LjUsNi4yIEMyMy40LDEwLjEgMjAuOCwyMi4yIDE0LjYsMjEgQzguNiwxOS44IDE3LjUsMTAuMiAxMi44LDguMyBDOC45LDYuOCA2LjksMTMgOC43LDE2LjEgQzcuNiwyMS40IDUuMywyNi40IDYuMiwzMy4xIEM5LjMsMzAuOSAxMC4zLDI2LjYgMTEuMSwyMi4yIEMxMi42LDIzLjEgMTMuNSwyNC4xIDE1LjQsMjQuMyBDMjIuNiwyNC45IDI2LjYsMTcuMSAyNS43LDkuOSBDMjQuOCwzLjUgMTguNSwwLjMgMTEuNywxLjEgWiIgaWQ9IlNoYXBlIj48L3BhdGg+CiAgICAgICAgICAgICAgICAgICAgPC9nPgogICAgICAgICAgICAgICAgPC9nPgogICAgICAgICAgICA8L2c+CiAgICAgICAgPC9nPgogICAgPC9nPgo8L3N2Zz4=);
}

.pinterest.icon-gray:hover {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDE5LjEuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkNhbHF1ZV8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB3aWR0aD0iNDBweCIgaGVpZ2h0PSI0MHB4IiB2aWV3Qm94PSIwIDAgNDAgNDAiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDQwIDQwIiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPGc+Cgk8aW1hZ2Ugb3ZlcmZsb3c9InZpc2libGUiIG9wYWNpdHk9IjAuMSIgd2lkdGg9IjM4IiBoZWlnaHQ9IjQ2IiB4bGluazpocmVmPSI4REY2NkQ0Qi5wbmciICB0cmFuc2Zvcm09Im1hdHJpeCgxIDAgMCAxIDIgLTEpIj4KCTwvaW1hZ2U+Cgk8Zz4KCQk8Zz4KCQkJPHBhdGggZmlsbD0iIzJmYjVkMiIgZD0iTTE4LjcsNS4xQzEzLjQsNS42LDguMSwxMCw3LjgsMTYuMWMtMC4xLDMuOCwwLjksNi42LDQuNSw3LjRjMS42LTIuNy0wLjUtMy4zLTAuOC01LjMKCQkJCWMtMS4zLTguMSw5LjQtMTMuNywxNS04YzMuOSwzLjksMS4zLDE2LTQuOSwxNC44Yy02LTEuMiwyLjktMTAuOC0xLjgtMTIuN2MtMy45LTEuNS01LjksNC43LTQuMSw3LjhjLTEuMSw1LjMtMy40LDEwLjMtMi41LDE3CgkJCQljMy4xLTIuMiw0LjEtNi41LDQuOS0xMC45YzEuNSwwLjksMi40LDEuOSw0LjMsMi4xYzcuMiwwLjYsMTEuMi03LjIsMTAuMy0xNC40QzMxLjgsNy41LDI1LjUsNC4zLDE4LjcsNS4xeiIvPgoJCTwvZz4KCTwvZz4KPC9nPgo8L3N2Zz4K);
}

.vimeo {
  background-image: url(data:image/svg+xml;base64,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);
}

.instagram {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDE5LjEuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkNhbHF1ZV8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKCSB3aWR0aD0iNDBweCIgaGVpZ2h0PSI0MHB4IiB2aWV3Qm94PSIwIDAgNDAgNDAiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDQwIDQwIiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPGc+Cgk8aW1hZ2Ugb3ZlcmZsb3c9InZpc2libGUiIG9wYWNpdHk9IjAuMSIgd2lkdGg9IjQyIiBoZWlnaHQ9IjQyIiB4bGluazpocmVmPSIxQkEwODYyMy5wbmciID4KCTwvaW1hZ2U+Cgk8Zz4KCQk8cGF0aCBmaWxsPSIjRkZGRkZGIiBkPSJNMjguOSw1LjZIMTEuMUM4LDUuNiw1LjYsOCw1LjYsMTEuMVYxN3YxMS44YzAsMy4xLDIuNSw1LjUsNS41LDUuNWgxNy43YzMuMSwwLDUuNS0yLjUsNS41LTUuNVYxN3YtNS45CgkJCUMzNC40LDgsMzIsNS42LDI4LjksNS42eiBNMzAuNSw4LjloMC42djAuNnY0LjNoLTQuOVY4LjlIMzAuNXogTTE1LjksMTdjMC45LTEuMiwyLjUtMi4xLDQuMS0yLjFzMy4yLDAuOSw0LjEsMi4xCgkJCWMwLjYsMC45LDEsMS44LDEsM2MwLDIuOC0yLjMsNS4xLTUuMSw1LjFjLTIuNywwLTUuMS0yLjItNS4xLTUuMUMxNC45LDE4LjksMTUuMywxNy45LDE1LjksMTd6IE0zMS42LDI4LjljMCwxLjUtMS4yLDIuNy0yLjcsMi43CgkJCUgxMS4xYy0xLjUsMC0yLjctMS4yLTIuNy0yLjdWMTdoNC4zYy0wLjQsMC45LTAuNiwyLTAuNiwzYzAsNC4zLDMuNiw3LjksNy45LDcuOXM3LjktMy42LDcuOS03LjljMC0xLTAuMi0yLjEtMC42LTNoNC4zCgkJCUwzMS42LDI4LjlMMzEuNiwyOC45eiIvPgoJPC9nPgo8L2c+Cjwvc3ZnPgo=);
}

.footer-container {
  margin-top: 1.25rem;
  padding-top: 1.25rem;
  overflow: hidden;
}

.footer-container li {
  margin-bottom: 0.3125rem;
}

.footer-container li a {
  color: #fff;
  cursor: pointer;
  font-size: 0.875rem;
}

.footer-container li a:hover {
  color: #b3b3b3;
}

.links .collapse {
  display: inherit;
}

@media (max-width: 767px) {
  .block_newsletter {
    padding-bottom: 0.625rem;
    border-bottom: 1px solid #f6f6f6;
  }

  .footer-container {
    box-shadow: none;
    margin-top: 0;
  }

  .footer-container .wrapper {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  .footer-container .links .h3 {
    line-height: 1.5;
    font-size: 1rem;
  }

  .footer-container .links ul > li {
    padding: 0.3rem;
  }

  .links .collapse.in {
    display: block;
  }

  .links .title {
    padding: 0.625rem;
    cursor: pointer;
  }

  .links .title .collapse-icons .remove,
  .links .title[aria-expanded='true'] .collapse-icons .add {
    display: none;
  }

  .links .title[aria-expanded='true'] .collapse-icons .remove {
    display: block;
  }

  .links .navbar-toggler {
    display: inline-block;
    padding: 0;
  }
}

@media (max-width: 991px) {
  .block-social {
    text-align: center;
  }

  .block-contact {
    padding-left: 1.5rem;
  }
}

.contact-rich {
  color: #7a7a7a;
  margin-bottom: 2rem;
  word-wrap: break-word;
}

.contact-rich h4 {
  text-transform: uppercase;
  color: #232323;
  margin-bottom: 2rem;
}

.contact-rich .block {
  height: auto;
  overflow: hidden;
}

.contact-rich .block .icon {
  float: left;
  width: 3.5rem;
}

.contact-rich .block .icon i {
  font-size: 2rem;
}

.contact-rich .block .data {
  color: #232323;
  font-size: 0.875rem;
  width: auto;
  overflow: hidden;
}

.contact-rich .block .data.email {
  padding-top: 0.375rem;
}

.contact-form {
  background: #fff;
  padding: 1rem;
  color: #7a7a7a;
  width: 100%;
}

.contact-form h3 {
  text-transform: uppercase;
  color: #232323;
}

#pagenotfound #main .page-header,
#products #main .page-header {
  margin: 2rem 0 3rem;
}

#pagenotfound #main .page-content,
#products #main .page-content {
  margin-bottom: 10rem;
}

#pagenotfound .page-not-found,
#products .page-not-found {
  box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, 0.2);
  background: #fff;
  padding: 1rem;
  font-size: 0.875rem;
  color: #7a7a7a;
  max-width: 570px;
  margin: 0 auto;
}

#pagenotfound .page-not-found h4,
#products .page-not-found h4 {
  font-size: 1rem;
  font-weight: 700;
  margin: 0.5rem 0 1rem;
}

#pagenotfound .page-not-found .search-widget,
#products .page-not-found .search-widget {
  float: none;
}

#pagenotfound .page-not-found .search-widget input,
#products .page-not-found .search-widget input {
  width: 100%;
}

.customization-modal .modal-content {
  border-radius: 0;
  border: 1px solid #f6f6f6;
}

.customization-modal .modal-content .modal-body {
  padding-top: 0;
}

.customization-modal .modal-content .modal-body .product-customization-line {
  padding-bottom: 0.9375rem;
  padding-top: 0.9375rem;
  border-bottom: 1px solid #f6f6f6;
}

.customization-modal .modal-content .modal-body .product-customization-line .label {
  font-weight: 700;
  text-align: right;
}

.customization-modal .modal-content .modal-body .product-customization-line:last-child {
  padding-bottom: 0;
  border-bottom: 0;
}

.sitemap-title {
  text-transform: capitalize;
}

.sitemap {
  margin-top: 0.9375rem;
}

.sitemap h2 {
  color: #232323;
  text-transform: uppercase;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 1px solid #7a7a7a;
  margin-left: -15px;
  width: 100%;
  height: 35px;
}

.sitemap ul {
  margin-left: -15px;
  margin-top: 20px;
}

.sitemap ul.nested {
  margin-left: 20px;
}

.sitemap ul li {
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

@media (max-width: 575px) {
  .sitemap {
    margin-top: 0;
  }
}

#header {
  background: #fff;
  color: #7a7a7a;
  padding: 0.5rem 1%;
}

#header .logo {
  max-width: 100%;
}

#header .tts-logo {
  max-width: 100%;
  width: 50px;
  height: 50px;
  margin-left: 12px;
}

#header #logo {
  display: flex !important;
  flex-direction: row;
  justify-content: center;
}

#header #logo .logo {
  height: 50px !important;
  border-radius: 6px;
}

#header #logo,
#header #nav,
#header #languageSelection,
#header #mobileNav {
  display: inline-block;
}

#headerLeft,
#headerRight {
  display: inline-block;
}

#headerLeft {
  width: 14%;
}

#headerRight {
  width: 86%;
  text-align: right;
  float: right;
}

#header a {
  position: relative;
  color: #232323;
}

#header a:hover {
  text-decoration: none;
  color: #2fb5d2;
  justify-content: center;
  align-items: center;
}

#header .menu {
  display: inline-block;
  position: relative;
  padding: 0;
}

#header .menu > ul > li {
  display: inline-block;
  position: relative;
  padding: 0 15px;
}

#header .header-nav {
  border-bottom: 2px solid #f1f1f1;
  max-height: 50px;
  margin-bottom: 30px;
}

#header .header-nav #menu-icon {
  vertical-align: middle;
  cursor: pointer;
  margin-left: 1rem;
}

#header .header-nav #menu-icon .material-icons {
  line-height: 50px;
}

#header .header-nav .right-nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}

#header .header-nav .currency-selector {
  margin-top: 0.9375rem;
  margin-left: 0.9375rem;
  white-space: nowrap;
}

#header .header-nav .user-info {
  margin-left: 2.5rem;
  margin-top: 0.9375rem;
  text-align: right;
  white-space: nowrap;
}

#header .header-nav .user-info .account {
  margin-left: 0.625rem;
}

#header .header-nav .language-selector {
  margin-top: 0.9375rem;
  white-space: nowrap;
}

#header .header-nav .cart-preview.active {
  background: #2fb5d2;
}

#header .header-nav .cart-preview.active a,
#header .header-nav .cart-preview.active i {
  color: #fff;
}

#header .header-nav .cart-preview .shopping-cart {
  vertical-align: middle;
  color: #7a7a7a;
}

#header .header-nav .cart-preview .body {
  display: none;
}

#header .header-nav .blockcart {
  background: #f1f1f1;
  height: 3rem;
  padding: 0.75rem;
  margin-left: 0.9375rem;
  text-align: center;
  white-space: nowrap;
}

#header .header-nav .blockcart a {
  color: #7a7a7a;
}

#header .header-nav .blockcart a:hover {
  color: #2fb5d2;
}

#header .header-nav .blockcart.active a:hover {
  color: #fff;
}

#header .header-nav .blockcart .header {
  margin-top: 0.125rem;
}

#header .header-nav #_desktop_contact_link {
  display: inline-block;
}

#header .header-nav #_desktop_contact_link #contact-link {
  margin-top: 0.9375rem;
}

#header .header-nav .search-widget {
  margin-top: 0.2rem;
}

#header .header-nav .material-icons {
  line-height: inherit;
}

#header .header-nav .material-icons.expand-more {
  margin-left: -0.375rem;
}

#header .header-top {
  padding-bottom: 1.25rem;
}

#header .header-top > .container {
  position: relative;
}

#header .header-top .menu {
  padding-left: 15px;
  margin-bottom: 0.375rem;
}

#header .header-top .position-static {
  position: static;
}

#header .header-top a[data-depth='0'] {
  color: #7a7a7a;
  text-transform: uppercase;
}

#header .header-top .search-widget {
  float: right;
}

#header .top-menu-link {
  margin-left: 1.25rem;
}

.popover {
  font-family: inherit;
}

#wrapper {
  background-color: #fff;
}

#wrapper .banner {
  margin-bottom: 1.5rem;
  display: block;
}

#wrapper .banner img {
  box-shadow: 1px 1px 7px 0 rgba(0, 0, 0, 0.15);
}

#wrapper .breadcrumb {
  background: transparent;
  padding: 0;
}

#wrapper .breadcrumb[data-depth='1'] {
  display: none;
}

#wrapper .breadcrumb ol {
  padding-left: 0;
  margin-bottom: 0;
}

#wrapper .breadcrumb li {
  display: inline;
}

#wrapper .breadcrumb li:after {
  content: '/';
  color: #7a7a7a;
  margin: 0.3125rem;
}

#wrapper .breadcrumb li:last-child {
  content: '/';
  color: #7a7a7a;
  margin: 0;
}

#wrapper .breadcrumb li:last-child:after {
  content: '';
}

#wrapper .breadcrumb li a {
  color: #232323;
}

#main .page-content,
#main .page-header {
  margin-bottom: 1.563rem;
}

#main .page-content h6 {
  margin-bottom: 1.125rem;
}

#main .page-content #notifications {
  margin-left: -15px;
  margin-right: -15px;
}

#notifications ul {
  margin-bottom: 0;
}

#footer {
  padding-top: 45px;
  padding-bottom: 25px;
  background-color: #048e53;
}

@media (max-width: 1199px) {
  #header .header-nav {
    max-height: inherit;
  }

  #header .header-nav .search-widget {
    float: none;
    width: 15.63rem;
  }

  .container {
    width: 100%;
    padding: 0;
  }
}

@media (max-width: 991px) {
  .container {
    max-width: 100%;
  }

  #header .logo {
    width: auto;
  }

  #header .header-top .search-widget {
    min-width: inherit;
  }

  #products .product-miniature,
  .featured-products .product-miniature {
    margin: 0 auto;
  }

  .sub-menu {
    left: 0;
    min-width: 100%;
  }

  #blockcart-modal .product-image {
    width: 100%;
    display: block;
    max-width: 15.63rem;
    margin: 0 auto 0.9375rem;
  }

  #blockcart-modal .cart-content {
    padding-left: 0;
  }

  #blockcart-modal .product-name,
  #product-availability {
    margin-top: 0.625rem;
  }

  #search_filters .facet .facet-label {
    text-align: left;
  }

  .block-category .category-cover {
    position: relative;
    text-align: center;
  }

  .block-category {
    padding-bottom: 0;
  }
}

@media (max-width: 767px) {
  #wrapper {
    box-shadow: none;
  }

  #checkout-cart-summary {
    float: none;
    width: 100%;
    margin-top: 1rem;
  }

  #header {
    background: #098061;
  }

  #header .header-nav {
    background: #fff;
    margin-bottom: 0.625rem;
    color: #232323;
  }

  #header .header-nav .top-logo {
    line-height: 50px;
    vertical-align: middle;
    width: 200px;
    margin: 0 auto;
  }

  #header .header-nav .top-logo a img {
    max-height: 50px;
    max-width: 100%;
  }

  #header .header-nav .right-nav {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
  }

  #header .header-nav .user-info {
    text-align: left;
    margin-left: 0;
  }

  #header .header-nav .user-info .logged {
    color: #2fb5d2;
  }

  #header .header-nav .blockcart {
    margin-left: 0;
    background: inherit;
  }

  #header .header-nav .blockcart.active {
    margin-left: 0.5rem;
  }

  #header .header-nav .blockcart.inactive .cart-products-count {
    display: none;
  }

  #header .header-top {
    background: #f1f1f1;
    padding-bottom: 0;
  }

  #header .header-top a[data-depth='0'] {
    color: #232323;
  }

  section.checkout-step {
    width: 100%;
  }

  .default-input {
    min-width: 100%;
  }

  label {
    clear: both;
  }

  #products .product-miniature,
  .featured-products .product-miniature {
    margin: 0 auto;
  }

  .block-contact {
    padding-left: 0.9375rem;
    border: none;
  }

  .dropdown-item,
  .menu {
    padding-left: 0;
  }

  #footer {
    padding-top: 0.5rem;
  }
}

@media (max-width: 575px) {
  #content-wrapper,
  #left-column {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}
