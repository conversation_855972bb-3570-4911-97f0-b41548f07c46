@font-face {
  font-family: 'MobiriseIcons';
  src:  url('fonts/mobirise-icons_spat4u.eot');
  src:  url('fonts/mobirise-icons_spat4u.eot#iefix') format('embedded-opentype'),
    url('fonts/mobirise-icons_spat4u.ttf') format('truetype'),
    url('fonts/mobirise-icons_spat4u.woff') format('woff'),
    url('fonts/mobirise-icons.svg?spat4u#MobiriseIcons') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^="mbri-"], [class*=" mbri-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: MobiriseIcons !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.mbri-alert:before {
  content: "\e900";
}
.mbri-android:before {
  content: "\e901";
}
.mbri-apple:before {
  content: "\e902";
}
.mbri-arrow-next:before {
  content: "\e903";
}
.mbri-arrow-prev:before {
  content: "\e904";
}
.mbri-bookmark:before {
  content: "\e905";
}
.mbri-bootstrap:before {
  content: "\e906";
}
.mbri-briefcase:before {
  content: "\e907";
}
.mbri-browse:before {
  content: "\e908";
}
.mbri-calendar:before {
  content: "\e909";
}
.mbri-camera:before {
  content: "\e90a";
}
.mbri-cart-add:before {
  content: "\e90b";
}
.mbri-cart-full:before {
  content: "\e90c";
}
.mbri-cash:before {
  content: "\e90d";
}
.mbri-change-style:before {
  content: "\e90e";
}
.mbri-chat:before {
  content: "\e90f";
}
.mbri-clock:before {
  content: "\e910";
}
.mbri-close:before {
  content: "\e911";
}
.mbri-cloud:before {
  content: "\e912";
}
.mbri-code:before {
  content: "\e913";
}
.mbri-contact-form:before {
  content: "\e914";
}
.mbri-credit-card:before {
  content: "\e915";
}
.mbri-cursor-click:before {
  content: "\e916";
}
.mbri-cust-feedback:before {
  content: "\e917";
}
.mbri-database:before {
  content: "\e918";
}
.mbri-delivery:before {
  content: "\e919";
}
.mbri-desctop:before {
  content: "\e91a";
}
.mbri-devices:before {
  content: "\e91b";
}
.mbri-down:before {
  content: "\e91c";
}
.mbri-download:before {
  content: "\e91d";
}
.mbri-drag-n-drop:before {
  content: "\e91e";
}
.mbri-edit:before {
  content: "\e91f";
}
.mbri-edit2:before {
  content: "\e920";
}
.mbri-error:before {
  content: "\e921";
}
.mbri-extension:before {
  content: "\e922";
}
.mbri-features:before {
  content: "\e923";
}
.mbri-file:before {
  content: "\e924";
}
.mbri-flag:before {
  content: "\e925";
}
.mbri-folder:before {
  content: "\e926";
}
.mbri-gift:before {
  content: "\e927";
}
.mbri-globe-2:before {
  content: "\e928";
}
.mbri-globe:before {
  content: "\e929";
}
.mbri-growing-chart:before {
  content: "\e92a";
}
.mbri-hearth:before {
  content: "\e92b";
}
.mbri-help:before {
  content: "\e92c";
}
.mbri-home:before {
  content: "\e92d";
}
.mbri-hot-cup:before {
  content: "\e92e";
}
.mbri-idea:before {
  content: "\e92f";
}
.mbri-image-gallery:before {
  content: "\e930";
}
.mbri-image-slider:before {
  content: "\e931";
}
.mbri-info:before {
  content: "\e932";
}
.mbri-key:before {
  content: "\e933";
}
.mbri-laptop:before {
  content: "\e934";
}
.mbri-layers:before {
  content: "\e935";
}
.mbri-left:before {
  content: "\e936";
}
.mbri-letter:before {
  content: "\e937";
}
.mbri-like:before {
  content: "\e938";
}
.mbri-link:before {
  content: "\e939";
}
.mbri-lock:before {
  content: "\e93a";
}
.mbri-login:before {
  content: "\e93b";
}
.mbri-logout:before {
  content: "\e93c";
}
.mbri-magic-stick:before {
  content: "\e93d";
}
.mbri-map-pin:before {
  content: "\e93e";
}
.mbri-menu:before {
  content: "\e93f";
}
.mbri-mobile:before {
  content: "\e940";
}
.mbri-mobile2:before {
  content: "\e941";
}
.mbri-mobirise:before {
  content: "\e942";
}
.mbri-music:before {
  content: "\e943";
}
.mbri-new-file:before {
  content: "\e944";
}
.mbri-opened-folder:before {
  content: "\e945";
}
.mbri-pages:before {
  content: "\e946";
}
.mbri-paper-plane:before {
  content: "\e947";
}
.mbri-paperclip:before {
  content: "\e948";
}
.mbri-photo:before {
  content: "\e949";
}
.mbri-photos:before {
  content: "\e94a";
}
.mbri-pin:before {
  content: "\e94b";
}
.mbri-play:before {
  content: "\e94c";
}
.mbri-plus:before {
  content: "\e94d";
}
.mbri-preview:before {
  content: "\e94e";
}
.mbri-print:before {
  content: "\e94f";
}
.mbri-protect:before {
  content: "\e950";
}
.mbri-question:before {
  content: "\e951";
}
.mbri-quote:before {
  content: "\e952";
}
.mbri-refresh:before {
  content: "\e953";
}
.mbri-responsive:before {
  content: "\e954";
}
.mbri-right:before {
  content: "\e955";
}
.mbri-rocket:before {
  content: "\e956";
}
.mbri-sad-face:before {
  content: "\e957";
}
.mbri-sale:before {
  content: "\e958";
}
.mbri-save:before {
  content: "\e959";
}
.mbri-search:before {
  content: "\e95a";
}
.mbri-setting:before {
  content: "\e95b";
}
.mbri-setting2:before {
  content: "\e95c";
}
.mbri-setting3:before {
  content: "\e95d";
}
.mbri-share:before {
  content: "\e95e";
}
.mbri-shoping-bag:before {
  content: "\e95f";
}
.mbri-shoping-basket:before {
  content: "\e960";
}
.mbri-shoping-cart:before {
  content: "\e961";
}
.mbri-sites:before {
  content: "\e962";
}
.mbri-smile-face:before {
  content: "\e963";
}
.mbri-speed:before {
  content: "\e964";
}
.mbri-star:before {
  content: "\e965";
}
.mbri-success:before {
  content: "\e966";
}
.mbri-sun:before {
  content: "\e967";
}
.mbri-tablet:before {
  content: "\e968";
}
.mbri-target:before {
  content: "\e969";
}
.mbri-timer:before {
  content: "\e96a";
}
.mbri-to-ftp:before {
  content: "\e96b";
}
.mbri-to-local-drive:before {
  content: "\e96c";
}
.mbri-touch-swipe:before {
  content: "\e96d";
}
.mbri-touch:before {
  content: "\e96e";
}
.mbri-trash:before {
  content: "\e96f";
}
.mbri-unlock:before {
  content: "\e970";
}
.mbri-up:before {
  content: "\e971";
}
.mbri-upload:before {
  content: "\e972";
}
.mbri-user:before {
  content: "\e973";
}
.mbri-user2:before {
  content: "\e974";
}
.mbri-users:before {
  content: "\e975";
}
.mbri-video-play:before {
  content: "\e976";
}
.mbri-video:before {
  content: "\e977";
}
.mbri-watch:before {
  content: "\e978";
}
.mbri-website-theme:before {
  content: "\e979";
}
.mbri-wifi:before {
  content: "\e97a";
}
.mbri-windows:before {
  content: "\e97b";
}
