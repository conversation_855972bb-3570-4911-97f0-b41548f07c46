import {useQueryClient} from '@tanstack/react-query';
import React, {createContext, useCallback, useEffect, useState} from 'react';
import {PermissionsAndroid, Platform} from 'react-native';
import PushNotification, {ReceivedNotification} from 'react-native-push-notification';
import {RefreshTokenSuccessData} from '~utils/api/auth';
import {bindAuthorizationAxios, bindZMPHeader} from '~utils/api/axios';
import {bindDeviceToken, unbindDeviceToken} from '~utils/api/notifications';
import {ACCESS_TOKEN_KEY, ASYNC_STORAGE_KEYS, env, REFRESH_TOKEN_KEY} from '~utils/config';
import ee, {EventNames} from '~utils/events';
import {openAuthInAppBrowser, popupCenter} from '~utils/helpers/browser';
import * as Sentry from '@sentry/react-native';
import AsyncStorage from '~utils/helpers/storage';
import isInZaloMiniApp from '~utils/helpers/isInZaloMiniApp';
// import AsyncStorage from '~utils/helpers/storage';
import api from '~utils/helpers/zmp-sdk';
import {useNavigation} from '~hooks';
import {useToast} from 'react-native-toast-notifications';

type AuthStorageType = {
  access_token: string;
  refresh_token: string;
};

type AuthContextType = {
  authData: AuthStorageType;
  setAuthData: unknown & React.Dispatch<React.SetStateAction<AuthStorageType>>;
  isLoadingAuth: boolean;
  isLoggingOut: boolean;
  isLoggedIn: boolean;
  isLoggingIn: boolean;
  logout: () => void;
  login: (auth: AuthStorageType) => Promise<void>;
  loginError?: string;
  openSSOAuth: (authType: 'login' | 'register', redirectURI?: string) => void;
  openLoginRequiredModal: (type?: 'login' | 'register') => void;
  withAuth: (fn: () => void) => void;
  openOAuth2InAppBrowser: (driver: string) => Promise<string | undefined | void>;
};

export const AuthContext = createContext<AuthContextType>({
  authData: {access_token: '', refresh_token: ''},
  setAuthData: () => {},
  isLoggedIn: false,
  isLoadingAuth: true,
  isLoggingIn: false,
  isLoggingOut: false,
  logout: () => {},
  login: async () => {},
  openSSOAuth: () => {},
  openLoginRequiredModal: () => {},
  withAuth: () => {},
  openOAuth2InAppBrowser: async () => {},
});

const AuthProvider: React.FC<{children: React.ReactNode}> = props => {
  const queryClient = useQueryClient();
  const [isLoadingAuth, setLoadingAuth] = useState(true);
  const [isLoggingOut, setLoggingOut] = useState(false);
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [loginError] = useState<string>('');
  const [authData, setAuthData] = useState<AuthStorageType>({
    refresh_token: '',
    access_token: '',
  });
  const [isZaloMiniApp] = useState(isInZaloMiniApp());
  const navigation = useNavigation();
  const toast = useToast();

  // useEffect(() => {
  //   if (__DEV__) {
  //     login({
  //       access_token:
  //         'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************.WCV1uwWaypE94L_L7QxX0bHkJaFOpg-S8CjUTmfnRSs',
  //       refresh_token: '123',
  //     });
  //   }
  // }, []);

  useEffect(() => {
    if (authData.access_token) {
      bindDeviceToken();
    }
  }, [authData.access_token]);

  useEffect(() => {
    if (Platform.OS !== 'web') {
      PushNotification.popInitialNotification((notification: ReceivedNotification | null) => {
        if (notification?.userInteraction) {
          // handleNotificationClick(notification);
        }
      });
    }
    if (Platform.OS === 'android') {
      try {
        PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS);
      } catch (error) {}
    }
  }, []);

  useEffect(() => {
    (async () => {
      try {
        if (isZaloMiniApp) {
          await api?.login({});
          bindZMPHeader();
        }
        const auth = await getAuthFromStorage();
        if (auth && auth.access_token) {
          setAuthData(auth);
          bindAuthorizationAxios(auth.access_token);
        }
      } catch (error) {}

      setLoadingAuth(false);
    })();

    ee.on(EventNames.RefreshTokenSuccess, handleRefreshTokenSuccess);
    ee.on(EventNames.RefreshTokenError, logout);

    return () => {
      ee.off(EventNames.RefreshTokenSuccess, handleRefreshTokenSuccess);
      ee.off(EventNames.RefreshTokenError, logout);
    };
  }, []);

  const logout = useCallback(async () => {
    if (!isLoggingOut) {
      setLoggingOut(true);

      try {
        setAuthData({
          access_token: '',
          refresh_token: '',
        });
        bindAuthorizationAxios('');
        await AsyncStorage.multiRemove([ACCESS_TOKEN_KEY, REFRESH_TOKEN_KEY, ASYNC_STORAGE_KEYS.FCM_TOKEN]);
        await unbindDeviceToken();

        queryClient.clear();
      } catch (error) {}

      setLoggingOut(false);
    }
  }, [isLoggingOut, authData.access_token]);

  const login = useCallback(async (auth: AuthStorageType) => {
    try {
      setIsLoggingIn(true);
      bindAuthorizationAxios(auth.access_token);
      setAuthData(auth);
      await AsyncStorage.multiSet([
        [ACCESS_TOKEN_KEY, auth.access_token],
        [REFRESH_TOKEN_KEY, auth.refresh_token],
      ]);

      toast.show('Đăng nhập thành công', {
        placement: 'bottom',
        duration: 2000,
      });
    } catch (error) {
      Sentry.captureMessage('Dang nhap that bai');
    }

    setIsLoggingIn(false);
  }, []);

  const openSSOAuth = useCallback(async (authType: 'login' | 'register', redirectURI?: string) => {
    const redirectURL = redirectURI
      ? redirectURI
      : Platform.OS === 'web'
      ? isZaloMiniApp
        ? `${env.ZALO_MINIAPP_LOGIN_CALLBACK}login/callback${(window as any).APP_ENV ? `?env=${(window as any).APP_ENV}&version=${(window as any).APP_VERSION}` : ''}`
        : env.WEB_LOGIN_CALLBACK
      : 'tts.com.dsbuyer://';

    const ssoURL = `${env.OAUTH_URI}/${authType}?next=${encodeURIComponent(redirectURL)}`;

    try {
      if (Platform.OS === 'web') {
        window.location.href = ssoURL;
        return;
      }

      const result = await openAuthInAppBrowser(ssoURL, redirectURL);

      if (result?.type === 'success') {
        const params = parseDeeplinkCallback(result.url);
        const {access_token, refresh_token} = params;

        if (access_token) {
          try {
            const authInfo: AuthStorageType | null = {
              access_token,
              refresh_token,
            };
            await login(authInfo);
          } catch (error: any) {
            // There was an error on the native side
          }
        }
      }
    } catch (error) {}
  }, []);

  const openOAuth2InAppBrowser = useCallback(async (driver: string) => {
    const ssoURL = `${env.API_BASE_URL}/v1/user/oauth/connect?driver=${driver}&redirect=true&redirect_uri=${encodeURIComponent(
      `${env.OAUTH_URI}/oauth/callback?driver=${driver}&app=dsbuyer${Platform.OS === 'web' ? '_web' : ''}`,
    )}`;

    if (Platform.OS === 'web') {
      const popup = popupCenter({
        url: ssoURL,
        h: 500,
        w: 500,
        title: `Đăng nhập với ${driver}`,
      });

      const checkPopup = setInterval(() => {
        if (popup) {
          if (popup.window.location.href.includes(env.WEB_LOGIN_CALLBACK)) {
            // process login
            loginFromCallbackURL(popup.window.location.href);
            popup.close();
            clearInterval(checkPopup);
          }
        }
        if (!popup || !popup.closed) {
          return;
        }
        clearInterval(checkPopup);
      }, 1000);
      return;
    }

    const result = await openAuthInAppBrowser(ssoURL, 'tts.com.dsbuyer://');
    if (result?.type === 'success') {
      loginFromCallbackURL(result.url);
    }
  }, []);

  const loginFromCallbackURL = useCallback(async (callbackURL: string) => {
    const params = parseDeeplinkCallback(callbackURL);
    const {access_token, refresh_token, oauth_key} = params;
    if (oauth_key && !access_token) {
      // user is not exist, redirect to OTP verification to complete registration
      navigation.navigate('PhoneVerification', {
        oauth_key: oauth_key,
      });
    }
    if (access_token && refresh_token) {
      try {
        const authInfo: AuthStorageType | null = {
          access_token,
          refresh_token,
        };
        await login(authInfo);
      } catch (error: any) {
        // There was an error on the native side
      }
    }
  }, []);

  const handleRefreshTokenSuccess = useCallback(async (data: RefreshTokenSuccessData) => {
    const access_token = data.access_token;
    const refresh_token = data.refresh_token;
    const newAuth: AuthStorageType | null = {
      access_token,
      refresh_token,
    };
    bindAuthorizationAxios(newAuth.access_token);
    setAuthData(newAuth);
    await AsyncStorage.multiSet([
      [ACCESS_TOKEN_KEY, newAuth.access_token],
      [REFRESH_TOKEN_KEY, newAuth.refresh_token],
    ]);
  }, []);

  const openLoginRequiredModal = useCallback((type?: 'login' | 'register') => {
    if (type === 'login') {
      navigation.navigate('Login');
    } else {
      navigation.navigate('Register');
    }
  }, []);

  const withAuth = useCallback(
    (fn: () => void) => {
      if (authData.access_token) {
        fn();
      } else {
        openLoginRequiredModal();
      }
    },
    [authData.access_token],
  );

  if (isLoadingAuth) {
    return null;
  }

  return (
    <AuthContext.Provider
      value={{
        authData,
        setAuthData,
        isLoggedIn: Boolean(authData.access_token),
        isLoadingAuth,
        isLoggingOut,
        isLoggingIn,
        logout,
        login,
        loginError,
        openSSOAuth,
        openLoginRequiredModal,
        withAuth,
        openOAuth2InAppBrowser,
      }}>
      {props.children}
    </AuthContext.Provider>
  );
};

const getAuthFromStorage = async () => {
  try {
    let auth: AuthStorageType = {
      access_token: '',
      refresh_token: '',
    };

    let res = await AsyncStorage.multiGet([ACCESS_TOKEN_KEY, REFRESH_TOKEN_KEY]);

    const [[, accessToken], [, refreshToken]] = res;
    if (accessToken && refreshToken) {
      auth.access_token = accessToken;
      auth.refresh_token = refreshToken;
    }

    return auth;
  } catch (error) {}
};

const parseDeeplinkCallback = (url: string) => {
  let regex = /[?&]([^=#]+)=([^&#]*)/g,
    params: any = {},
    match;
  while ((match = regex.exec(url))) {
    params[match[1]] = match[2];
  }
  return params;
};

export default AuthProvider;
