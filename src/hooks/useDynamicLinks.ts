import {parseURLToDeepLink} from '~utils/helpers/common';
import dynamicLinks, {FirebaseDynamicLinksTypes} from '@react-native-firebase/dynamic-links';
import {useEffect, useCallback} from 'react';
import {Linking} from 'react-native';

const useDynamicLinks = () => {
  const handleDynamicLink = useCallback(async (link: FirebaseDynamicLinksTypes.DynamicLink) => {
    if (link.url) {
      const parsedUrl = parseURLToDeepLink(link.url);
      const canOpenURL = await Linking.canOpenURL(parsedUrl);
      if (canOpenURL) {
        Linking.openURL(parsedUrl);
      }
    }
  }, []);

  useEffect(() => {
    const unsubscribe = dynamicLinks().onLink(handleDynamicLink);
    // When the component is unmounted, remove the listener
    return () => unsubscribe();
  }, []);

  useEffect(() => {
    dynamicLinks()
      .getInitialLink()
      .then(link => {
        if (link) {
          return handleDynamicLink(link);
        }
      });
  }, []);

  return null;
};

export default useDynamicLinks;
