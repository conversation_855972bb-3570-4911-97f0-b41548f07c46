import {useCallback, useState} from 'react';
import {ProductSortQuery} from '~utils/types/product';

export type SortParams = {
  ascending?: boolean;
  sort_by?: ProductSortQuery;
};

const useSort = (params: SortParams) => {
  const [sortState, setSortState] = useState<SortParams>(params);

  const updateSort = useCallback((newSort: SortParams) => {
    setSortState(newSort);
  }, []);

  return {sortState, updateSort};
};

export default useSort;
