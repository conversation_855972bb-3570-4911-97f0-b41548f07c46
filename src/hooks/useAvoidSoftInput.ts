import {AvoidSoftInput} from 'react-native-avoid-softinput';
import React from 'react';
import {useFocusEffect} from '@react-navigation/native';

const useAvoidSoftInput = () => {
  React.useEffect(() => {
    AvoidSoftInput.setEnabled(true);
  }, []);
};

export const useDisableAvoidSoftInput = () => {
  useFocusEffect(
    React.useCallback(() => {
      setTimeout(() => {
        AvoidSoftInput.setEnabled(false);
      }, 50);

      return () => {
        AvoidSoftInput.setEnabled(true);
      };
    }, []),
  );
};

export default useAvoidSoftInput;
