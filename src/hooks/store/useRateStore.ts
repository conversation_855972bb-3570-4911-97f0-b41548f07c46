import produce from 'immer';
import {create} from 'zustand';
import {devtools} from 'zustand/middleware';
// import zustandFlipper from 'react-native-flipper-zustand';
import {Asset} from 'react-native-image-picker';

export type RateOrderLineItem = {
  variant_id: string;
  comment: string;
  quality_rate: number;
  photos: string[];
  videos: string[];
  _attachments?: Asset[];
  image_src?: string;
  title?: string;
  variant_title?: string;
};

export type RateStoreType = Omit<RateStore, 'initRate' | 'updateRateStore' | 'updateRateLineItem'>;

type RateStore = {
  action_type: string;
  action_id: string;
  seller_service: number;
  delivery_service: number;
  line_items: {[key: string]: RateOrderLineItem};
  comment: string;
  anonymous: boolean;
  initRate: (data: Partial<RateStoreType>) => void;
  updateRateStore: (field: keyof RateStoreType, value: any) => void;
  updateRateLineItem: (variantId: string, field: keyof RateOrderLineItem, value: any) => void;
};

const initialState: RateStoreType = {
  action_type: '',
  action_id: '',
  seller_service: 0,
  delivery_service: 0,
  line_items: {},
  comment: '',
  anonymous: false,
};

const useRateStore = create<RateStore>()(
  // zustandFlipper(
  devtools(set => ({
    ...initialState,
    updateRateStore: (field: keyof RateStoreType, value: any) => {
      return set(state =>
        produce(state, draft => {
          // @ts-ignore
          draft[field] = value;
        }),
      );
    },
    updateRateLineItem: (variantId: string, field: keyof RateOrderLineItem, value: any) => {
      return set(state =>
        produce(state, draft => {
          if (draft.line_items[variantId]) {
            // @ts-ignore
            draft.line_items[variantId][field] = value;
          } else {
            // @ts-ignore
            draft.line_items[variantId] = {[field]: value};
          }
        }),
      );
    },
    initRate: (data: Partial<RateStore>) => {
      return set(state =>
        produce(state, draft => {
          // @ts-ignore
          draft.action_type = data.action_type;
          // @ts-ignore
          draft.action_id = data.action_id;
          // @ts-ignore
          draft.seller_service = data.seller_service;
          // @ts-ignore
          draft.delivery_service = data.delivery_service;
          // @ts-ignore
          draft.line_items = data.line_items;
          // @ts-ignore
          draft.comment = data.comment;
          // @ts-ignore
          draft.anonymous = data.anonymous;
        }),
      );
    },
  })),
  // ),
);

export default useRateStore;
