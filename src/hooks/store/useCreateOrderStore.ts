import Customer from '~utils/types/customer';
import {produce} from 'immer';
import {create} from 'zustand';
import {devtools} from 'zustand/middleware';
import {MarketPlace, OrderSourceName, PaymentMethod, ShippingLine} from '~utils/types/order';
// import zustandFlipper from 'react-native-flipper-zustand';
import {ShopPriceRule} from '~utils/api/voucher';
import {CartItem} from '~utils/types/cart';
import Address from '~utils/types/address';
import {Platform} from 'react-native';

type DraftOrder = {
  price_rules?: ShopPriceRule[];
  line_items?: CartItem[];
  shipping_lines?: ShippingLine[];
  [key: string]: any;
};

type NewOrderStore = {
  order_by_shop_id: {[key: string]: DraftOrder};
  is_initializing: boolean;
  cart_token?: string;
  shop_id?: string;
  payment_method?: PaymentMethod;
  base_source_name?: string;
  source_name?: OrderSourceName | null;
  is_from_other_marketplace: boolean;
  market_place: MarketPlace;
  source_identifier?: string;
  selected_shipping_line?: any;
  shipping_lines_loading?: boolean;
  customer?: Partial<Customer>;
  shipping_address?: Address;
  ordersInitialize: (updates: Partial<Omit<NewOrderStore, 'setNewOrderState' | 'batchUpdateOrderState' | 'is_initializing'>>) => void;
  setNewOrderState: (field: keyof Omit<NewOrderStore, 'setNewOrderState' | 'batchUpdateOrderState' | 'is_initializing'>, value: any) => void;
  setOrderByShopIdState?: (shopId: string, field: string, value: any) => void;
  cleanUp: () => void;
};

const baseSourceName = Platform.OS === 'web' ? 'dropship_web' : 'dropship_app';

const initialState = {
  is_initializing: true,
  order_by_shop_id: {},
  customer: undefined,
  base_source_name: baseSourceName,
  is_from_other_marketplace: false,
  marketplace: null,
  source_name: baseSourceName,
  shipping_address: undefined,
  source_identifier: undefined,
};

const useCreateOrderStore = create<NewOrderStore>()(
  // @ts-ignore
  // zustandFlipper(
  //@ts-ignore
  devtools(set => ({
    ...initialState,
    ordersInitialize: updates => {
      return set(state => ({...state, ...updates, is_initializing: false}));
    },
    setNewOrderState: (field, value) => {
      return set(state =>
        produce(state, draft => {
          // @ts-ignore
          draft[field] = value;
        }),
      );
    },
    setOrderByShopIdState: (shopId, field, value) => {
      return set(state =>
        produce(state, draft => {
          if (draft.order_by_shop_id[shopId]) {
            draft.order_by_shop_id[shopId][field] = value;
          }
        }),
      );
    },
    cleanUp: () => {
      return set(state => ({...state, ...initialState}));
    },
  })),
  // ),
);

export default useCreateOrderStore;
