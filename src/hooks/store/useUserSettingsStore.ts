import produce from 'immer';
import {create} from 'zustand';
import {devtools} from 'zustand/middleware';

type UserSettingsStore = {
  isDirty: boolean;
  isInitializing: boolean;
  data: {
    account_phone: string;
    address: string;
    district_name: string;
    email: string;
    full_name: string;
    province_name: string;
    ward_name: string;
  };
  initializeStore: (state: UserSettingsStore['data']) => void;
  updateAddressState: (field: 'address' | 'district_name' | 'province_name' | 'ward_name', value: string) => void;
  updateSettings: (field: keyof UserSettingsStore['data'], value: any) => void;
  cleanUp: () => void;
};

const useUserSettingsStore = create<UserSettingsStore>()(
  devtools(set => ({
    isDirty: false,
    isInitializing: true,
    data: {
      account_phone: '',
      address: '',
      district_name: '',
      email: '',
      full_name: '',
      province_name: '',
      ward_name: '',
    },

    updateAddressState: (field, value) => {
      return set(state =>
        produce(state, draft => {
          draft.data[field] = value;
          draft.isDirty = true;
        }),
      );
    },
    initializeStore: (data: UserSettingsStore['data']) => {
      return set(state =>
        produce(state, draft => {
          draft.data = data;
          draft.isInitializing = false;
          draft.isDirty = false;
        }),
      );
    },
    updateSettings: (field, value) => {
      return set(state =>
        produce(state, draft => {
          // @ts-ignore
          draft.data[field] = value;
          draft.isDirty = true;
        }),
      );
    },

    cleanUp: () => {
      return set(state =>
        produce(state, draft => {
          draft.isDirty = false;
          draft.isInitializing = true;
        }),
      );
    },
  })),
);

export default useUserSettingsStore;
