import {useAuthActions} from '~hooks';
import {useEffect} from 'react';
import {bindDeviceToken} from '~utils/api/notifications';
import PushNotification, {ReceivedNotification} from 'react-native-push-notification';
import {PermissionsAndroid, Platform} from 'react-native';

const useNotifications = () => {
  const {authData} = useAuthActions();

  useEffect(() => {
    if (authData.access_token) {
      bindDeviceToken();
    }
  }, [authData.access_token]);

  useEffect(() => {
    if (Platform.OS === 'android') {
      try {
        PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS);
      } catch (error) {}
    }
    PushNotification.popInitialNotification((notification: ReceivedNotification | null) => {
      if (notification?.userInteraction) {
        // handleNotificationClick(notification);
      }
    });
  }, []);
};

export default useNotifications;
