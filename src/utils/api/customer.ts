import {useQuery, useInfiniteQuery, useMutation, useQueryClient} from '@tanstack/react-query';
import axios from './axios';
import Customer from '~utils/types/customer';
import qs from 'query-string';
import Address from '~utils/types/address';
import produce from 'immer';
import {REACT_QUERY_CACHE_TIME} from '~utils/helpers/reactQuery';

type CustomerRequestQuery = {
  dropship?: boolean;
  page?: number;
  limit?: number;
  query?: string;
};

type CustomerListResponse = {
  total: number;
  customers: Customer[];
  page: number;
};

type AddressParseResponse = {
  address: string;
  city: string;
  province: string;
  ward: string;
  tokenizer: string[];
};

const CUSTOMER_LIST_LIMIT = 20;

export const getCustomers = async (query?: CustomerRequestQuery): Promise<CustomerListResponse> => {
  const queryString = qs.stringify({...query, limit: CUSTOMER_LIST_LIMIT, dropship: true});
  const res = await axios.get(`/v2/order/api/customers.json?${queryString}`);
  return {...res.data, page: query?.page ?? 1};
};

export const searchCustomers = async (query?: CustomerRequestQuery): Promise<CustomerListResponse> => {
  const queryString = qs.stringify({...query, limit: CUSTOMER_LIST_LIMIT, dropship: true});
  const res = await axios.get(`/v2/order/api/customers/search.json?${queryString}`);
  return {...res.data, page: query?.page ?? 1};
};

export const useGetCustomers = (query?: CustomerRequestQuery) =>
  useQuery(['customers'], () => {
    if (query?.query) {
      return searchCustomers(query);
    } else {
      return getCustomers(query);
    }
  });

export const getCustomerInfiniteQueryKey = (query?: CustomerRequestQuery) => {
  return ['customers_infinite', query];
};

export const useGetCustomersInfinite = (query?: CustomerRequestQuery) => {
  return useInfiniteQuery(
    getCustomerInfiniteQueryKey(query),
    ({pageParam}) => {
      if (query?.query) {
        return searchCustomers({...query, page: pageParam, limit: CUSTOMER_LIST_LIMIT});
      } else {
        return getCustomers({...query, page: pageParam, limit: CUSTOMER_LIST_LIMIT});
      }
    },
    {
      getNextPageParam: lastPage => {
        const totalPage = Math.ceil(lastPage.total / CUSTOMER_LIST_LIMIT);
        if (lastPage.page < totalPage) {
          return lastPage.page + 1;
        }
      },
    },
  );
};

export const createCustomer = async (customer: Partial<Customer>) => {
  const {data} = await axios.post('/v2/order/api/customers.json', {customer: {...customer, dropship: true}});
  return data.customer;
};

export const useCreateCustomerMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<any, any, {customer: Partial<Customer>}>(({customer}) => createCustomer(customer), {
    onSuccess: () => {
      queryClient.invalidateQueries(['customers']);
    },
  });
};

export const createCustomerAddress = async (address: Partial<Address>) => {
  const {data} = await axios.post(`/v2/order/api/customers/${address.customer_id}/addresses.json`, {address: {...address, dropship: true}});
  return data.customer_address;
};

export const useCreateCustomerAddressMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<
    Address,
    any,
    {
      address: Partial<Address>;
    }
  >(({address}) => createCustomerAddress(address), {
    onSuccess: () => {
      queryClient.invalidateQueries(['customers']);
    },
  });
};

export const setAddressDefault = async (customerId: string, addressId: string) => {
  const {data} = await axios.put(`/v2/order/api/customers/${customerId}/addresses/${addressId}/default.json?dropship=true`, {});
  return data.customer_address;
};

export const useSetAddressDefaultMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Address, any, {customerId: string; addressId: string}>(({customerId, addressId}) => setAddressDefault(customerId, addressId), {
    onSuccess: (data, {customerId, addressId}) => {
      queryClient.setQueryData<any>(['customers'], (customerData: CustomerListResponse) =>
        produce(customerData, draft => {
          if (draft) {
            const customerIndex = draft.customers.findIndex(c => c.id === customerId);
            if (customerIndex !== -1) {
              draft.customers[customerIndex].default_address = data;
              const addressIndex = draft.customers[customerIndex].addresses.findIndex(add => add.id === addressId);

              if (addressIndex !== -1) {
                draft.customers[customerIndex].addresses[addressIndex] = data;
              }
            }
          }
        }),
      );
    },
  });
};

export const addressParse = async (address: string): Promise<AddressParseResponse> => {
  const {data} = await axios.put(`/v2/order/api/address_parse.json?address=${encodeURIComponent(address)}`);
  return data;
};

export const useAddressParse = (address: string) => {
  return useQuery(['address_parse', address], () => addressParse(address), {
    cacheTime: REACT_QUERY_CACHE_TIME.FiveMinutes / 10,
    staleTime: REACT_QUERY_CACHE_TIME.FiveMinutes / 10,
    enabled: !!address,
  });
};
