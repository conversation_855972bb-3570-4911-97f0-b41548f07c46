import {REACT_QUERY_CACHE_TIME} from '~utils/helpers/reactQuery';
import {ASYNC_STORAGE_KEYS} from '~utils/config';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Product, {ProductCollection, ProductPost, SearchProduct} from './../types/product';
import {useInfiniteQuery, useMutation, useQuery, useQueryClient, UseQueryOptions} from '@tanstack/react-query';
import {ProductRequestQuery} from '~utils/types/product';
import {graphqlRequest} from './axios';
import qs from 'query-string';
import axios from './axios';
import {Aggregations, SmartCollection, SuggestKeyword} from '~utils/types/common';
import {ImageFile} from './image';

export const ProductDetailGql = `
query ProductById($id: String!) {
  ProductDetail(id: $id) {
    id
    id_number
    title
    handle
    status
    review_status
    is_deal
    body_html   
    warranty_period
    warranty_policy
    features    
    unit_name
    created_at
    shop_id
    product_type_slug
    product_type
    is_trade_assurance
    allow_purchase
    category_lv1
    category_lv2
    prices {
      id
      start_quantity
      end_quantity
      compare_at_price
      price          
    }
    badges {
      id
      text_color
      text_bg_color
      text
      icon
      placement
      placement_order
      border_color
      icon_width
      icon_height
    }

    shop {
      id
      created_at
      name
      total_view
      total_products
      total_following
      total_dropship_products
      review_state
      phone_number
      response_rate
      province {
        name
      }
      district {
        name
      }
      avatar
      slug
      rating_avg
      rating_count
      styles {
        bg {
          gradientColors
        }
        title {
          color
        }
      }
      
      
    }
    image {
      id
      src
      product_id
    }
    images {
      id
      src
      product_id
    }
    videos {
      id
      url
      duration
      thumb_url
      width
      height
    }
    variants {
      id
      title
      product_id
      product_title
      product_type
      inventory_quantity
      image_id
      shop_id
      dropship_price
      dropship_profit
      dropship_selling_price_min
      market_price
      option1
      option2
      customize_template_id
    }

    options {
      id
      name
      display_name
      values
      position
      product_id
    }

    categories {
      lv1 {        
        id
        category_id
        title
        slug
      }

      lv2 {
        id
        category_id
        title
        slug
        total_product
      }
    }
    is_deal_active
    is_deal_v2
    deal_start_at
    deal_end_at
    deal_ordered
    deal_max_sale_quantity
    is_hot_deal
    discount_rate
    available
    showroom {
      title
      slug
    }
    quantity_per_unit
    is_classifieds
    classifieds {
      area
      business_phone_number
      button_type
      expired_at
      label
      website_url
    }
    hide_prices
    dropship_price
    dropship_profit
    dropship_selling_price_min
    market_price
    specifications {
      name
      value
    }
    size_chart {
      image_id
      src
    }

    can_dropship
    dropship_status
    sales
    rating_avg
    customize
  }
}
`;

export const SearchProductsGql = `
query searchProductsQuery($query: String!) {
  searchProducts(query: $query) {
    products {
      id
      slug
      product_id
      shop_id
      product_type
      image_thumb
      price
      compare_at_price
      title
      dropship_price
      unit_name
      dropship_profit
      market_price
      stock
      shop {
        city
      }
      badges {
        id
        text_color
        text_bg_color
        text
        icon
        placement
        placement_order
        border_color
        icon_width
        icon_height
      }
      rating_avg
      rating_count
      total_sales
      videos
    }
    total
  }
}
`;

export const GetAggregationsGql = `
query getAggregationsQuery($query: String!) {
  searchProducts(query: $query) {
    aggregations {
      filter_category_lv1 {
        buckets {
          key
          category_id
          title
          slug
          doc_count
          image
        }
      }
      filter_category_lv2 {
        buckets {
          key
          category_id
          title
          slug
          doc_count
          image
        }
      }
      filter_province {
        buckets {
          key
          category_id
          title
          slug
          doc_count
          image
        }
      }
    }
  }
}
`;

export type GetSmartCollectionProductsResponse = {
  collection: SmartCollection;
  total: number;
  offset: number;
  products: SearchProduct[];
  aggregations: any[];
};

export type VectorSearchObject = {
  img_url: string;
};

export const getProductDetail = async (productId: string | number): Promise<Product | undefined> => {
  const res = await graphqlRequest(ProductDetailGql, {id: String(productId)});
  if (res.data.data === null && res.data.errors?.length) {
    throw res.data.errors[0]?.message;
  }
  return res.data.data.ProductDetail;
};

export const useGetProductDetail = (productId: string | number, options?: Partial<UseQueryOptions<Product | undefined>>) =>
  useQuery(['product', productId], () => getProductDetail(productId), {
    ...(options as any),
    stateTime: 0,
  });

export const searchProducts = async (query: ProductRequestQuery): Promise<{products: SearchProduct[]; total: number; offset: number}> => {
  const queryString = qs.stringify({...query, filter_only_dropship: true}, {skipNull: true});
  const res: any = await graphqlRequest(SearchProductsGql, {query: `?${queryString}`});

  if (res.data.data === null && res.data.errors?.length) {
    throw res.data.errors[0]?.message;
  }

  return {...res.data.data.searchProducts, offset: query.offset ?? 0};
};

export const useSearchProducts = (query: ProductRequestQuery, options?: UseQueryOptions<any, any, any, any>) =>
  useQuery(['products_gql', query], () => searchProducts(query), {...(options as any), staleTime: 0});

const SEARCH_PRODUCTS_LIMIT = 40;

export const useSearchProductsInfinite = (query: ProductRequestQuery, options?: UseQueryOptions<any, any, any, any>) =>
  useInfiniteQuery(['products_gql_infinite', query], ({pageParam}) => searchProducts({...query, offset: pageParam ?? 0, limit: SEARCH_PRODUCTS_LIMIT}), {
    ...(options as any),
    stateTime: 0,
    getNextPageParam: lastPage => {
      if (lastPage.offset + SEARCH_PRODUCTS_LIMIT < lastPage.total) {
        return lastPage.offset + SEARCH_PRODUCTS_LIMIT;
      }
    },
  });

export const getProductPosts = async (productId: string): Promise<ProductPost[] | undefined> => {
  try {
    const res = await axios.get(`/v2/order/api/products/${productId}/posts.json?sort_by=created_at`);
    return res.data.posts;
  } catch (error) {}
};

export const useGetProductPost = (productId: string) => useQuery(['product_posts', productId], () => getProductPosts(productId));

export const createProductPost = async (productId: string, post: Partial<ProductPost>) => {
  const res = await axios.post(`/v2/order/api/products/${productId}/posts.json`, {post});
  return res.data.post;
};

export const updateProductPost = async (postId: string, post: Partial<ProductPost>) => {
  const res = await axios.put(`/v2/order/api/product-posts/${postId}.json`, {post});
  return res.data.post;
};

export const useCreateProductPost = () => {
  const queryClient = useQueryClient();
  return useMutation<any, any, {productId: string; post: Partial<ProductPost>}>(({productId, post}) => createProductPost(productId, post), {
    onSuccess: (data, {productId}) => {
      queryClient.invalidateQueries(['product_posts', productId]);
    },
  });
};

export const useUpdateProductPost = () => {
  const queryClient = useQueryClient();
  return useMutation<any, any, {postId: string; productId: string; post: Partial<ProductPost>}>(({postId, post}) => updateProductPost(postId, post), {
    onSuccess: (post, {postId, productId}) => {
      queryClient.setQueryData(['product_posts', productId], (data: ProductPost[] | undefined) => {
        if (data) {
          const index = data.findIndex(p => p.id === postId);
          if (index !== -1) {
            let newData = [...data];
            newData[index] = post;
            return newData;
          }
          return data;
        }
      });
    },
  });
};

export const deleteProductPost = async (postId: string) => {
  return await axios.delete(`/v2/order/api/product-posts/${postId}.json`);
};

export const useDeleteProductPost = () => {
  const queryClient = useQueryClient();

  return useMutation<any, any, {postId: string; productId: string}>(({postId}) => deleteProductPost(postId), {
    onSuccess: (_, {postId, productId}) => {
      queryClient.setQueryData(['product_posts', productId], (data: ProductPost[] | undefined) => {
        if (data) {
          return data.filter(post => post.id !== postId);
        }
      });
    },
  });
};

export const trackProductPostCopy = (postId: string, productId?: string, content?: string) => {
  return axios.post(`/v2/order/api/product-posts/${postId}/track-copy.json`, {
    product_id: productId,
    content: content,
  });
};

export const addWishlist = async (productId: string) => {
  const {data} = await axios.post('/v1/marketing/wishlists', {
    product_id: productId,
    dropship: true,
  });

  return data;
};

export const removeWishlist = async (productId: string) => {
  const {data} = await axios.delete(`/v1/marketing/wishlists/${productId}`);

  return data;
};

export const isAddedWishlist = async (productId: string) => {
  const {data} = await axios.get(`/v1/marketing/wishlists/${productId}/is_added`);

  return data;
};

const WISHLIST_LIMIT = 20;

export const getWishlistProducts = async (query: any = {}) => {
  const queryString = qs.stringify({...query, limit: WISHLIST_LIMIT, dropship: true});
  const {data} = await axios.get(`/v1/marketing/wishlists/products${queryString ? `?${queryString}` : ''}`);

  return {...data, offset: query.offset || 0};
};

export const useGetWishlistProducts = (query = {}) =>
  useInfiniteQuery(
    ['wishlist', query],
    ({pageParam}) => {
      return getWishlistProducts({offset: pageParam, ...query});
    },
    {
      getNextPageParam: lastPage => {
        if (lastPage.offset + WISHLIST_LIMIT < lastPage.total) {
          return lastPage.offset + WISHLIST_LIMIT;
        }
      },
      cacheTime: REACT_QUERY_CACHE_TIME.OneWeek,
    },
  );

export const useRemoveFromWishlistMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<any, any, {productId: string}>(({productId}) => removeWishlist(productId), {
    onSuccess: () => {
      queryClient.invalidateQueries(['wishlist']);
    },
  });
};

export const useAddToWishlistMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<any, any, {productId: string}>(({productId}) => addWishlist(productId), {
    onSuccess: () => {
      queryClient.invalidateQueries(['wishlist']);
    },
  });
};

export const getRecentlyViewedProducts = async () => {
  try {
    const res = await AsyncStorage.getItem(ASYNC_STORAGE_KEYS.RECENT_PRODUCTS);
    if (res) {
      return JSON.parse(res);
    }
    return [];
  } catch (error) {
    return [];
  }
};

export const addRecentlyViewedProduct = async () => {
  try {
    // const res = await AsyncStorage.setItem(ASYNC_STORAGE_KEYS.RECENT_PRODUCTS);
  } catch (error) {}
};

export const getRecentProducts = async (query = {}): Promise<SearchProduct[]> => {
  const queryString = qs.stringify(query);
  const {data} = await axios.get(`/v1/analytics/user/recent_products${queryString ? `?${queryString}` : ''}`);
  return data?.products ?? [];
};

export const useGetRecentProducts = (query = {}) => useQuery(['recent_products', query], () => getRecentProducts(query));

export const getHomeCollections = async (): Promise<ProductCollection[]> => {
  const {data} = await axios.get('/v2/order/api/collection_screens/home.json');
  return data.collections;
};

export const useGetHomeCollections = () => useQuery(['home_collections'], getHomeCollections);

export const getSmartCollection = async (collectionId: string, query: ProductRequestQuery): Promise<GetSmartCollectionProductsResponse> => {
  const queryString = qs.stringify(query);
  const {data} = await axios.get(`/v2/order/api/smart_collections/${collectionId}.json${queryString ? `?${queryString}` : ''}`);
  return {...data, offset: query.offset || 0};
};

const SMART_COLLECTION_LIMIT = 20;

export const useGetSmartCollection = (collectionId: string, query: ProductRequestQuery, options?: Partial<UseQueryOptions>) => {
  return useInfiniteQuery(
    ['sm_collection', collectionId, query],
    ({pageParam}) =>
      getSmartCollection(collectionId, {
        ...query,
        offset: pageParam || 0,
        limit: query.limit || SMART_COLLECTION_LIMIT,
      }),

    {
      enabled: !!collectionId,
      getNextPageParam: lastPage => {
        if (lastPage?.products?.length > 0) {
          return lastPage.offset + SMART_COLLECTION_LIMIT;
        }
      },
      ...(options as any),
    },
  );
};

export const getSearchSuggest = async (query: {match: 'broad'; q: string}): Promise<SuggestKeyword[]> => {
  try {
    const {data} = await axios.get(`/v2/search/suggestion/keyword?${qs.stringify(query)}`);
    return data?.keywords ?? [];
  } catch (error) {
    return [];
  }
};

export const useGetSearchSuggest = (query: {match: 'broad'; q: string; limit: 6}) => useQuery(['search_suggest', query], () => getSearchSuggest(query));

export const getSearchAggregations = async (query: ProductRequestQuery): Promise<Aggregations> => {
  const queryString = qs.stringify({...query, filter_only_dropship: true, only_aggs: true}, {skipNull: true, arrayFormat: 'comma'});
  const res: any = await graphqlRequest(GetAggregationsGql, {query: `?${queryString}`});

  if (res.data.data === null && res.data.errors?.length) {
    throw res.data.errors[0]?.message;
  }
  return res.data.data.searchProducts.aggregations;
};

export const useGetSearchAggregations = (query: ProductRequestQuery, options?: Partial<UseQueryOptions>) => {
  return useQuery(['products_aggs', query], () => getSearchAggregations(query), {staleTime: 0, ...(options as any)});
};

// export const https://api.thitruongsi.com/v1/vector-search/image

export const getProductsByVectorSearch = async (file: ImageFile | string, query: ProductRequestQuery): Promise<{products: SearchProduct[]; offset: number; total: number}> => {
  const queryString = qs.stringify(query);
  const url = `/v1/vector-search/products?filter_only_dropship=true&limit=${SEARCH_PRODUCTS_LIMIT}&${queryString}`;
  let uploadFile = file;

  if (typeof uploadFile !== 'string') {
    if (!uploadFile.name) {
      uploadFile.name = 'image.jpg';
    }
    if (!uploadFile.type) {
      uploadFile.type = 'image/jpeg';
    }
  }

  const formData = new FormData();
  formData.append('image', uploadFile as any);
  const {data} = await axios.post(url, formData);
  return {...data, offset: query.offset || 0};
};

export const getDetectedObjectsVectorSearch = async (file: ImageFile): Promise<{objects: VectorSearchObject[]}> => {
  const url = `/v1/vector-search/detect-objects`;
  let uploadFile = file;
  if (!uploadFile.name) {
    uploadFile.name = 'image.jpg';
  }
  if (!uploadFile.type) {
    uploadFile.type = 'image/jpeg';
  }

  const formData = new FormData();
  formData.append('image', uploadFile as any);
  const {data} = await axios.post(url, formData);
  return data;
};
