import {REACT_QUERY_CACHE_TIME} from './../helpers/reactQuery';
import {useQuery} from '@tanstack/react-query';
import {AdsContent} from '~utils/types/common';
import axios from './axios';

export const getPromotedSlider = async (): Promise<AdsContent[]> => {
  try {
    const {data} = await axios.get('/uab/api/get?position=slide&platform=dropship');
    return data.data[0].ads_content;
  } catch (error) {
    throw error;
  }
};

export const useGetPromotedSlider = () =>
  useQuery(['slide_home'], getPromotedSlider, {
    cacheTime: REACT_QUERY_CACHE_TIME.OneWeek,
    staleTime: REACT_QUERY_CACHE_TIME.FiveMinutes,
  });

const getAdsContent = async (position: string): Promise<AdsContent[]> => {
  const {data} = await axios.get(`/uab/api/get?position=${position}&platform=dropship`);
  // if (__DEV__) {
  //   return [
  //     // @ts-ignore
  //     {
  //       banner: 'https://ta.thitruongsi.com/storage/ads/December2024/cM0ZEqasm2APmZYU8M0H.png',
  //       // link: 'https://dropship.thitruongsi.com/webview?includeAuth=true&url=https%3A%2F%2Fdropship.thitruongsi.com%2Flookback',
  //       link: 'http://localhost:3000/lookback?includeAuth=true',
  //       id: 123,
  //     },
  //   ];
  // }
  return data.data[0]?.ads_content ?? [];
};

export const useGetAdsContent = (position: string) => {
  return useQuery(['ads_content', position], () => getAdsContent(position), {
    staleTime: REACT_QUERY_CACHE_TIME.FiveMinutes,
  });
};
