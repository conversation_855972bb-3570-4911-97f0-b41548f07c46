import {REACT_QUERY_CACHE_TIME} from '~utils/helpers/reactQuery';
import {useAuthActions} from '~hooks';
import {ASYNC_STORAGE_KEYS} from '~utils/config';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useInfiniteQuery, useMutation, useQuery, useQueryClient} from '@tanstack/react-query';
import Notification from '~utils/types/notification';
import axios from './axios';
import qs from 'query-string';

import messaging from '@react-native-firebase/messaging';
import {Platform} from 'react-native';
import {getUniqueIdSync} from 'react-native-device-info';

export type NotificationCategory = {
  type: string;
  title: string;
  icon_name: string;
  last_notify: string;
  unseen: number;
};

type NotificationRequestQuery = Partial<{
  platform: string;
  type: string;
  cursor: string;
}>;

type NotificationGetReponse = {
  per_page: number;
  next_cursor: string | null;
  next_page_url: string | null;
  prev_cursor: string | null;
  prev_page_url: string | null;
  notifications: Notification[];
};

const getNotificationCategories = async (): Promise<NotificationCategory[]> => {
  const res = await axios.get('/v3/notification/categories?platform=dropship');
  return res.data.categories;
};

export const useGetNotificationCategories = () =>
  useQuery(['noti_categories'], getNotificationCategories, {
    staleTime: REACT_QUERY_CACHE_TIME.OneDay,
    cacheTime: REACT_QUERY_CACHE_TIME.OneWeek,
  });

export const getNotifications = async (query?: NotificationRequestQuery): Promise<NotificationGetReponse> => {
  const queryString = qs.stringify(query || {});
  const res = await axios.get(`/v3/notification/notifications?${queryString}`);

  return res.data;
};

export const useGetNotifications = (query?: NotificationRequestQuery) =>
  useInfiniteQuery(['notifications', query], ({pageParam}) => getNotifications({...query, cursor: pageParam}), {
    staleTime: 0,
    refetchOnWindowFocus: true,
    getNextPageParam: lastPage => {
      if (lastPage.next_cursor) {
        return lastPage.next_cursor;
      }
    },
  });

export const markNotificationRead = async (ids: string[]) => {
  const res = await axios.post('/v3/notification/notifications/mark_read', {
    ids: ids,
  });
  return res;
};

export const useMarkNotificationRead = () => {
  const queryClient = useQueryClient();
  return useMutation<any, any, {ids: string[]}, any>(({ids}) => markNotificationRead(ids), {
    onSuccess: (data, {ids}) => {
      // queryClient.setQueryData()
      if (!ids.length) {
        queryClient.invalidateQueries(['notifications']);
      } else {
      }
    },
  });
};

export const countNotificationUnseen = async (): Promise<number> => {
  const res = await axios.get('/v3/notification/notifications/count_unseen');

  return res.data.unseen;
};

export const useCountNotificationUnseen = () => {
  const {isLoggedIn} = useAuthActions();
  return useQuery(['notification_unseen_count'], countNotificationUnseen, {
    refetchInterval: 1000 * 60 * 2,
    refetchOnWindowFocus: true,
    enabled: isLoggedIn,
  });
};
export const bindDeviceToken = async () => {
  try {
    if (Platform.OS === 'web') {
      return;
    }

    const fcmToken = await messaging().getToken();

    await AsyncStorage.setItem(ASYNC_STORAGE_KEYS.FCM_TOKEN, fcmToken);

    await axios.post('/v3/notification/tokens/bind', {
      os: Platform.OS.slice(0, 3),
      platform: 'dropship',
      token: fcmToken,
      device_id: getUniqueIdSync(),
    });
  } catch (error) {}
};

export const unbindDeviceToken = async () => {
  try {
    if (Platform.OS === 'web') {
      return;
    }
    const fcmToken = await AsyncStorage.getItem(ASYNC_STORAGE_KEYS.FCM_TOKEN);
    if (fcmToken) {
      return axios.post('/v3/notification/tokens/unbind', {
        token: fcmToken,
      });
    }
  } catch (error) {}
};

export const markAllSeenNotifications = (ids: string[]) => {
  return axios.post('/v3/notification/notifications/mark_seen', {ids});
};

export const useMarkAllSeenNotificationMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<any, any, {ids: string[]}, any>(({ids}) => markAllSeenNotifications(ids), {
    onSuccess: () => {
      queryClient.invalidateQueries(['notification_unseen_count']);
      queryClient.invalidateQueries(['noti_categories']);
    },
  });
};
