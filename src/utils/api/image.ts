import {UploadedImage} from '~utils/types/common';
import axios from './axios';

export type ImageFile = {
  _id?: string;
  width?: number;
  height?: number;
  uri: string;
  type: string;
  name: string;
};

export const uploadImage = async (file: ImageFile): Promise<UploadedImage | undefined> => {
  try {
    const url = '/v1/image/api/images';
    let uploadFile = file;
    if (!uploadFile.name) {
      uploadFile.name = 'image.jpg';
    }
    if (!uploadFile.type) {
      uploadFile.type = 'image/jpeg';
    }

    const formData = new FormData();
    formData.append('image[]', uploadFile as any);

    const {data} = await axios.post(url, formData);
    return data.data.images[0];
  } catch (error) {}
};
