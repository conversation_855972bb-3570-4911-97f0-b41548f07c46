import axios, {AxiosRequestConfig, CancelTokenSource} from 'axios';
import {env} from '~utils/config/env';
import {refreshToken} from './auth';
import ee, {EventNames} from '~utils/events';

const instance = axios.create({
  baseURL: env.API_BASE_URL,
});
const graphqlInstance = axios.create({
  baseURL: env.API_BASE_URL,
});

let isRefreshing = false;
let refreshSubscribers: any[] = [];

instance.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    const {config, response} = error;
    const originalRequest = config;
    if (response && response.status === 401 && originalRequest?.headers?.authorization) {
      if (!isRefreshing) {
        isRefreshing = true;
        refreshToken()
          .then(data => {
            onRefreshed(data.access_token);
            ee.emit(EventNames.RefreshTokenSuccess, data);
          })
          .catch(() => {
            ee.emit(EventNames.RefreshTokenError);
          })
          .finally(() => {
            isRefreshing = false;
            refreshSubscribers = [];
          });
      }

      const retryOrigReq = new Promise(resolve => {
        subscribeTokenRefresh((token: any) => {
          // replace the expired token and retry
          originalRequest.headers.authorization = 'Bearer ' + token;
          resolve(axios(originalRequest));
        });
      });
      return retryOrigReq;
    } else {
      return Promise.reject(error);
    }
  },
);

graphqlInstance.interceptors.response.use(
  response => {
    if (response.data?.errors?.[0]?.message === '401') {
      const originalRequest = response.config;
      if (!isRefreshing) {
        isRefreshing = true;
        refreshToken()
          .then(data => {
            onRefreshed(data.access_token);
            ee.emit(EventNames.RefreshTokenSuccess, data);
          })
          .catch(err => {
            if (err?.message !== 'REFRESH TOKEN NOT FOUND') {
              ee.emit(EventNames.RefreshTokenError);
            }
          })
          .finally(() => {
            isRefreshing = false;
            refreshSubscribers = [];
          });
      }

      const retryOrigReq = new Promise(resolve => {
        subscribeTokenRefresh((token: any) => {
          // replace the expired token and retry
          (originalRequest.headers as any).authorization = 'Bearer ' + token;
          resolve(axios(originalRequest));
        });
      });
      return retryOrigReq;
    }
    return response;
  },
  error => {
    Promise.reject(error);
  },
);

const subscribeTokenRefresh = (cb: any) => {
  refreshSubscribers.push(cb);
};

const onRefreshed = (token: any) => {
  refreshSubscribers.map(cb => cb(token));
};

export const bindAuthorizationAxios = (accessToken: string) => {
  instance.defaults.headers.common.authorization = accessToken ? `Bearer ${accessToken}` : '';
  graphqlInstance.defaults.headers.common.authorization = accessToken ? `Bearer ${accessToken}` : '';
};

export const bindZMPHeader = () => {
  instance.defaults.headers.common.app = 'zmp';
  graphqlInstance.defaults.headers.common.app = 'zmp';
};

export const graphqlRequest = (query: string, variables?: {[key: string]: any}, config?: AxiosRequestConfig<{query: string; variables: {[key: string]: any} | undefined}> | undefined) => {
  return graphqlInstance.post(env.API_BASE_URL + '/graphql', {query, variables}, config);
};

export function makeRequestCreator() {
  var call: CancelTokenSource;

  return function (url: string, data?: any, config?: AxiosRequestConfig<any> | undefined) {
    if (call) {
      call.cancel();
    }
    call = axios.CancelToken.source();
    return instance
      .post(url, data, {
        cancelToken: call.token,
        ...config,
      })
      .then(response => {
        return response;
      })
      .catch(function (thrown) {
        if (axios.isCancel(thrown)) {
          // console.log('First request canceled', thrown.message);
        } else {
          throw thrown;
        }
      });
  };
}

export const axiosCancellable = makeRequestCreator();

export default instance;
