import {produce} from 'immer';
import {useGetCurrentUser} from './auth';
import {useMutation} from '@tanstack/react-query';
import axios from './axios';
import {getUserAgent} from '~utils/helpers/userAgent';
import {Platform} from 'react-native';

type FeedbackType = {
  response: number | null;
  feedback: string | null;
  features: 'in-app-rate' | 'dropship-app' | 'dropship-inspiration';
  feature_id?: any;
};

type TTSAnalyticsEvent = {
  action: 'view';
  agent?: string;
  category: 'product';
  object_id: string;
  object_metadata: {
    page?: string;
    product_id?: string;
    product_name?: string;
    shop_id?: string;
  };
  object_title: string;
  object_type: 'product';
  platform?: string;
  referrer?: string;
  user_id?: string;
  user_title?: string;
  user_type?: string;
};

export const createFeedback = async (
  feedback: FeedbackType = {
    response: null,
    feedback: null,
    features: 'in-app-rate',
    feature_id: null,
  },
) => {
  return axios.post('/v1/analytics/api/survey/satisfied', feedback);
};

export const createAnalyticsEvent = async (event: Partial<TTSAnalyticsEvent>) => {
  return axios.post('/v1/analytics/api/event', event);
};

// export const createAnalytics

export const useCreateAnalyticsEventMutation = () => {
  const {data} = useGetCurrentUser();
  return useMutation<any, any, Partial<TTSAnalyticsEvent>>(
    params =>
      new Promise(async () => {
        await createAnalyticsEvent(
          await produce(params, async draft => {
            draft.agent = await getUserAgent();
            draft.platform = `dropship_buyer_${Platform.OS}`;
            if (data) {
              draft.referrer = '';
              draft.user_id = data.id;
              draft.user_title = data.full_name;
              draft.user_type = data.shop_name ? 'seller' : 'buyer';
            }
          }),
        );
      }),
  );
};
