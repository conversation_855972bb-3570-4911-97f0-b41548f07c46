import Cart, {CartItem} from '~utils/types/cart';
import axios from './axios';
import qs from 'query-string';
import {useMutation, useQuery, useQueryClient} from '@tanstack/react-query';
import {useAuthActions} from '~hooks';

type UpdateCartItem = Pick<CartItem, 'variant_id' | 'quantity' | 'note' | 'dropship_selling_price'>;
type GetCartQuery = {
  dropship?: boolean;
};

type AddToCartPayload = {
  user_id?: string;
  items: UpdateCartItem[];
};

export const getCart = async (query: GetCartQuery): Promise<Cart | undefined> => {
  const queryString = qs.stringify(query);
  const {data} = await axios.get(`/v2/order/api/carts.json?${queryString}`);
  return data.carts || {};
};

export const updateCart = async (payload: AddToCartPayload) => {
  try {
    const {data} = await axios.post('/v2/order/api/carts/update.json', {...payload, dropship: true});
    return data.cart;
  } catch (error: any) {
    if (error?.response) {
      throw error?.response.data;
    } else {
      throw error;
    }
  }
};

export const useGetCart = (query?: GetCartQuery) => {
  const {isLoggedIn} = useAuthActions();

  return useQuery(['cart'], () => getCart({dropship: true, ...query}), {enabled: isLoggedIn, staleTime: 0});
};

export const useAddToCartMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Cart, any, AddToCartPayload>(updateCart, {
    onSuccess: data => {
      queryClient.setQueryData(['cart'], data);
    },
  });
};
