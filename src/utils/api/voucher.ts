import {useQuery} from '@tanstack/react-query';
import axios from './axios';
import qs from 'query-string';

type ShopPriceRulesRequestQuery = {
  shop_id: string;
  prerequisite_subtotal_price?: number;
  sort_by?: 'value';
  order?: 'asc' | 'desc';
  target_type?: 'shipping_line' | 'commission';
};

export type ShopPriceRule = {
  id: string;
  shop_id: string;
  title: string;
  description: any;
  discount_code: DiscountCode;
  value_type: string;
  value: number;
  customer_selection: string;
  target_type: string;
  target_selection: string;
  allocation_method: string;
  allocation_limit: any;
  once_per_customer: boolean;
  usage_limit: number;
  starts_at: string;
  ends_at: string;
  created_at: string;
  updated_at: string;
  entitled_product_ids: any[];
  entitled_variant_ids: any[];
  entitled_collection_ids: any[];
  entitled_country_ids: any[];
  prerequisite_product_ids: any[];
  prerequisite_variant_ids: any[];
  prerequisite_collection_ids: any[];
  prerequisite_saved_search_ids: any[];
  prerequisite_customer_ids: any[];
  prerequisite_subtotal_range: PrerequisiteSubtotalRange;
  prerequisite_quantity_range: any;
  prerequisite_shipping_price_range: any;
  link: string | null;
};

export type DiscountCode = {
  discount_code: string;
  id: string;
  price_rule_id: string;
  usage_count: number;
};

export type PrerequisiteSubtotalRange = {
  greater_than_or_equal_to: number;
};

const getShopPriceRules = async (query: ShopPriceRulesRequestQuery): Promise<ShopPriceRule[]> => {
  const queryString = qs.stringify(query);
  const res = await axios.get(`/v2/order/api/price_rules.json?${queryString}`);
  return res.data.price_rules;
};

export const useGetShopPriceRules = (query: ShopPriceRulesRequestQuery) => useQuery(['shop_price_rules', query], () => getShopPriceRules(query));
