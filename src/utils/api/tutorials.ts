import {useQuery} from '@tanstack/react-query';
import axios from './axios';
import {REACT_QUERY_STALE_TIME} from '~utils/helpers/reactQuery';

export type Tutorial = {
  id: number;
  platform: string;
  title: string;
  type: string;
  thumbnail: string;
  url: string;
};

const getTutorials = async (): Promise<Tutorial[]> => {
  const {data} = await axios.get('/v1/marketing/api/tutorials/?platform=dropship');
  return data;
};

export const useGetTutorials = () => {
  return useQuery({queryKey: ['tutorials'], queryFn: getTutorials, staleTime: REACT_QUERY_STALE_TIME});
};

//  https://api.thitruongsi.com/v1/marketing/api/tutorials/track

export const trackViewTutorial = async (tutorialId: number) => {
  await axios.post('/v1/marketing/api/tutorials/track', {
    tutorial_id: tutorialId,
  });
};
