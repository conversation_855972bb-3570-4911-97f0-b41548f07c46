import {useInfiniteQuery, useMutation, useQuery, useQueryClient} from '@tanstack/react-query';
import {useAuthActions} from '~hooks';
import {REACT_QUERY_CACHE_TIME} from '~utils/helpers/reactQuery';
import {SearchShop, ShopV1} from '~utils/types/shop';
import axios, {graphqlRequest} from './axios';
import qs from 'query-string';
import {Aggregations} from '~utils/types/common';
import {ProductRequestQuery} from '~utils/types/product';

const SearchShopsGql = `
query searchShopQuery($query: String!) {
  searchShops(query: $query) {
    shops {
      id
      name
      logo
      city
      is_vip
      is_business
      mall
      created_at
      slug
      rating_avg
      rating_count
      total_products
      total_follows
    }
    aggregations {
      filter_category_lv1 {
        buckets {
          key
          category_id
          title
          slug
          doc_count
          image
        }
      }
      filter_category_lv2 {
        buckets {
          key
          category_id
          title
          slug
          doc_count
          image
        }
      }
      filter_province {
        buckets {
          key
          category_id
          title
          slug
          doc_count
          image
        }
      }
    }
    total
  }
}
`;

export type SearchShopsResponse = {
  shops: SearchShop[];
  total: number;
  offset: number;
  aggregations: Aggregations;
};

type GetShopFollowingRequestQuery = Partial<{
  limit: number;
  offset: number;
}>;

export const getShopInfo = async (slug: string): Promise<ShopV1> => {
  const {data} = await axios.get(`/v1/user/api/shop/${slug}`);
  return data.data.shop;
};

export const useGetShop = (slug: string, options?: any) =>
  useQuery(['shop', slug], () => getShopInfo(slug), {
    ...options,
  });

export const getIsFollowing = async (shopId: string) => {
  const {data} = await axios.get(`/v1/user/api/shops/${shopId}/is-following`);
  return Boolean(data.data.is_following);
};

export const useGetIsFollowing = (shopId: string) => {
  const {isLoggedIn} = useAuthActions();
  return useQuery(['is_following', shopId], () => getIsFollowing(shopId), {
    staleTime: REACT_QUERY_CACHE_TIME.OneHour,
    enabled: isLoggedIn,
  });
};

export const updateFollowShop = async (shopId: string, followState: boolean) => {
  await axios[followState ? 'post' : 'delete'](`/v1/user/api/shops/${shopId}/follow`);
  return;
};

export const useUpdateFollowShopMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<any, any, {shopId: string; followState: boolean}>(({shopId, followState}) => updateFollowShop(shopId, followState), {
    onSuccess: (_res, {shopId, followState}) => {
      queryClient.invalidateQueries(['is_following', shopId]);
      if (!followState) {
        queryClient.invalidateQueries(['shop_following']);
      }
    },
  });
};

const SHOP_FOLLOWING_LIMIT = 20;
export const getFollowingShop = async (query: GetShopFollowingRequestQuery) => {
  const queryString = qs.stringify({...query, limit: SHOP_FOLLOWING_LIMIT, offset: query.offset || 0});
  const {data} = await axios.get(`/v1/user/api/v4/me/shops/following${queryString ? `?${queryString}` : ''}`);
  return {shops: data.data.shops, offset: query.offset || 0, total: data.data.total};
};

export const useGetFollowingShop = (query: GetShopFollowingRequestQuery) => {
  return useInfiniteQuery(['shop_following'], ({pageParam}) => getFollowingShop({...query, offset: pageParam}), {
    getNextPageParam: lastPage => {
      if (lastPage.offset + SHOP_FOLLOWING_LIMIT < lastPage.total) {
        return lastPage.offset + SHOP_FOLLOWING_LIMIT;
      }
    },
  });
};

const SUPPLIER_LIMIT = 20;

export const searchSuppliers = async (query: ProductRequestQuery): Promise<SearchShopsResponse> => {
  const queryString = qs.stringify({...query, limit: query.limit || SUPPLIER_LIMIT, filter_only_dropship: true}, {skipNull: true, arrayFormat: 'comma'});
  const res: any = await graphqlRequest(SearchShopsGql, {query: `?${queryString}`});

  if (res.data.data === null && res.data.errors?.length) {
    throw res.data.errors[0]?.message;
  }

  return {...res.data.data.searchShops, offset: query.offset ?? 0};
};

export const useSearchSuppliers = (query: ProductRequestQuery) => {
  return useQuery(['suppliers', query], () => searchSuppliers(query));
};

export const useGetSearchSuppliersInfinite = (query: ProductRequestQuery) => {
  return useInfiniteQuery(['suppliers_infinite', query], ({pageParam}) => searchSuppliers({...query, limit: SUPPLIER_LIMIT, offset: pageParam}), {
    getNextPageParam: lastPage => {
      if (lastPage.offset + SUPPLIER_LIMIT < lastPage.total) {
        return lastPage.offset + SUPPLIER_LIMIT;
      }
    },
  });
};
