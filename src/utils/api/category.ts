import {useQuery} from '@tanstack/react-query';
import {REACT_QUERY_CACHE_TIME} from '~utils/helpers/reactQuery';
import Category from '~utils/types/category';
import {graphqlRequest} from './axios';

const getCategoriesGql = `
query GetCategories($query: String!) {
  categories(query: $query) {
    id
    category_id
    title
    thumb_image_src
    slug
  }
}
`;

const getCategoryGql = `
query GetCategory($id: String!, $query: String) {
  category(id: $id, query: $query) {
    category {
      id
      title
      thumb_image_src
      level
      parent_id
      category_id
      slug
    }
    relative_categories {
      id
      title
      thumb_image_src
      level
      parent_id
      slug
      category_id
    }    
  }
}
`;

type CategoryDetailResponse = {
  category: Partial<Category>;
  relative_categories: Partial<Category>[];
};

export const getCategories = async (level?: number): Promise<Partial<Category>[]> => {
  const res = await graphqlRequest(getCategoriesGql, {query: `?dropship=true&level=${level ?? 1}`});
  if (res.data.data === null && res.data.errors?.length) {
    throw res.data.errors[0]?.message;
  }
  return res.data.data.categories;
};

export const useGetCategories = (level?: number) => useQuery(['categories_gql'], () => getCategories(level), {staleTime: REACT_QUERY_CACHE_TIME.FiveMinutes});

export const getCategory = async (categoryId: string | number, query?: string | null): Promise<CategoryDetailResponse> => {
  const res = await graphqlRequest(getCategoryGql, {id: categoryId, query: `dropship=true&${query ?? ''}`});
  return res.data.data.category;
};

export const useGetCategory = (categoryId: string | number, query?: string | null) => useQuery(['category', categoryId], () => getCategory(categoryId, query));
