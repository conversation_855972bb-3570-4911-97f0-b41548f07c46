import {ChatGPTMessage, ChatGPTPromptUsage} from '~utils/types/chatgpt';
import axios from './axios';
import qs from 'query-string';
import {InfiniteData, useInfiniteQuery, useMutation, useQuery, useQueryClient} from '@tanstack/react-query';
import {nanoid} from 'nanoid';

type GenerateProductPostParams = {
  product_id: string;
  type: 'product_post';
  prompt: string | null;
  conversation_id: string | null;
  new_conversation: boolean;
};

type GetChatGPTMessagesParams = {
  type: 'product_post';
  product_id: string;
  limit?: number;
  cursor?: string;
};

type GenerateProductPostResponse = {
  conversation_id: string;
  success: boolean;
  request_prompt: string;
  usage: ChatGPTPromptUsage;
  content: string;
  prompt_templates: string[];
};

type GetChatGPTMessageResponse = {
  data: ChatGPTMessage[];
  path: string;
  per_page: number;
  next_cursor: string;
  next_page_url: string;
  prev_cursor: string;
  prev_page_url: string;
};

type GetPromptTemplatesParams = {
  type: 'product_post';
};

export const generateProductPost = async (params: GenerateProductPostParams): Promise<GenerateProductPostResponse> => {
  const {data} = await axios.post('/v1/ai/api/content_generation/product_posts.json', params);
  return data;
};

export const getChatGPTMessages = async (params: GetChatGPTMessagesParams): Promise<GetChatGPTMessageResponse> => {
  const queryString = qs.stringify(params);
  const {data} = await axios.get(`/v1/ai/api/chat/messages.json${queryString ? `?${queryString}` : ''}`);
  return data;
};

const getChatGPTProductPostQueryKey = (params: GetChatGPTMessagesParams) => {
  return ['chatgpt', params.type, params.product_id];
};

export const useGetChatMessages = (params: GetChatGPTMessagesParams) => {
  return useInfiniteQuery<GetChatGPTMessageResponse, Error>(getChatGPTProductPostQueryKey(params), ({pageParam = null}) => getChatGPTMessages({...params, cursor: pageParam}), {
    getNextPageParam: lastPage => {
      if (lastPage?.next_cursor) {
        return lastPage.next_cursor;
      }
      return undefined;
    },
  });
};

export const useGenerateProductPostMutation = () => {
  const queryClient = useQueryClient();

  return useMutation<GenerateProductPostResponse, Error, GenerateProductPostParams>(
    params => {
      return generateProductPost(params);
    },
    {
      onMutate: variables => {
        // if (variables.new_conversation) {
        //   // @ts-ignore
        //   queryClient.setQueriesData(getChatGPTProductPostQueryKey(variables), old => ({
        //     // @ts-ignore
        //     ...old,
        //     pages: [],
        //   }));
        // }
        if (variables.prompt && !variables.new_conversation) {
          // @ts-ignore
          const optimisticMessage: ChatGPTMessage = {
            _id: nanoid(),
            role: 'user',
            content: variables.prompt as string,
          };

          // @ts-ignore
          queryClient.setQueryData<InfiniteData<GetChatGPTMessageResponse>>(getChatGPTProductPostQueryKey(variables), old => {
            if (old) {
              const firstPage = old.pages[0];
              const newFirstPage = {...firstPage, data: [optimisticMessage, ...firstPage.data]};

              return {
                ...old,
                pages: old.pages.length <= 1 ? [newFirstPage] : [newFirstPage, ...old.pages.slice(1)],
              };
            }

            return null;
          });
        }
      },

      onSuccess: (data, variables) => {
        queryClient.invalidateQueries(getChatGPTProductPostQueryKey(variables));
      },
    },
  );
};

const getPromptTemplates = async (params: GetPromptTemplatesParams): Promise<string[]> => {
  const queryString = qs.stringify(params);
  const {data} = await axios.get(`/v1/ai/api/content_generation/prompt_templates.json${queryString ? `?${queryString}` : ''}`);

  return data.prompt_templates;
};

export const useGetPromptTemplates = (params: GetPromptTemplatesParams) => {
  return useQuery(['chatgpt', 'prompt_templates', params], () => getPromptTemplates(params));
};
