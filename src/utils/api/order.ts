import {OrderConfirmStatus, OrderFulfillmentEvent, OrderStatus, OrderFinancialStatus} from './../types/order';
import {useInfiniteQuery, useMutation, useQuery, useQueryClient} from '@tanstack/react-query';
import {DeepPartial, GraphqlListResponse} from '~utils/types/common';
import Order from '~utils/types/order';
import axios, {axiosCancellable, graphqlRequest} from './axios';
import qs from 'query-string';
import Address from '~utils/types/address';

export type OrdersRequestQuery = {
  page?: number;
  limit?: number;
  status?: OrderStatus;
  buyer_status?: 'any' | 'wait_confirm' | 'wait_checkout' | 'in_transit' | 'delivered' | 'cancelled';
  confirm_status?: OrderConfirmStatus;
  financial_status?: OrderFinancialStatus;
  query?: string;
  dropship?: boolean;
};

const OrderListQuery = `
  query OrderListQuery($query: String!) {
    orders(query: $query) {
      items {
        id
        status
        confirm_status
        financial_status
        fulfillment_status
        shipment_status
        shipment_status_text
        name
        created_at
        dropship_marketplace
        source_name
        source_identifier
        total_price
        line_items {
          image_src
          quantity
        }
        customer {
          id
          first_name
          last_name
        }
        status_label
        rate_expires_at
        rated_at
        escrow_release_at
       
        shipping {
          bill_of_lading {
            bill_of_lading
          }
        }
      }
      extensions {
        totalItems
        totalPages
        currentPage
        currentLimit
      }
    }
  }
`;

type ShippingRate = {
  origin: {
    shop_id: string;
  };
  destination:
    | {
        customer_address_id: string | number;
      }
    | Partial<Address>;
  items: {
    quantity: number;
    product_id: string;
    variant_id: string;
    dropship?: boolean;
  }[];
  cod?: boolean;
};

export const getShippingRate = async (rate: ShippingRate) => {
  const response = await axiosCancellable('/v2/order/api/shipping_rates.json', {rate});
  return response;
};

export const createOrder = async (order: DeepPartial<Order>): Promise<Order> => {
  const {data} = await axios.post('/v2/order/api/orders.json', {order});
  return data.order;
};

export const getOrder = async (orderId: number | string): Promise<Order> => {
  const {data} = await axios.get(`/v2/order/api/orders/${orderId}.json`);
  return data.order;
};

export const getOrderQueryKey = (orderId: number | string) => ['order', String(orderId)];

export const useGetOrder = (orderId: number | string) =>
  useQuery(getOrderQueryKey(orderId), () => getOrder(orderId), {
    staleTime: 0,
    refetchOnWindowFocus: true,
  });

const getFulfillmentEvents = async (orderId: number): Promise<OrderFulfillmentEvent[]> => {
  try {
    const {data} = await axios.get(`/v2/order/api/orders/${orderId}/fulfillment_events.json`);
    return data.fulfillment_events;
  } catch (error) {
    return [];
  }
};

export const useGetFulfillmentEvents = (orderId: number) =>
  useQuery(['fulfillment_events', orderId], () => getFulfillmentEvents(orderId), {
    staleTime: 0,
  });

export const getOrders = async (query: OrdersRequestQuery): Promise<GraphqlListResponse<Order>> => {
  try {
    const queryString = `?${qs.stringify(query)}`;
    const res = await graphqlRequest(OrderListQuery, {query: queryString});
    // const res = await axios.get(`/v2/order/api/orders.json${queryString}`);
    return res.data.data.orders;
  } catch (error: any) {
    if (error && error.response) {
      throw error.response;
    }
    throw error;
  }
};

export const useGetOrders = (query: OrdersRequestQuery, options = {}) =>
  useQuery(['orders', query], () => getOrders({...query, dropship: true}), {
    staleTime: 0,
    ...options,
  });

export const useGetOrdersInfinite = (query: OrdersRequestQuery) =>
  useInfiniteQuery(['orders_infinite', query], ({pageParam}) => getOrders({page: pageParam, ...query, dropship: true}), {
    staleTime: 0,
    getNextPageParam: lastPage => {
      if (lastPage.extensions.currentPage < lastPage.extensions.totalPages) {
        return lastPage.extensions.currentPage + 1;
      }
    },
  });

export const useSearchOrders = (query: OrdersRequestQuery) =>
  useInfiniteQuery(['search_orders', query], ({pageParam}) => getOrders({page: pageParam, ...query, dropship: true}), {
    staleTime: 0,
    enabled: !!query.query,
    getNextPageParam: lastPage => {
      if (lastPage.extensions.currentPage < lastPage.extensions.totalPages) {
        return lastPage.extensions.currentPage + 1;
      }
    },
  });

export const cancelOrder = async (orderId: number, reason: string): Promise<Order> => {
  const {data} = await axios.post(`/v2/order/api/orders/${orderId}/confirmed_cancelled.json`, {
    order: {
      id: orderId,
      confirm_status: 'cancelled',
      confirm_cancelled_reason: reason,
    },
  });

  return data.order;
};

export const useCancelOrderMutation = () => {
  const queryClient = useQueryClient();

  return useMutation<Order, any, {orderId: number; reason: string}, any>(({orderId, reason}) => cancelOrder(orderId, reason), {
    onSuccess: data => {
      if (data) {
        queryClient.setQueryData(getOrderQueryKey(data.id), data);
      }
    },
  });
};

// https://api-sandbox.thitruongsi.com/v2/order/api/orders/3965/fulfillment_label.json
export const updateOrderFulfillmentLabel = async (orderId: number, label: any) => {
  const data = new FormData();
  data.append('label', label as any);
  const res = await axios.post(`/v2/order/api/orders/${orderId}/fulfillment_label.json`, data);
  return res;
};

export const useUpdateOrderFulfillmentLabel = () => {
  const queryClient = useQueryClient();

  return useMutation<any, any, {orderId: number; label: any}>(({orderId, label}) => updateOrderFulfillmentLabel(orderId, label), {
    onSuccess: (_, {orderId}) => {
      queryClient.invalidateQueries(getOrderQueryKey(orderId));
    },
  });
};

export const createOrderTicket = (ticket: {[key: string]: any}) => {
  const ticketFormData = new FormData();
  ticketFormData.append('description', ticket.description);
  ticketFormData.append('type', ticket.type);
  ticketFormData.append('order_id', ticket.order_id);

  if (ticket.attachments && ticket.attachments.length > 0) {
    for (let i = 0; i < ticket.attachments.length; i++) {
      ticketFormData.append('attachments[]', ticket.attachments[i]);
    }
  }

  return axios.post(`/v2/order/api/orders-tickets.json`, ticketFormData);
};
