import {REACT_QUERY_CACHE_TIME} from '~utils/helpers/reactQuery';
import {env} from '~utils/config';
import {WP_REST_API_Post} from './../types/wp-json';
import axios from './axios';
import qs from 'query-string';
import {useQuery} from '@tanstack/react-query';

type GetPostsQuery = {
  doc_category?: number | number[];
  per_page?: number;
  _embed?: boolean;
  context?: 'view' | 'edit' | 'embed';
};

export const getDocs = async (query: GetPostsQuery = {}): Promise<WP_REST_API_Post[]> => {
  const queryString = qs.stringify(query);
  const res = await axios.get(env.BLOG_REST_URI + `/docs?${queryString}`);
  return res.data;
};

export const useGetDocs = (query: GetPostsQuery = {}) =>
  useQuery(['blog-post', query], () => getDocs(query), {staleTime: REACT_QUERY_CACHE_TIME.FiveMinutes, cacheTime: REACT_QUERY_CACHE_TIME.OneHour});
