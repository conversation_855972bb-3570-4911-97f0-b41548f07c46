import {REACT_QUERY_CACHE_TIME} from './../helpers/reactQuery';
import {useQuery} from '@tanstack/react-query';
import axios from './axios';

type Location_Province = {
  [key: string]: {
    [key: string]: string[];
  };
};

export const getLocations = async (): Promise<Location_Province[]> => {
  const res = await axios.get('/v1/user/api/vi/locations.json');
  return res.data.locations;
};

export const useGetLocations = () =>
  useQuery(['locations'], getLocations, {
    staleTime: REACT_QUERY_CACHE_TIME.FiveMinutes,
  });
