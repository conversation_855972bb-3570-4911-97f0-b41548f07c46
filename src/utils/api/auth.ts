import {REFRESH_TOKEN_KEY} from './../config/keys/index';
import {useAuthActions} from '~hooks';
import axios from './axios';
import {useMutation, useQuery, useQueryClient, useInfiniteQuery} from '@tanstack/react-query';
import {env} from '~utils/config';
import {MyIdCard, ReferralCode, Referrer, User} from '~utils/types/user';
import * as Sentry from '@sentry/react-native';
import {REACT_QUERY_CACHE_TIME} from '~utils/helpers/reactQuery';
import qs from 'query-string';
import AsyncStorage from '~utils/helpers/storage';

export type RefreshTokenSuccessData = {
  access_token: string;
  account_phone: string;
  refresh_token: string;
  expires_at: number;
  expires_in: number;
  first_name: string;
  full_name: string;
  last_name: string;
  scope: string;
};

export type DropshipProfile = {
  card_bg_img: string;
  card_border_color: string;
  order_count: number;
  rank: number;
  sales: number;
  title: string;
};

export type GetMyReferrersQuery = {
  limit?: number;
  page?: number;
};

type ExchangeTokenResult = {
  user: User;
  token: {
    access_token: string;
    expires_in: number;
    expires_at: number;
    scope: string;
    roles: number;
    sub: string;
    first_name: string;
    last_name: string;
    full_name: string;
    cre: number;
    referral_source: any;
    account_phone: string;
    phone_verified: number;
    phone_verify_required: number;
    register_step4_required: number;
    refresh_token: string;
  };
  oauth_user: {
    key: string;
    driver: string;
    created_at: string;
    oauth_id: string;
    updated_at: string;
    _id: string;
  };
};

type VerifyOTPSuccessResponse = Pick<ExchangeTokenResult, 'token' | 'user'>;

type VerifyOTPZaloParams = {
  access_token: string;
  code: string;
  oauth_key: string;
};

type VerifyOTPParams = {
  phone: string;
  code?: string;
  oauth_key: string;
  session_info_token?: string;
  firebase_id_token: string;
};

type SaveIdCardParams = {
  front_attachment_id: string;
  back_attachment_id: string;
  info: {
    address: string;
    issued_date: string;
    name: string;
    id_num: string;
    id_type: string;
  };
};
type LookbackDataResponse = {
  id: string;
  order_count: number;
  sales: number;
  highest_value_order_id: number;
  commission_earned: number;
  most_sales_product_id: string;
  most_sales_shop_id: string;
  sales_rank: number;
  sales_rank_percentile: number;
  most_sales_shop_order_count: number;
  top_sales_products: {
    dropship_price: number;
    img: string;
    product_id: string;
    profit: number;
    quantity: number;
    revenue: number;
    title: string;
  }[];
};

type UpdateTaxInformationParams = {
  tax_id: string;
  tax_address: string;
  tax_name: string;
  tax_email: string[];
  tax_type: string;
};

export const refreshToken = async (): Promise<RefreshTokenSuccessData> => {
  const refreshTokenString = await AsyncStorage.getItem(REFRESH_TOKEN_KEY);

  if (refreshTokenString) {
    const {data} = await axios.get(env.API_BASE_URL + `/v1/user/api/token/refresh?refresh_token=${refreshTokenString}`);
    return {...data.data, refresh_token: refreshTokenString};
  } else {
    throw new Error('REFRESH TOKEN NOT FOUND');
  }
};

export const getCurrentUser = async (): Promise<User | undefined> => {
  try {
    const res = await axios.get('/v1/user/api/auth/info');
    return res?.data?.data?.user;
  } catch (error) {}
};

export const useGetCurrentUser = () => {
  const {isLoggedIn} = useAuthActions();

  return useQuery(['user'], getCurrentUser, {
    enabled: isLoggedIn,
    staleTime: REACT_QUERY_CACHE_TIME.FiveMinutes,
    refetchOnWindowFocus: true,
    onSuccess: user => {
      if (user) {
        Sentry.setUser({
          id: user.id,
          email: user.email,
          username: user.full_name,
        });
      }
    },
  });
};

export const getUserDropshipProfile = async (): Promise<DropshipProfile> => {
  const res = await axios.get('/v1/user/dropship-profile/me');
  return res.data;
};

export const useGetUserDropshipProfile = () => {
  return useQuery(['dropship-profile'], getUserDropshipProfile, {
    staleTime: REACT_QUERY_CACHE_TIME.FiveMinutes,
  });
};

export const getChatWootIdHash = () => {
  return axios.get('/v1/user/id/chatwoot?inbox=3');
};

export const updateUserInfo = async (userInfo: any) => {
  const {data} = await axios.put('/v1/user/api/v4/users/me', userInfo);

  return data;
};

export const useUpdateUserInfoMutation = () => {
  const queryClient = useQueryClient();
  const {login, authData} = useAuthActions();

  return useMutation<any, any, Partial<User>>(data => updateUserInfo(data), {
    onSuccess: data => {
      if (data?.data?.refresh_token) {
        login({
          access_token: authData.access_token,
          refresh_token: data.data.refresh_token,
        });
      }
      queryClient.invalidateQueries(['user']);
    },
  });
};

export const requestToDeleteAccount = async () => {
  const {data} = await axios.post('/v1/user/api/me/delete');
  return data;
};
// https://api-sandbox.thitruongsi.com/v1/user/api/v4/referral_codes/link?code=LEQUY
export const linkRefCode = async (refCode: string) => {
  const {data} = await axios.put(`/v1/user/api/v4/referral_codes/link?code=${refCode}`);
  return data;
};

// https://api-sandbox.thitruongsi.com/v1/user/api/v4/referral_codes/check_available?code=LEQUY
export const checkRefCodeAvailable = async (refCode: string): Promise<{available: boolean; suggest: string[]}> => {
  const {data} = await axios.get(`/v1/user/api/v4/referral_codes/check_available?code=${refCode}`);
  return data;
};

// https://api-sandbox.thitruongsi.com/v1/user/api/v4/referral_codes/
export const getRefCodeDetail = async (refCode: string): Promise<ReferralCode> => {
  const {data} = await axios.get(`/v1/user/api/v4/referral_codes/${refCode}`);
  return data.code;
};

export const useGetRefCodeDetail = (refCode: string) => useQuery(['ref_code', refCode], () => getRefCodeDetail(refCode));

export const getMyReferrers = async (query: GetMyReferrersQuery): Promise<{total: number; referrers: Referrer[]; page: number}> => {
  const queryString = qs.stringify({...query, limit: query.limit || 50});
  const {data} = await axios.get(`/v1/marketing/api/affiliate/me/referrers?${queryString}`);
  return {...data, page: query.page || 1};
};

export const useGetMyReferrers = (query: GetMyReferrersQuery) => {
  return useInfiniteQuery(['my_referrers', query], ({pageParam}) => getMyReferrers({page: pageParam || 1}), {
    getNextPageParam: lastPage => {
      const totalPage = Math.ceil(lastPage.total / (query.limit || 50));
      if (lastPage.page < totalPage) {
        return lastPage.page + 1;
      }
    },
  });
};

const getAffiliateStatistic = async (): Promise<{total_referrers: number; total_commission: number}> => {
  const {data} = await axios.get('/v1/marketing/api/affiliate/statistic');
  return data;
};

export const useGetAffiliateStatistic = () => {
  return useQuery(['aff_statistic'], getAffiliateStatistic);
};

export const getOAuthDrivers = async (): Promise<string[]> => {
  const {data} = await axios.get('/v1/user/oauth/drivers');

  return data.drivers;
};

export const useGetOAuthDrivers = () => {
  return useQuery(['oauth_drivers'], getOAuthDrivers, {
    staleTime: REACT_QUERY_CACHE_TIME.OneHour,
    initialData: ['zalo', 'google', 'apple'],
  });
};

export const getConnectURL = async (driver: string, redirect_uri: string): Promise<{driver: string; url: string}> => {
  const {data} = await axios.get(`/v1/user/oauth/connect?driver=${driver}&redirect=false&redirect_uri=${redirect_uri}`);
  return data;
};

export const zmpExchangeToken = async (accessToken: string): Promise<ExchangeTokenResult> => {
  const {data} = await axios.get(`/v1/user/oauth/zmp?access_token=${accessToken}`);
  return data;
};

export const getOTP = async (params: {phone: string; oauth_key: string; recaptcha_token: string}): Promise<{message: string; via: string; session_info_token: string}> => {
  const {data} = await axios.get(`v1/user/oauth/get-otp?phone=${params.phone}&oauth_key=${params.oauth_key}&recaptcha_token=${params.recaptcha_token}&sitekey=${env.RECAPTCHA_SITE_KEY}`);
  return data;
};

export const verifyOTPZalo = async (params: VerifyOTPZaloParams): Promise<VerifyOTPSuccessResponse> => {
  const queryString = qs.stringify(params);
  // const {data} = await axios.post(`https://9773-45-122-222-247.ngrok-free.app/oauth/verify-otp-zalo?${queryString}`);
  const {data} = await axios.post(`/v1/user/oauth/verify-otp-zalo?${queryString}&sitekey=${env.RECAPTCHA_SITE_KEY}`);

  return data;
};

export const verifyOTP = async (params: VerifyOTPParams): Promise<VerifyOTPSuccessResponse> => {
  const queryString = qs.stringify(params);
  const {data} = await axios.post(`/v1/user/oauth/verify-otp?${queryString}&sitekey=${env.RECAPTCHA_SITE_KEY}&firebase_id_token=${params.firebase_id_token}`);
  return data;
};

export const verifyIdCardSave = async (params: SaveIdCardParams) => {
  const {data} = await axios.post(`/v1/user/verify-idcard/save`, params);

  return data;
};

export const idCardUpload = async (file: File, type: 'front' | 'back') => {
  const formData = new FormData();
  formData.append('image', file);
  formData.append('type', type);

  const {data} = await axios.post(`/v1/user/verify-idcard`, formData);

  return data;
};

const getMyIdCard = async (): Promise<MyIdCard> => {
  const {data} = await axios.get('/v1/user/idcard/');
  return data.id_card;
};

export const useGetMyIdCard = () => {
  return useQuery(['my_idcard'], getMyIdCard);
};

export const getLookbackData = async (): Promise<LookbackDataResponse> => {
  const {data} = await axios.get('/v1/dwh/lookback');
  return data;
};

export const useGetLookbackData = () => {
  return useQuery(['lookback_data'], getLookbackData, {
    staleTime: REACT_QUERY_CACHE_TIME.FiveMinutes,
  });
};

export const updateTaxInformation = async (params: UpdateTaxInformationParams) => {
  const {data} = await axios.post('/v1/user/tax-information', params);
  return data;
};

const getIdentityVerificationStatus = async (): Promise<{rollback: boolean}> => {
  const {data} = await axios.get('https://accounts.thitruongsi.com/feature-flag/id-verification');
  return data;
};

export const useGetIdentityVerificationStatus = () => {
  return useQuery(['identity_verification_status'], getIdentityVerificationStatus);
};
