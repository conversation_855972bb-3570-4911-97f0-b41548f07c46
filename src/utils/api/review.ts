import {Asset} from 'react-native-image-picker';
import axios from './axios';
import {imagePickerAssetsToFile} from '~utils/helpers/image';
import {UseQueryOptions, useInfiniteQuery, useQuery} from '@tanstack/react-query';
import qs from 'query-string';
import {Image as ImageResizer} from 'react-native-compressor';

export type ReviewsRequestQuery = Partial<{
  product_id: string;
  shop_id: string;
  rating: number;
  ascending: boolean;
  page: number;
  limit: number;
}>;

type ReviewAttachment = {
  attachment_id: string;
  file_size: number;
  media_type: 'image' | 'video';
  url: string;
};

export type Rating = {
  action_type: string;
  action_id: string;
  seller_service: number;
  delivery_service: number;
  created_at: string;
  line_items: RateLineItem[];
  comment: string;
  anonymous: boolean;
};

export type RateLineItemAttachment = {
  _id: string;
  created_at: string;
  encoded_at: string;
  file_size: number;
  media_type: 'image' | 'video';
  duration: number;
  path: string;
  rating_id: string;
  updated_at: string;
  url: string;
  user_id: string;
  video_thumb?: string;
  video_thumb_url?: string;
};

export type RateLineItem = {
  variant_id: string;
  comment: string;
  quality_rate: number;
  photos: RateLineItemAttachment[];
  videos: RateLineItemAttachment[];
  variant_title?: string;
  product_id: string;
  product_id_number: number;
  product_img: string;
  product_title: string;
  replies: ReviewReply[];
};

type ReviewReply = {
  _id: string;
  content: string;
  created_at: string;
  product_rating_id: string;
  updated_at: string;
};

export type ProductReview = {
  rating_avg: number;
  rating_count: number;
  rating_aggs: RatingAggs;
  reviews: Review[];
};

export type RatingAggs = {
  '1': Agg;
  '2': Agg;
  '3': Agg;
  '4': Agg;
  '5': Agg;
};

type Agg = {
  count: number;
  percent: number;
};

export type Review = {
  _id: string;
  order_id: string;
  variant_id: string;
  variant_title: string;
  product_id: string;
  rating: number;
  comment: string;
  user_id: string;
  user: {
    name: string;
    avatar: string;
    province: string;
  };
  order_created_at: string;
  order_received_at: string;
  photos: RateLineItemAttachment[];
  videos: RateLineItemAttachment[];
  updated_at: string;
  created_at: string;
  id: string;
  label: string;
};

export const uploadAttachment = async (attachment: Asset, compress?: boolean): Promise<ReviewAttachment | undefined> => {
  try {
    const form = new FormData();

    let attachmentToUpload = {...attachment};
    if (compress && attachment.uri) {
      attachmentToUpload.uri = await ImageResizer.compress(attachment.uri, {
        input: 'uri',

        maxWidth: 2048,
        quality: 0.9,
        output: 'jpg',
      });
    }
    form.append('file', imagePickerAssetsToFile(attachmentToUpload) as any);
    const res = await axios.post('/v1/review/attachments', form);
    return res.data;
  } catch (error) {
    console.log(error);
  }
};

export const addRatings = async (rating: Rating) => {
  const res = await axios.post('/v1/review/ratings', rating);
  return res.data;
};

const getRatingDetail = async (actionId: string, actionType: string): Promise<Rating & {can_edit: boolean}> => {
  const res = await axios.get(`/v1/review/ratings/${actionId}?action_type=${actionType}`);
  return res.data.rating;
};

export const useGetRatingDetail = (actionId: string, actionType: string, options?: Partial<UseQueryOptions>) => {
  return useQuery(['rating', actionId, actionType], () => getRatingDetail(actionId, actionType), {...(options as any)});
};

const getReviews = async (query: ReviewsRequestQuery): Promise<ProductReview & {limit: number; page: number}> => {
  const queryString = qs.stringify({...query, limit: query?.limit ?? 10, page: query?.page ?? 1});
  const res = await axios.get(`/v1/review/reviews?${queryString}`);

  return {...res.data, limit: query?.limit ?? 10, page: query?.page ?? 1};
};

export const useGetReviews = (query: ReviewsRequestQuery, options?: Partial<UseQueryOptions>) => {
  return useQuery(['reviews', query], () => getReviews(query), {
    ...(options as any),
  });
};

export const useGetReviewsInfinite = (query: ReviewsRequestQuery, options?: Partial<UseQueryOptions>) => {
  return useInfiniteQuery(['reviews_infinite', query], ({pageParam}) => getReviews({...query, page: pageParam}), {
    getNextPageParam: lastPage => {
      const totalPage = Math.ceil(lastPage.rating_count / lastPage.limit);
      if (lastPage.page < totalPage) {
        return lastPage.page + 1;
      }
    },
    ...(options as any),
  });
};
