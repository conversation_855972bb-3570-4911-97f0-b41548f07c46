import {REACT_QUERY_CACHE_TIME} from './../helpers/reactQuery';
import {useAuthActions} from '~hooks';
import {useMutation, useQuery, useQueryClient, UseQueryOptions, useInfiniteQuery} from '@tanstack/react-query';
import axios from './axios';
import qs from 'query-string';
import {Bank, MyBank} from '~utils/types/common';
import {EscrowTransaction, WalletTransaction} from '~utils/types/finance';

type IncomeSummary = {
  can_withdrawl: number;
  commission_available: number;
  commission_frozen: number;
  sale_available: number;
  sale_frozen: number;
  tax_deduction_pending: number;
};

export type WalletTransactionType = 'sale' | 'commission' | 'refund' | 'withdrawl';
export type EscrowTransactionType = 'commission' | 'order';
export type EscrowTransactionStatus = 'payment_received' | 'order_created' | 'success' | 'cancelled';

type WalletTransactionsQuery = Partial<{
  limit: number;
  page: number;
  types: WalletTransactionType[] | WalletTransactionType | null;
  from_date: string;
  to_date: string;
}>;

type EscrowTransactionsQuery = Partial<{
  type: EscrowTransactionType | null;
  limit: number;
  page: number;
  status: EscrowTransactionStatus | EscrowTransactionStatus[];
}>;

type WalletTransactionsResponse = {
  total: number;
  transactions: WalletTransaction[];
  hasNextPage?: boolean;
  currentPage: number;
};

type EscrowTransactionResponse = {
  total: number;
  transactions: EscrowTransaction[];
  hasNextPage?: boolean;
  currentPage: number;
};

export const getIncomeSummary = async (): Promise<IncomeSummary | undefined> => {
  const res = await axios.get('/v1/pay/api/finance/income_summary');
  return res.data.data;
};

export const useGetIncomeSummary = () => {
  const {isLoggedIn} = useAuthActions();
  return useQuery(['income_summary'], getIncomeSummary, {
    enabled: isLoggedIn,
  });
};

export const getWalletTransactions = async (query: WalletTransactionsQuery): Promise<WalletTransactionsResponse | undefined> => {
  try {
    const queryString = qs.stringify(query, {skipNull: true, arrayFormat: 'comma'});
    const res = await axios.get(`/v1/pay/api/finance/wallet_transactions${queryString ? `?${queryString}` : ''}`);
    return {...res.data, currentPage: query.page || 1, hasNextPage: (query.page || 1) * (query.limit || 10) < (res.data.total ?? 0)};
  } catch (error) {}
};

export const useGetWalletTransactions = (query: WalletTransactionsQuery) => {
  return useInfiniteQuery(['wallet_transactions', query], ({pageParam}) => getWalletTransactions({...query, limit: query.limit, page: pageParam}), {
    keepPreviousData: true,
    staleTime: 0,
    getNextPageParam: lastPage => {
      if (lastPage?.hasNextPage) {
        return lastPage.currentPage + 1;
      }
    },
  });
};

export const getAvailableTransactionTypes = async (): Promise<{[key: string]: string} | undefined> => {
  try {
    const res = await axios.get('/v1/pay/api/finance/transaction_types');
    return res.data.types;
  } catch (error) {}
};

export const useGetAvailableTransactionTypes = () => {
  return useQuery(['transaction_types'], getAvailableTransactionTypes, {
    cacheTime: REACT_QUERY_CACHE_TIME.OneWeek,
  });
};

export const getEscrowTransactionStatuses = async (): Promise<{[key: string]: string} | undefined> => {
  try {
    const res = await axios.get('/v1/pay/api/finance/escrow/transaction_statuses');
    return res.data.statuses;
  } catch (error) {}
};

export const useGetEscrowTransactionStatuses = () => {
  return useQuery(['es_trans_statuses'], getEscrowTransactionStatuses, {
    cacheTime: REACT_QUERY_CACHE_TIME.OneWeek,
  });
};

export const getEscrowTransactions = async (query: EscrowTransactionsQuery): Promise<EscrowTransactionResponse> => {
  const queryString = qs.stringify(query, {skipNull: true, arrayFormat: 'comma'});
  const res = await axios.get(`/v1/pay/api/finance/escrow/transactions${queryString ? `?${queryString}` : ''}`);
  return {...res.data, currentPage: query.page || 1, hasNextPage: (query.page || 1) * (query.limit || 10) < (res.data.total ?? 0)};
};

export const useGetEscrowTransactions = (query: EscrowTransactionsQuery) => {
  return useInfiniteQuery(['escrow_transactions', query], ({pageParam}) => getEscrowTransactions({...query, limit: query.limit, page: pageParam}), {
    staleTime: 0,
    getNextPageParam: lastPage => {
      if (lastPage.hasNextPage) {
        return lastPage.currentPage + 1;
      }
    },
  });
};

export const getBanks = async (): Promise<Bank[] | undefined> => {
  try {
    const res = await axios.get('/v1/pay/api/banks');
    return res.data.banks;
  } catch (error) {}
};

export const useGetBanks = () =>
  useQuery(['banks'], getBanks, {
    staleTime: REACT_QUERY_CACHE_TIME.FiveMinutes,
    cacheTime: REACT_QUERY_CACHE_TIME.OneWeek,
  });

export const addNewBankAccount = async (bankAccount: MyBank) => {
  const res = await axios.post('/v1/pay/api/banks/my_accounts', bankAccount);
  return res.data.account;
};

export const useAddNewBankAccountMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<any, any, {bankAccount: MyBank}>(({bankAccount}) => addNewBankAccount(bankAccount), {
    onSuccess: () => {
      queryClient.invalidateQueries(['my_bank_accounts']);
    },
  });
};

export const getMyBankAccounts = async (): Promise<MyBank[] | undefined> => {
  try {
    const res = await axios.get('/v1/pay/api/banks/my_accounts');
    return res.data.accounts;
  } catch (error) {}
};

export const useGetMyBankAccounts = (options?: UseQueryOptions<any, any, any, any>) => useQuery(['my_bank_accounts'], getMyBankAccounts, {...(options as any)});

export const removeBankAccount = async (accountId: number) => {
  return axios.delete(`/v1/pay/api/banks/my_accounts/${accountId}`);
};

export const useRemoveBankAccountMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<any, any, {bankAccountId: number}, any>(({bankAccountId}) => removeBankAccount(bankAccountId), {
    onSuccess: () => {
      queryClient.invalidateQueries(['my_bank_accounts']);
    },
  });
};

export const getWithdrawalFee = async (): Promise<number | undefined> => {
  const res = await axios.get('/v1/pay/api/wallet/withdrawal_fee');
  return res.data.fee;
};

export const createWithdrawalRequest = async (params: {amount: number; bank_account_id: number}): Promise<WalletTransaction> => {
  const res = await axios.post('/v1/pay/api/wallet/withdrawals', params);
  return res.data;
};

export const useWithdrawalRequestMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<any, any, {amount: number; bank_account_id: number}, any>(params => createWithdrawalRequest(params), {
    onSuccess: () => {
      queryClient.invalidateQueries(['wallet_transactions']);
      queryClient.invalidateQueries(['income_summary']);
    },
  });
};
