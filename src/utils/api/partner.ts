import {ProductRequestQuery} from '~utils/types/product';
import axios from './axios';
import {useInfiniteQuery, useQuery} from '@tanstack/react-query';
import {MarketplaceProduct} from '~utils/types/partner';

const SEARCH_PRODUCTS_LIMIT = 5;

const getPartnerShopProducts = async (partnerShopId: string, params?: ProductRequestQuery): Promise<{products: MarketplaceProduct[]; total: number; offset: number}> => {
  const {data} = await axios.get(`/v2/order/partners/tiktok/shops/${partnerShopId}/products.json`, {
    params: params,
  });

  return {...data, offset: params?.offset ?? 0};
};

export const useGetPartnerShopProducts = (partnerShopId: string, params?: ProductRequestQuery) => {
  return useQuery({
    queryKey: ['partnerShopProducts', partnerShopId, params],
    queryFn: () => getPartnerShopProducts(partnerShopId, params),
  });
};

export const useGetShopProductsInfinite = (partnerShopId: string, params?: ProductRequestQuery) => {
  return useInfiniteQuery({
    queryKey: ['partnerShopProductsInfinite', partnerShopId, params],
    queryFn: ({pageParam}) => getPartnerShopProducts(partnerShopId, {...params, offset: pageParam ?? 0, limit: SEARCH_PRODUCTS_LIMIT}),
    getNextPageParam: lastPage => {
      if (lastPage.offset + SEARCH_PRODUCTS_LIMIT < lastPage.total) {
        return lastPage.offset + SEARCH_PRODUCTS_LIMIT;
      }
    },
  });
};

export const addSyncProductJob = async (partnerShopId: string, productId: string) => {
  const {data} = await axios.post(`/v2/order/partners/tiktok/shops/${partnerShopId}/product_sync.json`, {
    product_id: productId,
  });
  return data;
};
