import globalStyles from './globalStyles';

export const palette = {
  white: '#FFFFFF',
  gray10: '#f8f9fa',
  gray20: '#e9ecef',
  gray30: '#dee2e6',
  gray40: '#ced4da',
  gray50: '#adb5bd',
  gray60: '#6c757d',
  gray70: '#495057',
  gray80: '#343a40',
  gray90: '#38383D',
  gray100: '#27272A',
  black: '#050609',
  primary: '#008060',
  primaryLight: '#dbfff6',
  secondary: '#fff1dc',
  pending: 'rgb(255, 145, 35)',
  red: '#FE2C55',
  blue: '#2089dc',
  text: '#001A33',
  textLight: '#667685',
  textLightest: '#BFC6CC',
};

export const theme = {
  colors: {
    mainBackground: palette.white,
    ...palette,
  },
  spacing: {
    xs: 4,
    s: 8,
    m: 16,
    l: 24,
    xl: 40,
  },
  breakpoints: {
    phone: 0,
    tablet: 768,
  },
  typography: {
    xs: 10,
    sm: 12,
    base: 14,
    md: 16,
    lg: 18,
    lg1: 20,
    lg2: 24,
    lg3: 28,
    lg4: 32,
    lg5: 48,
  },
  globalStyles: globalStyles,
};

export type Theme = typeof theme;
export default theme;
