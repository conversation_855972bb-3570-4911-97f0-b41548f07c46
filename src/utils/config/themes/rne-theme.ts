import {createTheme} from '@rneui/themed';
import {palette, theme} from './theme';
import '@rneui/themed';

declare module '@rneui/themed' {
  export interface Colors {}
  export interface Theme {
    typography: typeof theme.typography;
  }

  export interface TextProps {
    bold?: boolean;
  }

  export interface ComponentTheme {
    Text: Partial<TextProps>;
  }
}

const rneTheme = createTheme({
  spacing: {
    sm: theme.spacing.s,
    md: theme.spacing.m,
    lg: theme.spacing.l,
    xl: theme.spacing.xl,
  },
  darkColors: {
    primary: palette.primary,
    secondary: palette.secondary,
  },
  lightColors: {
    primary: palette.primary,
    secondary: palette.secondary,
  },
  typography: theme.typography,
  components: {
    Input: () => ({
      labelStyle: {
        fontWeight: '500',
        fontSize: theme.typography.md,
        color: theme.colors.textLight,
        marginBottom: 4,
      },
      inputContainerStyle: {
        borderWidth: 1,
        borderColor: theme.colors.gray30,
        borderRadius: 12,
        paddingHorizontal: 12,
      },
    }),
    Text: props => ({
      style: {
        color: theme.colors.text,
        fontSize: theme.typography.md,
        fontWeight: props.bold ? 'bold' : 'normal',
      },
    }),
  },
});

export default rneTheme;
