// @ts-ignore
import {Importance} from 'react-native-push-notification';

export const OLD_APP_USER_INFO = 'TtsBuyer.userInfo';
export const ACCESS_TOKEN_KEY = '@publicToken';
export const REFRESH_TOKEN_KEY = '@refreshToken';
export const DEVICE_TOKEN_KEY = '@deviceToken';
export const ASYNC_STORAGE_KEYS = {
  DRAFT_ORDER: 'DRAFT_ORDER',
  SHIPPING_ADDRESS: 'SHIPPING_ADDRESS',
  MONEY_MASK: 'MONEY_MASK',
  RECENT_PRODUCTS: 'RECENT_PRODUCTS',
  APP_RATED: 'APP_RATED',
  SAVED_PRODUCTS_DIRTY: 'SAVED_PRODUCTS_DIRTY',
  FCM_TOKEN: 'FCM_TOKEN',
  INSPIRATION_RATED: 'INSPIRATION_RATED',
  KEY<PERSON>ARD_HEIGHT: 'KEY<PERSON>ARD_HEIGHT',
  FIND_PRODUCT_TIP_DISSMISSED: 'FIND_PRODUCT_TIP_DISSMISSED',
  TUTORIALS_WATCHED: 'TUTORIALS_WATCHED',
  PARTNER_PRODUCT_SYNC_DIALOG_IGNORED: 'PARTNER_PRODUCT_SYNC_DIALOG_IGNORED',
};

export const AVAILABLE_CHANNELS = {
  default_channel: {
    channelId: 'default_channel',
    channelName: 'Khác',
    channelDescription: 'Các thông báo khác', // (optional) default: undefined.
    soundName: 'default', // (optional) See `soundName` parameter of `localNotification` function
    importance: Importance.HIGH, // (optional) default: Importance.HIGH. Int value of the Android notification importance
    vibrate: true,
  },
  new_message: {
    channelId: 'new_msg',
    channelName: 'Tin nhắn',
    channelDescription: 'Thông báo về tin nhắn', // (optional) default: undefined.
    soundName: 'default', // (optional) See `soundName` parameter of `localNotification` function
    importance: Importance.HIGH, // (optional) default: Importance.HIGH. Int value of the Android notification importance
    vibrate: true,
  },
};
