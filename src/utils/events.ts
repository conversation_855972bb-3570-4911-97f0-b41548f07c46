import EventEmitter from 'events';

export const EventNames = {
  NewMessage: 'new-message',
  ContactOnline: 'contact-online',
  ContactOffline: 'contact-offline',
  RefreshTokenSuccess: 'refresh-token-success',
  RefreshTokenError: 'refresh-token-error',
  OpenImageViewer: 'open-image-viewer',

  FeedScreen: {
    OpenCommentBottomSheet: 'open-comment-bottom-sheet',
  },

  OpenLoginRequiredModal: 'open-login-modal',
  CloseLoginRequiredModal: 'close-login-modal',
  OpenChatWoot: 'open-chat-woot',
  OpenAddToCart: 'open-add-to-cart',
  OpenAddressList: 'open-address-list',
  OpenFavoriteShopBottomSheet: 'open-favorite-shop-bs',
  //
  HomePageUpper200Scroll: 'home-page-upper-200-scroll',
  HomePageLower200Scroll: 'home-page-lower-200-scroll',
  InspirationPageOpenProductPost: 'inspiration-page-open-product-post',
  ShowPhoneHyperLinkBottomSheet: 'show-phone-hyper-link-bottom-sheet',
};

const ee = new EventEmitter();

export default ee;
