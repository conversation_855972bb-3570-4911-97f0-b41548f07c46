import Message from './message';

type Contact = {
  id: string;
  friend_id: string;
  is_oa?: boolean;
  is_online: boolean;
  is_system?: boolean;
  last_act?: string;
  last_message?: Message | null;
  last_online_at?: string;
  online_state: boolean;
  read: number;
  subject_id?: string;
  user?: ContactUser;
};

export type ContactUser = {
  id: string;
  account_type: number;
  avatar?: string;
  email?: string;
  full_address: string;
  full_name: string;
  phones?: string[];
  shop_name?: string;
  is_oa?: boolean;
};

export type ContactLastActivity = {
  activity: 'online' | 'offline';
  from_now: string;
  time: string;
};

export default Contact;
