type Category = {
  id: string;
  category_id: number;
  title: string;
  slug: string;
  description: string;
  level: number;
  parent_id: string;
  option: number;
  published: number;
  unit: string;
  total_product: number;
  banner_image_src: string;
  thumb_image_src: string;
  icon_image_src: string;
  featured: number;
  featured_image_src: string;
  order_by: number;
  keywords: string;
  suggest_image_src: string;
  p_id: number;
  updated_at: string;
  units: CategoryUnit[];
};

export type CategoryUnit = {
  id: string;
  name: string;
  min_order_quantity?: number;
};

export default Category;
