import {ProductCollectionStyles, ProductSortQuery} from './product';
import InventoryLevel from './inventory';

export type AuthStorageType = {
  access_token: string;
  refresh_token: string;
};

export enum WebViewMessageTypes {
  AuthChange = 'AuthChange',
  LoginSuccess = 'LoginSuccess',
  LogoutSuccess = 'LogoutSuccess',
  RateApp = 'RateApp',
  ShortVibrate = 'ShortVibrate',
  LongVibrate = 'LongVibrate',
  HapticFeedback = 'HapticFeedback',
  Share = 'Share',
  ExitApp = 'ExitApp',
  URLChanged = 'URLChanged',
  PageTitle = 'PageTitle',
  Navigate = 'Navigate',
  Replace = 'Replace',
  Push = 'Push',
  OpenURL = 'OpenURL',
  PopToTop = 'PopToTop',
  OpenImageViewer = 'OpenImageViewer',
  OpenLoginModal = 'OpenLoginModal',
  BlockShop = 'BlockShop',
  Goback = 'Goback',
  SetLastViewedCategories = 'SetLastViewedCategories',
  ViewProduct = 'ViewProduct',
  LogEvent = 'LogEvent', // GA4
  MakeCall = 'MakeCall',
  DownloadImagePOST = 'DownloadImagePOST',
}

export type ParsedWebViewMessage = {
  type: WebViewMessageTypes;
  data: any;
};

export type InventoryQuantity = Pick<InventoryLevel, 'location_id' | 'available'>;

export type UploadedImage = {
  id: string;
  delete_key: string;
  height: number;
  medium: string;
  owner_id: string;
  size: number;
  src: string;
  thumb: string;
  width: number;
};

export type DeepPartial<T> = T extends object
  ? {
      [P in keyof T]?: DeepPartial<T[P]>;
    }
  : T;

export type Notification = {
  bigPictureUrl: string;
  channelId: string;
  color: string | null;
  data: any;
  foreground: boolean;
  id: string;
  largeIconUrl: string;
  message: string;
  priority: string;
  smallIcon: string;
  sound: string;
  tag: string | null;
  title: string;
  userInteraction: boolean;
  visibility: string;
};

export type GraphqlListResponse<T> = {
  items: Partial<T>[];
  extensions: {
    totalItems: number;
    currentPage: number;
    totalPages: number;
    currentLimit: number;
  };
};

export type Bank = {
  id: number;
  name: string;
  code: string;
  bin: string;
  short_name: string;
  logo: string;
  swift_code: string;
  created_at: string;
  updated_at: string;
};

export type MyBank = {
  id: number;
  bank: Bank;
  tts_uid: string;
  id_number: string;
  name: string;
  number: string;
  branch: string;
  default: boolean;
  bank_id: number;
  created_at: string;
  updated_at: string;
};

export type AdsContent = {
  id: number;
  inventory_id: number;
  title: string;
  link: string;
  banner: string;
  start_at: string;
  end_at: string;
  created_at: string;
  updated_at: string;
  status: number;
  order_pos: number;
  keywords: any;
};

export type SmartCollection = {
  title: string;
  body_html: string;
  handle: string;
  image: {
    src: string;
    width: number;
    height: number;
  };
  sort_by: ProductSortQuery;
  ascending: boolean;
  styles: ProductCollectionStyles;
};

export type AggregationBucket = {
  key: string;
  title: string;
  doc_count: number;
  image?: string;
};

export type Aggregations = {
  filter_category_lv1: {
    buckets: AggregationBucket[];
  };
  filter_category_lv2: {
    buckets: AggregationBucket[];
  };
  filter_province: {
    buckets: AggregationBucket[];
  };
  filter_trade_assurance: {
    buckets: AggregationBucket[];
  };
};

export type SuggestKeyword = {
  keyword: string;
  ref_id: string;
  type: string;
};
