export type User = {
  id: string;
  account_phone: string;
  account_type: number;
  address: string;
  avatar: string;
  created_at: string;
  district_id: number;
  district_name: string;
  email: string;
  first_name: string;
  full_address: string;
  full_name: string;
  is_verified: boolean;
  is_vip: boolean;
  last_active: string;
  last_login: string;
  last_name: string;
  linked_source: string;
  linked_uid: null;
  merged_user_id: null;
  phone_number: string;
  phone_verified: number;
  province_id: number;
  province_name: string;
  ref_code: string;
  referral_source: string | null;
  register_step4_required: boolean;
  review_state: number;
  shop_name: string;
  subscribed: number;
  ward_id: number;
  ward_name: string;
  can_dropship: boolean;
  dropship_disallow_reason: string;
  idcard_verify: 'not_verified' | 'under_review' | 'verified';
  tax_information_verify: 'not_verified' | 'under_review' | 'verified';
};

export type ReferralCode = {
  _id: string;
  user_id: string;
  code: string;
  updated_at: string;
  created_at: string;
  user: User;
};

export type Referrer = {
  id: string;
  user_id: string;
  user_name: string;
  user_phone: string;
  province: string;
  promoter_user_id: string;
  promoter_user_name: string;
  promoter_user_phone: string;
  promoter_province: string;
  updated_at: string;
  created_at: string;
  avatar: string;
  dropship_sales: number;
  dropship_order_count: number;
};

export type IdCardUploadResponse = {
  message: string;
  success: boolean;
  attachment_id: string;
  info: IdCard;
};

export type IdCard = {
  address: string;
  address_entities: AddressEntities;
  hometown: string;
  hometown_entities: HometownEntities;
  issued_date: string;
  valid_until: string;
  national: string;
  name: string;
  id_num: string;
  gender: string;
  dob: string;
  id_type: string;
};

export type AddressEntities = {
  ward: string;
  district: string;
  street: string;
  province: string;
};

export type HometownEntities = {
  ward: string;
  district: string;
  street: string;
  province: string;
};

export type MyIdCard = {
  _id: string;
  front_attachment_id: string;
  back_attachment_id: string;
  info: MyIdCardInfo;
  user_id: string;
  updated_at: string;
  created_at: string;
  front_attachment: any;
  back_attachment: any;
  reject_reason: string;
  status: 'not_verified' | 'under_review' | 'verified' | 'rejected';
};

type MyIdCardInfo = {
  address: string;
  issued_date: string;
  name: string;
  id_num: string;
  id_type: string;
};
