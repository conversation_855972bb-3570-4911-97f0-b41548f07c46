type Ticket = {
  id: string;
  body: string;
  code: number;
  created_at: string;
  discount: number;
  is_building: boolean;
  is_deal: boolean;
  shop_id: string;
  ticket_state: TicketState;
  ticket_type: number;
  total_products: number;
  total_products_quantity: number;
  total_value: number;
  updated_at: string;
  user_id: string;
  user: {
    name: string;
    phone: string;
  };
  shop: {
    avatar: string;
    cover: string;
    full_address: string;
    is_verified: number;
    is_vip: number;
    name: string;
    phone: string;
  };
};

export type TicketState = 1 | 3 | 6;

export default Ticket;
