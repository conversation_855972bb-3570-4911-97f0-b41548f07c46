import {ImageFile} from '~api/image';
import {TicketState} from './ticket';

export type MessageImage = {
  id?: string;
  src: string;
  medium: string;
  thumb: string;
  width: number;
  height: number;
};

type Message = {
  __unsent?: boolean;
  __localImageFiles?: ImageFile[];
  body: string;
  from: string;
  messageId: string;
  payload: {
    id?: string;
    product_id?: string;
    send_back: boolean;
    medium?: string;
    title?: string;
    price?: number;
    url?: string;
    thumb?: string;
    images?: MessageImage[];
    details?: {
      price: number;
      product_id: string;
      quantity: number;
    }[];
    ticket_state?: TicketState;
    totalF?: string;
    total_products?: number;
    total_value?: number;
  };
  read: string | null;
  seen: string | null;
  server_mid: string;
  time: number;
  time_iso: string;
  to: string;
  type: 'text' | 'image' | 'ticket' | 'images' | 'product';
};

export default Message;
