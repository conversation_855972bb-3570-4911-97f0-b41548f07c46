import theme from '~utils/config/themes/theme';
import {SearchProduct} from './product';

export type MarketplaceProduct = SearchProduct & {
  synced_product: SyncedProduct | null;
};

export type SyncedProduct = {
  _id: string;
  partner_id: number;
  partner_shop_id: string;
  product_id: string;
  status: string;
  updated_at: string;
  created_at: string;
  message: string;
  partner_product: PartnerProduct | null;
};

export type PartnerProduct = {
  _id: string;
  partner_id: number;
  partner_shop_id: string;
  shop_id: string;
  item_id: string;
  title: string;
  status: string;
  body_html: string;
  images: Image[];
  weight: number;
  width: number;
  height: number;
  length: number;
  create_time: number;
  update_time: number;
  category_chains: CategoryChain[];
  is_cod_allowed: boolean;
  is_not_for_sale: boolean;
  main_images: MainImage[];
  package_weight: PackageWeight;
  package_dimensions: PackageDimensions;
  options: any[];
  updated_at: string;
  created_at: string;
  link_at: number;
  link_pid: string;
  link_sid: string;
};

type Image = {
  width: number;
  height: number;
  src: Src;
};

type Src = {
  height: number;
  thumb_urls: string[];
  uri: string;
  urls: string[];
  width: number;
};

type CategoryChain = {
  id: string;
  is_leaf: boolean;
  local_name: string;
  parent_id: string;
};

type MainImage = {
  height: number;
  thumb_urls: string[];
  uri: string;
  urls: string[];
  width: number;
};

type PackageWeight = {
  unit: string;
  value: string;
};

type PackageDimensions = {
  height: string;
  length: string;
  unit: string;
  width: string;
};

export const PartnerTiktokProductStatus = {
  DRAFT: 'Nháp',
  PENDING: 'Đang xét duyệt',
  FAILED: 'Thất bại',
  ACTIVATE: 'Đang hoạt động',
  SELLER_DEACTIVATED: 'Bị hủy kích hoạt (bởi seller)',
  PLATFORM_DEACTIVATED: 'Bị hủy kích hoạt (bởi TikTok)',
  FREEZE: 'Bị tạm ngưng',
  DELETED: 'Đã xóa',
};

export const PartnerTiktokProductStatusColor = {
  DRAFT: {
    text: theme.colors.text,
    background: theme.colors.gray20,
  },
  PENDING: {
    text: theme.colors.pending,
    background: '#fffdd0',
  },
  FAILED: {
    text: theme.colors.red,
    background: '#fff1f0',
  },
  ACTIVATE: {
    text: '#52c41a',
    background: '#e1f7e0',
  },
  SELLER_DEACTIVATED: {
    text: theme.colors.red,
    background: '#fff1f0',
  },
  PLATFORM_DEACTIVATED: {
    text: theme.colors.red,
    background: '#fff1f0',
  },
  FREEZE: {
    text: theme.colors.red,
    background: '#fff1f0',
  },
  DELETED: {
    text: theme.colors.red,
    background: '##fff1f0',
  },
};
