type RFQ = {
  category_lv1: string;
  category_lv1_title: string;
  category_lv2: string;
  category_lv2_title: string;
  close_reason: string[];
  closed_at?: string;
  closed_reason?: string;
  created_at?: string;
  description: string;
  id: string;
  images: RFQImage[];
  preferred_unit_price?: number;
  quantity: number;
  quantity_unit: string;
  quote_count: number;
  reject_at?: string;
  reject_reason?: string[];
  review_status?: 'approved' | 'pending' | 'rejected';
  slug: string;
  status: 'on' | 'off';
  title: string;
  up_at?: string;
  updated_at: string;
};

type RFQImage = {
  created_at: string;
  id: string;
  image_url: string;
  quote_id?: number;
  rfq_id: number;
  thumb: string;
  updated_at: string;
};

export default RFQ;
