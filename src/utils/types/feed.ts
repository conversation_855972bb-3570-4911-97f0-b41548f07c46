export type FeedAPIReponseWithPaging<T> = T & {paging: FeedPaging};

export type ActivityAttachment = {
  url?: string;
  attachment_id?: string;
  file_size?: number;
  media_type?: 'image' | 'video' | 'product';
  video_thumb_url?: string;
  links?: ActivityAttachmentLink[];
};

export type ActivityAttachmentLink = {
  link_id?: string;
  link_type?: string;
  url?: string;
  type?: string;
  title?: string;
  thumb?: string;
};

export type ActivityActor = {
  id: string;
  name: string;
  avatar: string;
  type: 'user' | 'shop';
  followed?: boolean;
  is_vip: boolean;
  is_business: boolean;
  is_oa: boolean;
};
export type Activity = {
  __nanoid?: string;
  id: string;
  feed_id: string;
  actor: ActivityActor;
  verb?: string;
  caption?: string;
  attachments?: ActivityAttachment[];
  reactions: {
    like: number;
    comment: number;
  };
  top_comments: ActivityComment[];
  liked: boolean;
  reach: number;
  created_at: string;
  updated_at?: string;
};

export type ActivityComment = {
  activity_id: string;
  actor: ActivityCommentActor;
  content: string;
  created_at: string;
  id: string;
  like: number;
  liked: boolean;
  parent_id: string;
  reply_comments: ActivityComment[];
  updated_at: string;
  user_id: string;
  reply_count: number;
};

export type ActivityCommentActor = {
  avatar: string;
  name: string;
  user_id: string;
};

export type FeedPaging = {
  cursors: {
    after: string;
    before: string;
  };
  next: string;
  previous: string;
};

export type FeedInfo = {
  created_at: string;
  follower: number;
  id: string;
  name: string;
  type: string;
};

export type NewsFeed = {
  activities: Activity[];
  feed: FeedInfo;
  is_coldstart: boolean;
  paging: FeedPaging;
};

export type ActivityCommentsGetReponse = FeedAPIReponseWithPaging<{comment_count: number; comments: {data: ActivityComment[]}}>;
