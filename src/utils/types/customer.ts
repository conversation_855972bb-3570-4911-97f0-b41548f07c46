import Address from './address';

type Customer = {
  id: string;
  accepts_marketing: boolean;
  accepts_marketing_updated_at?: string;
  addresses: Address[];
  created_at: string;
  currency: string;
  default_address: Address;
  email: string;
  first_name: string;
  last_name: string;
  last_order_id?: string;
  last_order_name?: string;
  note: string;
  orders_count: number;
  phone: string;
  tags?: string;
  tax_exempt: boolean;
  total_spent: number;
  updated_at: string;
  user_id: string;
  utm_campaign: string | null;
  utm_content: string | null;
  utm_medium: string | null;
  utm_source: string | null;
  utm_term: string | null;
  verified_email: boolean;
  dropship: boolean;
};

export default Customer;
