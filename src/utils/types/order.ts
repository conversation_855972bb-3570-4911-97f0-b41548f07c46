import Address from './address';
type Order = {
  buyer_accepts_marketing: boolean;
  buyer_cancel_notice: string;
  buyer_cancel_order_reasons: [string];
  buyer_cancellable: boolean;
  cancel_reason: string;
  cancelled_at: string;
  cancelled_by: string;
  cancelled_reason_code: string;
  cart_token: string;
  closed_at: string;
  commission_fee: string;
  complaint: boolean;
  complaint_url: string;
  completed_at: string;
  confirm_cancelled_reason: string;
  confirm_expired_at: string;
  confirm_status: OrderConfirmStatus;
  confirmed: boolean;
  confirmed_at: string;
  confirmed_by: string;
  contact_email: string;
  created_at: string;
  currency: string;
  customer: Address | null;
  customer_locale: string;
  email: string;
  escrow_release_at: string;
  estimated_escrow_release_at: string;
  financial_status: OrderFinancialStatus;
  fulfillment_expired_at: string;
  fulfillment_status: OrderFulfillmentStatus;
  fulfillments: OrderFulfillment[];
  gateway: string;
  id: number;
  line_items: OrderLineItem[];
  location_id: number;
  name: string;
  note: string;
  number: string;
  order_number: string;
  paid_at: string;
  paid_expired_at: string;
  payment_checkout_url: string;
  payment_label: string;
  payment_method: string;
  phone: string;
  presentment_currency: string;
  processed_at: string;
  processing_method: string;
  received_at: string;
  seller_cancel_notice: string;
  seller_cancel_order_reasons: [string];
  seller_cancellable: boolean;
  service_fee: number;
  shipment_status: string;
  shipment_status_text: string;
  shipped_at: string;
  shipping: OrderShipping;
  shipping_address: Address;
  shipping_lines: [OrderShippingLine];
  shop: OrderShop;
  shop_id: string;
  source_name: string | null;
  source_url: string;
  source_identifier: string;
  status: OrderStatus;
  status_label: string;
  subtotal_price: string;
  taxes_included: boolean;
  total_discounts: string;
  total_line_items_price: string;
  total_price: string;
  total_price_usd: string;
  total_tax: string;
  total_weight: number;
  transaction: OrderTransaction;
  transaction_fee: number;
  updated_at: string;
  user_id: string;
  dropship: boolean;
  dropship_marketplace: boolean;
  dropship_profit: string;
  dropship_promo_bonus: string;
  total_dropship_supplier_price: string;
  dropship_total_price: string;
  discount_codes?: {
    price_rule: string;
  }[];
  discount_applications: OrderDiscountApplication[];
  commission_promo_applications: OrderCommissionPromoApplication[];
  rate_expires_at: string;
  rated_at: string;
  rated_delivery_service: number;
  rated_item_quality: number;
  rated_seller_service: number;
  wait_tts_confirm: boolean;
};

export type OrderDiscountApplication = {
  id: number;
  created_at: string;
  deleted_at: string;
  description: string;
  order_id: number;
  shop_id: string;
  target_selection: string;
  target_type: string;
  title: string;
  updated_at: string;
  user_id: string;
  value: string;
  value_type: string;
  subsidy_by: 'dropshipper' | 'tts';
};

export type OrderStatus = 'open' | 'closed' | 'cancelled' | null;
export type OrderFulfillmentStatus = 'fulfilled' | 'partial' | null;
export type OrderFinancialStatus = 'paid' | 'pending' | 'partially_paid' | null;
export type OrderConfirmStatus = 'confirmed' | 'pending' | 'cancelled' | null;
export type OrderEvent = {
  title: string;
  message?: string;
  happened_at: string;
};

export type OrderFulfillment = {
  id: number;
  authorization: string;
  created_at: string;
  estimated_delivery_at: string;
  estimated_pickup_at: string;
  line_items: OrderLineItem;
  location_id: number;
  name: string;
  order_id: number;
  reason: string;
  service: string;
  shipment_status: string;
  shipping_order_code: string;
  shop_id: string;
  message: string;
  status_text: string;
  status: string;
  tracking_company: string;
  tracking_number: string;
  tracking_url: string;
  updated_at: string;
};

export type OrderFulfillmentEvent = {
  id: number;
  order_id: number;
  fulfillment_id: number;
  status: string;
  status_text: string;
  message: string | null;
  happened_at: string | null;
  city: string | null;
  province: string | null;
  country: string | null;
  zip: string | null;
  address1: string | null;
  latitude: string | null;
  longitude: string | null;
  estimated_delivery_at: string | null;
  updated_at: string | null;
  created_at: string | null;
};

export type OrderLineItem = {
  id: string;
  variant_id: string;
  variant_title: string;
  fulfillable_quantity: number;
  fulfillment_service: string;
  fulfillment_status: string;
  gift_card: boolean;
  grams: number;
  name: string;
  price: string;
  product_id: string;
  quantity: number;
  requires_shipping: boolean;
  shipment_status: string;
  sku: string;
  taxable: boolean;
  title: string;
  total_discount: number;
  vendor: string;
  image_src: string;
  dropship_price: string;
  product_type: string;
};

type OrderShipping = {
  carrier: string;
  delivered_time: number;
  method: string;
  note: string;
  pay_by: string;
  price: number;
  bill_of_lading?: {
    bill_of_lading: string;
    created_at: string;
    deleted_at: string;
    fulfillment_id: number;
    id: number;
    order_id: number;
    updated_at: string;
  };
};

type OrderShippingLine = {
  code: string;
  discounted_price: number;
  id: number;
  phone: string;
  price: number;
  source: string;
  title: string;
  estimated_delivery_at_min: string | null;
  estimated_delivery_at_max: string | null;
  pass_code: string;
};

type OrderShop = {
  name: string;
  plan: string;
  statistic: {cancel_rate: number; confirm_rate: number; confirm_time: number};
};

type OrderTransaction = {
  amount: string;
  authorization: string;
  checkout_url: string;
  created_at: string;
  currency: string;
  deleted_at: string;
  device_id: string;
  error_code: string;
  gateway: string;
  id: number;
  location_id: number;
  message: string;
  order_id: number;
  processed_at: string;
  shop_id: string;
  source_name: string;
  status: string;
  test: boolean;
  updated_at: string;
  user_id: string;
};

export type ShippingRate = {
  currency: string;
  description: string;
  max_delivery_date: string;
  min_delivery_date: string;
  service_code: string;
  service_image?: string | null;
  service_name: string;
  total_price: number;
  //
  __pay_by?: string | null;
  __pay_amount?: number | null;
};

export type ShippingLine = {
  carrier_identifier: string | null;
  code: string;
  delivery_category: string | null;
  discounted_price: number;
  id: string;
  phone: string;
  price: number;
  requested_fulfillment_service_id: string | null;
  source: string;
  title: string;
  pay_by?: string | null;
  __pay_amount?: number | null;
};

export type OrderCommissionPromoApplication = {
  id: number;
  order_id: number;
  code: string;
  created_at: string;
  description: string;
  seller_subsidy_rate: number;
  shop_id: string;
  title: string;
  type: string;
  updated_at: string;
  user_id: string;
  value: number;
  value_type: string;
};

export type PaymentMethod = 'zalopay_atm' | 'zalopay_app' | 'zalopay_cc' | 'bank_transfer' | 'cod';
export type OrderSourceName =
  | 'dropship_app'
  | 'dropship_web'
  | 'dropship_app:shopee'
  | 'dropship_app:lazada'
  | 'dropship_app:sendo'
  | 'dropship_app:tiktokshop'
  | 'dropship_app:other'
  | 'dropship_web:shopee'
  | 'dropship_web:lazada'
  | 'dropship_web:sendo'
  | 'dropship_web:tiktokshop'
  | 'dropship_web:other';

export type MarketPlace = 'shopee' | 'lazada' | 'sendo' | 'tiktokshop' | 'other';
export default Order;
