import {ShopStyles} from './product';

export type Shop = {
  accept_trade_assurance_tos: boolean;
  accept_trade_assurance_tos_at: string;
  address1: string;
  address2?: string;
  allow_purchase: boolean;
  allow_purchase_change_at: string;
  allow_purchase_change_by: string;
  blocked: boolean;
  total_follows: number;
  blocked_reason: string;
  city: string;
  cod_active: boolean;
  cod_max_amount: number;
  cod_max_shipping_fee: number;
  country: string;
  country_code: string;
  country_name: string;
  county_taxes: boolean;
  cover: string;
  created_at: string;
  currency: string;
  customer_email: string;
  domain: string;
  link: string;
  eligible_for_card_reader_giveaway: boolean;
  eligible_for_payments: boolean;
  email: string;
  finances: boolean;
  has_discounts: boolean;
  has_gift_cards: boolean;
  has_storefront: boolean;
  have_better_prices?: boolean;
  id: string;
  introduction: string;
  logo: string;
  min_order_product_quantity: number;
  min_order_product_total_price: number;
  multi_location_enabled: boolean;
  name: string;
  notice: string;
  paid_buyer_discount_rate: number;
  password_enabled: boolean;
  phone: string;
  plan_display_name: string;
  plan_expired_at: string;
  plan_name: string;
  plan_start_at: string;
  pre_launch_enabled: boolean;
  primary_locale: string;
  primary_location_id: number;
  province: string;
  province_code: string;
  ready_for_trade_assurance: boolean;
  requires_extra_payments_agreement: boolean;
  sell_province: string;
  sell_province_id: number;
  setup_required: boolean;
  shop_owner: string;
  tax_shipping: boolean;
  taxes_included: boolean;
  tts_domain: string;
  updated_at: string;
  user_id: string;
  ward: string;
  weight_unit: string;
  zip: string;
  response_rate: number;
  response_time: number;
};

export type ShopV1 = {
  id: string;
  created_at: string;
  name: string;
  review_state: number;
  phone_number: string;
  response_rate: number;
  province: {
    name: string;
  };
  district: {
    name: string;
  };
  avatar: string;
  slug: string;
  dropship_introduction: string;
  total_dropship_products: number;
  total_following: number;
  rating_avg: number;
  rating_count: number;
  dropship_marketplace: boolean;
  styles: ShopStyles;
};

export type FavoriteShop = {
  _index: number;
  avatar: string;
  district_id: number;
  id: string;
  last_action: string;
  name: string;
  phones: string[];
  province_id: number;
  shortAddress: string;
  slug: string;
};

export type FeaturedProduct = {
  id: string;
  id_number: number;
  slug: string;
  title: string;
  image: string;
  price: number;
};

export type SearchShop = {
  id: string;
  user_id: string;
  name: string;
  address: string;
  province_code: string;
  province: string;
  city: string;
  phone: string;
  email: string;
  website: string;
  logo: string;
  cover: string;
  banner: string;
  tagline: string;
  featured_products: FeaturedProduct[];
  total_views: number;
  total_follows: number;
  total_products: number;
  total_sales: number;
  transaction_level: number;
  total_revenues: number;
  business_type: string;
  main_products: string;
  product_keywords: string;
  response_rate: number;
  repeat_purchase_rate: number;
  response_time: number;
  min_order_quantity: number;
  min_order_price: number;
  slug: string;
  review_status: string;
  status: string;
  is_vip: boolean;
  is_business: boolean;
  mall: boolean;
  last_activity_at: string;
  created_at: string;
  updated_at: string;
  rating_avg: number;
  rating_count: number;
};
