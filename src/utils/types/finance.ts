export type WalletTransaction = {
  amount: number;
  bank_account_id: number;
  bank_account_name: string;
  bank_account_number: string;
  cancel_reason: string;
  cancelled_at: string;
  completed_at: string;
  created_at: string;
  description: string;
  id: number;
  new_balance: number;
  old_balance: number;
  order_code: string;
  order_id: number;
  status: string;
  transaction_fee: number;
  type: 'commission';
  type_text: string;
  updated_at: string;
  user_id: string;
};

export type EscrowTransaction = {
  id: number;
  user_id: string;
  shop_id: string;
  amount: number;
  order_id: number;
  order_code: string;
  status: string;
  error_message: any;
  released_at: any;
  released_target: any;
  released_to: any;
  description: string;
  buyer_shipping_fee: number;
  buyer_merchant_subtotal: number;
  transaction_fee: number;
  service_fee: number;
  commission_fee: number;
  commission_target: string;
  revenue: number;
  created_at: string;
  updated_at: string;
};
