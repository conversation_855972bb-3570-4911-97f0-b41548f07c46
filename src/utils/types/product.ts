import {Shop, ShopV1} from './shop';
import Variant from './variant';

type Product = {
  id: string;
  _nanoid: string;
  id_number: number;
  title: string;
  unit_id: string;
  quantity_per_unit: number;
  body_html: string;
  user_instruction: string;
  is_published_marketplace: boolean;
  is_trade_assurance: boolean;
  trade_assurance_reject_reason: string;
  trade_assurance_review_at: string;
  trade_assurance_review_by: string;
  trade_assurance_review_status: string;
  trade_assurance_submit_at: string;
  warranty_period: number;
  warranty_policy: string;
  features: string;
  vendor: string;
  is_deal: boolean;
  deal_status: string;
  shop_id: string;
  available: number;
  product_type: string;
  product_type_slug: string;
  unit_name: string;
  created_at: string;
  handle: string;
  updated_at: string;
  tags: [string];
  template_suffix: string;
  category_lv1: string;
  category_lv2: string;
  status: ProductStatus;
  blocked_at: string;
  blocked_reason: string;
  blocked_by: string;
  review_status: ProductReviewStatus;
  review_by: string;
  review_at: string;
  reject_reason: string;
  review_request_at: string;
  shop_allow_purchase: boolean;
  allow_purchase: boolean;
  variants: Variant[];
  options: ProductOption[];
  image: ProductImage;
  images: ProductImage[];
  videos: ProductVideo[];
  prices: ProductPrice[];
  is_deal_v2: boolean;
  is_hot_deal: boolean;
  is_deal_active: boolean;
  deal_start_at: string;
  deal_end_at: string;
  discount_rate: number;
  deal_max_sale_quantity: number;
  deal_ordered: number;
  url: string;
  is_classifieds: boolean;
  dropship_price: number;
  dropship_profit: number;
  market_price: number;
  specifications: ProductSpecification[];
  shop: ShopV1;
  categories?: ProductDetailCategories;
  size_chart: ProductSizeChart | null;
  dropship_selling_price_min: number;
  badges: ProductBadge[];
  can_dropship: boolean;
  dropship_status: string;
  sales: number;
  rating_avg: number;
  rating_count: number;
  customize: boolean;
};

export type ProductDetailCategories = {
  [key in 'lv1' | 'lv2']?: ProductDetailCategory;
};

type ProductDetailCategory = {
  category_id: number;
  id: string;
  slug: string;
  title: string;
  total_product: number;
};

export type SearchProduct = {
  id: string;
  allow_purchase: boolean;
  category_level1: string;
  category_level2: string;
  compare_at_price: number;
  content_quality_score: number;
  created_at: string;
  custom_up_at: string;
  deal_state: string;
  description: string;
  discount: number;
  featured_image_thumb: string;
  image_thumb: string;
  is_blocked: boolean;
  is_business: boolean;
  is_deal: boolean;
  is_showcase_enable: boolean;
  is_up_enable: boolean;
  is_vip: boolean;
  min_order_quantity: number;
  price: number;
  prices: ProductPrice[];
  product_id: number;
  product_type: string;
  province_code: string;
  repeat_purchase_rate: number;
  review_status: string;
  shop_id: string;
  shop_response_rate: number;
  sku: string;
  slug: string;
  status: string;
  stock: number;
  title: string;
  total_images: number;
  total_inquiries: number;
  total_likes: number;
  total_reviews: number;
  total_sales: number;
  total_views: number;
  trade_assurance: string;
  trending_score: number;
  unit_id: string;
  unit_name: string;
  up_at: string;
  updated_at: string;
  have_better_prices: boolean;
  quantity_per_unit: number;
  is_classifieds: boolean;
  hide_prices: boolean;
  dropship_price: number;
  dropship_profit: number;
  market_price: number;
  shop: Shop;
  badges: ProductBadge[];
  images: string[];
  rating_avg: number;
  rating_count: number;
  videos: string[];
};

export type ProductImage = {
  id: string;
  position?: number;
  product_id?: string;
  src: string;
  width?: number;
  height?: number;
  slug?: string;
  created_at?: string;
};

export type ProductVideo = {
  id: string;
  url: string;
  duration: number;
  thumb_url: string;
  width: number;
  height: number;
};

export type ProductPrice = {
  id?: string;
  __nanoid?: string;
  start_quantity: number;
  end_quantity: number;
  price: number;
  compare_at_price?: number;
  position?: number;
};

export type ProductBadge = {
  _id: string;
  type: string;
  placement: 'dropship_after_title' | 'dropship_before_title' | 'dropship_bottom_right' | 'dropship_bottom_left' | 'dropship_top_right' | 'dropship_top_left' | 'dropship_frame';
  placement_order: number;
  icon: string;
  text: string;
  text_color: string;
  text_bg_color: string;
  updated_at: string;
  border_color: string;
  created_at: string;
  id: string;
  icon_width: number;
  icon_height: number;
  url: string;
};

export type ProductBadgePlacement = 'dropship_after_title' | 'dropship_before_title' | 'dropship_bottom_right' | 'dropship_bottom_left' | 'dropship_top_right' | 'dropship_top_left' | 'dropship_frame';

export type ProductStatus = 'active' | 'inactive';
export type ProductStockStatus = 'in_stock' | 'out_of_stock';
export type ProductReviewStatus = 'approved' | 'pending' | 'rejected';
export type ProductOption = {
  id?: string;
  _nanoId?: string;
  name: string;
  display_name?: string;
  values: string[];
  product_id?: string;
  position: number;
};

export default Product;

export type ProductSortQuery =
  | '_score'
  | 'created_at'
  | 'up_at'
  | 'price'
  | 'shop_response_rate'
  | 'min_order_quantity'
  | 'total_sales'
  | 'total_views'
  | 'trending_score'
  | 'dropship_price'
  | 'dropship_profit'
  | 'dropship_published_at';

export type ProductRequestQuery = {
  limit?: number;
  sort_by?: ProductSortQuery;
  offset?: number;
  filter_category_lv1?: string;
  filter_category_lv2?: string;
  filter_category_lv3?: string;
  filter_price_min?: number;
  filter_price_max?: number;
  filter_shop_id?: string | null;
  ascending?: boolean | string; // "true" | "false"
  keyword?: string;
  filter_only_deal?: boolean;
  filter_only_hot_deal?: boolean;
  filter_only_trade_assurance?: boolean;
  filter_min_stock?: number;
  filter_only_paid?: boolean;
  filter_province?: string;
  q?: string;
  only_aggs?: boolean;
  page?: string | number;
  match_phrase?: boolean;
  exclude_product_ids?: string;
  filter_only_classifieds?: boolean;
  filter_classifieds_area?: string;
  filter_classifieds_label?: string;
  filter_only_dropship?: boolean;
  page_type?: 'search' | 'shop' | 'category';
  utm_source?: string;
  screen?: string;
};

export type ProductPost = {
  id: string;
  title: string;
  content: string;
  privacy: 'public' | 'private';
};

export type ProductSpecification = {
  name: string;
  value: string;
};

export type ProductSizeChart = {
  image_id: string;
  src: string;
};

export type _ProductCollection = {
  id: string;
  name: string;
  thumb: string;
  template: string;
  products: SearchProduct[];
};

export type ProductCollectionStyles = {
  bg: {
    gradientColors: string[];
  };
  title: {
    color: string;
  };
};

export type ShopStyles = ProductCollectionStyles;

export type ProductCollection = {
  id: string;
  handle: string;
  collection_id: string;
  template: 'carousel_products' | 'carousel_collections' | 'infinity_products';
  title: string;
  image?: string;
  products: SearchProduct[];
  collections?: ProductCollection[];
  styles: ProductCollectionStyles;
};
