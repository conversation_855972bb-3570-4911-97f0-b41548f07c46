import {InventoryQuantity} from './common';
import {ProductPrice} from './product';
type Variant = {
  id: string;
  inventoryQuantities?: InventoryQuantity[];
  product_id: string;
  title: string;
  shop_id: string;
  position: number;
  option1: string;
  option2?: string;
  option3?: string;
  inventory_policy: string;
  inventory_item_id: number;
  inventory_quantity: number;
  old_inventory_quantity: number;
  sku: string;
  fulfillment_service: string;
  inventory_management: string;
  taxable: boolean;
  width: number;
  height: number;
  length: number;
  weight: number;
  grams: number;
  weight_unit: string;
  image_id: string | null;
  requires_shipping: boolean;
  compare_at_price: number;
  suggested_retail_price: number;
  cost: number;
  product_title: string;
  product_vendor: string;
  product_handle: string;
  product_type: string;
  created_at: string;
  updated_at: string;
  prices: ProductPrice[];
  dropship_price: number;
  dropship_profit: number;
  market_price: number;
  dropship_selling_price_min: number;
  customize_template_id: string;
};

export default Variant;
