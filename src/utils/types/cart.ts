import {ProductPrice} from './product';

export type CartItem = {
  cart_id: string;
  featured_image: string;
  final_line_price: number;
  final_price: number;
  grams: number;
  handle: number;
  id: string;
  image: string;
  inventory_quantity: number;
  key: string;
  line_price: number;
  min_order_quantity: number;
  note: string;
  price: number;
  prices: ProductPrice;
  product_id: string;
  product_title: string;
  product_type: string;
  quantity: number;
  requires_shipping: boolean;
  shop_id: string;
  status: string;
  taxable: boolean;
  title: string;
  unit_name: string;
  variant_id: string;
  variant_title: string;
  vendor: string;
  dropship_price: number;
  dropship_selling_price: number;
  market_price: number;
  can_dropship: boolean;
  options_with_values: {[key: string]: string};
  properties: {[key: string]: string};
};

type Cart = {
  attributes: any[];
  currency: string;
  item_count: number;
  items: CartItem[];
  note: null;
  requires_shipping: boolean;
  token: string;
  total_price: number;
  total_weight: number;
  user_id: string;
};

export default Cart;
