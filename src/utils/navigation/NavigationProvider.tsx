import React, {lazy, Suspense} from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {NavigatorScreenParams} from '@react-navigation/native';
import MainTabsScreen, {MainTabsScreenList} from '~screens/MainTabs';
import CartScreen from '~screens/Cart';
import CustomerSelectScreen from '~screens/CustomerSelect';
import CreateCustomerScreen from '~screens/CreateCustomer';
import OrderDetailScreen from '~screens/OrderDetail';
import {Platform} from 'react-native';
import FulfillmentEventsScreen from '~screens/FulfillmentEvents';
import SearchScreen from '~screens/Search';
import SearchResult from '~screens/SearchResult';
import {ProductRequestQuery} from '~utils/types/product';
import CategoryDetail from '~screens/CategoryDetail';
import FAQ from '~screens/FAQ/FAQ';
import AccountInfoSettings from '~screens/AccountInfoSettings';
import AccountAndPayment from '~screens/AccountAndPayment';
import Settings from '~screens/Settings';
import AddBankAccount from '~screens/AddBankAccount';
import theme from '~utils/config/themes/theme';
import Feedback from '~screens/Feedback';
import Transactions from '~screens/Transactions';
import LoginCallbackScreen from '~screens/LoginCallback';
// @ts-ignore
import ChevronLeftPNG from '~assets/left-chevron.png';
import MemberRanking from '~screens/MemberRanking';
import Instruction from '~screens/Instructions';
import WebView from '~screens/WebView';
import OrderPayment from '~screens/OrderPayment';
import SupplierDetail from '~screens/SupplierDetail';
import BankAccountList from '~screens/BankAccountList';
import Withdraw from '~screens/Withdraw';
import Notifications from '~screens/Notifications';
import PrivacySettings from '~screens/PrivacySettings';
import AccountDeletion from '~screens/AccountDeletion';
import EscrowTransactions from '~screens/EscrowTransactions';
import AccountDisabled from '~screens/AccountDisabled';
import {useGetCurrentUser} from '~utils/api/auth';
import DownloadApp from '~screens/DownloadApp';
import AddToCart from '~screens/AddToCart';
import Invite from '~screens/Invite';
import CollectionDetail from '~screens/CollectionDetail';
import CreateCustomerAddress from '~screens/CreateCustomerAddress';
import MyReferrers from '~screens/MyReferrers';
import {Text} from '@rneui/themed';
import Wishlist from '~screens/Wishlist';
import RegisterScreen from '~screens/Register';
import PhoneVerification from '~components/by-screens/PhoneVerification';
import LoginScreen from '~screens/Login';
import {OrdersRequestQuery} from '~utils/api/order';
import SearchOrders from '~screens/SearchOrders';
import AllReviews from '~components/by-screens/AllReviews';
import OrderRate from '~screens/OrderRate';
import RateDetail from '~screens/RateDetail';
import UpdateRating from '~screens/UpdateRating';
import DownloadProductMedia from '~screens/DownloadProductMedia';
import SupplierReviews from '~components/by-screens/SupplierReviews';
import OrderTickets from '~screens/OrderTickets';
import SearchByImage from '~screens/SearchByImage';
import IdentityVerification from '~screens/IdentityVerification';
import ProductDetail from '~screens/ProductDetail';
import Appeton from '~screens/Appeton';
import WholeFoodsLink from '~screens/WholeFoodsLink';
import InviteFriends from '~screens/InviteFriends';
import ReferralDetail from '~screens/ReferralDetail';
import {Referrer} from '~utils/types/user';
import OrderConfirmScreen from '~screens/OrderConfirm';
import Lookback from '~screens/Lookback';
import PartnerShopProducts from '~screens/PartnerShopProducts';
import TaxInformation from '~screens/TaxInformation';
import TaxTransactions from '~screens/TaxTransactions';
import ProductPickerGame from '~screens/ProductPickerGame';
import MarkAllReadButton from '~components/by-screens/Notifications/MarkAllReadButton';

export type RootStackParamList = {
  MainTabs: NavigatorScreenParams<MainTabsScreenList>;
  ProductDetail: {productId: string | number};
  Cart:
    | {
        preSelectedVariantIds?: string | string[];
        productId?: string | number;
      }
    | undefined;
  OrderConfirm: undefined;
  SearchOrders: OrdersRequestQuery;
  CustomerSelect: undefined;
  CreateCustomer: {isCreateOrder?: boolean} | undefined;
  CreateCustomerAddress: {customerId: string; isCreateOrder?: boolean};
  OrderDetail: {orderId: number; showSuccessAlert?: boolean};
  FulfillmentEvents: {orderId: number};
  Search: {keyword?: string; shopId?: string} | undefined;
  SearchResult: ProductRequestQuery;
  CategoryDetail: {categoryId: string; slug: string} & Partial<ProductRequestQuery>;
  FAQ: undefined;
  AccountInfoSettings: undefined;
  AccountAndPayment: undefined;
  Settings: undefined;
  AddBankAccount: undefined;
  BankAccountList: undefined;
  Customers: undefined;
  Feedback: undefined | {initialStar?: number};
  Transactions: undefined;
  MemberRanking: undefined;
  Instruction: undefined;

  // app only
  WebView: {url: string; title?: string; includeAuth?: boolean; redirectNativeScreen?: boolean};

  // web only
  LoginCallback: {next?: string; access_token: string; refresh_token: string};
  // web only
  DownloadApp: undefined;

  OrderPayment: {payment_checkout_url: string; order_id?: string; status?: string; transaction_id?: string | number};
  SupplierDetail: {shopId: string} & Partial<ProductRequestQuery>;
  Withdraw: undefined;

  Notifications: undefined;

  PrivacySettings: undefined;
  AccountDeletion: undefined;
  EscrowTransactions: undefined;
  AccountDisabled: undefined;
  AddToCart: {productId: string | number; selectedVariantId?: string};
  InviteFriends: undefined;
  Invite: {ref_code: string};
  MyReferrers: undefined;
  CollectionDetail: {collectionId: string};
  Wishlist: undefined;
  Inspiration: {tabIndex?: number};
  Register: undefined;
  Login: undefined;
  PhoneVerification: {phone?: string; oauth_key: string};
  AllReviews: {productId: string};
  SupplierReviews: {shopId: string};
  OrderRate: {orderId: number | string; isUpdate?: boolean};
  OrderTickets: {orderId: number | string};
  RateDetail: {actionId: string};
  UpdateRating: {orderId: number | string; isUpdate?: boolean};
  DownloadProductMedia: {productId: string | number};
  SearchByImage: undefined;
  IdentityVerification: {msg?: string};
  TaxInformation: undefined;
  Appeton: undefined;
  WholeFoodsLink: undefined;
  ReferralDetail: {refferal: Referrer};
  Lookback: undefined;
  PartnerShopProducts: {partnerShopId: string};
  TaxTransactions: undefined;
  ProductPickerGame: undefined;
};

// const ProductDetail = lazy(() => import(/* webpackChunkName: "product-detail" */ '~screens/ProductDetail'));
// const OrderConfirmScreen = lazy(() => import(/* webpackChunkName: "order-confirm" */ '~screens/OrderConfirm'));
const Customers = lazy(() => import(/* webpackChunkName: "customers" */ '~screens/Customers'));

const Stack = createNativeStackNavigator<RootStackParamList>();

const NavigationProvider = () => {
  const {data: user} = useGetCurrentUser();

  return (
    <Suspense fallback={<Text>Loading...</Text>}>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          headerTitleStyle: {color: theme.colors.text},
          headerTitleAlign: 'left',
          headerBackImageSource: Platform.OS === 'web' ? ChevronLeftPNG : undefined,
          headerTintColor: theme.colors.text,
          headerBackButtonDisplayMode: 'minimal',
        }}>
        {user?.can_dropship === false ? (
          <>
            <Stack.Screen
              name="AccountDisabled"
              component={AccountDisabled}
              options={{
                title: '',
              }}
            />
          </>
        ) : (
          <>
            <Stack.Screen
              name="MainTabs"
              component={MainTabsScreen}
              options={{
                title: '',
              }}
            />
            <Stack.Screen
              name="ProductDetail"
              component={ProductDetail}
              options={{
                headerTransparent: true,
                headerTitle: '',
                headerShown: true,
              }}
            />
            <Stack.Screen
              name="Cart"
              component={CartScreen}
              options={{
                title: 'Giỏ hàng',
                headerShown: true,
              }}
            />
            <Stack.Screen
              name="OrderConfirm"
              component={OrderConfirmScreen}
              options={{
                title: 'Tạo đơn',
                headerShown: true,
              }}
            />
            <Stack.Screen
              name="SearchOrders"
              component={SearchOrders}
              options={{
                title: 'Tìm đơn hàng',
                headerShown: true,
              }}
            />
            <Stack.Screen
              name="CustomerSelect"
              component={CustomerSelectScreen}
              options={{
                title: 'Chọn khách hàng',
                headerShown: true,
              }}
            />
            <Stack.Screen
              name="CreateCustomer"
              component={CreateCustomerScreen}
              options={{
                title: 'Tạo một khách hàng',
                headerShown: true,
              }}
            />

            <Stack.Screen
              name="CreateCustomerAddress"
              component={CreateCustomerAddress}
              options={{
                title: 'Thêm địa chỉ mới',
                headerShown: true,
              }}
            />

            <Stack.Screen
              name="OrderDetail"
              component={OrderDetailScreen}
              options={{
                headerShown: true,
                title: 'Chi tiết đơn hàng',
              }}
            />
            <Stack.Screen
              name="FulfillmentEvents"
              component={FulfillmentEventsScreen}
              options={{
                headerShown: true,
                title: 'Theo dõi vận chuyển',
              }}
            />
            <Stack.Screen
              name="Search"
              component={SearchScreen}
              options={{
                headerShown: false,
                animation: 'fade',
                animationDuration: 300,
              }}
            />
            <Stack.Screen
              name="SearchResult"
              component={SearchResult}
              options={{
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CategoryDetail"
              component={CategoryDetail}
              options={{
                headerTitle: '',
                headerShown: true,
                title: '',
              }}
            />
            <Stack.Screen
              name="FAQ"
              component={FAQ}
              options={{
                headerShown: true,
                title: 'Câu hỏi thường gặp',
              }}
            />
            <Stack.Screen
              name="AccountInfoSettings"
              component={AccountInfoSettings}
              options={{
                title: 'Thông tin cá nhân',
                headerShown: true,
              }}
            />
            <Stack.Screen
              name="AccountAndPayment"
              component={AccountAndPayment}
              options={{
                title: 'Số dư & thanh toán',
                headerShown: true,
              }}
            />
            <Stack.Screen
              name="Settings"
              component={Settings}
              options={{
                title: 'Cài đặt',
                headerShown: true,
              }}
            />
            <Stack.Screen
              name="AddBankAccount"
              component={AddBankAccount}
              options={{
                title: 'Thêm tài khoản ngân hàng',
                headerShown: true,
              }}
            />
            <Stack.Screen
              name="Customers"
              component={Customers}
              options={{
                title: 'Khách hàng của tôi',
                headerShown: true,
              }}
            />
            <Stack.Screen
              name="Feedback"
              component={Feedback}
              options={{
                title: 'Góp ý - Báo lỗi',
                headerShown: true,
              }}
            />

            <Stack.Screen
              name="Transactions"
              component={Transactions}
              options={{
                title: 'Báo cáo giao dịch',
                headerShown: true,
              }}
            />

            <Stack.Screen
              name="EscrowTransactions"
              component={EscrowTransactions}
              options={{
                title: 'Tiền chờ đối soát',
                headerShown: true,
              }}
            />

            <Stack.Screen
              name="MemberRanking"
              component={MemberRanking}
              options={{
                title: 'Hạng thành viên',
                headerShown: true,
              }}
            />

            <Stack.Screen
              name="Instruction"
              component={Instruction}
              options={{
                title: 'Hướng dẫn sử dụng',
                headerShown: true,
              }}
            />

            <Stack.Screen
              name="OrderPayment"
              component={OrderPayment}
              options={{
                title: 'Thanh toán đơn hàng',
                headerShown: true,
              }}
            />

            <Stack.Screen
              name="SupplierDetail"
              component={SupplierDetail}
              options={{
                title: '',
                headerShown: true,
                headerTransparent: true,
              }}
            />

            <Stack.Screen
              name="BankAccountList"
              component={BankAccountList}
              options={{
                title: 'Tài khoản ngân hàng',
                headerShown: true,
              }}
            />

            <Stack.Screen
              name="Withdraw"
              component={Withdraw}
              options={{
                title: 'Rút tiền',
                headerShown: true,
              }}
            />

            <Stack.Screen
              name="Notifications"
              component={Notifications}
              options={{
                title: 'Thông báo',
                headerRight: MarkAllReadButton,
                headerShown: true,
              }}
            />

            <Stack.Screen
              name="PrivacySettings"
              component={PrivacySettings}
              options={{
                title: 'Cài đặt bảo mật',
                headerShown: true,
              }}
            />

            <Stack.Screen
              name="AccountDeletion"
              component={AccountDeletion}
              options={{
                title: 'Xóa tài khoản',
                headerShown: true,
              }}
            />

            <Stack.Screen
              name="DownloadApp"
              component={DownloadApp}
              options={{
                title: 'Tải ứng dụng',
                headerShown: true,
              }}
            />

            <Stack.Screen
              name="AddToCart"
              component={AddToCart}
              options={{
                animationDuration: 300,
                animation: 'fade_from_bottom',
                title: 'Thêm vào giỏ',
                headerShown: true,
              }}
            />

            <Stack.Screen
              name="InviteFriends"
              component={InviteFriends}
              options={{
                title: 'Mời bạn kiếm tiền',
                headerShown: true,
              }}
            />

            <Stack.Screen
              name="Invite"
              component={Invite}
              options={{
                title: 'Mời bạn kiếm tiền',
                headerShown: false,
              }}
            />

            <Stack.Screen
              name="MyReferrers"
              component={MyReferrers}
              options={{
                title: 'Đã mời thành công',
                headerShown: true,
              }}
            />

            <Stack.Screen
              name="ReferralDetail"
              component={ReferralDetail}
              options={{
                title: 'Chi tiết cộng tác viên',
                headerShown: true,
              }}
            />

            <Stack.Screen
              name="CollectionDetail"
              component={CollectionDetail}
              options={{
                title: '',
                headerShown: true,
              }}
            />

            <Stack.Screen
              name="Wishlist"
              component={Wishlist}
              options={{
                headerShown: true,
                headerTitle: 'Đã lưu',
                headerTintColor: theme.colors.textLight,
              }}
            />

            <Stack.Screen
              name="Register"
              component={RegisterScreen}
              options={{
                headerShown: true,
                headerTitle: '',
                headerTintColor: theme.colors.textLight,
                headerShadowVisible: false,
              }}
            />
            <Stack.Screen
              name="Login"
              component={LoginScreen}
              options={{
                headerShown: true,
                headerTitle: '',
                headerTintColor: theme.colors.textLight,
                headerShadowVisible: false,
              }}
            />

            <Stack.Screen
              name="PhoneVerification"
              component={PhoneVerification}
              options={{
                headerShown: true,
                headerTitle: '',
                headerTintColor: theme.colors.textLight,
                headerShadowVisible: false,
              }}
            />

            <Stack.Screen
              name="AllReviews"
              component={AllReviews}
              options={{
                headerShown: true,
                headerTitle: 'Tất cả đánh giá',
              }}
            />

            <Stack.Screen
              name="SupplierReviews"
              component={SupplierReviews}
              options={{
                headerShown: true,
                headerTitle: 'Đánh giá nhà cung cấp',
              }}
            />

            <Stack.Screen
              name="OrderRate"
              component={OrderRate}
              options={{
                headerShown: true,
                headerTitle: 'Gửi đánh giá',
              }}
            />

            <Stack.Screen
              name="RateDetail"
              component={RateDetail}
              options={{
                headerShown: true,
                headerTitle: 'Chi tiết đánh giá',
              }}
            />

            <Stack.Screen
              name="UpdateRating"
              component={UpdateRating}
              options={{
                headerShown: true,
                headerTitle: 'Chỉnh sửa đánh giá',
              }}
            />

            <Stack.Screen
              name="OrderTickets"
              component={OrderTickets}
              options={{
                headerShown: false,
                headerTitle: 'Hỗ trợ đơn hàng',
              }}
            />

            {Platform.OS !== 'web' && (
              <Stack.Screen
                name="SearchByImage"
                component={SearchByImage}
                options={{
                  headerShown: true,
                  headerTitle: 'Tìm bằng hình ảnh',
                }}
              />
            )}
          </>
        )}

        <Stack.Screen
          name="DownloadProductMedia"
          component={DownloadProductMedia}
          options={{
            headerShown: true,
            headerTitle: 'Tải hình ảnh',
          }}
        />

        <Stack.Screen
          name="IdentityVerification"
          component={IdentityVerification}
          options={{
            title: 'Định danh tài khoản',
            headerShown: true,
            headerTitle: 'Định danh tài khoản',
          }}
        />

        <Stack.Screen
          name="TaxInformation"
          component={TaxInformation}
          options={{
            headerShown: true,
            headerTitle: 'Cài đặt thông tin thuế',
          }}
        />

        <Stack.Screen name="TaxTransactions" component={TaxTransactions} options={{headerShown: true, headerTitle: 'Quản lý thuế'}} />

        <Stack.Screen name="WebView" component={WebView} />
        {Platform.OS === 'web' && <Stack.Screen name="LoginCallback" component={LoginCallbackScreen} />}

        <Stack.Screen name="Lookback" component={Lookback} />

        <Stack.Screen name="PartnerShopProducts" component={PartnerShopProducts} options={{headerShown: true, headerTitle: 'Quản lý sản phẩm'}} />

        <Stack.Screen name="Appeton" component={Appeton} />
        <Stack.Screen name="WholeFoodsLink" component={WholeFoodsLink} />
        <Stack.Screen name="ProductPickerGame" component={ProductPickerGame} options={{headerShown: true, title: 'Hôm nay bán gì?', headerTitleAlign: 'center'}} />
      </Stack.Navigator>
    </Suspense>
  );
};

export default NavigationProvider;
