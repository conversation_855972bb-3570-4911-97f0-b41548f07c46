import * as yup from 'yup';

const customerValidate = yup.object().shape({
  name: yup.string().min(3, 'Tên phải từ 3 kí tự').required('Không được để trống'),
  phone: yup
    .string()
    .required('<PERSON>hông được để trống')
    .matches(/((09|03|07|08|05)+([0-9]{8})\b)/g, {
      message: 'Số điện thoại không hợp lệ',
    }),
  addresses: yup.array().of(
    yup.object().shape({
      city: yup.string().required('<PERSON><PERSON> lòng chọn tỉnh thành'),
      province: yup.string().required('<PERSON>ui lòng chọn quận huyện'),
      ward: yup.string().required('<PERSON><PERSON> lòng chọn phường xã'),
      address1: yup.string().required('<PERSON><PERSON> lòng nhập số nhà, tên đường').min(3, '<PERSON><PERSON><PERSON> thiểu 3 kí tự'),
    }),
  ),
});

export default customerValidate;
