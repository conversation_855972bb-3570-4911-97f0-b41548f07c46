import * as yup from 'yup';

const reviewValidate = yup.object({
  seller_service: yup.number().min(1, '<PERSON>ui lòng đánh giá dịch vụ nhà cung cấp').required('<PERSON>ui lòng đánh giá dịch vụ nhà cung cấp'),
  delivery_service: yup.number().min(1, 'Vui lòng đánh giá dịch vụ vận chuyển').required('Vui đánh giá dịch vụ vận chuyển'),
  line_items: yup.array().of(
    yup.object().shape({
      quality_rate: yup.number().min(1, 'Vui lòng đánh giá sản phẩm').required('Vui lòng đánh giá sản phẩm'),
      comment: yup
        .string()
        .nullable()
        .when('quality_rate', (quality_rate, schema) => {
          if (quality_rate < 5) {
            return schema.required('<PERSON><PERSON><PERSON> giá dưới 5 sao cần viết đ<PERSON> giá').min(3, 'T<PERSON><PERSON> thiểu 3 ký tự');
          } else {
            return schema;
          }
        }),
    }),
  ),
});

export default reviewValidate;
