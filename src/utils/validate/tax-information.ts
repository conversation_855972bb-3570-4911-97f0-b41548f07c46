import * as yup from 'yup';

const taxInformationValidate = yup.object().shape({
  tax_id: yup.string().required('Không được để trống'),
  tax_address: yup.string().min(3, '<PERSON><PERSON><PERSON> thiểu 3 kí tự').required('Không được để trống'),
  tax_name: yup.string().required('Không được để trống'),
  tax_type: yup.string().required('Không được để trống'),
  tax_email: yup.array().of(yup.string().email('Email không hợp lệ').required('Không được để trống')).min(1, 'Không được để trống'),
});

export default taxInformationValidate;
