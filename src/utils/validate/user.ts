import * as yup from 'yup';
import {phoneNumberRegex} from './common';

const userValidate = yup.object().shape({
  account_phone: yup.string().matches(phoneNumberRegex, {message: '<PERSON><PERSON> điện thoại không hợp lệ'}).required('<PERSON><PERSON> điện thoại không được để trống'),
  address: yup.string().min(3, 'Tối thiểu 3 kí tự').required('Không được để trống'),
  full_name: yup.string().required('Không được để trống'),
  district_name: yup.string().required('Không được để trống'),
  province_name: yup.string().required('Không được để trống'),
  ward_name: yup.string().required('Không được để trống'),
});

export default userValidate;
