import * as yup from 'yup';

export const phoneNumberRegex = /((09|03|07|08|05)+([0-9]{8})\b)/g;

export const bankAccountValidate = yup.object().shape({
  name: yup.string().required('Không được để trống').min(3, 'Tên của bạn quá ngắn'),
  number: yup.string().required('Không dược để trống'),
  bank_id: yup.number().required('Không được để trống'),
});

export const passwordChangeValidate = yup.object().shape({
  password: yup.string().required('Không được để trống'),
  old_password: yup.string().required('Không được để trống'),
  confirm_password: yup
    .string()
    .required('Không được để trống')
    .oneOf([yup.ref('password'), null], 'Không giống với mật khẩu'),
});
