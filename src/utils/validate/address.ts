import * as yup from 'yup';

const addressValidate = yup.object().shape({
  name: yup.string().min(3, 'Tên phải từ 3 kí tự').required('Không được để trống'),
  phone: yup
    .string()
    .required('Không được để trống')
    .matches(/((09|03|07|08|05)+([0-9]{8})\b)/g, {
      message: 'Số điện thoại không hợp lệ',
    }),
  city: yup.string().required('<PERSON>ui lòng chọn tỉnh thành'),
  province: yup.string().required('<PERSON>ui lòng chọn quận huyện'),
  ward: yup.string().required('<PERSON>ui lòng chọn phường xã'),
  address1: yup.string().required('<PERSON><PERSON> lòng nhập số nhà, tên đường').min(3, 'T<PERSON><PERSON> thiểu 3 kí tự'),
});

export default addressValidate;
