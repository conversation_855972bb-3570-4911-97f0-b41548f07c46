import Reactotron from 'reactotron-react-native';
import {QueryClientManager, reactotronReactQuery} from 'reactotron-react-query';

//

export default function initReactotron(queryClient: any) {
  const queryClientManager = new QueryClientManager({
    // @ts-ignore
    queryClient,
  });

  Reactotron.use(reactotronReactQuery(queryClientManager))
    .configure({
      onDisconnect: () => {
        queryClientManager.unsubscribe();
      },
    })
    .useReactNative()
    .connect();
}
