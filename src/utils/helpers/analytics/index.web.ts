import {FirebaseAnalyticsTypes} from '@react-native-firebase/analytics';
import {getAnalytics, logEvent} from 'firebase/analytics';

import {initializeApp} from 'firebase/app';
import {GoogleAnalyticsInterface} from '.';
import {env} from '../../config';

export const app = initializeApp(env.FIREBASE_WEB_CONFIG);

const googleAnalytics: GoogleAnalyticsInterface = {
  logScreenView: async (_params?: FirebaseAnalyticsTypes.ScreenViewParameters) => {
    const analytics = getAnalytics(app);
    logEvent(analytics, 'screen_view');
  },
  logRemoveFromCart: async (params: FirebaseAnalyticsTypes.RemoveFromCartEventParameters) => {
    const analytics = getAnalytics(app);

    logEvent(analytics, 'remove_from_cart', params);
  },
  logAddShippingInfo: async (params: FirebaseAnalyticsTypes.AddShippingInfoParameters) => {
    const analytics = getAnalytics(app);

    logEvent(analytics, 'add_shipping_info', params);
  },
  logAddPaymentInfo: async (params: FirebaseAnalyticsTypes.AddPaymentInfoEventParameters) => {
    const analytics = getAnalytics(app);

    logEvent(analytics, 'add_payment_info', params);
  },
  logEvent: async (name: string, params?: {[key: string]: any}) => {
    const analytics = getAnalytics(app);

    logEvent(analytics, name, params);
  },
  logAddToWishlist: async (params: FirebaseAnalyticsTypes.AddToWishlistEventParameters) => {
    const analytics = getAnalytics(app);

    logEvent(analytics, 'add_to_wishlist', params);
  },
  logShare: async (params: FirebaseAnalyticsTypes.ShareEventParameters) => {
    const analytics = getAnalytics(app);

    logEvent(analytics, 'share', params);
  },
  logAppOpen: async () => {},
  logBeginCheckout: async (params?: FirebaseAnalyticsTypes.BeginCheckoutEventParameters) => {
    const analytics = getAnalytics(app);

    logEvent(analytics, 'begin_checkout', params);
  },
  logPurchase: async (params: FirebaseAnalyticsTypes.PurchaseEventParameters) => {
    const analytics = getAnalytics(app);

    logEvent(analytics, 'purchase', {
      ...(params as any),
    });
  },
  logViewItem: async (params: FirebaseAnalyticsTypes.ViewItemEventParameters) => {
    const analytics = getAnalytics(app);

    logEvent(analytics, 'view_item', params);
  },
  logSearch: async (params: FirebaseAnalyticsTypes.SearchEventParameters) => {
    const analytics = getAnalytics(app);

    logEvent(analytics, 'search', params);
  },
  logAddToCart: async (params: FirebaseAnalyticsTypes.AddToCartEventParameters) => {
    const analytics = getAnalytics(app);

    logEvent(analytics, 'add_to_cart', params);
  },
  logViewCart: async (params: FirebaseAnalyticsTypes.ViewCartEventParameters) => {
    const analytics = getAnalytics(app);

    logEvent(analytics, 'view_cart', params);
  },
};

export default googleAnalytics;
