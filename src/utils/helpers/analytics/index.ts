import analytics, {FirebaseAnalyticsTypes} from '@react-native-firebase/analytics';

export type GoogleAnalyticsInterface = {
  logScreenView: (params: FirebaseAnalyticsTypes.ScreenViewParameters) => Promise<void>;
  logRemoveFromCart: (params: FirebaseAnalyticsTypes.RemoveFromCartEventParameters) => Promise<void>;
  logAddShippingInfo: (params: FirebaseAnalyticsTypes.AddShippingInfoParameters) => Promise<void>;
  logAddPaymentInfo: (params: FirebaseAnalyticsTypes.AddPaymentInfoEventParameters) => Promise<void>;
  logEvent: (name: string, params?: {[key: string]: any}) => Promise<void>;
  logAddToWishlist(params: FirebaseAnalyticsTypes.AddToWishlistEventParameters): Promise<void>;
  logShare(params: FirebaseAnalyticsTypes.ShareEventParameters): Promise<void>;
  logAppOpen(): Promise<void>;
  logBeginCheckout(params?: FirebaseAnalyticsTypes.BeginCheckoutEventParameters): Promise<void>;
  logPurchase(params: FirebaseAnalyticsTypes.PurchaseEventParameters): Promise<void>;
  logViewItem(params: FirebaseAnalyticsTypes.ViewItemEventParameters): Promise<void>;
  logSearch(params: FirebaseAnalyticsTypes.SearchEventParameters): Promise<void>;
  logAddToCart(params: FirebaseAnalyticsTypes.AddToCartEventParameters): Promise<void>;
  logViewCart(params: FirebaseAnalyticsTypes.ViewCartEventParameters): Promise<void>;
};

const googleAnalytics: GoogleAnalyticsInterface = {
  logScreenView: async (params: FirebaseAnalyticsTypes.ScreenViewParameters) => {
    try {
      analytics().logScreenView(params);
    } catch (error) {}
  },
  logRemoveFromCart: async (params: FirebaseAnalyticsTypes.RemoveFromCartEventParameters) => {
    try {
      analytics().logRemoveFromCart(params);
    } catch (error) {}
  },
  logAddShippingInfo: async (params: FirebaseAnalyticsTypes.AddShippingInfoParameters) => {
    try {
      analytics().logAddShippingInfo(params);
    } catch (error) {}
  },
  logAddPaymentInfo: async (params: FirebaseAnalyticsTypes.AddPaymentInfoEventParameters) => {
    try {
      analytics().logAddPaymentInfo(params);
    } catch (error) {}
  },
  logEvent: async (name: string, params?: {[key: string]: any}) => {
    try {
      analytics().logEvent(name, params);
    } catch (error) {}
  },
  logAddToWishlist: async (params: FirebaseAnalyticsTypes.AddToWishlistEventParameters) => {
    try {
      analytics().logAddToWishlist(params);
    } catch (error) {}
  },
  logShare: async (params: FirebaseAnalyticsTypes.ShareEventParameters) => {
    try {
      analytics().logShare(params);
    } catch (error) {}
  },
  logAppOpen: async () => {
    try {
      analytics().logAppOpen();
    } catch (error) {}
  },
  logBeginCheckout: async (params?: FirebaseAnalyticsTypes.BeginCheckoutEventParameters) => {
    try {
      analytics().logBeginCheckout(params);
    } catch (error) {}
  },
  logPurchase: async (params: FirebaseAnalyticsTypes.PurchaseEventParameters) => {
    try {
      analytics().logPurchase(params);
    } catch (error) {}
  },
  logViewItem: async (params: FirebaseAnalyticsTypes.ViewItemEventParameters) => {
    try {
      analytics().logViewItem(params);
    } catch (error) {}
  },
  logSearch: async (params: FirebaseAnalyticsTypes.SearchEventParameters) => {
    try {
      analytics().logSearch(params);
    } catch (error) {}
  },
  logAddToCart: async (params: FirebaseAnalyticsTypes.AddToCartEventParameters) => {
    try {
      analytics().logAddToCart(params);
    } catch (error) {}
  },
  logViewCart: async (params: FirebaseAnalyticsTypes.ViewCartEventParameters) => {
    try {
      analytics().logViewCart(params);
    } catch (error) {}
  },
};

export default googleAnalytics;
