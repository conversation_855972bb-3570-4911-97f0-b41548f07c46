import AsyncStorage from '@react-native-async-storage/async-storage';
import Product, {ProductImage, ProductVideo} from '~utils/types/product';

export const hasVariants = (product: Product) => {
  if (product.variants.length === 1 && product.variants[0].title && ['default', 'default title'].includes(product.variants[0].title.toLowerCase())) {
    return false;
  }
  return true;
};

export const saveLastSelectedMediaItems = async (productId: string, mediaItems: {type: string; item: ProductImage | ProductVideo}[]) => {
  try {
    AsyncStorage.setItem(`lastSelectedMediaItems-${productId}`, JSON.stringify(mediaItems));
  } catch (error) {}
};

export const getLastSelectedMediaItems = async (productId: string): Promise<{type: string; item: ProductImage | ProductVideo}[]> => {
  try {
    const res = await AsyncStorage.getItem(`lastSelectedMediaItems-${productId}`);
    if (res) {
      return JSON.parse(res) || [];
    }
    return [];
  } catch (error) {
    return [];
  }
};
