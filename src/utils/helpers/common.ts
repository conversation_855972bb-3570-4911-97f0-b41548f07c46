import dayjs from 'dayjs';
import {Linking} from 'react-native';
import {ReceivedNotification} from 'react-native-push-notification';
import Rate, {AndroidMarket} from 'react-native-rate';
import {patternFormatter} from 'react-number-format';
import AsyncStorage from './storage';
import {ASYNC_STORAGE_KEYS} from '~utils/config';

export const handleRateApp = async () => {
  const options = {
    AppleAppID: '6443614020',
    GooglePackageName: 'com.tts.ds.buyer',
    OtherAndroidURL: 'https://play.google.com/store/apps/details?id=com.tts.ds.buyer',
    preferredAndroidMarket: AndroidMarket.Google,
    preferInApp: true,
    openAppStoreIfInAppFails: false,
    fallbackPlatformURL: 'https://go.thitruongsi.com/rate-app-fallback-platform',
  };
  Rate.rate(options, (success, errorMessage) => {
    AsyncStorage.setItem(ASYNC_STORAGE_KEYS.APP_RATED, '5');

    // if (success) {
    //   Alert.alert('Cảm ơn bạn đã đánh giá ứng dụng', '<PERSON>úng tôi sẽ liên tục ghi nhận ý kiến đánh giá và cải tiến ứng dụng để phục vụ bạn tốt hơn');
    // }
    if (errorMessage) {
      console.error(`Example page Rate.rate() error: ${errorMessage}`);
      //   Sentry.captureMessage(`Rate app error: ${errorMessage}`);
    }
  });
};

export const handleNotificationClick = async (notification: ReceivedNotification) => {
  try {
    switch (true) {
      default:
        if (notification.data?.url) {
          let deeplink = parseURLToDeepLink(notification.data.url);
          let canOpenURL = await Linking.canOpenURL(deeplink);
          if (canOpenURL) {
            Linking.openURL(deeplink);
          }
        }
        break;
    }
  } catch (error) {}
};

export const parseURLToDeepLink = (url: string) => {
  if (url.startsWith('/')) {
    // path only url
    return `tts.com.dsbuyer://${url}`;
  }
  return url.replace(/^(http|https):\/\/(dropship\.|dropship-sandbox\.)?thitruongsi\.com/g, 'tts.com.dsbuyer://');
};

export const stripHtml = (htmlString: string) => {
  return htmlString.replace(/(<([^>]+)>)/gi, '');
};

export const formatPhoneString = (phone: string) =>
  patternFormatter(phone, {
    format: '#### ### #####',
    hidden: false,
    formNoValidate: true,
    allowEmptyFormatting: true,
  });

export const isWorkingHour = (date?: string) => {
  const day = dayjs(date);

  if (day.day() === 0) {
    return false;
  } else {
    const workingHourEnd = day.set('hour', 17).set('minute', 0);
    const workingHourStart = day.set('hour', 8).set('minute', 0);
    const isInWorkingHour = day.isAfter(workingHourStart) && day.isBefore(workingHourEnd);
    if (!isInWorkingHour) {
      return false;
    }
  }

  return true;
};

export const formatDate = (date?: string) => {
  const diff = dayjs().diff(dayjs(date), 'days');
  if (diff < 3) {
    return dayjs(date).fromNow();
  }

  return dayjs(date).format('DD/MM/YYYY');
};
