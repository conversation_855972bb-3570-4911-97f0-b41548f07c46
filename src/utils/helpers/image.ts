import {PermissionsAndroid} from 'react-native';
import {Image} from 'react-native-image-crop-picker';
import {Asset} from 'react-native-image-picker';
import {ImageFile} from '~utils/api/image';
import {env} from '~utils/config';

export const handleImageDomain = (url: string): string => {
  if (url) {
    if (url.indexOf('http') > -1) {
      return url;
    }

    return `${env.IMAGE_URI}${url}`;
  }
  return '/static/images/no-image.jpg';
};

export const imageToFile = (image: Image): ImageFile => {
  return {
    uri: image.path,
    type: image.mime,
    name: image.filename || 'sanpham.jpg',
  };
};

export const imagePickerAssetsToFile = (asset: Asset): ImageFile => {
  return {
    name: asset.fileName || 'sanpham.jpg',
    type: asset.type || '',
    width: asset.width,
    height: asset.height,
    uri: asset.uri || '',
  };
};

export const getResizedImageUrl = (url: string, width: number, height: number): string => {
  return url?.replace(/rs\:fill\:\d+\:\d+/, 'rs:fill:' + width + ':' + height);
};

// https://api-sandbox.thitruongsi.com/v2/order/api/download/product-image/?product_id=&file_name=&image_url=https://imgcdn.thitruongsi.com/tts/rs:fill:200:200:1:1/g:sm/plain/file://product/2016/04/01/56fe2ea63ecae.jpg
export const getDownloadableURL = (imageURL: string, productId?: string, fileName?: string) => {
  return `${env.API_BASE_URL}/v2/order/api/download/product-image/?product_id=${productId || ''}&file_name=${fileName || ''}&image_url=${imageURL}`;
};

export const requestCameraPermission = async () => {
  try {
    const granted = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.CAMERA, {
      title: 'Cấp quyền sử dụng Camera',
      message: 'Ứng dụng cần sử dụng Camera để chụp hình ảnh',
      buttonNeutral: 'Hỏi lại sau',
      buttonNegative: 'Hủy',
      buttonPositive: 'Đồng ý',
    });
    if (granted === PermissionsAndroid.RESULTS.GRANTED) {
      console.log('Camera permission given');
    } else {
      console.log('Camera permission denied');
    }
  } catch (err) {
    console.warn(err);
  }
};
