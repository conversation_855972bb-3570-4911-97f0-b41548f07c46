import {Platform, Alert} from 'react-native';
import {PERMISSIONS, RESULTS, checkMultiple, requestMultiple, openSettings, check, request} from 'react-native-permissions';

// Hàm này không đổi
export async function checkAndRequestSavePhotoPermission(): Promise<boolean> {
  return Platform.OS === 'android' ? checkAndRequestMediaPermissionAndroid() : checkAndRequestSavePhotoiOS();
}

/**
 * Kiểm tra và yêu cầu quyền lưu media (ảnh và video) trên Android.
 * Trả về true nếu quyền cần thiết được cấp.
 */
export async function checkAndRequestMediaPermissionAndroid(): Promise<boolean> {
  const androidVersion = typeof Platform.Version === 'string' ? parseInt(Platform.Version, 10) : Platform.Version;

  // Danh sách quyền cần thiết
  const permissionsToRequest =
    androidVersion >= 33
      ? [PERMISSIONS.ANDROID.READ_MEDIA_IMAGES, PERMISSIONS.ANDROID.READ_MEDIA_VIDEO] // Android 13+
      : [PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE]; // Android < 13

  try {
    // 1. Kiểm tra trạng thái các quyền
    const statuses = await checkMultiple(permissionsToRequest);

    // Kiểm tra xem tất cả quyền đã được cấp chưa
    const allPermissionsGranted = Object.values(statuses).every(status => status === RESULTS.GRANTED);
    if (allPermissionsGranted) {
      return true;
    }

    // 2. Nếu có quyền chưa được cấp, yêu cầu chúng
    const requestedStatuses = await requestMultiple(permissionsToRequest);

    // 3. Kiểm tra kết quả sau khi yêu cầu
    // Chỉ cần ít nhất một quyền được cấp là có thể tiếp tục
    const atLeastOnePermissionGranted = Object.values(requestedStatuses).some(status => status === RESULTS.GRANTED);

    if (atLeastOnePermissionGranted) {
      return true;
    }

    // 4. Nếu tất cả đều bị từ chối hoặc chặn, hiển thị thông báo
    const anyPermissionBlocked = Object.values(requestedStatuses).some(status => status === RESULTS.BLOCKED);
    if (anyPermissionBlocked) {
      showPermissionBlockedAlert();
    } else {
      Alert.alert('Quyền bị từ chối', 'Bạn đã từ chối quyền lưu tệp.');
    }

    return false;
  } catch (error) {
    console.error('Lỗi khi kiểm tra quyền media:', error);
    return false;
  }
}

const showPermissionBlockedAlert = () => {
  Alert.alert(
    'Không thể lưu ảnh',
    'Bạn cần cài đặt cấp quyền truy cập ảnh để có thể lưu ảnh và video vào thiết bị',
    [
      {
        text: 'Trở về',
        style: 'cancel',
        isPreferred: false,
      },
      {
        text: 'Cài đặt',
        onPress: () => {
          openSettings();
        },
        style: 'default',
        isPreferred: true,
      },
    ],
    {
      cancelable: true,
    },
  );
};
async function checkAndRequestSavePhotoiOS() {
  const result = await check(PERMISSIONS.IOS.PHOTO_LIBRARY_ADD_ONLY);

  switch (result) {
    case RESULTS.GRANTED:
      return true;

    case RESULTS.DENIED:
      const requestResult = await request(PERMISSIONS.IOS.PHOTO_LIBRARY_ADD_ONLY);
      if (requestResult === RESULTS.BLOCKED) {
        showPermissionBlockedAlert();
        return false;
      }
      return requestResult === RESULTS.GRANTED;

    case RESULTS.BLOCKED:
      showPermissionBlockedAlert();
      return false;

    default:
      return false;
  }
}
