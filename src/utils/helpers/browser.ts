import {InAppBrowser} from 'react-native-inappbrowser-reborn';
import {Linking, Platform} from 'react-native';

export const openAuthInAppBrowser = async (url: string, redirectUrl: string) => {
  if (await InAppBrowser.isAvailable()) {
    const result = await InAppBrowser.openAuth(url, redirectUrl, {
      // iOS Properties
      ephemeralWebSession: false,
      // Android Properties
      showTitle: false,
      enableUrlBarHiding: true,
      enableDefaultShare: false,
      forceCloseOnRedirection: false,
      showInRecents: true,
    });

    return result;
  } else {
    Linking.openURL(url);
  }
};
export function isValidHttpUrl(string: string) {
  let url;

  try {
    url = new URL(string);
  } catch (_) {
    return false;
  }

  return url.protocol === 'http:' || url.protocol === 'https:';
}

export const popupCenter = ({url, title, w, h}: {url: string; title: string; w: number; h: number}) => {
  if (Platform.OS !== 'web') {
    return;
  }
  // Fixes dual-screen position                             Most browsers      Firefox
  const dualScreenLeft = window.screenLeft !== undefined ? window.screenLeft : window.screenX;
  const dualScreenTop = window.screenTop !== undefined ? window.screenTop : window.screenY;

  const width = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : window.screen.width;
  const height = window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : window.screen.height;

  const systemZoom = width / window.screen.availWidth;
  const left = (width - w) / 3 / systemZoom + dualScreenLeft;
  const top = (height - h) / 3 / systemZoom + dualScreenTop;
  const newWindow = window.open(
    url,
    title,
    `
    scrollbars=yes,
    width=${w / systemZoom}, 
    height=${h / systemZoom}, 
    top=${top}, 
    left=${left}
    `,
  );

  newWindow?.focus();

  return newWindow;
};
