import {<PERSON>Roll, SaveToCameraRollOptions} from '@react-native-camera-roll/camera-roll';
import axios from 'axios';
import {PermissionsAndroid, Platform} from 'react-native';
import RNFetchBlob from 'rn-fetch-blob';
import {Buffer} from 'buffer';

async function hasAndroidPermission() {
  const getCheckPermissionPromise = () => {
    // @ts-ignore
    if (Platform.Version >= 33) {
      return Promise.all([PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES), PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO)]).then(
        ([hasReadMediaImagesPermission, hasReadMediaVideoPermission]) => hasReadMediaImagesPermission && hasReadMediaVideoPermission,
      );
    } else {
      return PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE);
    }
  };

  const hasPermission = await getCheckPermissionPromise();
  if (hasPermission) {
    return true;
  }
  const getRequestPermissionPromise = () => {
    // @ts-ignore
    if (Platform.Version >= 33) {
      return PermissionsAndroid.requestMultiple([PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES, PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO]).then(
        statuses =>
          statuses[PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES] === PermissionsAndroid.RESULTS.GRANTED &&
          statuses[PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO] === PermissionsAndroid.RESULTS.GRANTED,
      );
    } else {
      return PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE).then(status => status === PermissionsAndroid.RESULTS.GRANTED);
    }
  };

  return await getRequestPermissionPromise();
}

const downloadImage = async (imageURL: string) => {
  await RNFetchBlob.config({
    fileCache: true,

    appendExt: 'jpeg',
  })

    .fetch('GET', imageURL)

    .then(res => {
      const path = Platform.OS === 'android' ? `file://${res.path()}` : res.path();
      CameraRoll.saveAsset(path, {type: 'photo'});
    })

    .catch(() => {});
};

export const downloadImageWithPOST = async (url: string, body: any, ext?: string) => {
  await axios
    .post(url, body, {
      responseType: 'arraybuffer',
    })
    .then(async res => {
      const base64Data = `data:image/${ext || 'jpeg'};base64,${Buffer.from(res.data, 'binary').toString('base64')}`;
      await CameraRoll.save(base64Data, {type: 'photo'});
    });
};

export const downloadVideo = async (videoURL: string) => {
  await RNFetchBlob.config({
    fileCache: true,
    appendExt: 'mp4',
  })
    .fetch('GET', videoURL)
    .then(res => {
      CameraRoll.save(res.data);
    })
    .catch(() => {});
};

export const savePicture = async (tag: string, options?: SaveToCameraRollOptions | undefined) => {
  return CameraRoll.save(tag, options);
};

export default downloadImage;
