import React, {useCallback, useEffect, useState} from 'react';

import Hyperlink from 'react-native-hyperlink';

import LinkifyIt from 'linkify-it';
import {Linking, TextStyle, TouchableOpacity, View} from 'react-native';
import ee, {EventNames} from '~utils/events';
import {Text} from '@rneui/themed';
import {BottomSheet} from '@rneui/base';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import theme from '~utils/config/themes/theme';
import Clipboard from '@react-native-clipboard/clipboard';
import {useToast} from 'react-native-toast-notifications';

const linkifyObject = LinkifyIt();

linkifyObject.add('0', {
  validate: /^(3|5|7|8|9|1[2|6|8|9]|\s)+(([0-9]|\s)+)\b/,

  normalize: function (match) {
    match.url = 'tel:' + match.raw.replace(/\s/g, '');
  },
});

type PhoneHyperLinkProps = React.PropsWithChildren & {
  linkStyle: TextStyle;
  onPress?: (url: string, text?: string | undefined) => void;
};

const PhoneHyperLink: React.FC<PhoneHyperLinkProps> = props => {
  const handlePress = useCallback((url: string, text?: string | undefined) => {
    if (props.onPress) {
      props.onPress(url, text);
    }

    ee.emit(EventNames.ShowPhoneHyperLinkBottomSheet, {url, text});
  }, []);

  return (
    <Hyperlink linkify={linkifyObject} linkStyle={props.linkStyle} onPress={handlePress}>
      {props.children as any}
    </Hyperlink>
  );
};

type PhoneHyperLinkBottomSheetProps = {};

export const PhoneHyperLinkBottomSheet: React.FC<PhoneHyperLinkBottomSheetProps> = () => {
  const [visible, setVisible] = useState(false);
  const [bottomSheetLinkData, setBottomSheetLinkData] = useState({url: '', text: ''});
  const insets = useSafeAreaInsets();
  const toast = useToast();

  useEffect(() => {
    ee.on(EventNames.ShowPhoneHyperLinkBottomSheet, handleOpenPhoneHyperLinkBottomSheet);
    return () => {
      ee.off(EventNames.ShowPhoneHyperLinkBottomSheet, handleOpenPhoneHyperLinkBottomSheet);
    };
  }, []);

  const handleOpenPhoneHyperLinkBottomSheet = useCallback(({url, text}: {url: string; text: string}) => {
    setVisible(true);
    setBottomSheetLinkData({url, text});
  }, []);

  const handleClose = useCallback(() => {
    setVisible(false);
  }, []);

  const list = [
    {
      title: 'Gọi điện thoại',

      onPress: () => {
        Linking.openURL(bottomSheetLinkData.url);
        handleClose();
      },
    },
    {
      title: 'Sao chép ',
      onPress: () => {
        Clipboard.setString(bottomSheetLinkData.text);
        handleClose();
        toast.show('Đã sao chép');
      },
    },
  ];

  return (
    <BottomSheet isVisible={visible} onBackdropPress={handleClose} containerStyle={{}}>
      <View style={{marginBottom: insets.bottom + theme.spacing.s, marginHorizontal: theme.spacing.s, overflow: 'hidden'}}>
        <View style={{backgroundColor: theme.colors.white, padding: 8, borderTopLeftRadius: 12, borderTopRightRadius: 12}}>
          <Text style={{color: theme.colors.textLight, fontSize: theme.typography.base, textAlign: 'center'}}>{bottomSheetLinkData.text}</Text>
        </View>
        {list.map((l, i) => (
          <TouchableOpacity
            onPress={l.onPress}
            key={i}
            style={[{backgroundColor: theme.colors.white, padding: 16, borderTopWidth: 1, borderTopColor: theme.colors.gray20}, i === 0 && {borderTopLeftRadius: 0, borderTopRightRadius: 0}]}>
            <Text style={{textAlign: 'center', color: theme.colors.blue, fontWeight: '500', fontSize: theme.typography.md}}>{l.title}</Text>
          </TouchableOpacity>
        ))}

        <TouchableOpacity style={[{marginTop: theme.spacing.s, borderRadius: 12, backgroundColor: theme.colors.white, padding: 12}]} onPress={handleClose}>
          <Text style={{textAlign: 'center', fontWeight: '600', fontSize: theme.typography.md, color: theme.colors.text}}>Đóng</Text>
        </TouchableOpacity>
      </View>
    </BottomSheet>
  );
};

export default PhoneHyperLink;
