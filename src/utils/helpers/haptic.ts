import {Platform} from 'react-native';
import ReactNativeHapticFeedback, {HapticFeedbackTypes, HapticOptions} from 'react-native-haptic-feedback';

const defaultOptions = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: false,
};

const triggerHapticFeedback = (type: HapticFeedbackTypes, options?: HapticOptions) => {
  if (Platform.OS !== 'web') {
    ReactNativeHapticFeedback.trigger(type, options ?? defaultOptions);
  }
};

export default triggerHapticFeedback;
