import {AsyncStorageStatic} from '@react-native-async-storage/async-storage/lib/typescript/types';
import isInZaloMiniApp from './isInZaloMiniApp';
import api from '~utils/helpers/zmp-sdk';
import RNAsyncStorage from '@react-native-async-storage/async-storage';

const isZaloMiniApp = isInZaloMiniApp();

const AsyncStorage: AsyncStorageStatic = {
  async clear(callback) {
    if (isZaloMiniApp) {
      try {
        await api.clearStorage({});
        if (callback) {
          callback(null);
        }
      } catch (error: any) {
        if (callback) {
          callback(error);
        }
      }

      return;
    }

    return await RNAsyncStorage.clear(callback);
  },
  async getItem(key, callback) {
    if (isZaloMiniApp) {
      try {
        const data = await api.getStorage({
          keys: [key],
        });
        let result = data[key];
        if ((data[key] as string)?.startsWith('"')) {
          result = JSON.parse(data[key]);
        }
        if (callback) {
          callback(null, result);
        }
        return result;
      } catch (error: any) {
        if (callback) {
          callback(error);
        }
      }
      return;
    }

    return await RNAsyncStorage.getItem(key, callback);
  },
  async multiGet(keys, callback) {
    if (isZaloMiniApp) {
      try {
        const data = await api.getStorage({
          keys: [...keys],
        });
        const result: any = keys.map(key => [key, (data[key] as string)?.startsWith('"') ? JSON.parse(data[key]) : data[key]]);

        if (callback) {
          callback(null, result);
        }

        return result;
      } catch (error: any) {
        if (callback) {
          callback([error]);
        }
      }
      return;
    }
    return await RNAsyncStorage.multiGet(keys, callback);
  },

  async multiRemove(keys, callback) {
    if (isZaloMiniApp) {
      try {
        await api.removeStorage({
          keys: [...keys],
        });

        if (callback) {
          callback();
        }
      } catch (error) {
        if (callback) {
          callback([new Error('Loi zalo sdk')]);
        }
      }

      return;
    }
    return await RNAsyncStorage.multiRemove(keys, callback);
  },
  async multiSet(keyValuePairs, callback) {
    if (isZaloMiniApp) {
      try {
        await api.setStorage({
          data: keyValuePairs.reduce((a, c) => ({...a, [c[0]]: c[1]}), {}),
        });
        return;
      } catch (error) {}
    }
    return await RNAsyncStorage.multiSet(keyValuePairs, callback);
  },

  async removeItem(key, callback) {
    if (isZaloMiniApp) {
      try {
        await api.removeStorage({
          keys: [key],
        });
        if (callback) {
          callback();
        }
      } catch (error) {
        if (callback) {
          callback(new Error('Loi Zalo SDK'));
        }
      }

      return;
    }
    return await RNAsyncStorage.removeItem(key, callback);
  },

  async setItem(key, value, callback) {
    if (isZaloMiniApp) {
      try {
        await api.setStorage({
          data: {
            [key]: value,
          },
        });

        if (callback) {
          callback();
        }

        return;
      } catch (error) {
        if (callback) {
          callback(new Error('Loi Zalo SDK'));
        }
        return;
      }
    }
    return await RNAsyncStorage.setItem(key, value, callback);
  },

  flushGetRequests() {
    return RNAsyncStorage.flushGetRequests();
  },

  getAllKeys(callback) {
    return RNAsyncStorage.getAllKeys(callback);
  },
};

export default AsyncStorage;
