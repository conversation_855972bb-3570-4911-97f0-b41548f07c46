import {Platform} from 'react-native';

export const formatPriceToString = (price: number, isShowCurrency = true, fallback?: any): string => {
  if (typeof price !== 'undefined' && typeof price === 'number') {
    if (Platform.OS === 'android') {
      return price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.') + (isShowCurrency ? '₫' : '');
    }
    return price.toLocaleString('vi-VN') + (isShowCurrency ? '₫' : '');
  }
  return typeof fallback === undefined ? '---' : fallback;
};

export const kFormatter = (num: number) => {
  return num > 999 ? `${parseFloat((num / 1000).toFixed(1)).toLocaleString('vi-VN')}k` : num?.toString();
};
