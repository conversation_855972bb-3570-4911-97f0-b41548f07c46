import {WebViewMessageTypes} from '~utils/types/common';

export const sendWebViewEvent = (type: keyof typeof WebViewMessageTypes, data: any) => {
  try {
    if (typeof window !== 'undefined' && (window as any).ReactNativeWebView) {
      const stringified = JSON.stringify({type, data});
      (window as any).ReactNativeWebView?.postMessage(stringified);
    }
  } catch (error) {}
};

export const isInReactNativeWebview = () => {
  return typeof window !== 'undefined' && !!(window as any).ReactNativeWebView;
};
