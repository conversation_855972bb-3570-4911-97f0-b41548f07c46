import theme from '~utils/config/themes/theme';
import Order, {MarketPlace, OrderEvent, ShippingLine, ShippingRate} from '~utils/types/order';
import {isWorkingHour} from './common';

export const shippingRateToShippingLine = (shippingRate: ShippingRate | undefined, customerPhone: string): ShippingLine | undefined => {
  if (!shippingRate) {
    return;
  }

  return {
    carrier_identifier: null,
    code: shippingRate.service_code,
    delivery_category: null,
    discounted_price: 0,
    id: '',
    phone: customerPhone,
    price: shippingRate.total_price,
    requested_fulfillment_service_id: null,
    source: shippingRate.service_name.split(' - ')[0],
    title: shippingRate.service_name,
    pay_by: shippingRate.__pay_by,
    __pay_amount: shippingRate.__pay_amount,
  };
};

export const getOrderStatusLabelColor = (statusLabel: string) => {
  let statusLabelLowerCase = statusLabel.toLowerCase();
  switch (true) {
    case statusLabelLowerCase === 'đã hủy':
      return 'red';

    case statusLabelLowerCase.startsWith('đã'):
      return theme.colors.gray50;

    case statusLabelLowerCase.startsWith('chờ') || statusLabelLowerCase.startsWith('đang'):
      return theme.colors.pending;

    default:
      return 'gray';
  }
};

export const getOrderBeforeFulfillmentEvents = (order?: Order): OrderEvent[] => {
  if (!order) {
    return [];
  }

  const isBusyTime = order.confirm_status === 'pending' && !isWorkingHour();

  let events: OrderEvent[] = [
    {title: 'Đặt hàng thành công', happened_at: order.created_at, message: ''},
    {
      title: 'Chờ xác nhận',
      happened_at: order.created_at,
      message: isBusyTime
        ? 'Thường xác nhận trong giờ làm việc 8h-17h, từ Thứ 2 đến Thứ 7.'
        : `Chờ nhà cung cấp xác nhận đơn hàng (thường xác nhận trong ${
            order.shop.statistic.confirm_time > 3600
              ? `${Math.floor(order.shop.statistic.confirm_time / 3600)} giờ`
              : order.shop.statistic.confirm_time > 60
              ? `${Math.floor(order.shop.statistic.confirm_time / 60)} phút`
              : `${order.shop.statistic.confirm_time} giây`
          })`,
    },
  ];

  if (order.confirm_status === 'confirmed') {
    events.push({
      title: 'Đã xác nhận',
      happened_at: order.confirmed_at,
    });

    if (order.payment_method !== 'cod') {
      events.push({
        title: 'Chờ bạn thanh toán',
        happened_at: order.confirmed_at,
        message: 'Nhà cung cấp sẽ đóng gói & gửi hàng sau khi bạn thanh toán',
      });
      if (order.financial_status === 'paid') {
        events.push({
          title: 'Thanh toán thành công',
          happened_at: order.paid_at,
        });
      }
    }

    if (order.financial_status === 'paid' || order.payment_method === 'cod') {
      events.push({
        title: 'Chờ xử lý giao hàng',
        happened_at: order.confirmed_at,
        message: 'Đang chờ nhà cung cấp đóng gói và gửi hàng',
      });
    }
  }

  if (order.confirm_status === 'cancelled') {
    events.push({
      title: 'Đã hủy',
      happened_at: order.confirmed_at,
      message: order.confirm_cancelled_reason,
    });
  }

  if (order.status === 'cancelled') {
    events.push({
      title: 'Đã hủy',
      happened_at: order.cancelled_at,
      message: order.cancel_reason,
    });
  }

  return events.reverse();
};

export const getMarketPlaceLabel = (marketPlace: MarketPlace) => {
  // 'shopee' | 'lazada' | 'sendo' | 'tiktokshop' | 'other'
  const supportedMarketPlaces = {
    shopee: 'Shopee',
    lazada: 'Lazada',
    sendo: 'Sendo',
    tiktokshop: 'Tiktok Shop',
    other: 'sàn khác',
  };

  return supportedMarketPlaces[marketPlace];
};

export const getMarketPlaceBrandColor = (marketPlace: MarketPlace) => {
  // EE4D2D
  const marketPlaceBrandColor = {
    shopee: '#EE4D2D',
    lazada: '#5f67fa',
    sendo: '#EE4D2D',
    tiktokshop: '#545454',
    other: '#000083',
  };

  return marketPlaceBrandColor[marketPlace] ?? '#000083';
};
