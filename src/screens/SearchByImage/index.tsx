import {Text} from '@rneui/themed';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {FlatList, StyleSheet, TouchableOpacity, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import {Asset} from 'react-native-image-picker';
import Ionicons from 'react-native-vector-icons/Ionicons';
import SearchByImageOptionDialog from '~components/shared/SearchByImageOptionDialog';
import SearchLayoutSkeleton from '~components/shared/SearchLayoutSkeleton';
import {VectorSearchObject, getDetectedObjectsVectorSearch, getProductsByVectorSearch} from '~utils/api/product';
import {useUpdateEffect} from '~hooks';
import {imagePickerAssetsToFile} from '~utils/helpers/image';
import {SearchProduct} from '~utils/types/product';
import {ListRenderItemInfo} from 'react-native';
import ProductCard from '~components/shared/ProductCard';
import SelectedImage from '~components/by-screens/SearchByImage/SelectedImage';
import SelectedObject from '~components/by-screens/SearchByImage/SelectedObject';

type ImageToSearch = {
  type: 'asset' | 'object';
  asset: Asset | VectorSearchObject;
};

const SearchByImage = () => {
  const [imagesToSearch, setImagesToSearch] = useState<ImageToSearch[]>([]);
  const [selectedPhotoIndex, setSelectedPhotoIndex] = useState<number>(-1);
  const [products, setProducts] = useState<SearchProduct[]>([]);

  const [isLoading, setLoading] = useState(false);
  const [searchImageOptionDialogOpen, setSearchImageOptionDialogOpen] = useState(false);
  const flatlistRef = useRef<FlatList>(null);
  const [isSeenAll, setIsSeenAll] = useState(false);

  useUpdateEffect(() => {
    if (selectedPhotoIndex !== -1 && imagesToSearch[selectedPhotoIndex]) {
      setLoading(true);
      flatlistRef.current?.scrollToOffset({
        offset: 0,
        animated: true,
      });

      const image = imagesToSearch[selectedPhotoIndex];

      if (image.type === 'asset') {
        const fileToSearch = imagePickerAssetsToFile(image.asset as Asset);
        getProductsByVectorSearch(fileToSearch, {})
          .then(res => {
            setIsSeenAll(false);
            setProducts(res.products || []);
          })
          .finally(() => {
            setLoading(false);
          });

        const isHasDetectedObject = imagesToSearch.some(image => image.type === 'object');
        if (!isHasDetectedObject) {
          getDetectedObjectsVectorSearch(fileToSearch).then(res => {
            setImagesToSearch(prev => [...prev, ...res.objects.map<ImageToSearch>(object => ({type: 'object', asset: object}))]);
          });
        }
      }

      if (image.type === 'object') {
        // getProductsByVectorSearch by img url
        getProductsByVectorSearch((image.asset as VectorSearchObject).img_url, {})
          .then(res => {
            setProducts(res.products || []);
          })
          .finally(() => {
            setLoading(false);
          });
      }
    }
  }, [selectedPhotoIndex]);

  useUpdateEffect(() => {
    if (imagesToSearch[imagesToSearch.length - 1]?.type === 'asset') {
      setSelectedPhotoIndex(imagesToSearch.length - 1);
    }
  }, [imagesToSearch.length]);

  useEffect(() => {
    if (!imagesToSearch.length) {
      setTimeout(() => {
        setSearchImageOptionDialogOpen(true);
      }, 200);
    }
  }, []);

  const renderItem = useCallback((item: ListRenderItemInfo<SearchProduct>) => {
    return (
      <View style={styles.productItem}>
        <ProductCard product={item.item} />
      </View>
    );
  }, []);

  const handleEndReached = () => {
    if (!isSeenAll && products.length > 0 && imagesToSearch[selectedPhotoIndex]) {
      getProductsByVectorSearch(imagePickerAssetsToFile(imagesToSearch[selectedPhotoIndex].asset as Asset), {
        offset: products.length,
      })
        .then(res => {
          if (!res.products?.length) {
            setIsSeenAll(true);
          } else {
            setProducts(prev => [...prev, ...res.products]);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };

  const onSelectPhotoComplete = (asset: Asset) => {
    // reset
    setSelectedPhotoIndex(-1);
    setImagesToSearch([]);

    setTimeout(() => {
      setImagesToSearch([{type: 'asset', asset}]);
    }, 100);
  };

  const onCroppedImage = (newAsset: Asset) => {
    // setSelectedPhotos(prev => [...prev, newAsset]);
    setImagesToSearch(prev => [...prev, {type: 'asset', asset: newAsset}]);
  };

  return (
    <View style={{flex: 1}}>
      <View style={{flexDirection: 'row', flexWrap: 'wrap', padding: theme.spacing.s, backgroundColor: theme.colors.white}}>
        {imagesToSearch.map((image, index) => {
          if (image.type === 'asset') {
            return (
              <SelectedImage
                isSelected={index === selectedPhotoIndex}
                onPress={() => setSelectedPhotoIndex(index)}
                index={index}
                key={`${(image.asset as Asset).fileName}${index}`}
                asset={image.asset as Asset}
                onImageCropped={onCroppedImage}
              />
            );
          }
          if (image.type === 'object') {
            return <SelectedObject index={index} object={image.asset as VectorSearchObject} onPress={() => setSelectedPhotoIndex(index)} isSelected={index === selectedPhotoIndex} />;
          }

          return null;
        })}

        <TouchableOpacity
          onPress={() => setSearchImageOptionDialogOpen(true)}
          style={{width: 48, height: 48, borderRadius: 10, borderWidth: 2, borderColor: theme.colors.gray50, borderStyle: 'dashed', justifyContent: 'center', alignItems: 'center'}}>
          <Ionicons name="add" color={theme.colors.gray60} size={30} />
        </TouchableOpacity>
      </View>

      <SearchByImageOptionDialog
        open={searchImageOptionDialogOpen}
        onClose={() => {
          setSearchImageOptionDialogOpen(false);
        }}
        onSelectPhotoComplete={onSelectPhotoComplete}
      />

      {isLoading && (
        <View style={{marginLeft: theme.spacing.s, marginTop: theme.spacing.s}}>
          <SearchLayoutSkeleton />
        </View>
      )}
      <FlatList
        renderItem={renderItem}
        ref={flatlistRef}
        style={{flex: 1}}
        data={products}
        numColumns={2}
        keyExtractor={(item, index) => item.id + `${index}`}
        onEndReached={handleEndReached}
        ListEmptyComponent={
          <>
            {!isLoading && !products.length && (
              <View style={{marginTop: theme.spacing.l, alignSelf: 'center', alignItems: 'center', paddingHorizontal: theme.spacing.l}}>
                <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight}}>Không tìm thấy sản phẩm nào.</Text>
                <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight, textAlign: 'center'}}>
                  Bạn có thể thử cắt chỉ lấy phần quan trọng của hình ảnh tìm kiếm chính xác hơn
                </Text>
              </View>
            )}
            {!imagesToSearch.length && (
              <View style={{alignSelf: 'center', marginTop: 40, alignItems: 'center', paddingHorizontal: theme.spacing.l * 2}}>
                <Ionicons name="camera-outline" color={theme.colors.gray30} size={120} />
                <Text style={{textAlign: 'center'}}>Hãy thêm hình ảnh để bắt đầu tìm kiếm sản phẩm.</Text>
              </View>
            )}
          </>
        }
        ListFooterComponent={<>{isSeenAll && <Text style={styles.endReachedIndicator}>Bạn đã xem hết!</Text>}</>}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  productItem: {
    flex: 1,
    backgroundColor: '#fff',
    marginHorizontal: 4,
    marginVertical: 4,
    borderRadius: 12,
  },
  sortItem_text: {
    fontSize: 14,
    flexShrink: 0,
    color: theme.colors.gray70,
  },
  sortItem_btn: {
    paddingHorizontal: 0,
    // borderRightWidth: 1,
    // borderRightColor: 'rgba(0,0,0,.05)',
    paddingVertical: 8,
    borderBottomColor: theme.colors.primary,
    borderRadius: 0,
  },
  sortItem: {
    flex: 1,
    borderRadius: 0,
  },
  endReachedIndicator: {
    textAlign: 'center',
    fontSize: theme.typography.base,
    marginTop: theme.spacing.s,
    color: theme.colors.textLight,
  },
});

export default SearchByImage;
