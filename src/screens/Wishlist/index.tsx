import React, {useCallback, useMemo, useRef, useState} from 'react';
import {FlatList, ListRenderItemInfo, Pressable, RefreshControl, StyleSheet, View} from 'react-native';
import ProductCard from '~components/shared/ProductCard';
import {useGetWishlistProducts} from '~utils/api/product';
import {SearchProduct} from '~utils/types/product';

import {useScrollToTop} from '@react-navigation/native';
import ListEmptyProduct from '~components/shared/ListEmptyProduct';
import theme from '~utils/config/themes/theme';
import {Text} from '@rneui/themed';
import {useGetFollowingShop} from '~utils/api/shop';
import FavoriteShopItem from '~components/by-screens/Wishlist/FavoriteShopItem';
import FavoriteShopBottomSheet from '~components/by-screens/Wishlist/FavoriteShopBottomSheet';
import useRefreshOnFocus from '~hooks/useRefreshOnFocus';
import FindProductTips from '~components/shared/FindProductTips';

const Wishlist = () => {
  const {data, isLoading, refetch, fetchNextPage, isFetchingNextPage} = useGetWishlistProducts({});
  const {data: shopFollowing, isLoading: isShopLoading, refetch: shopRefetch, fetchNextPage: fetchNextShopPage, isFetchingNextPage: isFetchingNextShopPage} = useGetFollowingShop({offset: 0});
  const [isRefreshing, setRefreshing] = useState(false);
  const flashListRef = useRef(null);
  const [tabSelected, setTabSelected] = useState<'product' | 'shop'>('product');

  useScrollToTop(flashListRef);
  useRefreshOnFocus(shopRefetch);
  useRefreshOnFocus(refetch);

  const dataFlat = useMemo(() => data?.pages.map(page => page.products).flat() ?? [], [data]);

  const shopFollowingFlat = useMemo(() => {
    return shopFollowing?.pages.flatMap(page => Object.keys(page.shops).map(shopId => page.shops[shopId])) ?? [];
  }, [shopFollowing]);

  const renderItem = useCallback(
    (item: ListRenderItemInfo<SearchProduct>) => {
      return <ProductCard product={item.item} />;
    },
    [dataFlat],
  );

  const renderShopItem = useCallback(
    (item: ListRenderItemInfo<any>) => {
      return <FavoriteShopItem shop={item.item} />;
    },
    [shopFollowingFlat],
  );

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  }, [refetch]);

  const handleChangeTab = useCallback((tab: 'shop' | 'product') => {
    setTabSelected(tab);
  }, []);

  const backgroundColorStyles = useMemo(() => {
    if (tabSelected === 'product') {
      return !isLoading && !dataFlat.length && {backgroundColor: theme.colors.white};
    } else {
      return !isShopLoading && !shopFollowingFlat.length && {backgroundColor: theme.colors.white};
    }
  }, [tabSelected, isLoading, isShopLoading, dataFlat, shopFollowingFlat]);

  const handleProductEndReached = useCallback(() => {
    if (!isFetchingNextPage) {
      fetchNextPage();
    }
  }, [isFetchingNextPage]);

  const handleShopEndReached = useCallback(() => {
    if (!isFetchingNextShopPage) {
      fetchNextShopPage();
    }
  }, [isFetchingNextShopPage]);

  return (
    <View style={[{flex: 1}, backgroundColorStyles]}>
      <View style={styles.modeContainer}>
        <Pressable onPress={() => handleChangeTab('product')} style={styles.tabPressable}>
          <View style={[styles.tab, tabSelected === 'product' && styles.tabSelected]}>
            <Text style={[styles.tabText, tabSelected === 'product' && styles.tabTextSelected]}>Sản phẩm</Text>
          </View>
        </Pressable>
        <Pressable onPress={() => handleChangeTab('shop')} style={styles.tabPressable}>
          <View style={[styles.tab, tabSelected === 'shop' && styles.tabSelected]}>
            <Text style={[styles.tabText, tabSelected === 'shop' && styles.tabTextSelected]}>Nhà cung cấp</Text>
          </View>
        </Pressable>
      </View>
      {tabSelected === 'product' && (
        <FlatList
          ListHeaderComponent={
            <>
              <View style={{marginHorizontal: theme.spacing.s}}>
                <FindProductTips />
              </View>
            </>
          }
          ref={flashListRef}
          refreshControl={<RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />}
          ListEmptyComponent={isLoading ? null : <ListEmptyProduct content="Hãy lưu vài sản phẩm để tiện theo dõi nhé!" />}
          data={dataFlat}
          renderItem={renderItem}
          numColumns={2}
          style={{
            paddingHorizontal: theme.spacing.s,
          }}
          contentContainerStyle={{
            paddingBottom: 20,
          }}
          onEndReached={handleProductEndReached}
        />
      )}
      {tabSelected === 'shop' && (
        <FlatList
          ref={flashListRef}
          refreshControl={<RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />}
          ListEmptyComponent={isLoading ? null : <ListEmptyProduct content="Bạn chưa theo dõi nhà cung cấp nào!" />}
          data={shopFollowingFlat}
          renderItem={renderShopItem}
          style={{
            paddingHorizontal: theme.spacing.s,
          }}
          contentContainerStyle={{
            paddingBottom: 20,
          }}
          onEndReached={handleShopEndReached}
        />
      )}

      <FavoriteShopBottomSheet />
    </View>
  );
};

const styles = StyleSheet.create({
  modeContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.s,
    backgroundColor: theme.colors.white,
    paddingHorizontal: theme.spacing.m,
    marginHorizontal: -10,
  },
  tabSelected: {
    backgroundColor: theme.colors.gray10,
  },
  tab: {
    padding: theme.spacing.xs,
    paddingHorizontal: theme.spacing.m,
    borderRadius: 100,
    marginRight: theme.spacing.s,
  },
  tabText: {
    fontSize: theme.typography.base,
  },
  tabTextSelected: {
    fontWeight: 'bold',
  },
  tabPressable: {
    paddingVertical: theme.spacing.s,
  },
});

export default Wishlist;
