import {Text} from '@rneui/themed';
import React from 'react';
import {ScrollView, StyleSheet, View} from 'react-native';
import Hyperlink from 'react-native-hyperlink';
import {useNavigation} from '~hooks';
import {useGetIncomeSummary} from '~utils/api/wallet';
import theme from '~utils/config/themes/theme';

const TaxTransactions = () => {
  const {data} = useGetIncomeSummary();
  const navigation = useNavigation();

  const handleFAQPress = () => {
    navigation.push('WebView', {
      url: 'https://go.thitruongsi.com/thuetncn',
    });
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.infoText}>
          Thuế TNCN tạm giữ sẽ được hoàn lại vào số dư
          <Text style={[styles.infoText, theme.globalStyles.fontBold]}> Tiền có thể rút </Text>
          trong tháng tiếp theo nếu tổng thu nhập tháng này ở TTS Dropship của bạn không hơn 2.000.000đ.
        </Text>
      </View>

      <View style={styles.content}>
        <Text>Thuế TNCN tạm giữ tháng này</Text>
        <Text style={styles.moneyText}>{data?.tax_deduction_pending}</Text>
      </View>
      <View style={styles.content}>
        <Hyperlink onPress={handleFAQPress} linkStyle={{color: theme.colors.primary}}>
          <Text>Nếu có bất kỳ thắc mắc, bạn có thể xem các câu hỏi thường gặp về thuế tại đây: https://go.thitruongsi.com/thuetncn</Text>
        </Hyperlink>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    backgroundColor: theme.colors.primaryLight,
  },
  infoText: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
  },
  content: {
    marginTop: theme.spacing.s,
    padding: 16,
    backgroundColor: theme.colors.white,
  },
  moneyText: {
    fontSize: theme.typography.lg1,
    color: theme.colors.primary,
    fontWeight: 'bold',
    marginTop: theme.spacing.s,
  },
  taxInfo: {
    padding: 16,
  },
});

export default TaxTransactions;
