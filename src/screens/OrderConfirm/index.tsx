import AsyncStorage from '@react-native-async-storage/async-storage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React, {useEffect, useMemo, useState} from 'react';
import {ActivityIndicator, ScrollView, View} from 'react-native';
import useCreateOrderStore from '~hooks/store/useCreateOrderStore';
import {ASYNC_STORAGE_KEYS} from '~utils/config';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import {useGetCart} from '~utils/api/cart';
import {CartItem} from '~utils/types/cart';
import groupBy from 'lodash/fp/groupBy';
import CustomerSection from '~components/by-screens/OrderConfirm/CustomerSection';
import OrderByShop from '~components/by-screens/OrderConfirm/OrderByShop';
import {Button} from '@rneui/base';
import PaymentSection from '~components/by-screens/OrderConfirm/PaymentSection';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Order from '~utils/types/order';
import {DeepPartial} from '~utils/types/common';
import {createOrder} from '~utils/api/order';
import * as Sentry from '@sentry/react-native';
import {FirebaseAnalyticsTypes} from '@react-native-firebase/analytics';
import {useQueryClient} from '@tanstack/react-query';
import googleAnalytics from '~utils/helpers/analytics';
import {useToast} from 'react-native-toast-notifications';
import OrderSummary from '~components/by-screens/OrderConfirm/OrderSummary';
import OrderSource from '~components/by-screens/OrderConfirm/OrderSource';
import {useGetShop} from '~utils/api/shop';

const OrderConfirmScreen: React.FC<NativeStackScreenProps<RootStackParamList, 'OrderConfirm'>> = ({navigation}) => {
  const {data: cart} = useGetCart();
  const queryClient = useQueryClient();
  const ordersInitialize = useCreateOrderStore(state => state.ordersInitialize);
  const orderByShopId = useCreateOrderStore(state => state.order_by_shop_id);

  const [isSubmitLoading, setSubmitLoading] = useState(false);
  const state = useCreateOrderStore();
  const insets = useSafeAreaInsets();
  const toast = useToast();
  const isFromOtherMarketPlace = useCreateOrderStore(state => state.is_from_other_marketplace);
  const sourceIdentifier = useCreateOrderStore(state => state.source_identifier);
  const {data: shop} = useGetShop(Object.keys(orderByShopId)?.[0], {
    enabled: !!Object.keys(orderByShopId)?.[0],
  });

  useEffect(() => {
    if (!cart) {
      return;
    }

    try {
      AsyncStorage.getItem(ASYNC_STORAGE_KEYS.DRAFT_ORDER).then(value => {
        if (value) {
          const cartItems: CartItem[] = JSON.parse(value);
          const orderGroupBy = groupBy('shop_id', cartItems);

          ordersInitialize({
            cart_token: cart.token,
            payment_method: 'cod',

            // customer: undefined,
            order_by_shop_id: Object.keys(orderGroupBy).reduce(
              (acc, cur) => ({
                ...acc,
                [cur]: {
                  line_items: orderGroupBy[cur],
                },
              }),
              {},
            ),
          });

          // analytics
          const beginCheckoutParams: FirebaseAnalyticsTypes.BeginCheckoutEventParameters = {
            items: cartItems.map(item => ({
              item_id: item.product_id,
              item_name: item.product_title,
              item_variant: item.variant_title,
              price: item.dropship_selling_price,
              quantity: item.quantity,
              item_category: item.product_type?.split(' > ')[0],
              item_category2: item.product_type?.split(' > ')[1],
            })),
          };

          googleAnalytics.logBeginCheckout(beginCheckoutParams);
        }
      });
    } catch (error) {}
  }, [cart]);

  const isSubmitDisabled = useMemo(() => {
    return (!isFromOtherMarketPlace && (!state.shipping_address?.id || !state.payment_method || state.shipping_lines_loading)) || (isFromOtherMarketPlace && !state.payment_method);
  }, [state, isFromOtherMarketPlace]);

  const handleSubmit = async () => {
    try {
      setSubmitLoading(true);

      const orders = Object.keys(state.order_by_shop_id).map<DeepPartial<Order>>(shopId => ({
        dropship: true,
        note: state.order_by_shop_id[shopId].note,
        cart_token: state.cart_token,
        ...(state.is_from_other_marketplace
          ? // ecommerce marketplace order
            {
              shipping: {
                method: 'marketplace',
                pay_by: 'buyer',
                price: 0,
                carrier: state.market_place,
              },
              customer: null,
              source_identifier: state.source_identifier,
            }
          : {
              // default order
              customer: {
                id: state.shipping_address?.customer_id,
              },
              shipping_address: state.shipping_address,
            }),
        line_items: state.order_by_shop_id[shopId].line_items?.map((item: CartItem) => ({
          quantity: item.quantity,
          variant_id: item.variant_id,
          product_id: item.product_id,
          dropship_selling_price: item.dropship_selling_price,
          dropship: true,
          properties: item.properties,
        })),
        shipping_lines: state.order_by_shop_id[shopId].shipping_lines as any,
        shop_id: shopId,
        payment_method: state.payment_method,
        discount_codes:
          state.order_by_shop_id[shopId].price_rules?.map(priceRule => ({
            price_rule: priceRule.id,
          })) ?? [],
        source_name: state.source_name,
      }));

      await Promise.all(orders.map(async order => createOrder(order)))
        .catch(error => {
          throw error;
        })
        .then(res => {
          // invalidate balance
          setTimeout(() => {
            queryClient.invalidateQueries(['escrow_transactions']);
          }, 500);

          // analytics
          const purchaseParams: FirebaseAnalyticsTypes.PurchaseEventParameters = {
            currency: 'VND',
            transaction_id: String(res[0]?.id),
            items: res
              .flatMap(order => order.line_items)
              .map(item => ({
                item_id: item.product_id,
                item_name: item.title,
                quantity: item.quantity,
                price: parseInt(item.price, 10),
                item_variant: item.variant_title,
                item_category: item.product_type.split(' > ')[0],
                item_category2: item.product_type.split(' >')[1],
              })),

            value: res.reduce((a, c) => a + parseInt(c.total_price, 10), 0),
            shipping: res.reduce((a, c) => a + c.shipping_lines?.[0]?.price ?? 0, 0),
          };

          googleAnalytics.logPurchase(purchaseParams);

          if ((res?.length as number) > 1) {
            navigation.replace('MainTabs', {screen: 'Order', params: {}});
          } else {
            if (res && res[0]) {
              navigation.replace('OrderDetail', {orderId: res[0].id, showSuccessAlert: true});
            }
          }
        });
    } catch (error: any) {
      console.log(error);
      Sentry.captureMessage('Tao don hang that bai');
      toast.show(error?.response?.data?.message ?? 'Có lỗi xảy ra, vui lòng bấm nhắn tin để được hỗ trợ', {type: 'danger'});
    }

    setSubmitLoading(false);
  };

  const isMissingShoppingRates = useMemo(() => {
    return Object.keys(orderByShopId).some(key => !orderByShopId[key]?.shipping_lines?.length);
  }, [orderByShopId]);

  return (
    <View style={{flex: 1, paddingBottom: insets.bottom}}>
      <ScrollView style={{flex: 1}}>
        {!state.is_initializing && (
          <>
            {shop?.dropship_marketplace && <OrderSource />}

            <CustomerSection />
            {Object.keys(state.order_by_shop_id).map(shopId => {
              let order = state.order_by_shop_id[shopId];
              return order ? <OrderByShop key={shopId} order={order} shopId={shopId} /> : null;
            })}
            <PaymentSection />
            <OrderSummary />
          </>
        )}

        {state.is_initializing && (
          <View style={{flexDirection: 'row', justifyContent: 'center', marginVertical: 40}}>
            <ActivityIndicator color={'#008060'} />
          </View>
        )}

        <View style={{height: 120}} />
      </ScrollView>

      <Button
        color={'#008060'}
        title="Tạo đơn hàng"
        onPress={handleSubmit}
        disabled={isSubmitDisabled || isSubmitLoading || (!isFromOtherMarketPlace && isMissingShoppingRates) || (isFromOtherMarketPlace && !sourceIdentifier)}
        loading={isSubmitLoading}
        buttonStyle={{borderRadius: 12}}
        containerStyle={{
          paddingHorizontal: 12,
        }}
      />
    </View>
  );
};

export default OrderConfirmScreen;
