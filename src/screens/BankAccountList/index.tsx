import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Button, Dialog} from '@rneui/themed';
import React, {useCallback, useEffect, useState} from 'react';
import {Text, View} from 'react-native';
import {useToast} from 'react-native-toast-notifications';
import BankAccountItem from '~components/by-screens/BankAccountList/BankAccountItem';
import {useGetMyBankAccounts, useRemoveBankAccountMutation} from '~utils/api/wallet';
import theme from '~utils/config/themes/theme';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import {MyBank} from '~utils/types/common';

const BankAccountList: React.FC<NativeStackScreenProps<RootStackParamList, 'BankAccountList'>> = ({navigation}) => {
  const {data, isLoading} = useGetMyBankAccounts();
  const [bankRemoveDialogOpen, setBankRemoveDialogOpen] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState<MyBank>();
  const {mutateAsync, isLoading: isRemoving} = useRemoveBankAccountMutation();
  const toast = useToast();

  useEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <Button type="clear" titleStyle={{color: theme.colors.blue, fontSize: theme.typography.md}} onPress={() => navigation.navigate('AddBankAccount')} buttonStyle={{paddingVertical: 0}}>
          Thêm
        </Button>
      ),
    });
  }, []);

  const toggleDialogOpen = useCallback(() => {
    setBankRemoveDialogOpen(open => !open);
  }, []);

  const handleRemoveRequest = useCallback((bankAccount: MyBank) => {
    setSelectedAccount(bankAccount);
    toggleDialogOpen();
  }, []);

  const handleRemoveBankAccount = useCallback(async () => {
    if (selectedAccount) {
      await mutateAsync({
        bankAccountId: selectedAccount.id,
      }).catch(err => {
        toast.show(err?.response?.data?.message ?? 'Có lỗi xảy ra, vui lòng thử lại sau', {placement: 'bottom', duration: 3000, type: 'danger'});
      });
    }

    toggleDialogOpen();
  }, [selectedAccount]);

  return (
    <View style={{paddingHorizontal: 12}}>
      {!isLoading && !data?.length && (
        <View style={{marginVertical: theme.spacing.l}}>
          <Text style={{textAlign: 'center', fontSize: theme.typography.md, color: theme.colors.textLight}}>Bạn chưa thêm tài khoản ngân hàng nào</Text>
        </View>
      )}
      <View style={{marginTop: theme.spacing.m}}>
        {data?.map(item => (
          <BankAccountItem item={item} key={item.id} onRemoveRequest={handleRemoveRequest} />
        ))}
      </View>

      <Dialog isVisible={bankRemoveDialogOpen} onBackdropPress={toggleDialogOpen}>
        <Dialog.Title title="Gỡ tài khoản ngân hàng" />
        <Text>
          Gỡ tài khoản ngân hàng ({selectedAccount?.bank.short_name} - {selectedAccount?.number}) ra khỏi TTS Dropship
        </Text>
        <Dialog.Actions>
          <Dialog.Button title="Trở về" onPress={toggleDialogOpen} />
          <Dialog.Button loading={isRemoving} color={theme.colors.red} titleStyle={{color: theme.colors.red}} title="Gỡ tài khoản" onPress={handleRemoveBankAccount} />
        </Dialog.Actions>
      </Dialog>
    </View>
  );
};

export default BankAccountList;
