import {Text} from '@rneui/themed';
import React, {useCallback, useState} from 'react';
import {Image, ScrollView, StyleSheet, View} from 'react-native';
// @ts-ignore
import GoldMedal from '~assets/medals/gold.png';
// @ts-ignore
import GoldMedalLocked from '~assets/medals/gold_locked.png';
// @ts-ignore
import PlatinumMedal from '~assets/medals/platinum.png';
// @ts-ignore
import PlatinumMedalLocked from '~assets/medals/platinum_locked.png';
// @ts-ignore
import Diamond from '~assets/medals/diamond.png';
// @ts-ignore
import DiamondLocked from '~assets/medals/diamond_locked.png';
import theme from '~utils/config/themes/theme';
import * as Progress from 'react-native-progress';
import {NavigationState, SceneRendererProps, TabBar, TabView} from 'react-native-tab-view';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

type State = NavigationState<{
  key: string;
  title: string;
}>;

const MemberRanking = () => {
  const [index, setIndex] = useState(0);
  const insets = useSafeAreaInsets();
  const [routes, setRoutes] = useState([
    {
      key: 'gold',
      source: GoldMedal,
      isLocked: false,
      title: 'Vàng',
    },
    {key: 'platinum', source: PlatinumMedalLocked, title: 'Bạch Kim', isLocked: true},
    {key: 'diamond', source: DiamondLocked, title: 'Kim Cương', isLocked: true},
  ]);

  const renderTabBar = (tabbarProps: SceneRendererProps & {navigationState: State}) => (
    <TabBar
      {...tabbarProps}
      indicatorStyle={styles.indicator}
      style={styles.tabbar}
      tabStyle={styles.tab}
      renderLabel={(scene: any) => (
        <View style={{alignItems: 'center', backgroundColor: theme.colors.white, flex: 1}}>
          <Image source={scene.route.source} style={{width: 60, height: 60, opacity: scene.route.isLocked ? 0.5 : 1}} />
          <Text style={{fontSize: theme.typography.base, marginTop: 4}}>{scene.route.title}</Text>
        </View>
      )}
    />
  );

  const renderScene = useCallback(({route}: any) => {
    switch (route.key) {
      case 'gold':
        return <GoldMemberDetail />;

      case 'platinum':
        return <PlatinumMemberDetail />;

      case 'diamond':
        return <DiamondMemberDetail />;

      default:
        return null;
    }
  }, []);

  const handleIndexChange = useCallback((newIndex: number) => {
    setIndex(newIndex);
  }, []);

  return (
    <ScrollView style={{paddingHorizontal: 12, paddingBottom: insets.bottom}} contentContainerStyle={{flex: 1}}>
      <View style={{flexDirection: 'row', justifyContent: 'space-between', marginTop: theme.spacing.l, alignItems: 'center', backgroundColor: '#fff', borderRadius: 12, padding: 12}}>
        <View style={{alignItems: 'center'}}>
          <Image source={GoldMedal} style={{width: 80, height: 80}} />
          <Text style={{marginBottom: 4}}>Vàng</Text>
          <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight}}>Hạng của bạn</Text>
        </View>
        <Progress.Bar progress={0.8} color={theme.colors.primary} unfilledColor={'#c8faed'} borderWidth={0} height={8} style={{width: 100, flexShrink: 1}} />
        <View style={{alignItems: 'center'}}>
          <Image source={PlatinumMedal} style={{width: 80, height: 80}} />
          <Text style={{marginBottom: 4}}>Bạch Kim</Text>
          <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight}}>Hạng tiếp theo</Text>
        </View>
      </View>

      <TabView
        style={{marginTop: 20, borderRadius: 12}}
        renderTabBar={renderTabBar}
        navigationState={{index, routes}}
        renderScene={renderScene}
        onIndexChange={handleIndexChange}
        sceneContainerStyle={{backgroundColor: '#fff', padding: 12}}
      />
    </ScrollView>
  );
};

const GoldMemberDetail = () => (
  <View style={{backgroundColor: '#fff'}}>
    <Text style={{fontWeight: 'bold'}}>Quyền lợi của thành viên Vàng</Text>
    <Text style={{marginTop: 8}}>
      - Thưởng thêm <Text style={{fontWeight: 'bold'}}>5% hoa hồng</Text> nhận được cho tất cả đơn hàng
    </Text>
    <Text style={{marginTop: 8}}>- Tặng ngay 1 mã freeship</Text>

    <Text style={{fontWeight: 'bold', marginTop: 40}}>Cách đạt được thành viên Vàng</Text>
    <Text style={{marginTop: 8}}>- 5 đơn hàng giao thành công</Text>
    <View style={{marginVertical: 8, flexDirection: 'row', alignItems: 'center'}}>
      <Progress.Bar progress={0} color={theme.colors.primary} unfilledColor={'#c8faed'} borderWidth={0} height={8} />
      <Text style={{marginLeft: 8}}>0/5</Text>
    </View>

    <Text style={{marginTop: 8}}>- 1.000.000đ doanh số</Text>
    <View style={{marginVertical: 8, flexDirection: 'row', alignItems: 'center'}}>
      <Progress.Bar progress={0} color={theme.colors.primary} unfilledColor={'#c8faed'} borderWidth={0} height={8} />
      <Text style={{marginLeft: 8}}>0/1.000.000đ</Text>
    </View>
  </View>
);

const PlatinumMemberDetail = () => (
  <View style={{backgroundColor: '#fff'}}>
    <Text style={{fontWeight: 'bold'}}>Quyền lợi của thành viên Bạch Kim</Text>
    <Text style={{marginTop: 8}}>
      - Thưởng thêm <Text style={{fontWeight: 'bold'}}>10% hoa hồng</Text> nhận được cho tất cả đơn hàng
    </Text>
    <Text style={{marginTop: 8}}>- Tặng ngay 2 mã freeship</Text>

    <Text style={{fontWeight: 'bold', marginTop: 40}}>Cách đạt được thành viên Bạch Kim</Text>
    <Text style={{marginTop: 8}}>- 15 đơn hàng giao thành công</Text>
    <View style={{marginVertical: 8, flexDirection: 'row', alignItems: 'center'}}>
      <Progress.Bar progress={0} color={theme.colors.primary} unfilledColor={'#c8faed'} borderWidth={0} height={8} />
      <Text style={{marginLeft: 8}}>0/15</Text>
    </View>

    <Text style={{marginTop: 8}}>- 5.000.000đ doanh số</Text>
    <View style={{marginVertical: 8, flexDirection: 'row', alignItems: 'center'}}>
      <Progress.Bar progress={0} color={theme.colors.primary} unfilledColor={'#c8faed'} borderWidth={0} height={8} />
      <Text style={{marginLeft: 8}}>0/5.000.000đ</Text>
    </View>
  </View>
);

const DiamondMemberDetail = () => (
  <View style={{backgroundColor: '#fff'}}>
    <Text style={{fontWeight: 'bold'}}>Quyền lợi của thành viên Kim Cương</Text>
    <Text style={{marginTop: 8}}>
      - Thưởng thêm <Text style={{fontWeight: 'bold'}}>15% hoa hồng</Text> nhận được cho tất cả đơn hàng
    </Text>
    <Text style={{marginTop: 8}}>- Tặng ngay 3 mã freeship</Text>

    <Text style={{fontWeight: 'bold', marginTop: 40}}>Cách đạt được thành viên Kim Cương</Text>
    <Text style={{marginTop: 8}}>- 30 đơn hàng giao thành công</Text>
    <View style={{marginVertical: 8, flexDirection: 'row', alignItems: 'center'}}>
      <Progress.Bar progress={0} color={theme.colors.primary} unfilledColor={'#c8faed'} borderWidth={0} height={8} />
      <Text style={{marginLeft: 8}}>0/30</Text>
    </View>

    <Text style={{marginTop: 8}}>- 10.000.000đ doanh số</Text>
    <View style={{marginVertical: 8, flexDirection: 'row', alignItems: 'center'}}>
      <Progress.Bar progress={0} color={theme.colors.primary} unfilledColor={'#c8faed'} borderWidth={0} height={8} />
      <Text style={{marginLeft: 8}}>0/10.000.000đ</Text>
    </View>
  </View>
);

const styles = StyleSheet.create({
  locationItem: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,.05)',
  },
  locationItem_text: {
    fontSize: 16,
  },
  tabbar: {
    backgroundColor: '#fff',
    borderBottomWidth: 2,
    borderBottomColor: 'rgba(0,0,0,.05)',
  },
  tab: {
    // width: 'auto',f
  },
  indicator: {
    backgroundColor: '#008060',
  },
  label: {
    fontSize: 16,
    fontWeight: '400',
    color: '#333',
    textTransform: 'none',
  },
});

export default MemberRanking;
