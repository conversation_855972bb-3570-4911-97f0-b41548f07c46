import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React, {useCallback} from 'react';
import {Platform, Text, View} from 'react-native';
import IdentitySubmited from '~components/by-screens/IdentityVerification/IdentitySubmited';
import Step0Intro from '~components/by-screens/IdentityVerification/Step0Intro';
import Step1UploadInfo from '~components/by-screens/IdentityVerification/Step1UploadInfo';
import Step2Confirm from '~components/by-screens/IdentityVerification/Step2Confirm';
import theme from '~utils/config/themes/theme';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import {IdCardUploadResponse} from '~utils/types/user';
import inInTTSDropshipWebview from '~utils/helpers/isInTTSDropshipWebview';
import {sendWebViewEvent} from '~utils/helpers/webview';
import {WebViewMessageTypes} from '~utils/types/common';
import {useAuthActions} from '~hooks';
import {env} from '~utils/config';

const IdentityVerification: React.FC<NativeStackScreenProps<RootStackParamList, 'IdentityVerification'>> = ({route}) => {
  const [step, setStep] = React.useState(0);
  const [step1Data, setStep1Data] = React.useState<IdCardUploadResponse[]>([]);
  const [step2Data, setStep2Data] = React.useState<any>(null);
  const {authData} = useAuthActions();

  const handleStep0Done = useCallback(() => {
    if (inInTTSDropshipWebview() && parseInt((window as any).TTS_DROPSHIP_VERSION || '0') >= 3) {
      const nextURL = Platform.OS === 'web' ? env.WEBVIEW_URL : 'tts.com.dsbuyer://';
      sendWebViewEvent(WebViewMessageTypes.OpenURL, `${env.OAUTH_URI}/kyc/verify?access_token=${authData.access_token}&next=${encodeURIComponent(nextURL)}`);
    } else {
      setStep(1);
    }
    //
  }, []);

  const handleStep1Done = useCallback((step1Data: IdCardUploadResponse[]) => {
    setStep(2);
    setStep1Data(step1Data);
  }, []);

  const handleStep2Done = useCallback((step2Data: any) => {
    setStep2Data(step2Data);
    setStep(3);
  }, []);

  return (
    <View style={{flex: 1}}>
      {route.params?.msg && (
        <View style={{padding: 16, backgroundColor: '#fff3cd', borderRadius: 8}}>
          <Text style={{color: theme.colors.text, fontSize: theme.typography.base}}>{route.params.msg}</Text>
        </View>
      )}
      {step === 0 ? <Step0Intro onDone={handleStep0Done} /> : null}
      {step === 1 ? <Step1UploadInfo onDone={handleStep1Done} /> : null}
      {step === 2 ? <Step2Confirm step1Data={step1Data} onDone={handleStep2Done} /> : null}
      {step === 3 ? <IdentitySubmited step2Data={step2Data} /> : null}
    </View>
  );
};

export default IdentityVerification;
