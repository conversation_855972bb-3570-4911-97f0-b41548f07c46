import React, {useEffect} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import {env} from '~utils/config';

const IdentityVerification: React.FC<NativeStackScreenProps<RootStackParamList, 'IdentityVerification'>> = ({route, navigation}) => {
  useEffect(() => {
    navigation.replace('WebView', {url: `${env.WEBVIEW_URL}/account/id-verification${route.params?.msg ? `?msg=${route.params?.msg}` : ''}`, includeAuth: true});
  }, []);
  return null;
};

export default IdentityVerification;
