import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Button, Input} from '@rneui/themed';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {Platform, ScrollView, StyleSheet} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useToast} from 'react-native-toast-notifications';
import BankSelectModal from '~components/shared/BankSelectModal';
import Select from '~components/shared/Form/Select';
import {useGetCurrentUser, useGetIdentityVerificationStatus} from '~utils/api/auth';
import {useAddNewBankAccountMutation, useGetBanks} from '~utils/api/wallet';
import theme from '~utils/config/themes/theme';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import {MyBank} from '~utils/types/common';
import {bankAccountValidate} from '~utils/validate/common';

const AddBankAccount: React.FC<NativeStackScreenProps<RootStackParamList, 'AddBankAccount'>> = ({navigation}) => {
  const [isSubmitLoading, setSubmitLoading] = useState(false);
  const mutation = useAddNewBankAccountMutation();
  const [bankAccount, setBankAccount] = useState<Partial<MyBank> & {bankName?: string}>({});
  const {data} = useGetBanks();
  const [errors, setErrors] = useState<Partial<{[key in keyof MyBank]: string}>>({});
  const {data: currentUser} = useGetCurrentUser();
  const {data: identityVerificationStatus} = useGetIdentityVerificationStatus();

  const toast = useToast();

  const isIdentityVerificationRequired = useMemo(() => {
    return currentUser?.idcard_verify && currentUser?.idcard_verify !== 'verified' && !identityVerificationStatus?.rollback;
  }, [currentUser?.idcard_verify, identityVerificationStatus?.rollback]);

  useEffect(() => {
    if (isIdentityVerificationRequired) {
      navigation.replace('IdentityVerification', {msg: 'Vui lòng định danh tài khoản trước khi thêm tài khoản ngân hàng'});
    }
  }, [isIdentityVerificationRequired]);

  // native only
  const [bankSelectModalOpen, setBankSelectModalOpen] = useState(false);

  const bankOptions = useMemo(() => {
    return data?.map(bank => `${bank.short_name} - ${bank.name} - ${bank.id}`) ?? [];
  }, [data]);

  const onChange = useCallback((field: keyof (Partial<MyBank> & {bankName: string}), value: any) => {
    setBankAccount(prev => ({...prev, [field]: value}));
  }, []);

  const handleBankSelectModalClose = useCallback(() => {
    setBankSelectModalOpen(false);
  }, []);

  const handleSubmit = useCallback(async () => {
    try {
      setErrors({});
      let isValid = true;
      setSubmitLoading(true);
      await bankAccountValidate.validate(bankAccount, {abortEarly: false}).catch(err => {
        isValid = false;
        setErrors(err.inner.reduce((a: any, c: any) => ({...a, [c.path]: c.message}), {}));
        setSubmitLoading(false);
      });
      if (!isValid) {
        return;
      }
      await mutation
        .mutateAsync({
          bankAccount: bankAccount as MyBank,
        })
        .catch(err => {
          throw err;
        })
        .then(() => {
          toast.show('Đã thêm tài khoản', {duration: 3000});
          navigation.replace('BankAccountList');
        });
    } catch (error: any) {
      toast.show(error?.response?.data?.message ?? 'Có lỗi xảy ra', {type: 'danger', duration: 3000});
    }
    setSubmitLoading(false);
  }, [bankAccount]);

  return (
    <SafeAreaView style={{flex: 1}} edges={['left', 'right', 'bottom']}>
      <ScrollView style={{flex: 1, backgroundColor: '#fff', paddingHorizontal: 12, paddingVertical: 20}}>
        <Input
          label="Tên chủ tài khoản"
          placeholder="Nhập tên chủ tài khoản"
          containerStyle={styles.formContainer}
          inputContainerStyle={styles.inputContainer}
          inputStyle={styles.input}
          labelStyle={styles.label}
          value={bankAccount.name}
          onChangeText={text => {
            onChange('name', text);
          }}
          errorMessage={errors.name}
        />

        <Input
          label="Số tài khoản"
          placeholder="Nhập số tài khoản"
          containerStyle={styles.formContainer}
          inputContainerStyle={styles.inputContainer}
          inputStyle={styles.input}
          labelStyle={styles.label}
          value={bankAccount.number}
          onChangeText={text => {
            onChange('number', text);
          }}
          errorMessage={errors.number}
        />
        <Select
          options={bankOptions}
          label="Ngân hàng"
          onChange={val => {
            onChange('bankName', val);
            onChange('bank_id', parseInt(val.toString().split(' - ')[val.toString().split(' - ').length - 1], 10));
          }}
          value={bankAccount.bankName}
          errorMessage={errors.bank_id}
          onPress={() => setBankSelectModalOpen(true)}
        />
      </ScrollView>
      <Button size="sm" onPress={handleSubmit} loading={isSubmitLoading} containerStyle={{paddingHorizontal: 12}} buttonStyle={{borderRadius: 12}}>
        Thêm tài khoản
      </Button>

      {Platform.OS !== 'web' && (
        <BankSelectModal
          onClose={handleBankSelectModalClose}
          open={bankSelectModalOpen}
          onChange={bank => {
            onChange('bankName', `${bank.code} - ${bank.name}`);
            onChange('bank_id', bank.id);
          }}
          selectedId={bankAccount.bank_id}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 8,
    paddingVertical: 20,
    backgroundColor: '#fff',
    position: 'relative',
    flex: 1,
  },
  inputContainer: {
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,.3)',
    borderRadius: 8,
    marginTop: 8,
  },
  formContainer: {
    marginBottom: theme.spacing.s,
  },
  input: {
    padding: 12,
    fontSize: 18,
  },
  label: {
    color: theme.colors.textLight,
    fontWeight: '500',
    fontSize: 16,
  },
});
export default AddBankAccount;
