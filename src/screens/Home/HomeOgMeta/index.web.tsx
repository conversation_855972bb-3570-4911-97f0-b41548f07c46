import React, {useMemo} from 'react';
import {Helmet} from 'react-helmet';
import {env} from '~utils/config';

const HomeOgMeta = () => {
  const ogThumb = useMemo(() => {
    return 'https://files.thitruongsi.com/assets/dropship/dropship-og-image.png';
  }, []);
  return (
    <Helmet>
      <meta charSet="UTF-8" />
      <meta name="keywords" content="dropship, bán hàng online, bán hàng ctv" />
      <meta
        name="description"
        content="Làm cộng tác viên bán hàng online chưa bao giờ dễ đến thế. Trở thành cộng tác viên cho các nguồn hàng lớn hàng đầu Việt Nam, lợi nhuận tốt, đa dạng sản phẩm."
      />
      <meta property="og:title" content="TTS Dropship - Làm CTV Bán Hàng Online không cần vốn" />
      <meta property="og:description" content="Ứng dụng bán hàng cộng tác viên, dropship có lợi nhuận cao nhất. Trở thành cộng tác viên cho các nguồn hàng lớn hàng đầu Việt Nam." />
      <meta property="og:site_name" content="Thị Trường Sỉ" />
      <meta property="og:locale" content="vi-VN" />
      <meta property="og:type" content="website" />
      <meta property="og:url" content={env.WEBVIEW_URL} />
      <link rel="canonical" href={env.WEBVIEW_URL} />
      <meta property="og:image" content={ogThumb} />
    </Helmet>
  );
};

export default HomeOgMeta;
