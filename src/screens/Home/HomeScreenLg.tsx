import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {ActivityIndicator, Animated, FlatList, ListRenderItemInfo, Pressable, RefreshControl, StyleSheet, View} from 'react-native';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
// @ts-ignore
import Ionicons from 'react-native-vector-icons/dist/Ionicons';
import Categories from '~components/by-screens/Home/Categories';
import {Text} from '@rneui/themed';
import {useGetSmartCollection} from '~utils/api/product';
import {SearchProduct} from '~utils/types/product';
import ProductCard from '~components/shared/ProductCard';
import {useAuthActions} from '~hooks';
import {CompositeScreenProps, useScrollToTop} from '@react-navigation/native';
import NotificationIcon from '~components/shared/NotificationIcon';
import {BottomTabScreenProps} from '@react-navigation/bottom-tabs';
import {MainTabsScreenList} from '~screens/MainTabs';
import HomeCollections from '~components/by-screens/Home/HomeCollections';
import theme from '~utils/config/themes/theme';
import {useExtendContainerWidth} from '~components/shared/web/WebContainer';
import IntroductionLg from '~components/by-screens/Home/Introduction/IntroductionLg';
import HeaderSearchLg from '~components/shared/web/WebHeader/HeaderSearchLg';
import {Helmet} from 'react-helmet';
import {env} from '~utils/config';
import HomeScreenLogo from './HomeScreenLogo';

export type HomeScreenProps = CompositeScreenProps<BottomTabScreenProps<MainTabsScreenList, 'Home'>, NativeStackScreenProps<RootStackParamList>>;

const HomeScreen: React.FC<HomeScreenProps> = ({navigation, route}) => {
  useExtendContainerWidth('lg');
  const {isLoggedIn, withAuth} = useAuthActions();

  const flatListRef = useRef<FlatList>(null);
  const [isRefreshing, setRefreshing] = useState(false);
  const [infiniteListCollectionId, setInfiniteListCollectionId] = useState('');
  const {data, fetchNextPage, isFetchingNextPage, refetch, hasNextPage} = useGetSmartCollection(infiniteListCollectionId, {});

  useScrollToTop(flatListRef);

  const isShowOnboard = useMemo(() => {
    return route.params?.onboard ?? !isLoggedIn;
  }, [isLoggedIn]);

  useEffect(() => {
    navigation.setOptions({
      headerTitleAlign: 'left',
      headerTitle: () => <HomeScreenLogo />,
      headerTitleStyle: {color: !isShowOnboard ? theme.colors.text : theme.colors.text},
      headerShadowVisible: true,
      headerRight: props => (
        <View style={styles.headerRightContainer}>
          <HeaderSearchLg />
          <NotificationIcon color={props.tintColor} />
          <Ionicons name="cart-outline" size={28} color={props.tintColor} style={{marginLeft: 12}} onPress={() => withAuth(() => navigation.navigate('Cart'))} />
        </View>
      ),
    });
  }, [isShowOnboard]);

  const dataFlat = useMemo(() => data?.pages.flatMap(page => page.products) ?? [], [data]);

  const renderItem = useCallback((item: ListRenderItemInfo<SearchProduct>) => {
    return <ProductCard product={item.item} />;
  }, []);

  const numColumns = useMemo(() => {
    return 4;
  }, []);

  const handleReachedEnd = () => {
    if (!isFetchingNextPage && hasNextPage) {
      fetchNextPage();
    }
  };

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  }, [refetch]);

  const handleReceiveInfiniteProductsCollectionId = useCallback((collectionId: string) => {
    setInfiniteListCollectionId(collectionId);
  }, []);

  const handleInfiniteListNavigate = useCallback(() => {
    navigation.navigate('CollectionDetail', {
      collectionId: infiniteListCollectionId,
    });
  }, [infiniteListCollectionId]);

  const ogThumb = useMemo(() => {
    return 'https://files.thitruongsi.com/assets/dropship/dropship-og-image.png';
  }, []);

  return (
    <>
      <Helmet>
        <meta charSet="UTF-8" />
        <meta name="keywords" content="dropship, bán hàng online, bán hàng ctv" />
        <meta
          name="description"
          content="Làm cộng tác viên bán hàng online chưa bao giờ dễ đến thế. Trở thành cộng tác viên cho các nguồn hàng lớn hàng đầu Việt Nam, lợi nhuận tốt, đa dạng sản phẩm."
        />
        <meta property="og:title" content="TTS Dropship - Làm CTV Bán Hàng Online không cần vốn" />
        <meta property="og:description" content="Ứng dụng bán hàng cộng tác viên, dropship. Trở thành cộng tác viên cho các nguồn hàng lớn hàng đầu Việt Nam." />
        <meta property="og:site_name" content="Thị Trường Sỉ" />
        <meta property="og:locale" content="vi-VN" />
        <meta property="og:type" content="website" />
        <meta property="og:url" content={env.WEBVIEW_URL} />
        <meta property="og:image" content={ogThumb} />
      </Helmet>

      <View style={{flex: 1, paddingHorizontal: 0, backgroundColor: theme.colors.white}}>
        <Animated.FlatList
          showsVerticalScrollIndicator={false}
          ref={flatListRef}
          refreshControl={<RefreshControl onRefresh={handleRefresh} refreshing={isRefreshing} />}
          ListHeaderComponent={
            <>
              {isShowOnboard && (
                <>
                  <IntroductionLg />
                  {/* <FourStepsOfMoney onCTAPress={onCTAPress} /> */}
                </>
              )}
              {/* <PrimaryBanner /> */}
              {/* <SearchInput /> */}
              <Categories />
              <HomeCollections onReceiveInfiniteProductsCollectionId={handleReceiveInfiniteProductsCollectionId} />
              <Pressable style={[theme.globalStyles.flexRow, {paddingBottom: theme.spacing.m, paddingHorizontal: theme.spacing.m, marginTop: theme.spacing.xl}]} onPress={handleInfiniteListNavigate}>
                <Text style={{fontWeight: '500', fontSize: theme.typography.lg}}>Sản phẩm mới</Text>
                <Ionicons name="arrow-forward" size={22} color={theme.colors.text} style={{marginLeft: 'auto', paddingRight: 20}} />
              </Pressable>
            </>
          }
          numColumns={numColumns}
          data={dataFlat}
          renderItem={renderItem}
          style={{flex: 1, width: '100%'}}
          keyExtractor={item => item.id}
          scrollEventThrottle={16}
          // onScroll={handleScroll}
          onEndReached={handleReachedEnd}
          columnWrapperStyle={{paddingHorizontal: theme.spacing.m - 8}}
          ListFooterComponent={
            isFetchingNextPage ? (
              <View style={{flexDirection: 'row', justifyContent: 'center', marginBottom: 40}}>
                <ActivityIndicator color={theme.colors.textLight} />
                <Text style={{marginLeft: 8, color: theme.colors.textLight, fontSize: theme.typography.sm}}>Đang tải thêm kết quả</Text>
              </View>
            ) : null
          }
        />
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  headerRightContainer: {
    paddingRight: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default HomeScreen;
