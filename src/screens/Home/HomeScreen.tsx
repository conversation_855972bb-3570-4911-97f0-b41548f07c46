import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {Animated, FlatList, ListRenderItemInfo, NativeScrollEvent, NativeSyntheticEvent, Pressable, RefreshControl, StyleSheet, View} from 'react-native';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
// @ts-ignore
import Ionicons from 'react-native-vector-icons/dist/Ionicons';
import Categories from '~components/by-screens/Home/Categories';
import {Text} from '@rneui/themed';
import {useGetSmartCollection} from '~utils/api/product';
import {SearchProduct} from '~utils/types/product';
import ProductCard from '~components/shared/ProductCard';
import Introduction from '~components/by-screens/Home/Introduction';
import {useAuthActions} from '~hooks';
import FourStepsOfMoney from '~components/by-screens/Home/FourStepsOfMoney';
import {CompositeScreenProps, useScrollToTop} from '@react-navigation/native';
import NotificationIcon from '~components/shared/NotificationIcon';
import {BottomTabScreenProps} from '@react-navigation/bottom-tabs';
import {MainTabsScreenList} from '~screens/MainTabs';
import PrimaryBanner from '~components/by-screens/Home/PrimaryBanner';
import HomeCollections, {HomeCollectionsRef} from '~components/by-screens/Home/HomeCollections';
import theme from '~utils/config/themes/theme';
import ee, {EventNames} from '~utils/events';
import {ScreenWidth} from '@rneui/base';
import isInZaloMiniApp from '~utils/helpers/isInZaloMiniApp';
import {AuthStorageType} from '~utils/types/common';
import InAppBrowser from 'react-native-inappbrowser-reborn';
import HomeOgMeta from './HomeOgMeta';
import FloatingBanner from '~components/shared/FloatingBanner';
import NewUserTutorials from '~components/by-screens/Home/NewUserTutorials';
import HideIfHasOrderCompleted from '~components/shared/HideIfHasOrderCompleted';

export type HomeScreenProps = CompositeScreenProps<BottomTabScreenProps<MainTabsScreenList, 'Home'>, NativeStackScreenProps<RootStackParamList>>;

const HomeScreen: React.FC<HomeScreenProps> = ({navigation, route}) => {
  const headerChanged = useRef<Boolean>(false);
  const homeCollectionsRef = useRef<HomeCollectionsRef>(null);
  const yOffset = useRef(new Animated.Value(0)).current;
  const {isLoggedIn, withAuth, login} = useAuthActions();
  const headerOpacity = yOffset.interpolate({
    inputRange: [0, 200],
    outputRange: ['rgb(0, 128, 96)', 'rgb(255, 255, 255)'],
  });
  const flatListRef = useRef<FlatList>(null);
  const [isRefreshing, setRefreshing] = useState(false);
  const [infiniteListCollectionId, setInfiniteListCollectionId] = useState('');
  const {data, fetchNextPage, isFetchingNextPage, refetch, hasNextPage} = useGetSmartCollection(infiniteListCollectionId, {});
  const isZaloMiniApp = useMemo(() => isInZaloMiniApp(), []);

  useEffect(() => {
    if (!isLoggedIn) {
      if (route.params?.access_token || route.params?.refresh_token || route.params?.oauth_key) {
        handleLoginCallback();
      }
    }
  }, [route.params, isLoggedIn]);

  useScrollToTop(flatListRef);

  const isShowOnboard = useMemo(() => {
    return route.params?.onboard ?? !isLoggedIn;
  }, [isLoggedIn]);

  useEffect(() => {
    navigation.setOptions({
      headerTitleAlign: 'left',
      headerTitle: 'TTS Dropship',
      headerTitleStyle: {color: !isShowOnboard ? theme.colors.primary : theme.colors.white},
      headerTintColor: !isShowOnboard ? theme.colors.text : theme.colors.white,
      headerRight: props => (
        <View style={styles.headerRightContainer}>
          <Ionicons
            name="search-outline"
            size={28}
            color={props.tintColor}
            accessibilityLabel="Tìm kiếm sản phẩm"
            style={{paddingHorizontal: theme.spacing.s}}
            onPress={() => navigation.navigate('Search')}
          />
          <NotificationIcon color={props.tintColor} />
          <Ionicons
            name="cart-outline"
            size={28}
            color={props.tintColor}
            style={{paddingHorizontal: theme.spacing.s, paddingRight: 0}}
            onPress={() => withAuth(() => navigation.navigate('Cart'))}
            accessibilityLabel="Đến trang giỏ hàng"
          />
        </View>
      ),
    });
  }, [isShowOnboard]);

  useEffect(() => {
    if (isShowOnboard) {
      navigation.setOptions({
        // @ts-ignore
        headerStyle: {
          backgroundColor: headerOpacity,
        },
      });
    } else {
      navigation.setOptions({
        // @ts-ignore
        headerStyle: {
          backgroundColor: theme.colors.white,
        },
      });
    }
  }, [headerOpacity, navigation, isShowOnboard]);

  const dataFlat = data?.pages.flatMap(page => page.products) ?? [];

  const handleLoginCallback = async () => {
    const {access_token, refresh_token, oauth_key} = route.params ?? {};
    const isInAppBrowserAvailable = await InAppBrowser.isAvailable();
    if (isInAppBrowserAvailable) {
      return;
    }

    if (oauth_key && !access_token) {
      // user is not exist, redirect to OTP verification to complete registration
      navigation.navigate('PhoneVerification', {
        oauth_key: oauth_key,
      });
      navigation.setParams({access_token: '', oauth_key: '', refresh_token: ''});
    }
    if (access_token && refresh_token) {
      try {
        const authInfo: AuthStorageType | null = {
          access_token,
          refresh_token,
        };
        login(authInfo);
        navigation.setParams({access_token: '', oauth_key: '', refresh_token: ''});
      } catch (error: any) {
        // There was an error on the native side
      }
    }
  };

  const renderItem = (item: ListRenderItemInfo<SearchProduct>) => {
    return <ProductCard product={item.item} />;
  };

  const numColumns = useMemo(() => {
    return ScreenWidth >= 768 ? 4 : 2;
  }, []);

  const handleReachedEnd = () => {
    if (!isFetchingNextPage && hasNextPage) {
      fetchNextPage();
    }
  };

  const handleScroll = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      yOffset.setValue(event.nativeEvent.contentOffset.y);

      if (event.nativeEvent.contentOffset.y > 200 && !headerChanged.current) {
        headerChanged.current = true;
        navigation.setOptions({
          headerTintColor: theme.colors.text,
          headerTitle: 'TTS Dropship',
          headerTitleStyle: {color: theme.colors.primary},
        });

        ee.emit(EventNames.HomePageUpper200Scroll);
      }
      if (event.nativeEvent.contentOffset.y < 200 && headerChanged.current) {
        headerChanged.current = false;
        navigation.setOptions({
          headerTintColor: isShowOnboard ? theme.colors.white : theme.colors.text,
          headerTitle: 'TTS Dropship',
          headerTitleStyle: {color: !isShowOnboard ? theme.colors.primary : theme.colors.white},
        });

        ee.emit(EventNames.HomePageLower200Scroll);
      }
    },
    [isShowOnboard],
  );

  const onCTAPress = useCallback(() => {
    if (flatListRef.current) {
      flatListRef.current.scrollToIndex?.({
        animated: true,
        index: 0,
        viewOffset: 330,
      });
    }
  }, []);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await homeCollectionsRef.current?.refetch();
    await refetch();
    setRefreshing(false);
  }, [refetch]);

  const handleReceiveInfiniteProductsCollectionId = useCallback((collectionId: string) => {
    setInfiniteListCollectionId(collectionId);
  }, []);

  const handleInfiniteListNavigate = useCallback(() => {
    navigation.navigate('CollectionDetail', {
      collectionId: infiniteListCollectionId,
    });
  }, [infiniteListCollectionId]);

  const ListFooterComponent = useMemo(() => {
    return (
      <View style={{flexDirection: 'row', justifyContent: 'center', marginBottom: 40}}>
        <Text style={{marginLeft: 8, color: theme.colors.textLight, fontSize: theme.typography.sm}}>Đang tải</Text>
      </View>
    );
  }, []);

  const ListHeaderComponent = useMemo(() => {
    return (
      <>
        {isShowOnboard && (
          <>
            <Introduction />
            <FourStepsOfMoney onCTAPress={onCTAPress} />
          </>
        )}
        <HideIfHasOrderCompleted>
          <NewUserTutorials />
        </HideIfHasOrderCompleted>
        {!isZaloMiniApp && <PrimaryBanner />}
        {/* <SearchInput /> */}
        <Categories />
        <HomeCollections ref={homeCollectionsRef} onReceiveInfiniteProductsCollectionId={handleReceiveInfiniteProductsCollectionId} />
        <Pressable style={[theme.globalStyles.flexRow, styles.viewMoreBtn]} onPress={handleInfiniteListNavigate} accessible={false}>
          <Text style={styles.newProductsTitle}>Sản phẩm mới</Text>
          <Ionicons name="arrow-forward" size={22} color={theme.colors.text} style={{marginLeft: 'auto', paddingRight: 20}} accessibilityLabel="Xem tất cả" />
        </Pressable>
      </>
    );
  }, [onCTAPress, isShowOnboard, isZaloMiniApp, handleInfiniteListNavigate]);

  return (
    <>
      <HomeOgMeta />
      <View style={{flex: 1, paddingHorizontal: 0, backgroundColor: theme.colors.gray10}}>
        <Animated.FlatList
          showsVerticalScrollIndicator={false}
          ref={flatListRef}
          refreshControl={<RefreshControl onRefresh={handleRefresh} refreshing={isRefreshing} />}
          ListHeaderComponent={ListHeaderComponent}
          numColumns={numColumns}
          data={dataFlat}
          renderItem={renderItem}
          style={styles.flatList}
          keyExtractor={item => item.id}
          scrollEventThrottle={16}
          onScroll={handleScroll}
          onEndReached={handleReachedEnd}
          columnWrapperStyle={styles.columnWrapperStyle}
          ListFooterComponent={ListFooterComponent}
        />
      </View>
      <FloatingBanner />
    </>
  );
};

const styles = StyleSheet.create({
  headerRightContainer: {
    paddingRight: 20,
    flexDirection: 'row',
  },
  flatList: {
    flex: 1,
    width: '100%',
  },
  columnWrapperStyle: {
    paddingHorizontal: theme.spacing.m - 8,
  },
  newProductsTitle: {
    fontWeight: '500',
    fontSize: theme.typography.lg,
  },
  viewMoreBtn: {paddingBottom: theme.spacing.m, paddingHorizontal: theme.spacing.m, marginTop: theme.spacing.xl},
});

export default HomeScreen;
