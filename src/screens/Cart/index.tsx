import AsyncStorage from '@react-native-async-storage/async-storage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Button} from '@rneui/base';
import produce from 'immer';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {ActivityIndicator, Image, RefreshControl, ScrollView, StyleSheet, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useGetCart} from '~utils/api/cart';
import {ASYNC_STORAGE_KEYS} from '~utils/config';
import {formatPriceToString} from '~utils/helpers/price';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import {CartItem as CartItemType} from '~utils/types/cart';
import groupBy from 'lodash/fp/groupBy';
import sortBy from 'lodash/fp/sortBy';
import uniq from 'lodash/fp/uniq';
import CartItemByShop from '~components/by-screens/Cart/CartItemByShop';
import {Text} from '@rneui/themed';
import theme from '~utils/config/themes/theme';

// @ts-ignore
import EmptyProductPNG from '~assets/empty-product.png';
import useCreateOrderStore from '~hooks/store/useCreateOrderStore';
import CartRemoveButton from '~components/by-screens/Cart/CartRemoteButton';

const CartScreen: React.FC<NativeStackScreenProps<RootStackParamList, 'Cart'>> = ({route, navigation}) => {
  const insets = useSafeAreaInsets();
  const {data: cart, isLoading: isLoadingCart, refetch} = useGetCart();
  const [isRefreshing, setRefreshing] = useState(false);
  const cleanUpOrderStore = useCreateOrderStore(state => state.cleanUp);

  const getSelectedItemsFromVariantIds = useCallback(
    (variantIds: string | string[]) => {
      let ids: string[] = [];

      if (isLoadingCart) {
        return [];
      }
      if (typeof variantIds === 'string') {
        ids = variantIds.split(',');
      } else {
        ids = variantIds;
      }

      return ids
        .map(variantId => {
          const cartItem = cart?.items.find(item => item.variant_id === variantId);
          return cartItem;
        })
        .filter(Boolean) as CartItemType[];
    },
    [cart, isLoadingCart],
  );

  const [selectedItems, setSelectedItems] = useState(route.params?.preSelectedVariantIds ? getSelectedItemsFromVariantIds(route.params.preSelectedVariantIds) : []);

  useEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <CartRemoveButton
          onRemoveSuccess={() => {
            setSelectedItems([]);
          }}
          isCheckout={!!route.params?.preSelectedVariantIds}
          selectedCartItems={selectedItems}
        />
      ),
    });
  }, [selectedItems, navigation, route.params?.preSelectedVariantIds]);

  useEffect(() => {
    if (!isLoadingCart) {
      if (route.params?.preSelectedVariantIds?.length && !selectedItems.length) {
        setSelectedItems(getSelectedItemsFromVariantIds(route.params?.preSelectedVariantIds));
      }
    }
  }, [isLoadingCart]);

  const selectedItemIds = useMemo(() => selectedItems.map(cartItem => cartItem.variant_id), [selectedItems]);

  const groupByCart = useMemo<{[key: string]: CartItemType[]}>(() => {
    const selectedShopIds = selectedItems.map(item => item.shop_id);
    let selectedItemVIds: string[] = [];
    let preselectedItem = route.params?.preSelectedVariantIds;

    if (typeof preselectedItem === 'string') {
      selectedItemVIds = preselectedItem.split(',');
    } else {
      if (Array.isArray(preselectedItem)) {
        selectedItemVIds = preselectedItem;
      }
    }

    const cartItemsByShopId = groupBy('shop_id', cart?.items ?? []);
    return [...uniq(selectedShopIds), ...(cart?.items ?? []).filter(item => !selectedShopIds.includes(item.shop_id)).map(item => item.shop_id)].reduce((a, c) => {
      if (cartItemsByShopId[c]?.length > 0) {
        return {...a, [c]: sortBy(variant => !selectedItemVIds.includes(variant.variant_id), cartItemsByShopId[c])};
      }
      return a;
    }, {});
  }, [cart]);

  const onProcessToCheckout = useCallback(async () => {
    await AsyncStorage.setItem(ASYNC_STORAGE_KEYS.DRAFT_ORDER, JSON.stringify(selectedItems));
    // clean up order store
    cleanUpOrderStore();

    navigation.navigate('OrderConfirm');
  }, [selectedItems]);

  const totalSelectedPrice = useMemo(() => {
    return selectedItems.reduce((acc, cur) => {
      return acc + cur.dropship_selling_price * cur.quantity;
    }, 0);
  }, [selectedItems]);

  const totalSelectedProfit = useMemo(() => {
    return selectedItems.reduce((acc, cur) => {
      return acc + (cur.dropship_selling_price - cur.dropship_price) * cur.quantity;
    }, 0);
  }, [selectedItems]);

  const handleCheckedChange = useCallback((cartItem: CartItemType, checked: boolean) => {
    setSelectedItems(prev =>
      produce(prev, draft => {
        if (checked) {
          draft.push(cartItem);
        } else {
          const index = draft.findIndex(item => item.variant_id === cartItem.variant_id);
          if (index !== -1) {
            draft.splice(index, 1);
          }
        }
      }),
    );
  }, []);

  const handleCheckedAllChange = useCallback((isCheckedAll: boolean, items: CartItemType[]) => {
    setSelectedItems(prev =>
      produce(prev, draft => {
        if (isCheckedAll) {
          const selectedIds = draft.map(item => item.variant_id);
          draft.push(...items.filter(item => !selectedIds.includes(item.variant_id)));
        } else {
          const removedIds = items.map(item => item.variant_id);
          return draft.filter(item => !removedIds.includes(item.variant_id));
        }
      }),
    );
  }, []);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  }, [refetch]);

  return (
    <View style={{flex: 1}}>
      <ScrollView style={{flex: 1}} refreshControl={<RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />}>
        {/* @ts-ignore */}
        {cart?.items.length >= 100 && (
          <View style={styles.cartFullAlert}>
            <Text style={styles.cartFullAlertText}>Giỏ hàng của bạn chứa quá nhiều sản phẩm ({cart?.items.length}/100). Xóa bớt sản phẩm giúp giỏ hàng chạy nhanh hơn.</Text>
          </View>
        )}
        {isLoadingCart && (
          <View style={{marginVertical: 40}}>
            <ActivityIndicator color={'#008060'} />
          </View>
        )}
        {!isLoadingCart &&
          cart &&
          Object.keys(groupByCart).map(shopId => (
            <CartItemByShop
              onCheckedAllChange={handleCheckedAllChange}
              onCheckedChange={handleCheckedChange}
              key={shopId}
              selectedItemIds={selectedItemIds}
              shopId={shopId}
              items={groupByCart[shopId]}
            />
          ))}

        {!isLoadingCart && !cart?.items.length && (
          <View style={{flex: 1, justifyContent: 'center', alignItems: 'center', marginVertical: theme.spacing.l}}>
            <Image source={EmptyProductPNG} style={{width: 200, height: 200, opacity: 0.6}} />

            <Text style={{marginTop: theme.spacing.m, fontSize: theme.typography.lg2, color: theme.colors.textLight}}>Giỏ hàng trống</Text>
          </View>
        )}
      </ScrollView>
      <View style={[styles.summary, {paddingBottom: styles.summary.paddingVertical + insets.bottom}]}>
        <View style={{flex: 1}}>
          <Text style={{fontSize: theme.typography.base}}>
            Tổng tiền hàng: <Text style={{fontWeight: 'bold', fontSize: theme.typography.base}}>{formatPriceToString(totalSelectedPrice)}</Text>
          </Text>
          <Text style={{fontSize: theme.typography.base}}>
            Tổng lợi nhuận: <Text style={{fontWeight: '600', color: '#008060', fontSize: theme.typography.base}}>{formatPriceToString(totalSelectedProfit)}</Text>
          </Text>
        </View>
        <Button
          icon={{
            type: 'ionicon',
            name: 'chevron-forward',
            color: isLoadingCart || !selectedItems.length ? theme.colors.textLightest : theme.colors.white,
            size: 20,
          }}
          iconRight
          buttonStyle={styles.confirmBtn}
          onPress={onProcessToCheckout}
          color="#008060"
          disabled={isLoadingCart || !selectedItems.length}>
          Tạo đơn
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  summary: {
    backgroundColor: '#fff1dc',
    flexDirection: 'row',
    paddingHorizontal: 8,
    paddingVertical: 8,
    alignItems: 'center',
  },
  confirmBtn: {
    paddingHorizontal: 40,
    borderRadius: 30,
  },
  cartFullAlert: {
    backgroundColor: theme.colors.secondary,
    padding: theme.spacing.m,
    borderRadius: 12,
  },
  cartFullAlertText: {
    color: theme.colors.text,
    fontSize: theme.typography.base,
  },
});

export default CartScreen;
