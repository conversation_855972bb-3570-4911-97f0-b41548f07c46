import {NativeStackNavigationOptions, NativeStackScreenProps} from '@react-navigation/native-stack';
import {Text} from '@rneui/themed';
import React, {useCallback, useEffect, useMemo, useRef} from 'react';
import {ActivityIndicator, FlatList, ListRenderItemInfo, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import HeaderRightSearch from '~components/shared/HeaderRightSearch';
import ProductCard from '~components/shared/ProductCard';
import ScrollToTop from '~components/shared/ScrollToTop';
import SearchLayoutSkeleton from '~components/shared/SearchLayoutSkeleton';
import SortBar from '~components/shared/SortBar';
import useSort, {SortParams} from '~hooks/useSort';
import {useGetSmartCollection} from '~utils/api/product';
import theme from '~utils/config/themes/theme';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import {SearchProduct} from '~utils/types/product';

const CollectionDetail: React.FC<NativeStackScreenProps<RootStackParamList, 'CollectionDetail'>> = ({navigation, route}) => {
  const {sortState, updateSort} = useSort({});
  const {data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading} = useGetSmartCollection(route.params.collectionId, {...sortState});
  const flatListRef = useRef<FlatList<any>>(null);

  useEffect(() => {
    navigation.setOptions({
      headerRight: props => <HeaderRightSearch {...props} />,
    });
  }, [navigation]);

  const collection = useMemo(() => {
    return data?.pages[0]?.collection;
  }, [data]);

  useEffect(() => {
    if (collection) {
      // collection.styles = mockupCollectionStyle.styles;

      let options: Partial<NativeStackNavigationOptions> = {
        headerTitle: collection.title,
      };

      if (collection.styles?.bg?.gradientColors && collection.styles?.bg?.gradientColors.length > 0) {
        options.headerBackground = () => <LinearGradient start={{x: 0, y: 0.2}} colors={collection.styles.bg.gradientColors} style={{flex: 1}} />;
      }

      if (collection.styles?.title?.color) {
        options.headerTintColor = collection.styles?.title?.color;
        options.headerTitleStyle = {color: collection.styles?.title?.color};
      }

      navigation.setOptions(options);
    }
  }, [navigation, collection]);

  const dataFlat = useMemo(() => {
    return data?.pages.flatMap(p => p.products) ?? [];
  }, [data]);

  const renderItem = useCallback(
    (item: ListRenderItemInfo<SearchProduct>) => {
      return (
        <View style={{flex: 1}}>
          <ProductCard product={item.item} />
        </View>
      );
    },
    [dataFlat],
  );

  const handleUpdateSort = useCallback((newSortState: SortParams) => {
    updateSort(newSortState);
    if (flatListRef.current && flatListRef.current.scrollToOffset) {
      flatListRef.current.scrollToOffset({offset: 0, animated: true});
    }
  }, []);

  const handleEndReached = () => {
    if (!isFetchingNextPage && hasNextPage) {
      fetchNextPage();
    }
  };

  const handleScrollToTop = useCallback(() => {
    if (flatListRef.current && flatListRef.current.scrollToOffset) {
      flatListRef.current.scrollToOffset({offset: 0, animated: true});
    }
  }, []);

  return (
    <View style={{flex: 1, paddingHorizontal: theme.spacing.m - 8}}>
      <View style={{marginHorizontal: -(theme.spacing.m - 8)}}>
        {!isLoading ? <SortBar sortState={sortState.sort_by ? sortState : {sort_by: collection?.sort_by, ascending: collection?.ascending}} updateSort={handleUpdateSort} disableScoreSort /> : null}
      </View>

      <FlatList
        ref={flatListRef}
        showsVerticalScrollIndicator={false}
        ListHeaderComponent={
          <>
            <View style={{height: theme.spacing.s}} />
          </>
        }
        data={dataFlat}
        ListEmptyComponent={<>{isLoading && <SearchLayoutSkeleton />}</>}
        renderItem={renderItem}
        keyExtractor={item => item.id}
        numColumns={2}
        onEndReached={handleEndReached}
        ListFooterComponent={
          isFetchingNextPage ? (
            <View style={{flexDirection: 'row', justifyContent: 'center'}}>
              <ActivityIndicator color={theme.colors.primary} />
              <Text style={{marginLeft: 8}}>Đang tải thêm kết quả</Text>
            </View>
          ) : null
        }
      />

      <ScrollToTop onPress={handleScrollToTop} />
    </View>
  );
};

export default CollectionDetail;
