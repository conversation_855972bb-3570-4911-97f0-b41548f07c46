import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Dialog, Text} from '@rneui/themed';
import React, {useCallback, useMemo, useState} from 'react';
import {Pressable, ScrollView, StyleSheet, View} from 'react-native';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import Ionicons from 'react-native-vector-icons/Ionicons';
import theme from '~utils/config/themes/theme';
import {useAuthActions} from '~hooks';

const Settings: React.FC<NativeStackScreenProps<RootStackParamList, 'Settings'>> = ({navigation}) => {
  const {logout, isLoggedIn, withAuth, isLoggingOut} = useAuthActions();
  const [logoutConfirmOpen, setLogoutConfirmOpen] = useState(false);

  const closeLogoutConfirm = useCallback(() => {
    setLogoutConfirmOpen(false);
  }, []);

  const handleLogoutConfirmPress = useCallback(() => {
    logout();
    navigation.navigate('MainTabs', {screen: 'Account'});
  }, [logout]);

  const settingsList = useMemo(() => {
    const menu = [
      {
        icon: <Ionicons name="person-outline" size={22} color={theme.colors.text} />,
        label: 'Tài khoản & bảo mật',
        onPress: () =>
          withAuth(() => {
            navigation.navigate('AccountInfoSettings');
          }),
      },
      {
        icon: <Ionicons name="card-outline" size={22} color={theme.colors.text} />,
        label: 'Số dư & thanh toán',
        onPress: () =>
          withAuth(() => {
            navigation.navigate('AccountAndPayment');
          }),
      },
    ];
    if (isLoggedIn) {
      menu.push({
        icon: <Ionicons name="log-out-outline" size={22} color={theme.colors.text} />,
        label: 'Đăng xuất',
        onPress: () => {
          setLogoutConfirmOpen(true);
        },
      });
    }
    return menu;
  }, [isLoggedIn]);

  return (
    <ScrollView style={{flex: 1, backgroundColor: '#fff', paddingVertical: 20}}>
      {settingsList.map((setting, index) => (
        <Pressable style={styles.container} onPress={setting.onPress} key={index}>
          <View style={{marginRight: theme.spacing.s}}>{setting.icon}</View>
          <View style={{flexDirection: 'row', paddingVertical: theme.spacing.m, borderBottomWidth: 1, borderBottomColor: theme.colors.gray20, flex: 1}}>
            <Text>{setting.label}</Text>
            <Ionicons name="chevron-forward" size={18} color={theme.colors.text} style={{marginLeft: 'auto'}} />
          </View>
        </Pressable>
      ))}

      <Dialog isVisible={logoutConfirmOpen} onBackdropPress={closeLogoutConfirm}>
        <Dialog.Title titleStyle={{color: theme.colors.text}} title="Đăng xuất tài khoản này?" />
        <Dialog.Actions>
          <Dialog.Button title={'TRỞ VỀ'} titleStyle={{color: theme.colors.textLight}} onPress={closeLogoutConfirm} />
          <Dialog.Button title={'ĐĂNG XUẤT'} loading={isLoggingOut} onPress={handleLogoutConfirmPress} />
        </Dialog.Actions>
      </Dialog>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    backgroundColor: '#fff',
  },
});
export default Settings;
