import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {Modal, Platform, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import {useGetCurrentUser, useUpdateUserInfoMutation} from '~utils/api/auth';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {Button, Dialog, Input, Text} from '@rneui/themed';
import useUserSettingsStore from '~hooks/store/useUserSettingsStore';
import {useGetLocations} from '~utils/api/location';
import Select from '~components/shared/Form/Select';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import LocationTabView from '~components/shared/LocationTabView';
import Address from '~utils/types/address';
import {User} from '~utils/types/user';
import {useToast} from 'react-native-toast-notifications';
import UserAvatar from '~components/by-screens/AccountInfoSettings/UserAvatar';
import userValidate from '~utils/validate/user';
import theme from '~utils/config/themes/theme';
import Card from '~components/shared/Card';
import SettingItem from '~components/by-screens/AccountInfoSettings/SettingItem';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import {useAuthActions} from '~hooks';
import UserMGMCard from '~components/by-screens/AccountInfoSettings/UserMGMCard';

const AccountInfoSettings: React.FC<NativeStackScreenProps<RootStackParamList, 'AccountInfoSettings'>> = ({navigation}) => {
  const {data, isLoading} = useGetCurrentUser();
  const state = useUserSettingsStore();
  const {data: locations} = useGetLocations();
  const [errors, setErrors] = useState<Partial<{[key in keyof User]: string}>>({});
  const [locationSelectOpen, setLocationSelectOpen] = useState(false);
  const [initialTab, setInitialTab] = useState(0);
  const insets = useSafeAreaInsets();
  const updateUserInfoMutation = useUpdateUserInfoMutation();
  const toast = useToast();
  const [submitLoading, setSubmitLoading] = useState(false);
  const {logout, isLoggingOut} = useAuthActions();
  const [logoutConfirmOpen, setLogoutConfirmOpen] = useState(false);

  useEffect(() => {
    return () => {
      state.cleanUp();
    };
  }, []);

  useEffect(() => {
    if (!isLoading && data?.id) {
      state.initializeStore({
        account_phone: data.account_phone,
        address: data.address,
        full_name: data.full_name,
        district_name: data.district_name,
        province_name: data.province_name,
        ward_name: data.ward_name,
        email: data.email,
      });
    }
  }, [isLoading]);

  const closeLogoutConfirm = useCallback(() => {
    setLogoutConfirmOpen(false);
  }, []);

  const handleLogoutConfirmPress = useCallback(() => {
    logout();
    navigation.navigate('MainTabs', {screen: 'Account'});
  }, [logout]);

  const getProvinces = useMemo(() => (locations || []).reduce<string[]>((acc, cur) => [...acc, ...Object.keys(cur)], []), [locations]);

  const getDistricts = useMemo(() => {
    for (const province of locations || []) {
      if (Object.keys(province).includes(state.data.province_name)) {
        return Object.keys(province[state.data.province_name]);
      }
    }
    return [];
  }, [state.data.province_name, locations]);

  const getWards = useMemo(() => {
    for (const city of locations || []) {
      if (Object.keys(city).includes(state.data.province_name)) {
        return city[state.data.province_name][Object.keys(city[state.data.province_name]).filter(province => province === state.data.district_name)[0]] || [];
      }
    }
    return [];
  }, [state.data.province_name, state.data.district_name, locations]);

  const handleLocationSelectPress = useCallback((type: 'province_name' | 'district_name' | 'ward_name') => {
    const tab = {
      province_name: 0,
      district_name: 1,
      ward_name: 2,
    };
    setInitialTab(tab[type]);
    setLocationSelectOpen(true);
  }, []);

  const handleSelectDone = useCallback((add: Partial<Address>) => {
    state.updateAddressState('province_name', add.city as string);
    state.updateAddressState('district_name', add.province as string);
    state.updateAddressState('ward_name', add.ward as string);
    setLocationSelectOpen(false);
  }, []);

  const handleSubmit = async () => {
    let isValid = true;
    setErrors({});
    setSubmitLoading(true);

    await userValidate.validate({...state.data}, {abortEarly: false}).catch(err => {
      isValid = false;
      setErrors(err.inner.reduce((a: any, c: any) => ({...a, [c.path]: c.message}), {}));
      setSubmitLoading(false);
    });

    if (!isValid) {
      return;
    }

    await updateUserInfoMutation.mutateAsync({
      ...state.data,
    });

    setSubmitLoading(false);
    toast.show('Lưu thành công', {
      placement: 'bottom',
      duration: 1000,
    });
  };

  const verifyStatus = useMemo(() => {
    let statusText = {
      not_verified: 'Chưa định danh',
      under_review: 'Đang xử lý',
      verified: 'Đã định danh',
    };

    return data?.idcard_verify ? statusText[data?.idcard_verify] : '';
  }, [data]);

  return (
    <View style={{flex: 1, paddingBottom: insets.bottom}}>
      <ScrollView style={{flex: 1, paddingHorizontal: 12}}>
        <UserMGMCard />
        {/* <UserAvatar avatar={data?.avatar as string} /> */}
        <View style={{marginTop: theme.spacing.m}} />

        <Card rounded title="Thông tin cá nhân">
          <Input
            label="Số điện thoại"
            value={state.data.account_phone}
            containerStyle={styles.formContainer}
            inputContainerStyle={[styles.inputContainer, {backgroundColor: theme.colors.gray10}]}
            inputStyle={[styles.input]}
            labelStyle={styles.label}
            onChangeText={text => state.updateSettings('full_name', text)}
            errorMessage={errors.full_name}
            editable={false}
          />

          <Input
            label="Họ và tên"
            value={state.data.full_name}
            containerStyle={styles.formContainer}
            inputContainerStyle={styles.inputContainer}
            inputStyle={styles.input}
            labelStyle={styles.label}
            onChangeText={text => state.updateSettings('full_name', text)}
            errorMessage={errors.full_name}
          />

          <Select
            options={getProvinces}
            label="Tỉnh / thành phố"
            onPress={() => handleLocationSelectPress('province_name')}
            value={state.data.province_name}
            onChange={newProvince => state.updateAddressState('province_name', newProvince as string)}
            errorMessage={errors.province_name}
          />
          <Select
            options={getDistricts}
            label="Quận / huyện"
            onPress={() => handleLocationSelectPress('district_name')}
            value={state.data.district_name}
            onChange={newDistrict => state.updateAddressState('district_name', newDistrict as string)}
            errorMessage={errors.district_name}
          />
          <Select
            options={getWards}
            label="Phường / xã"
            onPress={() => handleLocationSelectPress('ward_name')}
            value={state.data.ward_name}
            onChange={ward => state.updateAddressState('ward_name', ward as string)}
            errorMessage={errors.ward_name}
          />
          <Input
            label="Số nhà, tên đường"
            value={state.data.address}
            containerStyle={styles.formContainer}
            inputContainerStyle={styles.inputContainer}
            inputStyle={styles.input}
            labelStyle={styles.label}
            placeholder="Ví dụ: 28 Âu Cơ"
            onChangeText={text => state.updateSettings('address', text)}
            errorMessage={errors.address}
          />
        </Card>

        <View style={{marginTop: theme.spacing.l, borderRadius: 12, overflow: 'hidden'}}>
          <SettingItem
            title="Định danh tài khoản"
            description={
              <>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  {data?.idcard_verify === 'verified' && <Ionicons name="checkmark-circle" color={theme.colors.primary} size={16} style={{marginRight: theme.spacing.xs}} />}
                  <Text style={{color: theme.colors.textLight, fontSize: theme.typography.base}}>{verifyStatus}</Text>
                </View>
              </>
            }
            onPress={() => navigation.navigate('IdentityVerification', {})}
          />
          {/* <SettingItem title="Cài đặt thuế" description={'Thông tin thuế'} onPress={() => navigation.navigate('TaxInformation')} /> */}
          <SettingItem title="Bảo mật" description="Thay đổi mật khẩu, xóa tài khoản" onPress={() => navigation.navigate('PrivacySettings')} />
          <SettingItem title="Đăng xuất" description="Đăng xuất khỏi thiết bị này" onPress={() => setLogoutConfirmOpen(true)} />
        </View>

        {Platform.OS !== 'web' && (
          <Modal visible={locationSelectOpen} onRequestClose={() => setLocationSelectOpen(false)} animationType="slide">
            <View style={{flex: 1, paddingTop: insets.top, paddingBottom: insets.bottom}}>
              <View style={{paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: 'rgba(0,0,0,.08)', position: 'relative'}}>
                <View style={{position: 'absolute', left: 12, top: 4, zIndex: 100}}>
                  <Ionicons name="close-circle" size={38} color="rgba(0,0,0,.5)" onPress={() => setLocationSelectOpen(false)} />
                </View>
                <Text style={{textAlign: 'center', fontSize: 18, fontWeight: '500', color: '#008060'}}>Chọn địa chỉ</Text>
                <View />
              </View>
              <LocationTabView
                initialTab={initialTab}
                onSelectDone={handleSelectDone}
                initialAddress={{city: state.data.province_name, province: state.data.district_name, ward: state.data.ward_name}}
              />
            </View>
          </Modal>
        )}

        <View style={{marginTop: 40}} />

        <Dialog isVisible={logoutConfirmOpen} onBackdropPress={closeLogoutConfirm}>
          <Dialog.Title title="Đăng xuất tài khoản này?" />
          <Dialog.Actions>
            <Dialog.Button title={'TRỞ VỀ'} titleStyle={{color: theme.colors.textLight}} onPress={closeLogoutConfirm} />
            <Dialog.Button title={'ĐĂNG XUẤT'} loading={isLoggingOut} onPress={handleLogoutConfirmPress} />
          </Dialog.Actions>
        </Dialog>
      </ScrollView>
      <Button size="sm" onPress={handleSubmit} disabled={!state.isDirty} loading={updateUserInfoMutation.isLoading || submitLoading}>
        Lưu
      </Button>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 8,
    paddingVertical: 20,
    backgroundColor: '#fff',
    position: 'relative',
    flex: 1,
  },
  inputContainer: {
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,.2)',
    borderRadius: 8,
    marginTop: 8,
  },
  formContainer: {
    // marginTop: 12,
  },
  input: {
    padding: 12,
    fontSize: theme.typography.md,
  },
  label: {
    fontWeight: '500',
    fontSize: theme.typography.md,
  },

  identityVerifyContainer: {
    backgroundColor: theme.colors.white,
    padding: theme.spacing.s,
    paddingHorizontal: theme.spacing.m,
    borderRadius: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});

export default AccountInfoSettings;
