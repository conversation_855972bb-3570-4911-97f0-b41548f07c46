import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React from 'react';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {ActivityIndicator, Platform, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import {Button, Text} from '@rneui/themed';
import WebView from 'react-native-webview';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {env} from '~utils/config';

const OrderPayment: React.FC<NativeStackScreenProps<RootStackParamList, 'OrderPayment'>> = ({route, navigation}) => {
  const insets = useSafeAreaInsets();
  const handleBackToOrder = () => {
    if (route.params.order_id) {
      navigation.replace('OrderDetail', {
        orderId: parseInt(route.params.order_id, 10),
      });
    }
  };

  if (!route.params.status && route.params.payment_checkout_url) {
    return (
      <View style={{flex: 1, paddingBottom: insets.bottom}}>
        <WebView
          decelerationRate="normal"
          style={{flex: 1}}
          allowFileAccess={true}
          pullToRefreshEnabled={true}
          androidHardwareAccelerationDisabled
          startInLoadingState={true}
          allowsBackForwardNavigationGestures
          renderLoading={() => (
            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
              <ActivityIndicator size="large" color={theme.colors.primary} />
            </View>
          )}
          source={{uri: `${route.params.payment_checkout_url}?callback_url=${Platform.OS === 'web' ? env.WEBVIEW_URL : 'tts.com.dsbuyer://'}/order-payment`}}
        />
      </View>
    );
  }

  return (
    <View style={{flex: 1, backgroundColor: theme.colors.white, alignItems: 'center'}}>
      <View style={{marginTop: 100, alignItems: 'center'}}>{route.params.status === 'success' ? <SuccessPayment /> : <FailedPayment />}</View>

      <Button onPress={handleBackToOrder} size="sm" containerStyle={{marginTop: theme.spacing.l}} color="secondary" titleStyle={{color: theme.colors.text}} buttonStyle={{borderRadius: 8}}>
        &laquo; Về trang đơn hàng
      </Button>
    </View>
  );
};

const SuccessPayment = () => {
  return (
    <>
      <Ionicons name="checkmark-circle" size={150} color={theme.colors.primary} />
      <Text style={{fontSize: theme.typography.lg2}}>Thanh toán thành công</Text>

      <Text style={{paddingHorizontal: theme.spacing.l, textAlign: 'center', color: theme.colors.textLight, marginTop: theme.spacing.l}}>
        Chúng tôi sẽ thông báo để nhà cung cấp đóng gói và gửi hàng
      </Text>
    </>
  );
};

const FailedPayment = () => {
  return (
    <>
      <Ionicons name="close-circle" size={150} color={theme.colors.red} />
      <Text style={{fontSize: theme.typography.lg2, color: theme.colors.red}}>Thanh toán thất bại</Text>
      <Text style={{paddingHorizontal: theme.spacing.l, textAlign: 'center', color: theme.colors.textLight, marginTop: theme.spacing.l}}>
        Có lỗi xảy ra trong quá trình thanh toán, vui lòng thử lại sau hoặc liên hệ hotline 19006074 để được hỗ trợ
      </Text>
    </>
  );
};

export default OrderPayment;
