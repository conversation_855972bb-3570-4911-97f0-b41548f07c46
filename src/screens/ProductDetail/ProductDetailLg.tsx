import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React, {useEffect} from 'react';
import {Platform, ScrollView, View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useExtendContainerWidth} from '~components/shared/web/WebContainer';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import ImageCarouselLg from '~components/by-screens/ProductDetail/ImageCarouselLg';
import theme from '~utils/config/themes/theme';
import {useGetProductDetail} from '~utils/api/product';
import ProductInfo from '~components/by-screens/ProductDetail/ProductInfo';
import ShopDiscountApplications from '~components/by-screens/ProductDetail/ShopDiscountApplications';
import EstimateShippingRate from '~components/by-screens/ProductDetail/EstimateShippingRate';
import ProductVariants from '~components/by-screens/ProductDetail/ProductVariants';
import PriceInfo from '~components/by-screens/ProductDetail/PriceInfo';
import ShopInfo from '~components/by-screens/ProductDetail/ShopInfo';
import SupplierProducts from '~components/by-screens/ProductDetail/SupplierProducts';
import ProductDescription from '~components/by-screens/ProductDetail/ProductDescription';
import Appbar from '~components/by-screens/ProductDetail/Appbar';
import googleAnalytics from '~utils/helpers/analytics';
import {FirebaseAnalyticsTypes} from '@react-native-firebase/analytics';
import {useCreateAnalyticsEventMutation} from '~utils/api/analytics';
import HeaderSearchLg from '~components/shared/web/WebHeader/HeaderSearchLg';
import NotificationIcon from '~components/shared/NotificationIcon';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useAuthActions} from '~hooks';
import ProductNotFound from '~components/by-screens/ProductDetail/ProductNotFound';
import SEOMeta from '~components/by-screens/ProductDetail/SEOMeta';
import Reviews from '~components/by-screens/ProductDetail/Reviews';
import HomeScreenLogo from '~screens/Home/HomeScreenLogo';

const ProductDetailLg: React.FC<NativeStackScreenProps<RootStackParamList, 'ProductDetail'>> = ({route, navigation}) => {
  useExtendContainerWidth('lg');
  const createAnalyticsEventMutation = useCreateAnalyticsEventMutation();
  const {withAuth} = useAuthActions();
  const {data, isLoading, isError, error} = useGetProductDetail(route.params.productId, {
    onSuccess: product => {
      if (product) {
        if (Platform.OS === 'web') {
          navigation.setOptions({
            title: product.title,
          });
        }
      }
    },
  });

  useEffect(() => {
    navigation.setOptions({
      headerTransparent: false,
      headerTitle: HomeScreenLogo,
      headerRight: () => (
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <HeaderSearchLg />
          <NotificationIcon color={theme.colors.textLight} />
          <Ionicons name="cart-outline" size={28} color={theme.colors.textLight} style={{marginLeft: 12}} onPress={() => withAuth(() => navigation.navigate('Cart'))} />
        </View>
      ),
    });
  }, []);

  useEffect(() => {
    if (!isLoading && data) {
      setTimeout(() => {
        createAnalyticsEventMutation.mutateAsync({
          action: 'view',
          category: 'product',
          object_id: data.id,
          object_metadata: {
            page: `https://dropship.thitruongsi.com/product/${data.id_number}`,
            product_id: data.id,
            product_name: data.title,
            shop_id: data.shop_id,
          },
          object_type: 'product',
          object_title: data.title,
        });
        // analytics
        const logViewItemParams: FirebaseAnalyticsTypes.ViewItemEventParameters = {
          items: [
            {
              item_id: data.id,
              item_name: data.title,
              item_category: data.categories?.lv1?.title,
              item_category2: data.categories?.lv2?.title,
            },
          ],
        };
        googleAnalytics.logViewItem(logViewItemParams);
      }, 500);
    }
  }, [isLoading]);

  if (isError || ['need_review', 'rejected'].includes(data?.review_status ?? '') || data?.can_dropship === false || data?.dropship_status === 'rejected' || data?.status === 'inactive') {
    return <ProductNotFound error={error} />;
  }

  if (isLoading || !data) {
    return null;
  }

  return (
    <>
      <SEOMeta product={data} />

      <View style={{flexDirection: 'row', alignItems: 'flex-start', backgroundColor: theme.colors.white, paddingTop: theme.spacing.m}}>
        <ImageCarouselLg images={data?.images ?? []} product={data} />
        <View style={{marginLeft: theme.spacing.m, flex: 1}}>
          <ProductInfo product={data} />
          <ShopDiscountApplications shopId={data.shop_id} />
          <EstimateShippingRate product={data} />
          <ProductVariants product={data} />
          <PriceInfo product={data} />
          <Appbar product={data} />
        </View>
      </View>
      <View
        style={{
          marginTop: theme.spacing.s,
        }}>
        <ShopInfo shop={data.shop} />
      </View>
      <SupplierProducts shopId={data.shop_id} excludeProductId={data.id} />
      <ProductDescription product={data} />
      <Reviews product={data} />
    </>
  );
};

export default ProductDetailLg;
