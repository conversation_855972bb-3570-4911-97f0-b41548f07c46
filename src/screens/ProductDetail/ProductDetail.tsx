import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {Animated, NativeSyntheticEvent, Platform, RefreshControl, StyleSheet, View} from 'react-native';
import Appbar from '~components/by-screens/ProductDetail/Appbar';
import EstimateShippingRate from '~components/by-screens/ProductDetail/EstimateShippingRate';
import ImageSwiper from '~components/by-screens/ProductDetail/ImageSwiper';
import PriceInfo from '~components/by-screens/ProductDetail/PriceInfo';
import ProductDescription from '~components/by-screens/ProductDetail/ProductDescription';
import ProductDetailSkeleton from '~components/by-screens/ProductDetail/ProductDetailSkeleton';
import ProductInfo from '~components/by-screens/ProductDetail/ProductInfo';
import ProductNotFound from '~components/by-screens/ProductDetail/ProductNotFound';
import ShopInfo from '~components/by-screens/ProductDetail/ShopInfo';
import {useCreateAnalyticsEventMutation} from '~utils/api/analytics';
import {useGetProductDetail} from '~utils/api/product';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import {FirebaseAnalyticsTypes} from '@react-native-firebase/analytics';
import googleAnalytics from '~utils/helpers/analytics';
import SupplierProducts from '~components/by-screens/ProductDetail/SupplierProducts';
import ProductVariants from '~components/by-screens/ProductDetail/ProductVariants';
import ShopDiscountApplications from '~components/by-screens/ProductDetail/ShopDiscountApplications';
import HeaderLeft from '~components/by-screens/ProductDetail/Header/HeaderLeft';
import HeaderRight from '~components/by-screens/ProductDetail/Header/HeaderRight';
import theme from '~utils/config/themes/theme';
import {SafeAreaView} from 'react-native-safe-area-context';
import Reviews from '~components/by-screens/ProductDetail/Reviews';
import SEOMeta from '~components/by-screens/ProductDetail/SEOMeta';

const ProductDetail: React.FC<NativeStackScreenProps<RootStackParamList, 'ProductDetail'>> = ({route, navigation}) => {
  const createAnalyticsEventMutation = useCreateAnalyticsEventMutation();
  const [isRefreshing, setRefreshing] = useState(false);

  const scrollY = useRef(new Animated.Value(0)).current;
  const headerOpacity = scrollY.interpolate({
    inputRange: [0, 200],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  const [iconReverse, setIconReverse] = useState(false);

  const {data, error, isError, isLoading, refetch} = useGetProductDetail(route.params.productId, {
    onSuccess: product => {
      if (product) {
        if (Platform.OS === 'web') {
          navigation.setOptions({
            title: product.title,
          });
        }
      }
    },
  });

  useEffect(() => {
    if (!isLoading && data) {
      setTimeout(() => {
        createAnalyticsEventMutation.mutateAsync({
          action: 'view',
          category: 'product',
          object_id: data.id,
          object_metadata: {
            page: `https://dropship.thitruongsi.com/product/${data.id_number}`,
            product_id: data.id,
            product_name: data.title,
            shop_id: data.shop_id,
          },
          object_type: 'product',
          object_title: data.title,
        });
        // analytics
        const logViewItemParams: FirebaseAnalyticsTypes.ViewItemEventParameters = {
          items: [
            {
              item_id: data.id,
              item_name: data.title,
              item_category: data.categories?.lv1?.title,
              item_category2: data.categories?.lv2?.title,
            },
          ],
        };
        googleAnalytics.logViewItem(logViewItemParams);
      }, 500);
    }
  }, [isLoading]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  }, [refetch]);

  useEffect(() => {
    navigation.setOptions({
      headerBackground: () => (
        <Animated.View
          style={{
            backgroundColor: 'white',
            ...StyleSheet.absoluteFillObject,
            opacity: headerOpacity,
            borderBottomWidth: 1,
            borderBottomColor: theme.colors.gray20,
          }}
        />
      ),
      headerLeft: props => <HeaderLeft {...props} reverse={iconReverse} />,
      headerRight: props => <HeaderRight {...props} reverse={iconReverse} product={data} />,
    });
  }, [navigation, headerOpacity, iconReverse, data?.id]);

  const handleScroll = useCallback(
    (event: NativeSyntheticEvent<any>) => {
      const offsetY = event.nativeEvent.contentOffset.y;
      if (!iconReverse && offsetY > 200) {
        setIconReverse(true);
      }
      if (iconReverse && offsetY < 200) {
        setIconReverse(false);
      }
    },
    [iconReverse],
  );

  if (isError || ['need_review', 'rejected'].includes(data?.review_status ?? '') || data?.can_dropship === false || data?.dropship_status === 'rejected' || data?.status === 'inactive') {
    return <ProductNotFound error={error} />;
  }

  if (!data) {
    return <ProductDetailSkeleton />;
  }

  return (
    <>
      <SEOMeta product={data} />
      <SafeAreaView style={{flex: 1}} edges={['bottom']}>
        <Animated.ScrollView
          style={{flex: 1}}
          keyboardShouldPersistTaps="handled"
          scrollEventThrottle={16}
          refreshControl={<RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />}
          onScroll={Animated.event(
            [
              {
                nativeEvent: {
                  contentOffset: {
                    y: scrollY,
                  },
                },
              },
            ],
            {
              useNativeDriver: true,
              listener: handleScroll,
            },
          )}>
          <ImageSwiper product={data} />
          <ProductInfo product={data} />

          <ShopDiscountApplications shopId={data.shop_id} />
          <EstimateShippingRate product={data} />
          <ProductVariants product={data} />
          <PriceInfo product={data} />
          <View
            style={{
              marginTop: theme.spacing.s,
            }}>
            <ShopInfo shop={data.shop} />
          </View>
          <ProductDescription product={data} />
          <Reviews product={data} />
          <SupplierProducts shopId={data.shop_id} excludeProductId={data.id} />
          <View style={{height: 100}} />
        </Animated.ScrollView>
        <Appbar product={data} />
      </SafeAreaView>
    </>
  );
};

export default ProductDetail;
