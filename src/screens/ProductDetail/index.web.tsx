import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React from 'react';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import ProductDetail from './ProductDetail';
import ProductDetailLg from './ProductDetailLg';
import MediaQuery from 'react-responsive';

const ProductDetailWeb: React.FC<NativeStackScreenProps<RootStackParamList, 'ProductDetail'>> = props => {
  return (
    <>
      <MediaQuery minWidth={768}>
        <ProductDetailLg {...props} />
      </MediaQuery>
      <MediaQuery maxWidth={768}>
        <ProductDetail {...props} />
      </MediaQuery>
    </>
  );
};

export default ProductDetailWeb;
