import {Text} from '@rneui/themed';
import React, {useEffect, useRef, useState} from 'react';
import {View} from 'react-native';
import theme from '~utils/config/themes/theme';

const Resend: React.FC<{onResend: () => void}> = ({onResend}) => {
  const [countDownSeconds, setCountDownSeconds] = useState(60);
  const countIntervalRef = useRef<NodeJS.Timer>();

  useEffect(() => {
    startInterval();

    return () => {
      clearInterval(countIntervalRef.current);
    };
  }, []);

  const startInterval = () => {
    setCountDownSeconds(60);
    countIntervalRef.current = setInterval(() => {
      setCountDownSeconds(prev => {
        if (prev === 0) {
          clearInterval(countIntervalRef.current);
        }
        return prev > 0 ? prev - 1 : prev;
      });
    }, 1000);
  };

  const handleSubmitPress = () => {
    onResend();
    startInterval();
  };

  return (
    <View style={{marginBottom: theme.spacing.s}}>
      <Text style={{textAlign: 'center', fontSize: theme.typography.md, color: theme.colors.textLight}}>
        Chưa nhận được mã?{' '}
        <Text
          onPress={handleSubmitPress}
          style={{
            color: countDownSeconds === 0 ? theme.colors.text : theme.colors.textLight,
          }}>
          Gửi lại {countDownSeconds === 0 ? '' : `(${countDownSeconds})`}
        </Text>
      </Text>
    </View>
  );
};

export default Resend;
