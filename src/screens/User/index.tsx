import {BottomTabScreenProps} from '@react-navigation/bottom-tabs';
import React, {useCallback, useEffect, useMemo, useRef} from 'react';
import {Linking, Pressable, ScrollView, StyleSheet, View, Platform} from 'react-native';
import {MainTabsScreenList} from '~screens/MainTabs';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useGetCurrentUser} from '~utils/api/auth';
import {handleImageDomain} from '~utils/helpers/image';
import {Button} from '@rneui/base';

import {Text} from '@rneui/themed';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import theme from '~utils/config/themes/theme';
import {CompositeScreenProps, useScrollToTop} from '@react-navigation/native';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import InAppRating from '~components/by-screens/User/InAppRating';
// @ts-ignore
import LuckyCatImage from '~assets/luck-cat.png';
import {useAuthActions} from '~hooks';
import BalanceOverview from '~components/shared/BalanceOverview';
//  @ts-ignore
import InviteFriend from '~assets/invite-friend.png';

import Footer from '~components/shared/Footer';
import {TouchableOpacity} from 'react-native-gesture-handler';
import {QuickImage} from '~components/shared/QuickImage';
// @ts-ignore
import MessengerIcon from '~assets/messenger.png';
// @ts-ignore
import TiktokShopIcon from '~assets/tiktokshop.png';

import FacebookGroupInvite from '~components/by-screens/User/FacebookGroupInvite';
import ViewProfileButton from '~components/by-screens/User/ViewProfileButton';
import {env} from '~utils/config';
import CommonTools from '~components/shared/CommonTools';

type MenuItem = {
  icon: React.ReactNode | any;
  label: string;
  onPress: () => void;
};

type UserScreenProps = CompositeScreenProps<BottomTabScreenProps<MainTabsScreenList, 'Account'>, NativeStackScreenProps<RootStackParamList>>;

const UserScreen: React.FC<UserScreenProps> = ({navigation}) => {
  const {data} = useGetCurrentUser();
  const {isLoggedIn, openLoginRequiredModal, withAuth, authData} = useAuthActions();
  const scrollViewRef = useRef(null);

  useScrollToTop(scrollViewRef);

  useEffect(() => {
    navigation.setOptions({
      headerRight: props => (
        <View style={theme.globalStyles.flexRow}>
          {isLoggedIn && <Ionicons onPress={handleCartPress} name="cart-outline" style={{paddingRight: 16}} color={props.tintColor} size={26} />}

          <Ionicons name="settings-outline" size={26} color={props.tintColor} style={{paddingRight: 16}} onPress={goToSettings} />
        </View>
      ),
    });
  }, [isLoggedIn]);

  const goToSettings = useCallback(() => {
    navigation.navigate('Settings');
  }, []);

  const goToUserSettings = useCallback(() => {
    navigation.navigate('AccountInfoSettings');
  }, []);

  const handleCartPress = useCallback(() => {
    navigation.navigate('Cart');
  }, []);

  const handleGoToWishlist = useCallback(() => {
    navigation.navigate('Wishlist');
  }, []);

  const goToPaymentSettings = useCallback(() => {
    if (!isLoggedIn) {
      openLoginRequiredModal();
    } else {
      navigation.navigate('AccountAndPayment');
    }
  }, [isLoggedIn]);

  // const goToCustomers = useCallback(() => {
  //   navigation.navigate('Customers');
  // }, []);

  const goToInviteFriends = useCallback(() => {
    navigation.navigate('InviteFriends');
  }, []);

  const goToTiktokShopConnect = useCallback(() => {
    navigation.navigate('WebView', {
      url: `${env.OAUTH_URI}/connect/tiktok-shop?${isLoggedIn && Platform.OS === 'web' ? `access_token=${authData?.access_token}&refresh_token=${authData?.refresh_token}` : ''}`,
      includeAuth: true,
    });
  }, [isLoggedIn, authData]);

  const menuList = useMemo<MenuItem[]>(() => {
    return [
      // {
      //   icon: <Ionicons name="person-outline" size={20} color={theme.colors.text} />,
      //   label: 'Khách hàng của tôi',
      //   onPress: () => withAuth(goToCustomers),
      // },
      // {icon: <QuickImage source={TiktokShopIcon} style={{width: 19, height: 22}} />, label: 'Bán trên Tiktok Shop', onPress: goToTiktokShopConnect},
      {
        icon: <Ionicons name="bookmark-outline" size={20} color={theme.colors.text} />,
        label: 'Đã lưu',
        onPress: () => {
          withAuth(handleGoToWishlist);
        },
      },
      {
        icon: <Ionicons name="book-outline" size={20} color={theme.colors.text} />,
        label: 'Hướng dẫn sử dụng',
        onPress: () => {
          navigation.navigate('Instruction');
        },
      },
      {
        icon: <Ionicons name="help-circle-outline" size={20} color={theme.colors.text} />,
        label: 'Câu hỏi thường gặp',
        onPress: () => {
          navigation.navigate('FAQ');
        },
      },
      {
        icon: <Ionicons name="chatbox-ellipses-outline" size={20} color={theme.colors.text} />,
        label: 'Góp ý - Báo lỗi',
        onPress: () => {
          navigation.navigate('Feedback');
        },
      },
      Platform.OS !== 'web' && {
        icon: <QuickImage source={MessengerIcon} style={{width: 18, height: 18}} />,
        label: 'Liên hệ hỗ trợ',
        onPress: () => handleContactSupport(),
      },
    ].filter(Boolean) as MenuItem[];
  }, [data]);

  const handleContactSupport = useCallback(async () => {
    const supportURL = `https://go-ds.thitruongsi.com/ds-inbox`;

    const canOpenURL = await Linking.canOpenURL(supportURL);
    if (canOpenURL) {
      Linking.openURL(supportURL);
    }
  }, [data]);

  const handleGoToAccountDetail = () => {
    navigation.navigate('AccountInfoSettings');
  };

  return (
    <SafeAreaProvider style={{flex: 1}}>
      <ScrollView style={[styles.container]} ref={scrollViewRef}>
        <View style={styles.myAccount}>
          <Pressable onPress={isLoggedIn ? handleGoToAccountDetail : undefined}>
            <QuickImage style={styles.myAccount_avatar} source={isLoggedIn ? {uri: handleImageDomain(data?.avatar as string)} : LuckyCatImage} />
          </Pressable>
          {isLoggedIn && (
            <View style={{marginLeft: 8, alignItems: 'flex-start'}}>
              <Text numberOfLines={1} style={styles.myAccount_name} onPress={handleGoToAccountDetail}>
                {data?.full_name}
              </Text>
              <ViewProfileButton />
            </View>
          )}
          {!isLoggedIn && (
            <View style={{marginLeft: 8, flexDirection: 'row', alignItems: 'center', alignSelf: 'center'}}>
              <Button onPress={() => openLoginRequiredModal('login')} type="clear" titleStyle={{color: theme.colors.text, fontSize: theme.typography.md, fontWeight: '500'}}>
                Đăng nhập
              </Button>
              <Text>/</Text>
              <Button onPress={() => openLoginRequiredModal('register')} type="clear" titleStyle={{color: theme.colors.text, fontSize: theme.typography.md, fontWeight: '500'}}>
                Đăng ký
              </Button>
            </View>
          )}

          {/* <Pressable onPress={() => navigation.navigate('MemberRanking')} style={{marginLeft: 'auto', alignSelf: 'flex-start'}}>
            <Image source={GoldMedal} style={{width: 40, height: 40}} />
          </Pressable> */}
        </View>

        <BalanceOverview
          primaryAction={
            <Button
              title="Quản lý số dư"
              containerStyle={{marginLeft: 'auto'}}
              titleStyle={{fontSize: 16, color: '#008060', fontWeight: '500'}}
              titleProps={{
                allowFontScaling: false,
              }}
              buttonStyle={{borderRadius: 30, paddingHorizontal: 8, borderColor: '#008060', paddingVertical: 4}}
              color="#008060"
              onPress={goToPaymentSettings}
              iconRight
              icon={{
                type: 'ionicon',
                name: 'chevron-forward',
                size: 18,
                color: theme.colors.primary,
              }}
              type="outline"
            />
          }
        />

        <TouchableOpacity
          onPress={goToInviteFriends}
          style={[{backgroundColor: theme.colors.white, marginTop: theme.spacing.m, borderRadius: 12, paddingHorizontal: 21}, theme.globalStyles.flexRow]}
          accessible={false}>
          {<QuickImage source={InviteFriend} style={{width: 80, height: 80, marginRight: theme.spacing.s, marginTop: -10}} />}
          <View>
            <Text style={{fontSize: theme.typography.md, fontWeight: 'bold'}}>Mời bạn bè kiếm tiền</Text>
            <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight}}>Cùng nhau nhận thưởng 💰</Text>
          </View>
          <View style={{marginLeft: 'auto'}}>
            <Ionicons name="chevron-forward" color={theme.colors.textLight} size={20} />
          </View>
        </TouchableOpacity>

        <CommonTools />

        <View style={styles.menuItemContainer}>
          {menuList.map((menuItem, index) => (
            <Pressable onPress={menuItem.onPress} style={styles.menuItem} key={index}>
              {menuItem.icon}
              <View style={{marginLeft: 12, alignSelf: 'stretch', paddingVertical: 16, flex: 1, borderBottomWidth: 1, borderBottomColor: 'rgba(0,0,0,.05)'}}>
                <Text style={styles.menuItem_text}>{menuItem.label}</Text>
              </View>
            </Pressable>
          ))}
        </View>

        {Platform.OS !== 'ios' && <InAppRating />}
        <FacebookGroupInvite />
        <Footer />
      </ScrollView>
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 20,
    flex: 1,
  },

  myAccount: {
    backgroundColor: '#fff',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  myAccount_avatar: {
    width: 60,
    height: 60,
    borderRadius: 100,
  },
  myAccount_name: {
    fontSize: 20,
    fontWeight: '500',
  },

  text1: {
    fontSize: theme.typography.md,
    // fontWeight: '600',
  },

  menuItemContainer: {
    backgroundColor: '#fff',
    overflow: 'hidden',
    borderRadius: 12,
    marginTop: theme.spacing.m,
  },
  menuItem: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
  },
  menuItem_text: {
    fontSize: theme.typography.base,
  },

  footerText: {
    fontSize: 16,
    marginBottom: 8,
  },
});

export default UserScreen;
