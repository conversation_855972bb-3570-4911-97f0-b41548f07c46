import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {<PERSON>ton, CheckBox, Dialog, Text} from '@rneui/themed';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {useToast} from 'react-native-toast-notifications';
import Card from '~components/shared/Card';
import {useAuthActions} from '~hooks';
import {createFeedback} from '~utils/api/analytics';
import {requestToDeleteAccount} from '~utils/api/auth';
import theme from '~utils/config/themes/theme';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';

const AccountDeletion: React.FC<NativeStackScreenProps<RootStackParamList, 'AccountDeletion'>> = ({navigation}) => {
  const [buttonDisabledSecond, setButtonDisabledSecond] = useState(1);
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  const intervalRef = useRef<any>(null);
  const [selectedReasonIndex, setSelectedReasonIndex] = useState(-1);
  const {isLoggingOut, logout} = useAuthActions();
  const toast = useToast();

  useEffect(() => {
    intervalRef.current = setInterval(() => {
      setButtonDisabledSecond(prev => {
        if (prev === 1) {
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
          }
        }
        if (prev - 1 >= 0) {
          return prev - 1;
        }

        return 0;
      });
    }, 1000);
  }, []);

  const handleConfirmModalOpen = useCallback(() => {
    setConfirmModalOpen(true);
  }, []);

  const reasons = useMemo(() => {
    return ['Không có nhu cầu sử dụng', 'Thông báo nhiều gây phiền', 'Ứng dụng không hữu ích', 'Lý do khác'];
  }, []);

  const handleCheck = useCallback((index: number) => {
    setSelectedReasonIndex(index);
  }, []);

  const handleDeleteConfirm = useCallback(async () => {
    try {
      await createFeedback({
        features: 'in-app-rate',
        response: 1,
        feedback: `[Dropship-App] - Account deletion - ${reasons[selectedReasonIndex]}`,
      });
      await requestToDeleteAccount();
      await logout();

      toast.show('Thành công', {
        duration: 1000,
        placement: 'bottom',
        type: 'success',
      });
      navigation.navigate('MainTabs', {screen: 'Account'});
    } catch (error) {}
  }, [selectedReasonIndex]);

  return (
    <View style={{paddingHorizontal: 12, marginVertical: theme.spacing.l}}>
      <Card rounded title="Xóa tài khoản này?">
        <Text style={[theme.globalStyles.fontBold, theme.globalStyles.mb3]}>Bạn có chắc chắn muốn gửi yêu cầu để xóa tài khoản này?</Text>
        <Text style={[theme.globalStyles.mb3]}>Những gì sẽ xảy ra khi bạn xóa tài khoản:</Text>
        <Text style={[theme.globalStyles.mb3]}>- Tài khoản không thể mở lại</Text>
        <Text style={[theme.globalStyles.mb3]}>- Nếu tài khoản của bạn đang có số dư, vui lòng liên hệ với Thị Trường Sỉ để thanh toán.</Text>
        <Text style={[theme.globalStyles.mb3]}>- TTS Dropship vẫn sẽ lưu trữ các thông tin giao dịch liên quan đến tài khoản của bạn để kiểm kê</Text>
        <Text style={[theme.globalStyles.mb3]}>- Bạn sẽ không thể đăng nhập lại vào tài khoản này để xem lịch sử giao dịch, lịch sử đơn hàng</Text>
      </Card>

      <Button
        onPress={handleConfirmModalOpen}
        size="sm"
        disabled={buttonDisabledSecond !== 0}
        color={theme.colors.red}
        buttonStyle={{borderRadius: 100, paddingHorizontal: 20}}
        containerStyle={{marginTop: theme.spacing.l, flexDirection: 'row', justifyContent: 'center'}}
        title={`Xóa tài khoản${buttonDisabledSecond !== 0 ? ` (${buttonDisabledSecond})` : ''}`}
      />

      <Dialog isVisible={confirmModalOpen}>
        <Dialog.Title title="Lý do xóa tài khoản" />
        <View>
          <Text>Chúng tôi rất tiếc vì chưa phục vụ bạn tốt. Vui lòng cho chúng tôi biết lý do bạn xóa tài khoản nhé.</Text>
          {reasons.map((reason, index) => {
            return (
              <CheckBox
                key={index}
                title={reason}
                iconType="ionicon"
                uncheckedIcon="square-outline"
                checkedIcon="checkbox"
                checkedColor="#008060"
                uncheckedColor={theme.colors.text}
                containerStyle={{backgroundColor: 'white', borderWidth: 0}}
                textStyle={styles.checkBoxTitle}
                checked={selectedReasonIndex === index}
                onPress={() => handleCheck(index)}
              />
            );
          })}
        </View>

        <Dialog.Actions>
          <Dialog.Button title="TRỞ VỀ" titleStyle={{color: theme.colors.textLight}} onPress={() => setConfirmModalOpen(false)} />
          <Dialog.Button title="XÓA TÀI KHOẢN" titleStyle={{color: theme.colors.red}} loading={isLoggingOut} onPress={handleDeleteConfirm} />
        </Dialog.Actions>
      </Dialog>
    </View>
  );
};

const styles = StyleSheet.create({
  checkBoxTitle: {
    fontSize: theme.typography.base,
    fontWeight: '400',
    color: theme.colors.text,
  },
});

export default AccountDeletion;
