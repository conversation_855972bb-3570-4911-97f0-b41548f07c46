import {Button} from '@rneui/themed';
import React, {useCallback} from 'react';
import {ScrollView, View} from 'react-native';
import BalanceOverview from '~components/shared/BalanceOverview';
import theme from '~utils/config/themes/theme';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import PaymentHistory from '~components/by-screens/AccountAndPayment/PaymentHistory';
import {SafeAreaView} from 'react-native-safe-area-context';
import BankAccounts from '~components/by-screens/AccountAndPayment/BankAccounts';
import EscrowPaymentHistory from '~components/by-screens/AccountAndPayment/EscrowPaymentHistory';

const AccountAndPayment: React.FC<NativeStackScreenProps<RootStackParamList, 'AccountAndPayment'>> = ({navigation}) => {
  const handleWithdraw = useCallback(() => {
    navigation.navigate('Withdraw');
  }, []);

  return (
    <SafeAreaView style={{flex: 1}} edges={['right', 'left', 'bottom']}>
      <ScrollView style={{flex: 1, paddingHorizontal: theme.spacing.m, paddingVertical: 12}}>
        <BalanceOverview
          primaryAction={
            <Button
              onPress={handleWithdraw}
              title={'Rút tiền'}
              size="sm"
              buttonStyle={{borderRadius: 30, paddingVertical: 4}}
              titleStyle={{fontSize: theme.typography.md}}
              containerStyle={{marginLeft: 'auto'}}
              icon={{
                type: 'ionicon',
                name: 'arrow-down',
                color: '#fff',
                size: 16,
              }}
            />
          }
        />

        <BankAccounts />

        <EscrowPaymentHistory />

        <PaymentHistory />

        <View style={{height: 50}} />
      </ScrollView>
    </SafeAreaView>
  );
};

export default AccountAndPayment;
