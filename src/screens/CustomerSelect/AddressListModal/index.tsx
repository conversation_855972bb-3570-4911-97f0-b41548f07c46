import {Text} from '@rneui/themed';
import React from 'react';
import Modal from 'react-native-modal';

type AddressListModalProps = {
  open: boolean;
  onClose: () => void;
  customerId: string;
};

const AddressListModal: React.FC<AddressListModalProps> = props => {
  return (
    <Modal isVisible={props.open} onBackButtonPress={props.onClose} onBackdropPress={props.onClose}>
      <Text>OK</Text>
    </Modal>
  );
};

export default AddressListModal;
