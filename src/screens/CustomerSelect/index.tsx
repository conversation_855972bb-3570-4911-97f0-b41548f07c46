import {Button} from '@rneui/base';
import {Input, Text} from '@rneui/themed';
import {FlashList, ListRenderItemInfo} from '@shopify/flash-list';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {ActivityIndicator, View} from 'react-native';
import CustomerItem from '~components/by-screens/CreateCustomer/CustomerItem';
import {useDebounce, useNavigation} from '~hooks';
import useCreateOrderStore from '~hooks/store/useCreateOrderStore';
import {useGetCustomersInfinite} from '~utils/api/customer';
import theme from '~utils/config/themes/theme';
import ee, {EventNames} from '~utils/events';
import Address from '~utils/types/address';
import Customer from '~utils/types/customer';
import AddressListBottomSheet from './AddressListBottomSheet';

const CustomerSelectScreen = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const {data, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage} = useGetCustomersInfinite({
    query: debouncedSearchQuery,
  });
  const navigation = useNavigation();
  const orderShippingAddress: Address | undefined = useCreateOrderStore(state => state.shipping_address);
  const setNewOrderState = useCreateOrderStore(state => state.setNewOrderState);
  const [addressListModalOpen, setAddressListModalOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);

  useEffect(() => {
    ee.on(EventNames.OpenAddressList, handleOpenAddressList);
    return () => {
      ee.off(EventNames.OpenAddressList, handleOpenAddressList);
    };
  }, []);

  const handleOpenAddressList = useCallback((customer: Customer) => {
    setSelectedCustomer(customer);
    setAddressListModalOpen(true);
  }, []);

  const handleCreateCustomer = useCallback(() => {
    navigation.navigate('CreateCustomer', {isCreateOrder: true});
  }, []);

  const handleCreateCustomerAddress = useCallback((customer: Customer) => {
    setAddressListModalOpen(false);
    navigation.navigate('CreateCustomerAddress', {customerId: customer.id, isCreateOrder: true});
  }, []);

  const handleSelect = useCallback(
    (customer: Customer, address?: Address) => {
      if (setNewOrderState) {
        setNewOrderState('shipping_address', address || customer.default_address);
      }
      setTimeout(() => {
        navigation.popTo('OrderConfirm');
      }, 200);
    },
    [selectedCustomer],
  );

  const topItems = useMemo(
    () => [
      <View style={{marginHorizontal: 8, marginTop: 40}}>
        <Button title="Tạo khách hàng mới" color={'#008060'} onPress={handleCreateCustomer} buttonStyle={{borderRadius: 30}} />
        <Text style={{marginTop: theme.spacing.l, marginBottom: theme.spacing.s}}>Gần đây</Text>
      </View>,
      (data?.pages[0].total ?? 0) > 10 || searchQuery ? (
        <Input
          renderErrorMessage={false}
          containerStyle={{
            paddingHorizontal: 8,
            // paddingVertical: 8,
            marginBottom: theme.spacing.m,
            backgroundColor: 'rgb(242, 242, 242)',
          }}
          inputContainerStyle={{
            backgroundColor: theme.colors.gray20,
            borderWidth: 1,
            // borderBottomWidth: 0,
            borderColor: theme.colors.gray20,
          }}
          placeholder="Tìm khách hàng"
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      ) : null,
    ],
    [searchQuery, data],
  );

  const dataFlat = useMemo(() => {
    return [...topItems, ...(data?.pages.flatMap(page => page.customers) ?? [])] ?? topItems;
  }, [data, topItems, searchQuery, orderShippingAddress]);

  const renderItem = useCallback(
    (item: ListRenderItemInfo<Customer>) => {
      if (item.index < topItems.length) {
        return item.item;
      }
      return <CustomerItem selectedAddress={orderShippingAddress} selected={orderShippingAddress?.customer_id === item.item.id} customer={item.item} onSelect={handleSelect} />;
    },
    [data, orderShippingAddress, selectedCustomer, topItems],
  );

  const handleEndReached = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  return (
    <View style={[{flex: 1}, theme.globalStyles.paddingH12]}>
      <FlashList
        showsVerticalScrollIndicator={false}
        data={dataFlat as any}
        ListEmptyComponent={
          <>
            {!isLoading ? (
              <Text style={{marginTop: theme.spacing.l, textAlign: 'center', color: theme.colors.textLight}}>Không có khách hàng nào</Text>
            ) : (
              <View>
                <ActivityIndicator color={'#008060'} />
              </View>
            )}
          </>
        }
        ListFooterComponent={
          isFetchingNextPage ? (
            <View style={{flexDirection: 'row', justifyContent: 'center'}}>
              <ActivityIndicator color={theme.colors.text} />
              <Text style={{marginLeft: 8}}>Đang tải thêm kết quả</Text>
            </View>
          ) : isLoading ? (
            <Text style={{textAlign: 'center', marginTop: theme.spacing.m}}>Đang tải...</Text>
          ) : null
        }
        estimatedItemSize={101}
        renderItem={renderItem as any}
        onEndReached={handleEndReached}
      />

      <AddressListBottomSheet
        onSelect={handleSelect}
        open={addressListModalOpen}
        onClose={() => setAddressListModalOpen(false)}
        customer={selectedCustomer}
        onCreateNewAddress={handleCreateCustomerAddress}
        selectedAddressId={orderShippingAddress?.id}
      />
    </View>
  );
};

export default CustomerSelectScreen;
