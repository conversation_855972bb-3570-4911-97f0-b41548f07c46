import {BottomSheetBackdrop, BottomSheetFlatList, BottomSheetModal} from '@gorhom/bottom-sheet';
import {Button} from '@rneui/themed';
import produce from 'immer';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {ListRenderItemInfo} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useSetAddressDefaultMutation} from '~utils/api/customer';
import theme from '~utils/config/themes/theme';
import Address from '~utils/types/address';
import Customer from '~utils/types/customer';
import AddressItem from './AddressItem';
import {useReducedMotion} from 'react-native-reanimated';

export type AddressListBottomSheetProps = {
  open: boolean;
  onClose: () => void;
  customer: Customer | null;
  selectedAddressId: string | undefined;
  onSelect: (customer: Customer, address: Address) => void;
  onCreateNewAddress: (customer: Customer) => void;
};

const AddressListBottomSheet: React.FC<AddressListBottomSheetProps> = props => {
  const insets = useSafeAreaInsets();
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  const mutation = useSetAddressDefaultMutation();
  const snapPoints = useMemo(() => ['50%', '95%'], []);
  const [addresses, setAddresses] = useState(props.customer?.addresses ?? []);

  const renderBackdrop = useCallback((bsProps: any) => <BottomSheetBackdrop {...bsProps} disappearsOnIndex={-1} appearsOnIndex={1} />, []);
  const reducedMotion = useReducedMotion();

  useEffect(() => {
    if (props.open) {
      bottomSheetRef.current?.present();
    } else {
      bottomSheetRef.current?.close();
    }
  }, [props.open]);

  useEffect(() => {
    if (props.customer?.addresses) {
      setAddresses(props.customer.addresses);
    }
  }, [props.customer]);

  const handleAddressSelect = useCallback(
    (address: Address) => {
      if (props.customer) {
        props.onSelect(props.customer, address);
      }
    },
    [props.customer],
  );

  const handleCreateNewAddressPress = useCallback(() => {
    props.onClose();
    setTimeout(() => {
      if (props.customer) {
        props.onCreateNewAddress(props.customer);
      }
    }, 300);
  }, [props.customer]);

  const handleSetAddressDefault = useCallback(
    async (address: Address) => {
      await mutation.mutateAsync({addressId: address.id, customerId: address.customer_id});
      setAddresses(prev =>
        produce(prev, draft => {
          const currentDefault = draft.findIndex(add => add.default);
          if (currentDefault !== -1) {
            draft[currentDefault].default = false;
          }
          const addrIndex = draft.findIndex(add => add.id === address.id);
          if (addrIndex !== -1) {
            draft[addrIndex].default = true;
          }
        }),
      );
    },
    [addresses],
  );

  const handleDeleteAddress = useCallback(async (_address: Address) => {
    // TODO:
  }, []);

  const renderItem = useCallback(
    (item: ListRenderItemInfo<Address>) => {
      return (
        <AddressItem address={item.item} onSelect={handleAddressSelect} selected={props.selectedAddressId === item.item.id} onSetDefault={handleSetAddressDefault} onDelete={handleDeleteAddress} />
      );
    },
    [props.customer, props.selectedAddressId],
  );

  return (
    <>
      <BottomSheetModal
        animateOnMount={!reducedMotion}
        backdropComponent={renderBackdrop}
        ref={bottomSheetRef}
        index={0}
        snapPoints={snapPoints}
        enablePanDownToClose
        onDismiss={props.onClose}
        enableDismissOnClose
        handleComponent={() => (
          <>
            <Button
              size="sm"
              containerStyle={{alignSelf: 'flex-end', marginRight: 4}}
              type="clear"
              titleStyle={{fontSize: theme.typography.md}}
              icon={{type: 'ionicon', name: 'add', color: theme.colors.primary}}
              onPress={handleCreateNewAddressPress}>
              Thêm địa chỉ
            </Button>
          </>
        )}>
        <BottomSheetFlatList keyboardShouldPersistTaps="handled" data={addresses ?? []} renderItem={renderItem} contentContainerStyle={{paddingBottom: insets.bottom}} />
      </BottomSheetModal>
    </>
  );
};

export default AddressListBottomSheet;
