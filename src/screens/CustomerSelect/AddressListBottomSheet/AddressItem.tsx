import {But<PERSON>, <PERSON>B<PERSON>, Dialog, Text} from '@rneui/themed';
import React, {useCallback, useMemo, useState} from 'react';
import {Pressable, StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import Address from '~utils/types/address';
import {patternFormatter} from 'react-number-format';

type AddressItemProps = {
  selected: boolean;
  address: Address;
  onSelect?: (address: Address) => void;
  onSetDefault?: (address: Address) => void;
  onDelete?: (address: Address) => void;
};

const AddressItem: React.FC<AddressItemProps> = props => {
  const [deleteConfirmDialogOpen, setDeleteConfirmDialogOpen] = useState(false);

  const handleCloseDialog = useCallback(() => {
    setDeleteConfirmDialogOpen(false);
  }, []);

  // const handleDeletePress = useCallback(() => {
  //   setDeleteConfirmDialogOpen(true);
  // }, []);

  const formatedPhone = useMemo(() => {
    return patternFormatter(props.address.phone, {
      format: '#### ### ###',
      allowEmptyFormatting: true,
    });
  }, [props.address]);

  return (
    <>
      <Pressable
        style={styles.container}
        onPress={() => {
          if (props.onSelect) {
            props.onSelect(props.address);
          }
        }}>
        {props.onSelect ? (
          <CheckBox
            size={28}
            checked={props.selected}
            onPress={() => props.onSelect?.(props.address)}
            iconType="ionicon"
            uncheckedIcon="radio-button-off-outline"
            checkedIcon="checkmark-circle"
            checkedColor="#008060"
            containerStyle={{margin: 0, padding: 0, backgroundColor: 'transparent'}}
            style={{marginHorizontal: 0}}
          />
        ) : null}
        <View
          style={{
            flex: 1,
            marginLeft: theme.spacing.s,
            borderBottomWidth: 1,
            borderBottomColor: theme.colors.gray30,
            paddingVertical: theme.spacing.s,
          }}>
          {props.address.default ? (
            <Text style={styles.defaultText}>Địa chỉ mặc định</Text>
          ) : (
            <Button
              type="clear"
              containerStyle={{alignSelf: 'flex-end'}}
              size="sm"
              titleStyle={{fontSize: theme.typography.sm, color: theme.colors.textLight}}
              onPress={() => {
                if (props.onSetDefault) {
                  props.onSetDefault(props.address);
                }
              }}>
              Đặt làm mặc định
            </Button>
          )}
          <View style={[theme.globalStyles.flexRow]}>
            <View style={{flex: 1}}>
              <Text>{[props.address.first_name, props.address.last_name].filter(Boolean).join(' ')}</Text>
              <Text style={styles.textSecondary}>{formatedPhone}</Text>
              <Text style={styles.textSecondary}>{[props.address.address1, props.address.ward, props.address.province, props.address.city].join(', ')}</Text>
            </View>
            <View>
              {/* <Button
                type="clear"
                onPress={handleDeletePress}
                icon={{type: 'ionicon', name: 'trash-outline', size: theme.typography.md, color: theme.colors.text}}
                buttonStyle={{paddingHorizontal: 8}}
              /> */}
            </View>
          </View>
        </View>
      </Pressable>
      <Dialog isVisible={deleteConfirmDialogOpen} onBackdropPress={handleCloseDialog}>
        <Dialog.Title title="Xóa địa chỉ này?" />
        <Dialog.Actions>
          <Dialog.Button
            title={'XÓA'}
            titleStyle={{color: theme.colors.red}}
            onPress={() => {
              if (props.onDelete) {
                props.onDelete(props.address);
              }
            }}
          />
          <Dialog.Button title="KHÔNG" onPress={handleCloseDialog} titleStyle={{color: theme.colors.textLight, fontWeight: 'bold'}} />
        </Dialog.Actions>
      </Dialog>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: theme.spacing.m,
    flexDirection: 'row',
    alignItems: 'center',
  },
  textSecondary: {
    color: theme.colors.textLight,
    fontSize: theme.typography.base,
    marginTop: theme.spacing.xs,
  },
  defaultText: {
    fontSize: theme.typography.sm,
    color: theme.colors.blue,
    fontStyle: 'italic',
    textAlign: 'right',
  },
});

export default AddressItem;
