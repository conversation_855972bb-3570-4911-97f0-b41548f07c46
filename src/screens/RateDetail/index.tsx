import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Button, Text} from '@rneui/themed';
import React, {useCallback, useEffect} from 'react';
import {ActivityIndicator, Pressable, ScrollView, StyleSheet, View} from 'react-native';
import {useGetOrder} from '~utils/api/order';
import {useGetRatingDetail} from '~utils/api/review';
import theme from '~utils/config/themes/theme';
import {handleImageDomain} from '~utils/helpers/image';
import {Rating} from 'react-native-ratings';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import ReviewItem from '~components/by-screens/ProductDetail/Reviews/ReviewItem';
import {QuickImage} from '~components/shared/QuickImage';
import IoniconsRating from '~components/shared/IoniconsRating';

const RateDetail: React.FC<NativeStackScreenProps<RootStackParamList, 'RateDetail'>> = ({route, navigation}) => {
  const {data} = useGetRatingDetail(route.params.actionId, 'order');
  const {data: order} = useGetOrder(Number(route.params.actionId));

  useEffect(() => {
    if (data?.can_edit) {
      navigation.setOptions({
        headerRight: () => (
          <Button size="sm" type="clear" titleStyle={{fontSize: theme.typography.base}} onPress={() => navigation.push('UpdateRating', {orderId: data.action_id, isUpdate: true})}>
            Chỉnh sửa
          </Button>
        ),
      });
    }
  }, [data?.can_edit]);

  const handleLineItemPress = useCallback((productId?: string) => {
    if (productId) {
      navigation.push('ProductDetail', {productId: productId});
    }
  }, []);

  if (!data) {
    return <ActivityIndicator />;
  }

  return (
    <ScrollView>
      <View style={[styles.rateUnitContainer, {borderBottomColor: theme.colors.gray20, borderBottomWidth: 1}]}>
        <Text style={{fontWeight: 'bold', fontSize: theme.typography.base}}>Dịch vụ nhà cung cấp</Text>
        <IoniconsRating value={data?.seller_service} size={20} />
      </View>
      <View style={[styles.rateUnitContainer, {marginBottom: theme.spacing.m}]}>
        <Text style={{fontWeight: 'bold', fontSize: theme.typography.base}}>Dịch vụ vận chuyển</Text>
        <IoniconsRating value={data?.delivery_service} size={20} />
      </View>

      {data?.line_items.map(lineItem => {
        const orderLineItem = order?.line_items.find(item => item.variant_id === lineItem.variant_id);
        return (
          <View style={styles.orderLineItemContainer} key={lineItem.variant_id}>
            <Pressable onPress={() => handleLineItemPress(orderLineItem?.product_id)} style={styles.orderLineItem}>
              <QuickImage source={{uri: handleImageDomain(orderLineItem?.image_src ?? '')}} style={{width: 40, height: 40, borderRadius: theme.spacing.xs}} />
              <View style={{marginLeft: theme.spacing.s, flex: 1}}>
                <Text style={{fontSize: theme.typography.base}} numberOfLines={2}>
                  {orderLineItem?.title}
                </Text>
                <Text style={{fontSize: theme.typography.base}}>{['default', 'default title'].includes(orderLineItem?.variant_title?.toLowerCase() ?? '') ? '' : orderLineItem?.variant_title}</Text>
              </View>
            </Pressable>
            <ReviewItem rateLineItem={lineItem} createdAt={data.created_at} />
          </View>
        );
      })}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  rateUnitContainer: {
    backgroundColor: theme.colors.white,
    padding: theme.spacing.m,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  orderLineItemContainer: {
    marginTop: theme.spacing.s,
    paddingHorizontal: theme.spacing.m,
    paddingBottom: theme.spacing.m,
    backgroundColor: theme.colors.white,
  },
  orderLineItem: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    paddingVertical: theme.spacing.s,
    borderBottomColor: theme.colors.gray20,
  },
});

export default RateDetail;
