import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React, {useCallback} from 'react';
import {View, ScrollView} from 'react-native';
import OrderLineItem from '~components/by-screens/OrderLineItem';
import {useGetFulfillmentEvents, useGetOrder} from '~utils/api/order';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import dayjs from 'dayjs';
import VerticalStepper from '~components/shared/VerticalStepper';
import {Button} from '@rneui/base';
import Clipboard from '@react-native-clipboard/clipboard';
import {useToast} from 'react-native-toast-notifications';
import {OrderFulfillmentEvent} from '~utils/types/order';
import {Text} from '@rneui/themed';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {getOrderBeforeFulfillmentEvents} from '~utils/helpers/order';
import theme from '~utils/config/themes/theme';
import useRefreshOnFocus from '~hooks/useRefreshOnFocus';
import PhoneHyperLink from '~utils/helpers/phone-hyperlink';

const FulfillmentEventsScreen: React.FC<NativeStackScreenProps<RootStackParamList, 'FulfillmentEvents'>> = ({route}) => {
  const {data: fulfillmentEvents, refetch: refetchFulfillments} = useGetFulfillmentEvents(route.params.orderId);
  const {data: order, refetch: refetchOrder} = useGetOrder(route.params.orderId);
  const toast = useToast();
  const insets = useSafeAreaInsets();
  useRefreshOnFocus(refetchOrder);
  useRefreshOnFocus(refetchFulfillments);

  const steps = [
    ...(fulfillmentEvents ?? []).map(fulfillmentEvent => ({
      title: fulfillmentEvent.status_text,
      description: <FulfillmentEventDescription event={fulfillmentEvent} />,
      isActive: false,
    })),
    ...getOrderBeforeFulfillmentEvents(order).map(event => ({
      title: event.title,
      description: (
        <View style={{flex: 1}}>
          {Boolean(event.happened_at) && <Text style={{color: theme.colors.textLight, fontSize: theme.typography.sm}}>{dayjs(event.happened_at).format('DD-MM-YYYY HH:mm')}</Text>}
          {Boolean(event.message) && <Text style={{color: theme.colors.textLight, fontSize: theme.typography.base, marginTop: 8}}>{event.message}</Text>}
        </View>
      ),
    })),
  ];

  const fulfillment = order?.fulfillments?.[0];

  const isExistLadingCode = fulfillment?.shipping_order_code && fulfillment?.tracking_number;

  const handleCopy = useCallback(() => {
    Clipboard.setString([fulfillment?.shipping_order_code, fulfillment?.tracking_number].filter(Boolean).join(' - '));
    toast.show('Đã sao chép', {placement: 'center', duration: 1000});
  }, [fulfillment, isExistLadingCode]);

  return (
    <ScrollView style={{flex: 1, paddingBottom: insets.bottom + 20}}>
      <View style={{backgroundColor: '#fff', paddingVertical: 20}}>
        {order?.line_items.map(lineItem => (
          <View key={lineItem.id} style={{paddingHorizontal: 12, paddingVertical: 20, borderBottomColor: 'rgba(0,0,0,.07)', borderBottomWidth: 1}}>
            <OrderLineItem key={lineItem.id} lineItem={lineItem} />
          </View>
        ))}
      </View>
      <View style={{marginTop: 20, paddingHorizontal: 12, paddingVertical: 16, backgroundColor: '#fff'}}>
        <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: theme.spacing.s}}>
          <Text style={{fontSize: theme.typography.base, flexShrink: 0, marginRight: 10}}>Nhận hàng dự kiến</Text>
          <Text style={{textAlign: 'right', fontSize: theme.typography.base, color: theme.colors.textLight}}>
            {fulfillment?.estimated_delivery_at ? dayjs(fulfillment.estimated_delivery_at).format('DD-MM-YYYY HH:mm (dddd)') : 'Chưa có'}
          </Text>
        </View>
        <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: theme.spacing.s}}>
          <Text style={{fontSize: theme.typography.base, flexShrink: 0, marginRight: 10}}>Vận chuyển bởi</Text>
          <Text style={{textAlign: 'right', fontSize: theme.typography.base, color: theme.colors.textLight}}>
            {(order?.shipping_lines ?? []).map(shippingLine => shippingLine.title.split(' - ')[0]).join(', ')}
          </Text>
        </View>

        <View
          style={{
            flexDirection: 'row',
            alignItems: 'flex-start',
            justifyContent: 'space-between',
            paddingBottom: 16,
            marginBottom: theme.spacing.m,
            borderBottomWidth: 1,
            borderBottomColor: 'rgba(0,0,0,.07)',
          }}>
          <Text style={{fontSize: theme.typography.base, flexShrink: 0, marginRight: 20}}>Mã vận đơn</Text>
          <View style={{alignItems: 'flex-end', flexShrink: 1}}>
            <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight}}>
              {isExistLadingCode
                ? fulfillment.shipping_order_code === fulfillment.tracking_number
                  ? fulfillment.tracking_number
                  : [fulfillment.shipping_order_code, fulfillment.tracking_number].join(' - ')
                : 'Chưa có'}
            </Text>
            {isExistLadingCode && <Button onPress={handleCopy} title={'SAO CHÉP'} type="clear" titleStyle={{fontSize: theme.typography.base}} />}
          </View>
        </View>
        <VerticalStepper steps={steps} destructive={order?.status === 'cancelled'} />
      </View>
    </ScrollView>
  );
};

export const FulfillmentEventDescription: React.FC<{event: OrderFulfillmentEvent}> = props => (
  <>
    {(Boolean(props.event.happened_at) || Boolean(props.event.created_at)) && (
      <Text style={{color: theme.colors.textLight, fontSize: theme.typography.sm}}>{dayjs(props.event.happened_at || props.event.created_at).format('DD-MM-YYYY HH:mm')}</Text>
    )}
    {Boolean(props.event.message) && (
      <PhoneHyperLink linkStyle={{color: theme.colors.blue}}>
        <Text style={{color: theme.colors.textLight, marginTop: 8, fontSize: theme.typography.base}}>{props.event.message}</Text>
      </PhoneHyperLink>
    )}
  </>
);

export default FulfillmentEventsScreen;
