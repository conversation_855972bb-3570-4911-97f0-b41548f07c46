import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Button, Text} from '@rneui/themed';
import React, {useCallback, useMemo} from 'react';
import {Image, StyleSheet, View} from 'react-native';
import Hyperlink from 'react-native-hyperlink';
import theme from '~utils/config/themes/theme';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Clipboard from '@react-native-clipboard/clipboard';
import {useToast} from 'react-native-toast-notifications';
import {SafeAreaView} from 'react-native-safe-area-context';
// @ts-ignore
import LogoTTSDropship from '~assets/logo-dropship-reverse-icon.png';
import {useGetCurrentUser} from '~utils/api/auth';
import {useAuthActions} from '~hooks';

const AccountDisabled: React.FC<NativeStackScreenProps<RootStackParamList, 'AccountDisabled'>> = () => {
  const toast = useToast();
  const {data: user} = useGetCurrentUser();
  const {logout} = useAuthActions();
  const contactEmail = useMemo(() => '<EMAIL>', []);

  const handleCopyContactEmail = useCallback(() => {
    Clipboard.setString(contactEmail);
    toast.show('Đã sao chép', {
      duration: 1000,
      placement: 'center',
    });
  }, [contactEmail]);

  return (
    <SafeAreaView style={styles.container}>
      <View style={{alignSelf: 'flex-end'}}>
        <Button type="clear" titleStyle={{color: theme.colors.text}} onPress={logout}>
          Đăng xuất
        </Button>
      </View>
      <View style={{alignSelf: 'center', marginTop: 100}}>
        <Image source={LogoTTSDropship} style={{width: 100, height: 100}} />
      </View>
      <Text style={styles.headerTitle}>Chưa thể tham gia Dropship</Text>
      <Text style={styles.text}>Tài khoản của bạn chưa được phê duyệt để tham gia dropship.</Text>

      {Boolean(user?.dropship_disallow_reason) && <Text style={styles.text}>Lý do: {user?.dropship_disallow_reason}</Text>}

      <Hyperlink linkDefault linkStyle={{color: theme.colors.text, textDecorationColor: theme.colors.text, textDecorationLine: 'underline'}}>
        <Text>
          Nếu có bất kì thắc mắc, vui lòng gửi email đến địa chỉ: <Text><EMAIL></Text> <Ionicons name="copy" size={16} color={theme.colors.textLight} onPress={handleCopyContactEmail} />
        </Text>
      </Hyperlink>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.white,
    paddingHorizontal: theme.spacing.l,
  },
  headerTitle: {
    textAlign: 'center',
    fontSize: theme.typography.lg,
    marginVertical: theme.spacing.l,
    fontWeight: 'bold',
    marginTop: 50,
  },
  text: {
    // textAlign: 'center',
    marginBottom: theme.spacing.s,
  },
});

export default AccountDisabled;
