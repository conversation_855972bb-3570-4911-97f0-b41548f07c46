import React from 'react';
import {useIsMobile} from '~hooks';
import CategoryDetail, {CategoryDetailProps} from './CategoryDetail';
import CategoryDetailLg from './CategoryDetailLg';

const CategoryDetailWeb: React.FC<CategoryDetailProps> = props => {
  const isMobile = useIsMobile();
  if (isMobile) {
    return <CategoryDetail {...props} />;
  }
  return <CategoryDetailLg {...props} />;
};

export default CategoryDetailWeb;
