import React, {useCallback, useEffect, useMemo, useRef} from 'react';
import {ActivityIndicator, FlatList, Text, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import {useSearchProductsInfinite} from '~utils/api/product';
import {SearchProduct} from '~utils/types/product';
import ProductCard from '~components/shared/ProductCard';
import ListEmptyProduct from '~components/shared/ListEmptyProduct';
import useSort from '~hooks/useSort';
import {useGetCategory} from '~utils/api/category';
import RelativeCategories from '~components/by-screens/CategoryDetail/RelativeCategories';
import SortBar from '~components/by-screens/CategoryDetail/SortBar';
import {ListRenderItemInfo} from '@shopify/flash-list';
import {Helmet} from 'react-helmet';
import {env} from '~utils/config';
import {handleImageDomain} from '~utils/helpers/image';
import ScrollToTop from '~components/shared/ScrollToTop';
import {useExtendContainerWidth} from '~components/shared/web/WebContainer';
import {ScreenWidth} from '@rneui/base';
import {CategoryDetailProps} from './CategoryDetail';

const CategoryDetail: React.FC<CategoryDetailProps> = ({route, navigation}) => {
  const flatListRef = useRef<FlatList>(null);
  useExtendContainerWidth('lg');

  const {sortState, updateSort} = useSort({
    sort_by: route.params.sort_by || 'up_at',
    ascending: ['true', 'false'].includes(route.params.ascending as any) ? (route.params.ascending === 'true' ? true : false) : undefined,
  });
  const {data: category} = useGetCategory(route.params.categoryId);
  const {data, fetchNextPage, isFetchingNextPage, isLoading} = useSearchProductsInfinite(
    {...route.params, sort_by: sortState.sort_by, ascending: sortState.ascending, [`filter_category_lv${category?.category.level}`]: category?.category.id},
    {
      enabled: Boolean(category),
    },
  );

  const seoTitle = useMemo(() => {
    if (category?.category) {
      return `Tuyển CTV bán ${category.category.title} kiếm thu nhập tốt`;
    }

    return '';
  }, [category?.category]);

  useEffect(() => {
    navigation.setOptions({
      title: category?.category ? seoTitle : '',
      headerTitle: category?.category?.title,
    });
  }, [navigation, category?.category]);

  const topItems = useMemo(
    () => [
      <RelativeCategories relativeCategories={category?.relative_categories ?? []} />,
      <View />,
      <View />,
      <View />,
      <View style={{marginHorizontal: -4, flex: 1}}>
        <SortBar sortState={sortState} onChange={updateSort} />
      </View>,
      <View />,
      <View />,
      <View />,
    ],
    [category, sortState],
  );

  const dataFlat = useMemo(() => {
    return [...topItems, ...(data?.pages.flatMap(page => page.products) ?? [])] ?? topItems;
  }, [data, category]);

  const renderItem = useCallback(
    (item: ListRenderItemInfo<SearchProduct>) => {
      if (item.index < topItems.length) {
        return item.item;
      }
      return <ProductCard product={item.item} />;
    },
    [category],
  );

  const handleEndReached = () => {
    if (!isFetchingNextPage) {
      fetchNextPage();
    }
  };

  const canonicalURL = useMemo(() => {
    if (category?.category.category_id) {
      return `${env.WEBVIEW_URL}/cat/${category.category.slug}/${category.category.category_id}`;
    }
    return '';
  }, [category?.category]);

  const ogImage = useMemo(() => {
    let firstProduct = data?.pages?.[0]?.products?.[0];
    if (firstProduct) {
      return handleImageDomain(firstProduct.image_thumb);
    }

    return '';
  }, [data]);

  const ogDescription = useMemo(() => {
    if (category?.category) {
      return `Trở thành cộng tác viên bán hàng ${category.category.title} thật nhanh chóng dễ dàng, lợi nhuận cao, thu nhập tốt.`;
    }

    return '';
  }, [category?.category]);

  const handleScrollToTop = useCallback(() => {
    if (flatListRef.current) {
      flatListRef.current.scrollToOffset({offset: 0, animated: true});
    }
  }, []);

  const numColumns = useMemo(() => {
    return ScreenWidth >= 992 ? 4 : 2;
  }, []);

  return (
    <>
      {/* @ts-ignore */}
      <Helmet>
        <meta charSet="UTF-8" />
        {ogDescription ? <meta name="description" content={ogDescription} /> : null}
        {ogDescription ? <meta property="og:description" content={ogDescription} /> : null}
        {seoTitle ? <meta property="og:title" content={seoTitle + ' - TTS Dropship'} /> : null}
        <meta property="og:site_name" content="Thị Trường Sỉ" />
        <meta property="og:locale" content="vi-VN" />
        <meta property="og:type" content="website" />
        {canonicalURL ? <meta property="og:url" content={canonicalURL} /> : null}
        {canonicalURL ? <link rel="canonical" href={canonicalURL} /> : null}
        {ogImage ? <meta property="og:image" content={ogImage} /> : null}
      </Helmet>
      <View style={[{flex: 1}, !isLoading && dataFlat.length === topItems.length && {backgroundColor: theme.colors.white}]}>
        <FlatList
          ref={flatListRef}
          data={dataFlat}
          renderItem={renderItem as any}
          numColumns={numColumns}
          stickyHeaderIndices={[1]}
          onEndReached={handleEndReached}
          columnWrapperStyle={{paddingHorizontal: theme.spacing.m - 8}}
          ListFooterComponent={
            isFetchingNextPage ? (
              <View style={{flexDirection: 'row', justifyContent: 'center'}}>
                <ActivityIndicator color={theme.colors.primary} />
                <Text style={{marginLeft: 8}}>Đang tải thêm kết quả</Text>
              </View>
            ) : dataFlat.length === topItems.length && !isLoading ? (
              <ListEmptyProduct content="Danh mục này hiện chưa có sản phẩm nào, bạn hãy quay lại sau nhé!" />
            ) : null
          }
        />
        <ScrollToTop onPress={handleScrollToTop} />
      </View>
    </>
  );
};

export default CategoryDetail;
