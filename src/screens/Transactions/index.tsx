import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Text} from '@rneui/themed';
import React, {useCallback, useMemo} from 'react';
import {StyleSheet, View} from 'react-native';
import {useGetAvailableTransactionTypes} from '~utils/api/wallet';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import {NavigationState, SceneRendererProps, TabBar, TabView} from 'react-native-tab-view';
import TransactionList from '~components/by-screens/Transactions/TransactionList';

type State = NavigationState<{
  key: string;
  title: string;
}>;

const Transactions: React.FC<NativeStackScreenProps<RootStackParamList, 'Transactions'>> = () => {
  const {data: availableTypes} = useGetAvailableTransactionTypes();
  const [index, setIndex] = React.useState(0);

  const routes = useMemo(() => {
    return [{key: 'all', title: 'T<PERSON>t cả'}, ...Object.keys(availableTypes ?? {}).map(key => ({key: key, title: (availableTypes ?? {})[key]}))];
  }, [availableTypes]);

  const renderTabBar = (tabbarProps: SceneRendererProps & {navigationState: State}) => (
    <TabBar
      {...tabbarProps}
      scrollEnabled
      indicatorStyle={styles.indicator}
      style={styles.tabbar}
      tabStyle={styles.tab}
      renderLabel={scene => (
        <View>
          <Text numberOfLines={1} style={{color: scene.focused ? '#008060' : '#333', paddingHorizontal: 4}}>
            {scene.route.title}
          </Text>
        </View>
      )}
    />
  );

  const renderScene = useCallback(
    ({route}: any) => {
      return <TransactionList type={route.key} />;
    },
    [availableTypes],
  );

  const handleIndexChange = useCallback((newIndex: number) => {
    setIndex(newIndex);
  }, []);

  return <TabView lazy renderTabBar={renderTabBar} navigationState={{index, routes}} renderScene={renderScene} onIndexChange={handleIndexChange} />;
};

const styles = StyleSheet.create({
  tabbar: {
    backgroundColor: '#fff',
    borderBottomWidth: 2,
    borderBottomColor: 'rgba(0,0,0,.05)',
  },
  tab: {
    width: 'auto',
  },
  indicator: {
    backgroundColor: '#008060',
  },
});

export default Transactions;
