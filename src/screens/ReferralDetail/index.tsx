import Clipboard from '@react-native-clipboard/clipboard';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import dayjs from 'dayjs';
import React, {useMemo} from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import {useToast} from 'react-native-toast-notifications';
import SaleProgress from '~components/by-screens/RefferalDetail/SaleProgress';
import Card from '~components/shared/Card';
import theme from '~utils/config/themes/theme';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';

const ReferralDetail: React.FC<NativeStackScreenProps<RootStackParamList, 'ReferralDetail'>> = ({route}) => {
  const {refferal} = route.params;

  const onboardStatusButton = useMemo(
    () =>
      refferal.dropship_order_count < 1 ? (
        <View style={[styles.onboardButton, styles.onboardingButton]}>
          <Text style={styles.onboardingText}>Chưa có đơn hàng</Text>
        </View>
      ) : (
        <View style={[styles.onboardButton, styles.successOnboardButton]}>
          <Text style={styles.successOnboardText}>{refferal.dropship_order_count} đơn</Text>
        </View>
      ),
    [refferal.dropship_order_count],
  );

  return (
    <View>
      {refferal.dropship_order_count < 2 ? <SaleProgress refferal={refferal} /> : null}
      <Card>
        <View>
          <InfoItem label="Ngày tham gia" value={dayjs(refferal.created_at).format('DD/MM/YYYY')} />
          <InfoItem label="Tên" value={refferal.user_name} />
          <InfoItem label="Số điện thoại" value={refferal.user_phone} allowCopy />
          <InfoItem label="Trạng thái" value={onboardStatusButton} />
        </View>
      </Card>
    </View>
  );
};

const InfoItem: React.FC<{label: string; value: string | React.ReactNode; allowCopy?: boolean}> = ({label, value, allowCopy}) => {
  const toast = useToast();
  return (
    <View style={styles.infoItem}>
      <Text style={styles.label}>{label}</Text>
      {typeof value === 'string' ? (
        <Text
          onPress={() => {
            if (typeof value === 'string') {
              Clipboard.setString(value);
              toast.show('Đã sao chép số điện thoại', {
                placement: 'center',
              });
            }
          }}
          style={styles.value}>
          {value}
        </Text>
      ) : (
        value
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  infoItem: {
    marginBottom: theme.spacing.m,
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  label: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
    marginBottom: theme.spacing.xs,
  },
  value: {
    fontSize: theme.typography.lg,
    color: theme.colors.text,
  },
  onboardButton: {
    flexDirection: 'row',
    marginTop: theme.spacing.s,
    alignItems: 'center',
    paddingVertical: 1,
    paddingHorizontal: 8,
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  onboardingButton: {
    // backgroundColor: '#f5f0e1',
  },
  successOnboardButton: {
    backgroundColor: theme.colors.primaryLight,
  },
  onboardingText: {
    fontSize: theme.typography.base,
    color: '#a37d0f',
    fontWeight: 'bold',
  },
  successOnboardText: {
    fontSize: theme.typography.base,
    color: theme.colors.primary,
    fontWeight: 'bold',
  },
});

export default ReferralDetail;
