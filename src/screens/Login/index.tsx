import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Button, Text} from '@rneui/themed';
import React, {useEffect} from 'react';
import {ActivityIndicator, Platform, StyleSheet, View} from 'react-native';
import {useAuthActions} from '~hooks';
import theme from '~utils/config/themes/theme';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useGetOAuthDrivers} from '~utils/api/auth';

import auth from '@react-native-firebase/auth';
import {DRIVERS_DATA, OAUTH_BUTTON_WIDTH, OAuthButton} from '~screens/Register';

const LoginScreen: React.FC<NativeStackScreenProps<RootStackParamList, 'Login'>> = ({navigation}) => {
  const {isLoggingIn, openOAuth2InAppBrowser, openSSOAuth, isLoggedIn} = useAuthActions();
  const {data: drivers} = useGetOAuthDrivers();

  useEffect(() => {
    if (Platform.OS !== 'web') {
      auth()
        .signOut()
        .catch(() => {});
    }

    if (isLoggedIn) {
      navigation.pop();
    }
  }, [isLoggedIn]);

  const handleRegisterPress = () => {
    navigation.replace('Register');
  };

  const handleOAuthPress = async (driver: string) => {
    openOAuth2InAppBrowser(driver);
  };

  const driversByPlatform = Platform.OS === 'android' ? drivers.filter(d => d !== 'apple') : drivers;

  return (
    <View style={{flex: 1, backgroundColor: '#fff'}}>
      {isLoggingIn ? (
        <View style={styles.authLoading}>
          <ActivityIndicator size={30} />
          <Text>Đang đăng nhập</Text>
        </View>
      ) : (
        <View style={styles.container}>
          {/* <Image source={HappyRich} style={{width: 250, height: 167, marginBottom: theme.spacing.m}} /> */}
          <View style={{paddingHorizontal: theme.spacing.l}}>
            <Text style={{fontSize: theme.typography.lg2, textAlign: 'center', fontWeight: '500'}}>Đăng nhập</Text>
          </View>
          <View style={styles.actionContainer}>
            {/* {drivers?.[0] && DRIVERS_DATA[drivers?.[0] as keyof typeof DRIVERS_DATA] ? <OAuthButton driver={DRIVERS_DATA[drivers?.[0] as keyof typeof DRIVERS_DATA]} /> : null} */}
            {driversByPlatform?.map(driverName => {
              const driver = DRIVERS_DATA?.[driverName as keyof typeof DRIVERS_DATA];
              if (driver) {
                return <OAuthButton login key={driverName} driver={driver} onPress={() => handleOAuthPress(driverName)} />;
              }
              return null;
            })}

            <Button
              buttonStyle={{
                borderColor: theme.colors.gray50,
                borderWidth: 1,
                borderRadius: 4,
                justifyContent: 'center',
                alignItems: 'center',
                paddingHorizontal: theme.spacing.m,
                paddingVertical: 0,
                width: OAUTH_BUTTON_WIDTH,
                height: 45,
              }}
              size="sm"
              onPress={() => openSSOAuth('login')}
              type="outline"
              containerStyle={{
                marginBottom: theme.spacing.m,
              }}>
              <Ionicons name="person-circle-outline" size={28} color={theme.colors.gray90} style={{marginRight: theme.spacing.s, borderRadius: 100, position: 'absolute', left: 10}} />
              <Text style={{fontSize: theme.typography.md}} numberOfLines={1}>
                Đăng nhập bằng Email/SĐT
              </Text>
            </Button>
            <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 40, justifyContent: 'center'}}>
              <Text style={{fontSize: theme.typography.md}}>Chưa có tài khoản?</Text>
              <Button type="clear" titleStyle={{color: theme.colors.primary, fontWeight: 'bold', fontSize: 16}} title="Đăng ký" onPress={handleRegisterPress} />
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  actionContainer: {
    marginTop: 100,
  },
  authLoading: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 200,
  },
  loginReason: {
    fontSize: 16,
    marginTop: 8,
  },
});

export default LoginScreen;
