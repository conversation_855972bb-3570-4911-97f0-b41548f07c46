import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {<PERSON>ton, CheckBox} from '@rneui/themed';
import React, {useCallback, useEffect, useState} from 'react';
import {ListRenderItemInfo, View} from 'react-native';
import {FlatList} from 'react-native';
import * as Sentry from '@sentry/react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useToast} from 'react-native-toast-notifications';
import MediaItem from '~components/by-screens/DownloadProductMedia/MediaItem';
import {useUpdateEffect} from '~hooks';
import {useGetProductDetail} from '~utils/api/product';
import theme from '~utils/config/themes/theme';
import ee, {EventNames} from '~utils/events';
import downloadImage, {downloadVideo} from '~utils/helpers/downloadImage';
import {checkAndRequestSavePhotoPermission} from '~utils/helpers/downloadImagePermissions';
import {getDownloadableURL, handleImageDomain} from '~utils/helpers/image';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import {ProductImage, ProductVideo} from '~utils/types/product';

type ProductMedia = ProductImage & {isChecked?: boolean};

export type MediaItemType = {
  type: string;
  item: ProductImage | ProductVideo;
  isChecked?: boolean;
};
const DownloadProductMedia: React.FC<NativeStackScreenProps<RootStackParamList, 'DownloadProductMedia'>> = ({navigation, route}) => {
  const {data, isLoading} = useGetProductDetail(route.params.productId);
  const [mediaItems, setMediaItems] = useState<MediaItemType[]>([]);
  const insets = useSafeAreaInsets();
  const [isSelectedAll, setSelectedAll] = useState(false);
  const [totalSelected, setTotalSelected] = useState(0);
  const toast = useToast();

  useEffect(() => {
    if (data) {
      setMediaItems([...data.videos.map(video => ({type: 'video', item: video, isChecked: false})), ...data.images.map(image => ({type: 'image', item: image, isChecked: false}))]);
    }
  }, [data?.id, isLoading]);

  useEffect(() => {
    navigation.setOptions({
      headerTitle: totalSelected ? `Đã chọn ${totalSelected}` : 'Tải hình ảnh',
    });
  }, [totalSelected, navigation]);

  useUpdateEffect(() => {
    if (isSelectedAll) {
      setMediaItems(mediaItems.map(item => ({...item, isChecked: true})));
      setTotalSelected(mediaItems.length);
    } else {
      setMediaItems(mediaItems.map(item => ({...item, isChecked: false})));
      setTotalSelected(0);
    }
  }, [isSelectedAll]);

  const handleChangeChecked = (isChecked: boolean, imageId: string) => {
    let newSelected = 0;
    let newMediaItems = [];

    for (let i = 0; i < mediaItems.length; i++) {
      const item = mediaItems[i];
      if (item.item.id === imageId) {
        newMediaItems.push({...item, isChecked: isChecked});
        if (isChecked) {
          newSelected++;
        }
      } else {
        if (item.isChecked) {
          newSelected++;
        }
        newMediaItems.push(item);
      }
    }

    setMediaItems(newMediaItems);
    setTotalSelected(newSelected);
  };

  const handleViewPress = (imageId: string) => {
    const newImages = mediaItems.map((item, index) => {
      return {
        url: item.type === 'image' ? (item.item as ProductImage).src! : (item.item as ProductVideo).url,
        props: {
          type: item.type,
          productId: data?.id,
        },
      };
    });

    const newIndex = mediaItems.findIndex(item => item.item.id === imageId);

    ee.emit(EventNames.OpenImageViewer, {newImages: newImages, newIndex: newIndex === -1 ? 0 : newIndex});
  };

  const toggleSelectAll = useCallback(() => {
    setSelectedAll(prev => !prev);
  }, []);

  const renderItem = ({item}: ListRenderItemInfo<MediaItemType>) => {
    return <MediaItem item={item} checked={!!item.isChecked} onChange={handleChangeChecked} onViewPress={handleViewPress} />;
  };

  const handleSubmit = async () => {
    const mediaItemsToDownload = mediaItems.filter(item => item.isChecked);
    if (mediaItemsToDownload.length === 0) {
      toast.show('Không có ảnh nào được chọn', {
        type: 'danger',
        placement: 'center',
      });
      return;
    }

    setMediaItems(mediaItems.map(item => ({...item, isChecked: false})));

    if (isSelectedAll) {
      setSelectedAll(false);
    }
    setTotalSelected(0);
    const toastId = toast.show('Vui lòng đợi', {
      duration: 20000,
    });

    //
    const canContinue = await checkAndRequestSavePhotoPermission();

    if (!canContinue) {
      setTimeout(() => {
        toast.update(toastId, 'Không thể lưu ảnh', {
          duration: 500,
        });
      }, 1000);

      return;
    }

    Promise.all(
      mediaItemsToDownload.map(async item => {
        if (item.type === 'image') {
          await downloadImage(getDownloadableURL(handleImageDomain((item.item as ProductImage).src?.replace('rs:fill:600', 'rs:fill:1000')), data?.id, ''));
        } else if (item.type === 'video') {
          await downloadVideo((item.item as ProductVideo).url);
        }
      }),
    )
      .then(() => {
        setTimeout(() => {
          toast.update(toastId, 'Đã tải xong', {
            duration: 500,
          });
        }, 500);
      })
      .catch(err => {
        Sentry.captureException(err);
        console.log(err);
      });
  };

  if (!data) {
    return null;
  }

  return (
    <>
      <FlatList data={mediaItems} renderItem={renderItem} numColumns={3} style={{flex: 1}} />
      <View style={[theme.globalStyles.flexRow, {paddingBottom: insets.bottom, backgroundColor: theme.colors.white}]}>
        <CheckBox
          size={28}
          onPress={toggleSelectAll}
          checked={isSelectedAll}
          iconType="ionicon"
          uncheckedIcon="radio-button-off"
          checkedIcon="checkmark-circle"
          checkedColor="#008060"
          containerStyle={{margin: 0, padding: 0, paddingHorizontal: 0, paddingVertical: 0}}
          wrapperStyle={{padding: 0}}
          title={'Chọn tất cả'}
          textStyle={{fontWeight: 'normal', color: theme.colors.text}}
        />
        <Button
          onPress={handleSubmit}
          size="sm"
          buttonStyle={{paddingVertical: 12}}
          title="Tải về"
          containerStyle={{
            marginLeft: 'auto',
            flex: 1,
          }}
        />
      </View>
    </>
  );
};

export default DownloadProductMedia;
