import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Input, Text} from '@rneui/themed';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {Animated, Platform, Pressable, ScrollView, View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import theme from '~utils/config/themes/theme';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useGetRecentProducts} from '~utils/api/product';
import {handleImageDomain} from '~utils/helpers/image';
import {SearchProduct} from '~utils/types/product';
import {useNavigation} from '~hooks';
import useRefreshOnFocus from '~hooks/useRefreshOnFocus';
import googleAnalytics from '~utils/helpers/analytics';
import {useGetShop} from '~utils/api/shop';
import {useDisableAvoidSoftInput} from '~hooks/useAvoidSoftInput';
import {QuickImage} from '~components/shared/QuickImage';
import SearchPreview from '~components/by-screens/Search/SearchPreview';
import FindProductTips from '~components/shared/FindProductTips';

const SearchScreen: React.FC<NativeStackScreenProps<RootStackParamList, 'Search'>> = ({navigation, route}) => {
  const translateX = useRef(new Animated.Value(-20)).current;
  const [searchValue, setSearchValue] = useState(route.params?.keyword ?? '');
  const {data, refetch} = useGetRecentProducts({limit: 10, dropship: true});
  const [searchInShop, setSearchInShop] = useState(!!route.params?.shopId);
  const [showSearchMode, setShowSearchMode] = useState(!!route.params?.shopId);
  const {data: shop} = useGetShop(route.params?.shopId as any, {enabled: !!route.params?.shopId});

  useRefreshOnFocus(refetch);
  useDisableAvoidSoftInput();

  useEffect(() => {
    Animated.timing(translateX, {
      toValue: 0,
      useNativeDriver: true,
      duration: 300,
    }).start();
  }, []);

  useEffect(() => {
    if (route.params?.shopId) {
      setSearchInShop(!!route.params?.shopId);
      setShowSearchMode(!!route.params?.shopId);
    }
  }, [route.params?.shopId]);

  const handleSubmitEditing = useCallback(() => {
    navigation.replace('SearchResult', {keyword: searchValue, filter_shop_id: searchInShop ? shop?.id : undefined});

    // analytics
    googleAnalytics.logSearch({
      search_term: searchValue,
    });
  }, [searchValue, shop?.id, searchInShop]);

  const handleNavigateSearchByImage = useCallback(() => {
    navigation.navigate('SearchByImage');
  }, []);

  const toggleSearchInShop = useCallback(() => {
    setSearchInShop(prev => !prev);
  }, [searchInShop]);

  return (
    <SafeAreaView style={{backgroundColor: '#fff', flex: 1}}>
      <View style={{flexDirection: 'row', alignItems: 'center', paddingRight: 12, paddingVertical: 0, marginTop: theme.spacing.s}}>
        <Animated.View
          style={{
            transform: [{translateX: translateX}],
          }}>
          <Ionicons name="chevron-back" size={28} style={{paddingHorizontal: 12, paddingVertical: 8}} color={theme.colors.textLight} onPress={navigation.goBack} />
        </Animated.View>

        <View style={{position: 'relative', flex: 1, borderRadius: 8, flexDirection: 'row', borderWidth: 1, borderColor: theme.colors.primary}}>
          {showSearchMode && (
            <Pressable
              onPress={toggleSearchInShop}
              style={[
                theme.globalStyles.flexRow,
                {
                  borderRightWidth: 1,
                  paddingRight: 4,
                  marginLeft: 10,
                  paddingVertical: 6,
                  borderRightColor: theme.colors.gray30,
                  borderTopLeftRadius: 8,
                  borderBottomLeftRadius: 8,
                  alignSelf: 'center',
                },
              ]}>
              <Text style={{fontSize: theme.typography.sm, color: theme.colors.text}}>{searchInShop ? 'Trong shop' : 'Toàn sàn'}</Text>
              <Ionicons name="swap-horizontal" color={theme.colors.textLight} />
            </Pressable>
          )}
          <Input
            returnKeyType="search"
            placeholder={searchInShop ? 'Tìm trong shop' : 'Nhập từ khóa hoặc mã sản phẩm'}
            onSubmitEditing={handleSubmitEditing}
            renderErrorMessage={false}
            containerStyle={{paddingHorizontal: 0, paddingVertical: 0, flex: 1, borderRadius: 4}}
            autoFocus
            inputStyle={[
              {
                fontSize: theme.typography.md,
              },
            ]}
            inputContainerStyle={{
              backgroundColor: 'transparent',
              borderColor: 'transparent',
              borderWidth: 0,
              paddingHorizontal: theme.spacing.m,
              borderRadius: 0,
              height: 44,
            }}
            value={searchValue}
            onChangeText={setSearchValue}
            clearButtonMode="always"
            rightIcon={
              Platform.OS !== 'web'
                ? {
                    type: 'ionicon',
                    name: 'camera-outline',
                    size: 24,
                    color: theme.colors.textLight,
                    onPress: handleNavigateSearchByImage,
                  }
                : undefined
            }
          />
        </View>
      </View>

      {/* <SearchByImageOptionDialog open={true} /> */}

      <ScrollView keyboardShouldPersistTaps="handled">
        <View style={{marginTop: theme.spacing.m, marginHorizontal: theme.spacing.m}}>
          <FindProductTips />
        </View>
        {searchValue ? <SearchPreview searchValue={searchValue} /> : null}
        {!data?.length ? null : (
          <View style={{paddingHorizontal: 12, marginTop: theme.spacing.l}}>
            <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center'}}>
              <Text style={{fontSize: theme.typography.md}}>Đã xem gần đây</Text>
              <Ionicons name="chevron-forward" size={24} color={theme.colors.textLight} />
            </View>
            <View style={{flexDirection: 'row', flexWrap: 'wrap'}}>
              {(data ?? [])?.map(product => (
                <RecentProductItem key={product.id} product={product} />
              ))}
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const RecentProductItem: React.FC<{product: SearchProduct}> = props => {
  const navigation = useNavigation();

  const handlePress = useCallback(() => {
    navigation.navigate('ProductDetail', {productId: props.product.product_id});
  }, []);

  return (
    <Pressable onPress={handlePress} style={{marginTop: theme.spacing.s, marginRight: theme.spacing.s}}>
      <QuickImage
        source={{
          uri: handleImageDomain(props.product.image_thumb),
        }}
        style={{
          borderRadius: 8,
          width: 70,
          height: 70,
        }}
      />
    </Pressable>
  );
};

export default SearchScreen;
