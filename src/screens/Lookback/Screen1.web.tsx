import {ScreenHeight, ScreenWidth} from '@rneui/base';
import {Button, Text} from '@rneui/themed';
import React from 'react';
import {ImageBackground, StyleSheet} from 'react-native';
// @ts-ignore
import bg1 from '~assets/lookback/bg1.png';
import {useGetCurrentUser} from '~utils/api/auth';
import theme from '~utils/config/themes/theme';

type Screen1Props = {
  onNext: () => void;
};

const Screen1 = ({onNext}: Screen1Props) => {
  const {data: user} = useGetCurrentUser();

  return (
    <ImageBackground source={bg1} style={styles.container}>
      <Text style={styles.title}><PERSON><PERSON> tổng kết</Text>
      <Text style={styles.title}>năm 2024</Text>
      <Text style={styles.title}>
        của <Text style={styles.name}>{user?.full_name}</Text>
      </Text>
      <Button
        title="Bắt đầu"
        onPress={onNext}
        containerStyle={{
          marginTop: theme.spacing.l * 2,
          alignSelf: 'center',
          width: Math.min(ScreenWidth * 0.5, 300),
        }}
        buttonStyle={{
          borderRadius: 100,
        }}
        size="sm"
        icon={{
          type: 'ionicon',
          name: 'arrow-forward-outline',
          color: theme.colors.white,
        }}
        iconRight
      />
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    position: 'relative',
    minHeight: ScreenHeight,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: ScreenWidth * 0.1,
    fontWeight: 'bold',
    textAlign: 'left',
    color: theme.colors.primary,
  },
  name: {
    fontFamily: 'Pacifico',
    fontSize: ScreenWidth * 0.1,
    fontWeight: 'bold',
    textAlign: 'left',
    color: theme.colors.primary,
  },
});

export default Screen1;
