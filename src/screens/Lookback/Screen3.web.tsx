import {ScreenHeight, ScreenWidth} from '@rneui/base';
import {Image, Text} from '@rneui/themed';
import dayjs from 'dayjs';
import React from 'react';
import {ImageBackground, StyleSheet, View} from 'react-native';
// @ts-ignore
import bg3 from '~assets/lookback/bg3.png';
import {useGetCurrentUser, useGetLookbackData} from '~utils/api/auth';
import {getOrder, useGetOrder} from '~utils/api/order';
import theme from '~utils/config/themes/theme';
import {handleImageDomain} from '~utils/helpers/image';
import {formatPriceToString} from '~utils/helpers/price';
import Order from '~utils/types/order';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {sendWebViewEvent} from '~utils/helpers/webview';
import {WebViewMessageTypes} from '~utils/types/common';

type Screen3Props = {
  onNext: () => void;
  onPrevious: () => void;
};

const viewportWidth = Math.min(ScreenWidth, 910);

const Screen3 = ({onNext, onPrevious}: Screen3Props) => {
  const {data} = useGetLookbackData();
  const {data: order} = useGetOrder(data?.highest_value_order_id ?? '');
  const {data: user} = useGetCurrentUser();

  const timesLargerThanAverage = order?.total_price && (parseFloat(order?.total_price) ?? 0) > 250000 ? (parseFloat(order?.total_price) / 250000).toFixed(2) : 0;

  const handlePress = () => {
    sendWebViewEvent(WebViewMessageTypes.Push, ['OrderDetail', {orderId: order?.id}]);
  };

  return (
    <ImageBackground source={bg3} style={styles.container}>
      <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: theme.spacing.m}}>
        <Text style={styles.title}>Đơn hàng lớn nhất bạn đã bán được</Text>
        <Image source={{uri: handleImageDomain(user?.avatar ?? '')}} style={styles.avatar} containerStyle={{flexShrink: 0}} />
      </View>

      <View style={styles.orderContainer}>
        <View style={{paddingBottom: theme.spacing.s, marginBottom: theme.spacing.s, borderBottomWidth: 1, borderBottomColor: theme.colors.gray20}}>
          <Text style={{fontSize: viewportWidth * 0.03, color: theme.colors.textLight}}>{dayjs(order?.created_at).fromNow()}</Text>
        </View>

        <View style={styles.orderValueContainer}>
          <Text style={styles.orderLabel}>Mã đơn</Text>
          <Text style={[styles.orderValue, {textDecorationLine: 'underline'}]} onPress={handlePress}>
            {order?.name}

            <Ionicons name="open-outline" size={viewportWidth * 0.03} color={theme.colors.text} style={{marginLeft: theme.spacing.xs}} />
          </Text>
        </View>
        <View style={styles.orderValueContainer}>
          <Text style={styles.orderLabel}>Giá trị</Text>
          <Text style={styles.orderValue}>{formatPriceToString(parseFloat(order?.total_price || '0'), true)}</Text>
        </View>
        <View style={styles.orderValueContainer}>
          <Text style={styles.orderLabel}>Lợi nhuận</Text>
          <Text style={[styles.orderValue, {color: theme.colors.primary}]}>{formatPriceToString(parseFloat(order?.commission_fee || '0'), true)}</Text>
        </View>

        <Text style={styles.timesLargerThanAverage}>
          Đơn hàng của bạn gấp <Text style={[styles.timesLargerThanAverage, {fontWeight: 'bold'}]}>{timesLargerThanAverage} lần</Text> so với giá trị đơn hàng trung bình trên TTS Dropship
        </Text>

        <View style={{marginTop: theme.spacing.m, display: 'flex', justifyContent: 'center', flexDirection: 'row', flexWrap: 'wrap', gap: theme.spacing.s}}>
          {order?.line_items.map(item => (
            <View style={styles.orderItemContainer}>
              <Image source={{uri: handleImageDomain(item.image_src)}} style={{width: viewportWidth * 0.35, height: viewportWidth * 0.35, borderRadius: 8}} />

              <View style={{flex: 1, position: 'absolute', right: 2, bottom: 2, backgroundColor: theme.colors.primary, padding: 2, borderRadius: 4}}>
                <Text style={styles.orderItemPrice}>
                  {formatPriceToString(parseFloat(item.price || '0'), true)} x {item.quantity}
                </Text>
              </View>
            </View>
          ))}
        </View>
      </View>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: theme.spacing.l,
    justifyContent: 'center',
    position: 'relative',
    minHeight: ScreenHeight,
    paddingBottom: theme.spacing.l * 2,
  },
  title: {
    fontSize: theme.typography.lg2,
    fontWeight: 'bold',
    textAlign: 'left',
    color: '#dc2027',
    fontFamily: 'Pacifico',
  },
  orderContainer: {
    backgroundColor: 'white',
    padding: theme.spacing.s,
    borderRadius: theme.spacing.m,
    marginBottom: theme.spacing.l * 4,
  },
  orderTitle: {
    fontSize: viewportWidth * 0.04,
    color: theme.colors.text,
  },
  orderLabel: {
    fontSize: viewportWidth * 0.04,
    color: theme.colors.text,
    width: viewportWidth * 0.25,
  },
  orderValue: {
    fontSize: viewportWidth * 0.04,
    color: theme.colors.text,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 100,
  },

  orderValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: theme.spacing.xs,
  },

  orderItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    padding: theme.spacing.xs,
    backgroundColor: theme.colors.white,
    borderRadius: 8,
    position: 'relative',
    borderWidth: 1,
    borderColor: theme.colors.gray20,
    justifyContent: 'center',
  },
  orderItemTitle: {
    fontSize: viewportWidth * 0.04,
    color: theme.colors.text,
  },
  orderItemPrice: {
    fontSize: viewportWidth * 0.03,
    color: theme.colors.white,
  },
  timesLargerThanAverage: {
    fontSize: theme.typography.md,
    color: theme.colors.text,
    marginTop: theme.spacing.m,
  },
});

export default Screen3;
