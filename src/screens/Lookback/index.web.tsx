import {Helmet} from 'react-helmet';
import {View, Animated} from 'react-native';
import NavIndicator from './NavIndicator.web';
import Screen1 from './Screen1.web';
import Screen2 from './Screen2.web';
import {useState, useRef} from 'react';
import Nav from './Nav.web';
import Screen3 from './Screen3.web';
import Screen4 from './Screen4.web';
import Screen5 from './Screen5.web';
import Screen6 from './Screen6.web';
import {useAuthActions} from '~hooks';
import {Button, Text} from '@rneui/themed';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import theme from '~utils/config/themes/theme';
import inInTTSDropshipWebview from '~utils/helpers/isInTTSDropshipWebview';
import {sendWebViewEvent} from '~utils/helpers/webview';
import {WebViewMessageTypes} from '~utils/types/common';
import {useGetLookbackData} from '~utils/api/auth';

const Lookback: React.FC<NativeStackScreenProps<RootStackParamList, 'Lookback'>> = ({navigation}) => {
  const [currentScreen, setCurrentScreen] = useState(1);
  const slideAnim = useRef(new Animated.Value(0)).current;
  const [slideDirection, setSlideDirection] = useState<'left' | 'right'>('left');
  const {isLoggedIn} = useAuthActions();
  const {data, error, isError} = useGetLookbackData();

  const animate = (nextScreen: number, direction: 'left' | 'right') => {
    setSlideDirection(direction);
    const startValue = direction === 'left' ? -1 : 1;

    Animated.sequence([
      Animated.timing(slideAnim, {
        toValue: startValue,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 0,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setCurrentScreen(nextScreen);
    });
  };

  const animateToNext = () => {
    animate(currentScreen + 1, 'left');
  };

  const animateToPrevious = () => {
    animate(currentScreen - 1, 'right');
  };

  const handleLogin = () => {
    if (inInTTSDropshipWebview()) {
      sendWebViewEvent(WebViewMessageTypes.Push, ['Register']);
    } else {
      navigation.navigate('Register');
    }
  };

  if (!isLoggedIn) {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <Text>Vui lòng đăng nhập để xem trang này</Text>

        <Button size="sm" containerStyle={{marginTop: theme.spacing.m}} title="Đăng nhập" onPress={handleLogin} />
      </View>
    );
  }

  if (isError) {
    let message = 'Có lỗi xảy ra, vui lòng thử lại sau';
    if ((error as any)?.response?.data?.message) {
      message = (error as any)?.response?.data?.message;
    }
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center', paddingHorizontal: theme.spacing.l}}>
        <Text
          style={{
            textAlign: 'center',
          }}>
          {message}
        </Text>
      </View>
    );
  }

  return (
    <>
      <Helmet>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" />
        <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet" />
        {/* prefetch */}
        <link rel="preload" as="image" href="/src/assets/lookback/bg1.png" />
        <link rel="preload" as="image" href="/src/assets/lookback/bg2.png" />
        <link rel="preload" as="image" href="/src/assets/lookback/bg3.png" />
        <link rel="preload" as="image" href="/src/assets/lookback/bg4.png" />
        <link rel="preload" as="image" href="/src/assets/lookback/bg5.jpg" />
      </Helmet>

      <View style={{flex: 1, position: 'relative'}}>
        <NavIndicator maxScreen={6} currentScreen={currentScreen} />
        <Animated.ScrollView
          style={{
            flex: 1,
            transform: [
              {
                translateX: slideAnim.interpolate({
                  inputRange: [-1, 0, 1],
                  outputRange: ['-100%', '0%', '100%'],
                }),
              },
            ],
          }}>
          {currentScreen === 1 && <Screen1 onNext={animateToNext} />}
          {currentScreen === 2 && <Screen2 onNext={animateToNext} onPrevious={animateToPrevious} />}
          {currentScreen === 3 && <Screen3 onNext={animateToNext} onPrevious={animateToPrevious} />}
          {currentScreen === 4 && <Screen4 onNext={animateToNext} onPrevious={animateToPrevious} />}
          {currentScreen === 5 && <Screen5 onNext={animateToNext} onPrevious={animateToPrevious} />}
          {currentScreen === 6 && <Screen6 onNext={animateToNext} onPrevious={animateToPrevious} />}
        </Animated.ScrollView>

        {currentScreen > 1 && <Nav currentScreen={currentScreen} maxScreen={6} navigateToNext={animateToNext} navigateToPrevious={animateToPrevious} />}
      </View>
    </>
  );
};

export default Lookback;
