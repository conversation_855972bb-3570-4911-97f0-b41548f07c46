import {ScreenHeight, ScreenWidth} from '@rneui/base';
import {Image, Text} from '@rneui/themed';
import React, {useEffect, useRef, useState} from 'react';
import {Animated, ImageBackground, StyleSheet, View} from 'react-native';
// @ts-ignore
import bg1 from '~assets/lookback/bg1.png';
import {useGetCurrentUser, useGetLookbackData} from '~utils/api/auth';
import theme from '~utils/config/themes/theme';
import {handleImageDomain} from '~utils/helpers/image';
import {formatPriceToString} from '~utils/helpers/price';
import Ionicons from 'react-native-vector-icons/Ionicons';

type Screen6Props = {
  onNext: () => void;
  onPrevious: () => void;
};

const Screen6 = ({onNext, onPrevious}: Screen6Props) => {
  const {data: user} = useGetCurrentUser();
  const {data} = useGetLookbackData();
  const [tooltipVisible, setTooltipVisible] = useState(true);
  const zoomInAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(zoomInAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();
  }, []);

  return (
    <ImageBackground source={bg1} style={styles.container}>
      <Image
        source={{
          uri: handleImageDomain(user?.avatar ?? ''),
        }}
        style={styles.avatar}
        containerStyle={{alignSelf: 'center', marginBottom: theme.spacing.l}}
      />
      <Text style={styles.title}>Lợi nhuận bạn đã nhận được trong năm nay</Text>

      <Animated.Text style={[styles.price, {transform: [{scale: zoomInAnim}]}]}>{formatPriceToString(data?.commission_earned ?? 0)}</Animated.Text>

      {tooltipVisible && (
        <View style={styles.tooltipContainer}>
          <Ionicons
            name="close-circle-outline"
            size={24}
            color={theme.colors.textLight}
            style={{marginRight: theme.spacing.xs, position: 'absolute', right: 0, top: -10, backgroundColor: theme.colors.white, borderRadius: 100}}
            onPress={() => setTooltipVisible(false)}
          />
          <Text style={styles.tooltip}>Hãy chụp màn hình để lưu lại những khoảnh khắc ấn tượng, trước khi tính năng này biến mất trong vài ngày tới nhé!</Text>
        </View>
      )}
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: theme.spacing.l * 3,
    position: 'relative',
    backgroundColor: 'white',
    minHeight: ScreenHeight * 1.5,
  },
  title: {
    fontSize: theme.typography.lg4,
    textAlign: 'center',
    color: theme.colors.text,
    fontFamily: 'Pacifico',
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 100,
  },
  price: {
    marginTop: theme.spacing.l,
    fontSize: ScreenWidth * 0.1,
    fontWeight: 'bold',
    color: theme.colors.primary,
    // fontFamily: 'Pacifico',
    textAlign: 'center',
  },
  tooltipContainer: {
    marginTop: theme.spacing.l * 4,
    padding: theme.spacing.m,
    backgroundColor: theme.colors.gray20,
    borderRadius: theme.spacing.m,
    position: 'relative',
  },
  tooltip: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
    textAlign: 'center',
  },
});

export default Screen6;
