import {ScreenHeight, ScreenWidth} from '@rneui/base';
import {Image, Text} from '@rneui/themed';
import {useQueryClient} from '@tanstack/react-query';
import React, {useState, useEffect, useRef} from 'react';
import {ImageBackground, StyleSheet, View, Animated} from 'react-native';
// @ts-ignore
import bg2 from '~assets/lookback/bg2.png';
import {useGetCurrentUser, useGetLookbackData} from '~utils/api/auth';
import {getOrder, getOrderQueryKey} from '~utils/api/order';
import theme from '~utils/config/themes/theme';
import {handleImageDomain} from '~utils/helpers/image';
import {formatPriceToString} from '~utils/helpers/price';

type Screen2Props = {
  onNext: () => void;
  onPrevious: () => void;
};

const viewportWidth = Math.min(ScreenWidth, 910);

const Screen2 = ({onNext, onPrevious}: Screen2Props) => {
  const {data: user} = useGetCurrentUser();
  const [showPraise, setShowPraise] = useState(false);
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const {data} = useGetLookbackData();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (data?.highest_value_order_id) {
      // prefetch for screen 3
      queryClient.prefetchQuery({
        queryFn: () => getOrder(data?.highest_value_order_id),
        queryKey: getOrderQueryKey(data?.highest_value_order_id),
      });
    }
  }, [data?.highest_value_order_id]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowPraise(true);
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: 50,
        friction: 7,
      }).start();
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const rank = Math.max(1, data?.sales_rank_percentile ?? 0);

  return (
    <ImageBackground source={bg2} style={styles.container}>
      <Image source={{uri: handleImageDomain(user?.avatar ?? '')}} style={styles.avatar} containerStyle={{alignSelf: 'center', marginBottom: theme.spacing.m}} />
      <Text style={styles.title}>Kết quả bạn đạt được</Text>

      <View style={styles.metric}>
        <Text style={styles.metricLabel}>Đơn hàng</Text>
        <Text style={styles.metricValue}>{data?.order_count?.toLocaleString()}</Text>
      </View>
      <View style={styles.metric}>
        <Text style={styles.metricLabel}>Doanh số</Text>
        <Text style={styles.metricValue}>{formatPriceToString(data?.sales || 0, true)}</Text>
      </View>

      {showPraise && (
        <Animated.View
          style={[
            {marginTop: theme.spacing.m, paddingHorizontal: theme.spacing.m},
            {
              transform: [{scale: scaleAnim}],
              opacity: scaleAnim,
            },
          ]}>
          <Text style={styles.praiseText}>Bạn thuộc top {rank}% cộng tác viên bán hàng tốt nhất trên TTS Dropship</Text>
        </Animated.View>
      )}
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    position: 'relative',
    minHeight: ScreenHeight,
    justifyContent: 'center',
  },
  avatar: {
    width: viewportWidth * 0.3,
    height: viewportWidth * 0.3,
    borderRadius: 100,
    alignSelf: 'center',
  },
  title: {
    fontSize: theme.typography.lg4,
    fontWeight: 'bold',
    textAlign: 'center',
    color: theme.colors.primary,
    fontFamily: 'Pacifico',
    marginBottom: theme.spacing.m,
  },
  metric: {
    // fontSize: ScreenWidth * 0.1,
    // fontWeight: 'bold',
    // textAlign: 'center',
    alignSelf: 'center',
    marginTop: theme.spacing.m,
  },
  metricValue: {
    fontSize: viewportWidth * 0.07,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  metricLabel: {
    fontSize: viewportWidth * 0.07,
    fontWeight: 'bold',
    textAlign: 'center',
    color: theme.colors.primary,
  },
  praiseText: {
    fontSize: viewportWidth * 0.04,
    fontWeight: 'bold',
    textAlign: 'center',
    color: theme.colors.primary,
  },
});

export default Screen2;
