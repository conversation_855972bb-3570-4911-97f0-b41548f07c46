import {ScreenWidth} from '@rneui/base';
import React from 'react';
import {StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';

type NavIndicatorProps = {
  maxScreen: number;
  currentScreen: number;
};

const NavIndicator = ({maxScreen, currentScreen}: NavIndicatorProps) => {
  const getWidth = Math.min((ScreenWidth * 0.7) / maxScreen, 60);
  return (
    <View style={styles.container}>
      {Array.from({length: maxScreen}).map((_, index) => (
        <View key={index} style={{borderRadius: 10, width: getWidth, height: 3, backgroundColor: index + 1 === currentScreen ? theme.colors.primary : theme.colors.gray50}} />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    zIndex: 1000,
    position: 'absolute',
    top: theme.spacing.m,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: theme.spacing.s,
  },
});

export default NavIndicator;
