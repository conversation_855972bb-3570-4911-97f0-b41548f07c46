import {Button} from '@rneui/themed';
import React, {useEffect, useRef} from 'react';
import {Animated, View} from 'react-native';
import theme from '~utils/config/themes/theme';

type NavProps = {
  currentScreen: number;
  maxScreen: number;
  navigateToNext: () => void;
  navigateToPrevious: () => void;
};

const Nav = ({currentScreen, maxScreen, navigateToNext, navigateToPrevious}: NavProps) => {
  const nextButtonDisabled = currentScreen >= maxScreen;
  const previousButtonDisabled = currentScreen <= 1;
  // slide in effect
  const slideUp = useRef(new Animated.Value(100)).current;
  useEffect(() => {
    Animated.timing(slideUp, {
      toValue: 0,
      duration: 1000,
      useNativeDriver: true,
    }).start();
  }, []);

  return (
    <>
      <Animated.View
        style={{
          transform: [{translateY: slideUp}],
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          padding: 20,
          alignItems: 'center',
          flexDirection: 'row',
          justifyContent: 'center',
          gap: theme.spacing.s,
        }}>
        <View style={{}}>
          <Button
            icon={{
              type: 'ionicon',
              name: 'chevron-back',
              size: 30,
              color: theme.colors.white,
            }}
            size="sm"
            onPress={navigateToPrevious}
            disabled={previousButtonDisabled}
            buttonStyle={{
              borderRadius: 100,
              paddingHorizontal: 20,
            }}
          />
        </View>

        <View style={{}}>
          <Button
            size="sm"
            disabled={nextButtonDisabled}
            icon={{
              type: 'ionicon',
              name: 'chevron-forward',
              size: 30,
              color: theme.colors.white,
            }}
            buttonStyle={{
              borderRadius: 100,
              paddingHorizontal: 20,
            }}
            onPress={navigateToNext}
          />
        </View>
      </Animated.View>
    </>
  );
};

export default Nav;
