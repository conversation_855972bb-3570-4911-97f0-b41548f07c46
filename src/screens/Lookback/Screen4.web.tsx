import {ScreenHeight, ScreenWidth} from '@rneui/base';
import {Image, Text} from '@rneui/themed';
import React from 'react';
import {ImageBackground, StyleSheet, View} from 'react-native';
// @ts-ignore
import bg4 from '~assets/lookback/bg4.png';
import {useGetCurrentUser, useGetLookbackData} from '~utils/api/auth';
import {useGetProductDetail} from '~utils/api/product';
import theme from '~utils/config/themes/theme';
import {handleImageDomain} from '~utils/helpers/image';
import {formatPriceToString, kFormatter} from '~utils/helpers/price';

type Screen4Props = {
  onNext: () => void;
  onPrevious: () => void;
};

const viewportWidth = Math.min(ScreenWidth, 910);

const Screen4 = ({onNext, onPrevious}: Screen4Props) => {
  const {data} = useGetLookbackData();
  const {data: user} = useGetCurrentUser();
  const {data: product} = useGetProductDetail(data?.most_sales_product_id ?? '');

  return (
    <ImageBackground source={bg4} style={styles.container}>
      <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: theme.spacing.m}}>
        <Text style={styles.title}>Sản phẩm bạn đã bán nhiều nhất</Text>
        <Image source={{uri: handleImageDomain(user?.avatar ?? '')}} style={styles.avatar} />
      </View>

      <View style={{flexDirection: 'column', justifyContent: 'center', gap: theme.spacing.s}}>
        {data?.top_sales_products.map(product => {
          return <TopSaleProductItem product={product} key={product.product_id} />;
        })}
      </View>

      <Text style={styles.comment}>
        Bạn là một chuyên gia về <Text style={[styles.comment, {fontWeight: 'bold'}]}>{product?.categories?.lv2?.title}</Text>
      </Text>
    </ImageBackground>
  );
};

const TopSaleProductItem = ({product}: {product: any}) => {
  return (
    <View style={styles.productContainer}>
      <Image source={{uri: handleImageDomain(product?.img ?? '')}} style={styles.productImage} />

      <View style={{flex: 1, marginLeft: theme.spacing.m}}>
        <Text style={styles.productTitle} numberOfLines={1}>
          {product?.title}
        </Text>
        <Text style={styles.productPrice}>Đã bán {kFormatter(product?.quantity ?? 0)}</Text>
        <Text style={styles.productPrice}>Doanh số {formatPriceToString(product?.revenue ?? 0, true)}</Text>
        <Text style={styles.profitText}>
          Lợi nhuận <Text style={[styles.profitText, styles.profitValue]}>{formatPriceToString(product?.profit ?? 0, true)}</Text>
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: theme.spacing.l,
    position: 'relative',
    backgroundColor: 'white',
    minHeight: ScreenHeight,
    justifyContent: 'center',
  },
  title: {
    fontSize: theme.typography.lg2,
    fontWeight: 'bold',
    textAlign: 'left',
    color: '#dc2086',
    fontFamily: 'Pacifico',
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 100,
  },
  productContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: theme.spacing.s,
    paddingHorizontal: theme.spacing.s,
    borderRadius: theme.spacing.m,
    borderWidth: 1,
    borderColor: theme.colors.gray20,
  },
  productTitle: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: 'bold',
    marginBottom: theme.spacing.s,
  },

  productImage: {
    width: 50,
    height: 50,
    borderRadius: 100,
    flexShrink: 0,
    borderWidth: 1,
    borderColor: theme.colors.gray20,
  },

  productPrice: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    alignSelf: 'flex-start',
    marginTop: theme.spacing.xs,
  },

  profitText: {
    marginTop: theme.spacing.xs,
    fontSize: theme.typography.base,
    color: theme.colors.text,
    alignSelf: 'flex-start',
  },

  profitValue: {
    color: theme.colors.primary,
  },
  comment: {
    fontSize: theme.typography.md,
    color: theme.colors.text,
    fontWeight: 500,
    alignSelf: 'flex-start',
    marginTop: theme.spacing.m,
    marginBottom: theme.spacing.l * 4,
  },
});

export default Screen4;
