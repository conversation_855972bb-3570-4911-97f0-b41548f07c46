import {ScreenHeight, ScreenWidth} from '@rneui/base';
import {Image, Text} from '@rneui/themed';
import React from 'react';
import {ImageBackground, StyleSheet, View} from 'react-native';
// @ts-ignore
import bg5 from '~assets/lookback/bg5.jpg';
import {useGetCurrentUser, useGetLookbackData} from '~utils/api/auth';
import {useGetShop} from '~utils/api/shop';
import theme from '~utils/config/themes/theme';
import {handleImageDomain} from '~utils/helpers/image';

type Screen5Props = {
  onNext: () => void;
  onPrevious: () => void;
};

const Screen5 = ({onNext, onPrevious}: Screen5Props) => {
  const {data: user} = useGetCurrentUser();
  const {data} = useGetLookbackData();
  const {data: shop} = useGetShop(data?.most_sales_shop_id ?? '', {
    enabled: !!data?.most_sales_shop_id,
  });
  return (
    <ImageBackground source={bg5} style={styles.container}>
      <Text style={styles.title}>Nhà cung cấp được bạn lên đơn nhiều nhất</Text>

      <View style={{flexDirection: 'row', justifyContent: 'center', gap: theme.spacing.l * 2}}>
        <View style={styles.shopContainer}>
          <View style={{}}>
            <Image source={{uri: handleImageDomain(user?.avatar ?? '')}} style={styles.shopImage} />
          </View>
          <Text style={styles.shopName}>{user?.full_name}</Text>
        </View>
        <View style={styles.shopContainer}>
          <View style={{}}>
            <Image source={{uri: handleImageDomain(shop?.avatar ?? '')}} style={styles.shopImage} />
          </View>
          <Text style={styles.shopName}>{shop?.name}</Text>
        </View>
      </View>

      <Text style={{textAlign: 'center', marginTop: theme.spacing.l * 2, color: theme.colors.primary}}>
        Bạn đã bán được {data?.most_sales_shop_order_count} đơn hàng ở nhà cung cấp này trong năm qua
      </Text>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: theme.spacing.l * 2,
    position: 'relative',
    backgroundColor: 'white',
    minHeight: ScreenHeight,
  },
  title: {
    fontSize: ScreenWidth * 0.1,
    fontWeight: 'bold',
    textAlign: 'left',
    color: '#3a4d82',
    fontFamily: 'Pacifico',
    marginBottom: theme.spacing.m,
  },
  shopContainer: {
    alignItems: 'center',
    flex: 1,
  },
  shopName: {
    fontSize: theme.typography.md,
    color: theme.colors.text,
    fontWeight: 500,
    marginTop: theme.spacing.s,
    textAlign: 'center',
  },
  shopImage: {
    width: 100,
    height: 100,
    borderRadius: 100,
  },
});

export default Screen5;
