import React, {useEffect} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {env} from '~utils/config';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';

const Lookback: React.FC<NativeStackScreenProps<RootStackParamList, 'Lookback'>> = ({navigation}) => {
  useEffect(() => {
    navigation.replace('WebView', {url: `${env.WEBVIEW_URL}/lookback`, includeAuth: true});
  }, []);

  return null;
};

export default Lookback;
