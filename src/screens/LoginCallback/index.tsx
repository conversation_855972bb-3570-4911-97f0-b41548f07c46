import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React, {useEffect} from 'react';
import {ActivityIndicator, Platform, View} from 'react-native';
import {useToast} from 'react-native-toast-notifications';
import {useAuthActions} from '~hooks';
import theme from '~utils/config/themes/theme';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';

const LoginCallbackScreen: React.FC<NativeStackScreenProps<RootStackParamList, 'LoginCallback'>> = ({route}) => {
  const {login} = useAuthActions();
  const toast = useToast();

  useEffect(() => {
    (async () => {
      await login({
        access_token: route.params.access_token,
        refresh_token: route.params.refresh_token,
      });

      if (new URLSearchParams(window.location.search.substring(1)).has('oauth_key')) {
        // oauth2 case, no redirect
      } else {
        window.location.replace(route.params.next ?? '/');
      }

      toast.show('Đăng nhập thành công', {duration: 2000, placement: 'bottom', type: 'success'});
      if (!window.parent) {
        setTimeout(() => {
          window.location.replace(route.params.next ?? '/');
        }, 2000);
      }
    })();
  }, []);

  return (
    <View style={{justifyContent: 'center', alignItems: 'center', flex: 1}}>
      <ActivityIndicator color={theme.colors.primary} size="large" />
    </View>
  );
};

export default LoginCallbackScreen;
