import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React, {useCallback, useEffect, useMemo, useRef} from 'react';
import {ActivityIndicator, Platform, Pressable, ScrollView, StyleSheet, View} from 'react-native';
import {useGetOrder} from '~utils/api/order';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Card from '~components/shared/Card';
import OrderLineItem from '~components/by-screens/OrderLineItem';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {Button, Text} from '@rneui/themed';
import theme from '~utils/config/themes/theme';
import {formatPriceToString} from '~utils/helpers/price';
import CreateOrderSucessAlert from '~components/by-screens/OrderDetail/CreateOrderSuccessAlert';
import ConfettiCannon from 'react-native-confetti-cannon';
import {SCREEN_HEIGHT, SCREEN_WIDTH} from '@gorhom/bottom-sheet';
import triggerHapticFeedback from '~utils/helpers/haptic';
import OrderPrePayAlert from '~components/by-screens/OrderDetail/OrderPrePayAlert';
import useRefreshOnFocus from '~hooks/useRefreshOnFocus';
import OrderCancelButton from '~components/by-screens/OrderDetail/OrderCancelButton';
import ShippingTracking from '~components/by-screens/OrderDetail/ShippingTracking';
import OrderInfo from '~components/by-screens/OrderDetail/OrderInfo';
import MarketPlaceOrderProgress from '~components/by-screens/OrderDetail/MarketPlaceOrderProgress';
import OrderCancelledInfo from '~components/by-screens/OrderDetail/OrderCancelledInfo';
import OrderCompleted from '~components/by-screens/OrderDetail/OrderCompleted';
import AsyncStorage from '~utils/helpers/storage';
import {ASYNC_STORAGE_KEYS} from '~utils/config';
import {handleRateApp} from '~utils/helpers/common';

const OrderDetailScreen: React.FC<NativeStackScreenProps<RootStackParamList, 'OrderDetail'>> = ({route, navigation}) => {
  const {data, isLoading, refetch} = useGetOrder(route.params.orderId);
  const insets = useSafeAreaInsets();
  const scrollViewRef = useRef<ScrollView>(null);

  useRefreshOnFocus(refetch);

  useEffect(() => {
    navigation.setOptions({
      headerRight: () => {
        return (
          <Button
            type="clear"
            icon={{
              type: 'ionicon',
              name: 'help-circle-outline',
              size: 14,
              color: theme.colors.primary,
            }}
            titleStyle={{fontSize: theme.typography.base}}
            size="sm"
            onPress={handleNavigateTickets}>
            Trợ giúp
          </Button>
        );
      },
    });

    const unsubscribe = navigation.addListener('beforeRemove', async () => {
      const isAppRated = Boolean(await AsyncStorage.getItem(ASYNC_STORAGE_KEYS.APP_RATED));
      if (!isAppRated) {
        handleRateApp();
      }
    });
    return unsubscribe;
  }, [navigation, data?.id]);

  useEffect(() => {
    if (route.params.showSuccessAlert) {
      triggerHapticFeedback('notificationSuccess');

      if (Platform.OS !== 'web') {
        // reset routes
        const routes = navigation.getState().routes;
        const newRoutes = routes.filter(r => !['Cart', 'ProductDetail', 'AddToCart', 'CreateCustomerAddress'].includes(r.name));
        const orderDetailRoutesIndex = newRoutes.map(r => r.name).lastIndexOf('OrderDetail');
        // @ts-ignore
        navigation.reset({
          index: orderDetailRoutesIndex === -1 ? newRoutes.length - 1 : orderDetailRoutesIndex,
          routes: newRoutes,
        });
      }
    }
  }, []);

  const totalShippingLinesPrice = useMemo(() => {
    return data?.shipping_lines.reduce((a, c) => a + c.price, 0);
  }, [data]);

  const handleCancelSuccess = useCallback(() => {
    scrollViewRef.current?.scrollTo({
      animated: true,
      x: 0,
      y: 0,
    });
  }, []);

  const totalPromo = useMemo(() => {
    return parseInt(data?.dropship_promo_bonus ?? '0', 10);
  }, [data]);

  const handleNavigateSuplier = useCallback(() => {
    if (data?.shop_id) {
      navigation.navigate('SupplierDetail', {shopId: data.shop_id});
    }
  }, [data?.id]);

  const handleNavigateTickets = useCallback(() => {
    if (data?.id) {
      navigation.navigate('OrderTickets', {orderId: data.id});
    }
  }, [data?.id]);

  const dropshipperSubsidyApplications = useMemo(() => {
    return data?.discount_applications?.filter(app => app.subsidy_by === 'dropshipper') ?? [];
  }, [data?.id]);

  return (
    <ScrollView style={{paddingBottom: insets.bottom + 20}} ref={scrollViewRef}>
      {route.params.showSuccessAlert && <CreateOrderSucessAlert />}
      {isLoading && (
        <View style={{marginVertical: 40}}>
          <ActivityIndicator color={'#008060'} />
        </View>
      )}

      {!isLoading && data && (
        <>
          {data.cancelled_at && <OrderCancelledInfo order={data} />}
          {data.shipped_at && <OrderCompleted order={data} />}
          <OrderInfo order={data} />

          {data.status === 'open' && data.payment_method !== 'cod' && ['pending', null].includes(data.financial_status) && <OrderPrePayAlert orderId={route.params.orderId} />}

          {data.dropship_marketplace ? <MarketPlaceOrderProgress order={data} /> : null}

          <ShippingTracking order={data} />

          <Card
            title={
              <Pressable style={{width: '100%', flexDirection: 'row', alignItems: 'center'}} onPress={handleNavigateSuplier}>
                <MaterialCommunityIcons name="storefront-outline" color={theme.colors.textLight} size={18} style={{marginRight: 4}} />
                <Text style={{fontSize: theme.typography.md, color: theme.colors.textLight}}>{data.shop.name}</Text>
              </Pressable>
            }>
            {data.line_items.map(lineItem => (
              <View style={{marginBottom: 16}} key={lineItem.id}>
                <OrderLineItem lineItem={lineItem} />
              </View>
            ))}

            {data.note ? (
              <View style={[theme.globalStyles.flexRow, {alignItems: 'flex-start'}]}>
                <Text style={{paddingRight: theme.spacing.l}}>Ghi chú</Text>
                <View style={{marginLeft: 'auto', flex: 1, alignItems: 'flex-end', paddingRight: theme.spacing.s}}>
                  <Text style={{color: theme.colors.textLight}}>{data.note}</Text>
                </View>
              </View>
            ) : null}
          </Card>

          <Card
            title={
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <Ionicons name="receipt-outline" size={18} style={{marginRight: 8}} color={theme.colors.text} />
                <Text style={{fontSize: theme.typography.lg}}>Thông tin thanh toán</Text>
              </View>
            }>
            <View style={{paddingHorizontal: 8}}>
              <Text style={{fontSize: theme.typography.base, marginBottom: theme.spacing.xs}}>
                {data.payment_method !== 'cod' ? 'Thanh toán qua ' : ''}
                {data.payment_label}
              </Text>
              <View style={[theme.globalStyles.flexRow, {justifyContent: 'space-between'}]}>
                <Text style={styles.left}>Tổng tiền hàng:</Text>
                <Text style={styles.right}>{formatPriceToString(parseInt(data.total_line_items_price, 10))}</Text>
              </View>
              <View style={[theme.globalStyles.flexRow, {justifyContent: 'space-between', marginTop: theme.spacing.s}]}>
                <Text style={styles.left}>Phí vận chuyển:</Text>
                <Text style={styles.right}>{formatPriceToString(totalShippingLinesPrice as any)}</Text>
              </View>

              {(data.discount_applications || []).map(discountApplication => (
                <View style={[{marginTop: theme.spacing.s}, theme.globalStyles.flexRow, {justifyContent: 'space-between', alignItems: 'flex-start'}]} key={discountApplication.id}>
                  <Text style={styles.left}>{discountApplication.title}</Text>
                  <Text style={styles.right}>{formatPriceToString(parseInt(discountApplication.value, 10))}</Text>
                </View>
              ))}
              <View style={[theme.globalStyles.flexRow, {justifyContent: 'space-between', marginTop: theme.spacing.s}]}>
                <Text style={[styles.left, {color: theme.colors.text}]}>Tổng thanh toán:</Text>
                <Text style={[styles.right, {color: theme.colors.text, fontWeight: 'bold'}]}>
                  <Text style={{fontSize: theme.typography.sm}}>{data.payment_method !== 'cod' && data.financial_status === 'paid' ? `(đã thanh toán)  ` : ''}</Text>
                  {formatPriceToString(parseInt(data.total_price, 10))}
                </Text>
              </View>
            </View>
          </Card>

          <Card
            title={
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <Ionicons name="receipt-outline" size={18} style={{marginRight: 8}} color={theme.colors.text} />
                <Text style={{fontSize: theme.typography.lg, fontWeight: '500'}}>Chi tiết hoa hồng</Text>
              </View>
            }>
            <View style={{paddingHorizontal: 8}}>
              <View style={[theme.globalStyles.flexRow, {justifyContent: 'space-between'}]}>
                <Text style={styles.left}>Tổng giá bán</Text>
                <Text style={styles.right}>{formatPriceToString(parseInt(data.total_line_items_price, 10))}</Text>
              </View>
              <View style={[{marginTop: theme.spacing.s}, theme.globalStyles.flexRow, {justifyContent: 'space-between'}]}>
                <Text style={styles.left}>Tổng giá nhà cung cấp</Text>
                <Text style={styles.right}>{formatPriceToString(parseInt(data.dropship_total_price, 10))}</Text>
              </View>
              <View style={[{marginTop: theme.spacing.s}, theme.globalStyles.flexRow, {justifyContent: 'space-between'}]}>
                <Text style={styles.left}>Lợi nhuận bán hàng</Text>
                <Text style={styles.right}>{formatPriceToString(parseInt(data.dropship_profit, 10))}</Text>
              </View>
              {totalPromo > 0 && (
                <View style={[{marginTop: theme.spacing.s}, theme.globalStyles.flexRow, {justifyContent: 'space-between'}]}>
                  <Text style={styles.left}>Tổng thưởng</Text>
                  <Text style={[styles.right, {color: theme.colors.primary}]}>{formatPriceToString(parseInt(data.dropship_promo_bonus, 10))}</Text>
                </View>
              )}
              {dropshipperSubsidyApplications.map(discountApplication => (
                <View style={[{marginTop: theme.spacing.s}, theme.globalStyles.flexRow, {justifyContent: 'space-between', alignItems: 'flex-start'}]} key={discountApplication.id}>
                  <Text style={styles.left}>{discountApplication.title}</Text>
                  <Text style={[styles.right, {color: parseInt(discountApplication.value) < 0 ? theme.colors.red : theme.colors.textLight}]}>
                    {formatPriceToString(parseInt(discountApplication.value, 10))}
                  </Text>
                </View>
              ))}
              <View style={[{marginTop: theme.spacing.s}, theme.globalStyles.flexRow, {justifyContent: 'space-between'}]}>
                <Text style={[styles.left, {color: theme.colors.text}]}>Tổng lợi nhuận</Text>
                <Text style={[styles.right, {color: theme.colors.primary, fontWeight: 'bold'}]}>{formatPriceToString(parseInt(data.commission_fee, 10))}</Text>
              </View>
            </View>
          </Card>

          <OrderCancelButton order={data} onCancelSucess={handleCancelSuccess} />

          <View style={{height: 50}} />
          {!['web'].includes(Platform.OS) && route.params.showSuccessAlert && (
            <ConfettiCannon explosionSpeed={2000} fallSpeed={2000} count={50} origin={{x: 10, y: SCREEN_HEIGHT * 1.2}} autoStart={true} autoStartDelay={100} />
          )}
        </>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  text1: {
    fontSize: 20,
    fontWeight: '500',
  },
  text2: {
    fontSize: theme.typography.md,
  },
  text3: {
    fontSize: 16,
  },
  text4: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
  },
  card: {
    paddingHorizontal: 8,
    paddingVertical: 20,
    backgroundColor: '#fff',
  },
  textGray: {
    color: theme.colors.textLight,
    marginBottom: 4,
  },
  left: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
    maxWidth: SCREEN_WIDTH * 0.4,
  },
  right: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
  },
});

export default OrderDetailScreen;
