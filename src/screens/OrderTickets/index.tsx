import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React, {useEffect} from 'react';
import {env} from '~utils/config';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';

const OrderTickets: React.FC<NativeStackScreenProps<RootStackParamList, 'OrderTickets'>> = ({route, navigation}) => {
  useEffect(() => {
    navigation.replace('WebView', {url: `${env.WEBVIEW_URL}/order/${route.params.orderId}/tickets/new`, includeAuth: true});
  }, [route.params.orderId]);
  return null;
};

export default OrderTickets;
