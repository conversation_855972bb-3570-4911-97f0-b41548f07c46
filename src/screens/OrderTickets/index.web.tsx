import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Button, Input, Text} from '@rneui/themed';
import React, {ChangeEvent, useState} from 'react';
import {Linking, ScrollView, StyleSheet, View} from 'react-native';
import {useToast} from 'react-native-toast-notifications';
import Select from '~components/shared/Form/Select';
import {createOrderTicket, useGetOrder} from '~utils/api/order';
import theme from '~utils/config/themes/theme';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import Ionicons from 'react-native-vector-icons/Ionicons';
import inInTTSDropshipWebview from '~utils/helpers/isInTTSDropshipWebview';
import {sendWebViewEvent} from '~utils/helpers/webview';

const exchangeOrReturnType = 'Yêu cầu đổi/trả hàng';

const OrderTickets: React.FC<NativeStackScreenProps<RootStackParamList, 'OrderTickets'>> = ({route, navigation}) => {
  const {data} = useGetOrder(route.params.orderId);
  const [type, setType] = useState('');

  const [description, setDescription] = useState('');
  const [attachments, setAttachments] = useState<File[]>([]);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const toast = useToast();
  const [isLoading, setLoading] = useState(false);
  const [isTTSDropshipWebview] = useState(inInTTSDropshipWebview());
  const [isSent, setSent] = useState(false);

  const handleFilesChange = (event: ChangeEvent<HTMLInputElement>) => {
    // max 10 files
    const remainFiles = 10 - attachments.length;
    setAttachments(prev => [...prev, ...[...(event.target.files as any)].slice(0, remainFiles)]);
  };

  const handleSubmit = async () => {
    setErrors({});
    const ticket = {
      type,
      description,
      attachments,
      order_id: route.params.orderId,
    };
    const requiredFields = ['type', 'description'];
    let errs: any = {};
    for (const field of requiredFields) {
      if (!ticket[field as keyof typeof ticket]) {
        errs[field] = 'Vui lòng nhập thông tin';
      }
    }

    if (Object.keys(errs).length > 0) {
      setErrors(prev => ({...prev, ...errs}));
      return;
    }

    if (type === exchangeOrReturnType) {
      if (!attachments?.length) {
        toast.show('Yêu cầu đổi/trả phải đính kèm hình ảnh', {
          type: 'danger',
        });

        return;
      }
    }

    setLoading(true);
    const res = await createOrderTicket(ticket)
      .then(() => {
        setSent(true);
      })
      .catch(err => {
        toast.show(err?.response?.data?.message ?? 'Có lỗi xảy ra', {
          type: 'danger',
        });
      })
      .finally(() => setLoading(false));
  };

  const handleRemoveAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const handleGoBack = () => {
    if (isTTSDropshipWebview) {
      sendWebViewEvent('Goback', {});
    } else {
      navigation.goBack();
    }
  };

  const handlePressPolicy = (e: MouseEvent) => {
    if (isTTSDropshipWebview) {
      e.preventDefault();
      navigation.push('WebView', {
        url: 'https://go.thitruongsi.com/Vf3k9',
      });
    }
  };

  return (
    <ScrollView style={{flex: 1, backgroundColor: theme.colors.white, paddingHorizontal: theme.spacing.m}}>
      <Text style={{marginVertical: theme.spacing.l, alignSelf: 'center', fontSize: theme.typography.lg2}}>Gửi yêu cầu hỗ trợ</Text>
      {isSent ? (
        <>
          <View style={{alignSelf: 'center', marginTop: theme.spacing.l * 4}}>
            <Text style={{fontSize: theme.typography.lg2, color: theme.colors.primary}}>Gửi yêu cầu thành công</Text>
            <Button
              onPress={handleGoBack}
              size="sm"
              icon={{
                type: 'ionicon',
                name: 'chevron-back',
                size: 18,
                color: theme.colors.white,
              }}
              containerStyle={{marginTop: theme.spacing.l, borderRadius: 100}}>
              Quay lại
            </Button>
          </View>
        </>
      ) : (
        <>
          <Input label="Mã đơn hàng" value={data?.name} editable={false} inputContainerStyle={{backgroundColor: theme.colors.gray20}} />
          <Select
            options={[data?.wait_tts_confirm ? 'Giục xác nhận' : '', 'Giục lấy hàng', 'Giục giao hàng', 'Yêu cầu hoàn đơn', 'Yêu cầu đổi/trả hàng', 'Khiếu nại', 'Khác'].filter(Boolean)}
            label={'Loại yêu cầu'}
            onChange={type => setType(type as any)}
            value={type}
            errorMessage={errors.type}
            renderErrorMessage={!!errors.type}
          />
          {type === exchangeOrReturnType && (
            <>
              <View style={styles.exchangeOrReturnNotice}>
                <Text style={[styles.noticeText, {fontWeight: 'bold'}]}>Lưu ý với Yêu cầu đổi/trả hàng: </Text>
                <ol style={{paddingTop: 0, paddingBottom: 0, marginTop: 0, marginBottom: 0}}>
                  <li>
                    <Text style={styles.noticeText}>
                      Vui lòng xem kĩ chính sách đổi trả:{' '}
                      <a onClick={handlePressPolicy} href="https://go.thitruongsi.com/Vf3k9">
                        https://go.thitruongsi.com/Vf3k9
                      </a>
                    </Text>
                  </li>
                  <li>
                    <Text style={styles.noticeText}>Đơn hàng này sẽ tạm khóa đối soát để xử lý đổi/trả</Text>
                  </li>
                  <li>
                    <Text style={styles.noticeText}>Vui lòng đính kèm đầy đủ hình ảnh chứng minh sản phẩm lỗi, nhà cung cấp gửi sai yêu cầu.</Text>
                  </li>
                </ol>
              </View>
            </>
          )}
          <Input
            label="Nội dung"
            // labelStyle={{color: theme.colors.text, fontSize: theme.typography.base}}
            value={description}
            onChangeText={setDescription}
            inputContainerStyle={{borderBottomWidth: 1, paddingVertical: 8, borderRadius: 8}}
            placeholder={'Nhập nội dung cần hỗ trợ'}
            numberOfLines={3}
            style={
              {
                //   textAlignVertical: 'top',
              }
            }
            multiline
            inputStyle={{fontSize: theme.typography.md, minHeight: 60}}
            errorMessage={errors.description}
          />

          <View style={{paddingHorizontal: theme.spacing.m, marginTop: theme.spacing.s}}>
            <Text style={{color: theme.colors.textLight}}>Đính kèm (nếu có)</Text>

            <View style={{alignItems: 'center', marginTop: theme.spacing.s, flexDirection: 'row'}}>
              <button>
                {/* @ts-ignore */}
                <label for="file">Đính kèm file</label>
                <input onChange={handleFilesChange} type="file" id="file" name="file" multiple style={{display: 'none'}} />
              </button>
              <Text
                style={{
                  fontSize: theme.typography.base,
                  color: theme.colors.textLight,
                  marginLeft: theme.spacing.s,
                }}>
                Tối đa 10 file
              </Text>
            </View>

            {attachments.map((file, index) => (
              <View key={file.name} style={{marginTop: theme.spacing.s, flexDirection: 'row'}}>
                <Ionicons name="close" size={20} color={theme.colors.textLight} onPress={() => handleRemoveAttachment(index)} />
                <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight}}>{file.name}</Text>
              </View>
            ))}
          </View>

          <Button loading={isLoading} containerStyle={{marginTop: theme.spacing.l * 2, alignSelf: 'center'}} buttonStyle={{borderRadius: 100, paddingHorizontal: 40}} size="sm" onPress={handleSubmit}>
            Gửi yêu cầu
          </Button>
        </>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  exchangeOrReturnNotice: {
    padding: theme.spacing.m,
    backgroundColor: theme.colors.secondary,
    borderRadius: 8,
    marginBottom: theme.spacing.m,
  },
  noticeText: {
    paddingHorizontal: theme.spacing.m,
    marginBottom: theme.spacing.m,
    color: theme.colors.text,
  },
});

export default OrderTickets;
