import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Button, Input, Text} from '@rneui/themed';
import React, {useCallback, useState} from 'react';
import {ScrollView, StyleSheet, View} from 'react-native';
import AppRating from '~components/shared/AppRating';
import {createFeedback} from '~utils/api/analytics';
import theme from '~utils/config/themes/theme';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import Ionicons from 'react-native-vector-icons/Ionicons';

const Feedback: React.FC<NativeStackScreenProps<RootStackParamList, 'Feedback'>> = ({route, navigation}) => {
  const [feedbackContent, setFeedbackContent] = useState('');
  const [star, setStar] = useState(1);
  const [isSuccess, setSuccess] = useState(false);

  const handleSubmit = useCallback(() => {
    createFeedback({
      features: 'in-app-rate',
      response: star,
      feedback: `[Dropship-App] - ${feedbackContent}`,
    }).then(() => {
      setSuccess(true);
    });
  }, [star, feedbackContent]);

  const handleBack = useCallback(() => {
    navigation.goBack();
  }, []);

  return (
    <ScrollView style={styles.container}>
      {!isSuccess ? (
        <>
          <View style={{marginBottom: theme.spacing.l}}>
            <Text h4>Chúng tôi có thể làm gì cho bạn?</Text>
            <Text style={{marginTop: theme.spacing.s, fontSize: theme.typography.base}}>
              Đội ngũ TTS Dropship nỗ lực hằng ngày để cải thiện trải nghiệm người dùng. Chúng tôi luôn lắng nghe và đánh giá cao mọi góp ý của bạn!
            </Text>
          </View>

          <AppRating onChange={setStar} initialStar={route.params?.initialStar} />

          <View style={{backgroundColor: '#fff', marginTop: theme.spacing.l, borderRadius: 12, paddingVertical: 8}}>
            <Input
              value={feedbackContent}
              onChangeText={setFeedbackContent}
              inputContainerStyle={{borderBottomWidth: 1, paddingVertical: 12}}
              placeholder="Nhập góp ý về tính năng hoặc vấn đề mà bạn đang gặp phải tại đây"
              numberOfLines={5}
              multiline
              inputStyle={{fontSize: theme.typography.md, minHeight: 150}}
            />
          </View>

          <Button onPress={handleSubmit} disabled={!feedbackContent} size="sm" containerStyle={{marginTop: theme.spacing.l}} buttonStyle={{borderRadius: 30}}>
            Gửi
          </Button>
        </>
      ) : (
        <View style={{alignItems: 'center'}}>
          <Ionicons name="checkmark-circle" size={60} color={theme.colors.primary} />
          <Text h4 style={{textAlign: 'center', marginTop: theme.spacing.m}}>
            Phản hồi của bạn đã được gửi!
          </Text>
          <Text style={{marginTop: theme.spacing.m}}>Cảm ơn bạn đã gửi phản hồi. Chúng tôi thường xem phản hồi của khách hàng trong 2 giờ, và sẽ phản hồi lại nếu cần thiết.</Text>

          <Button
            onPress={handleBack}
            size="sm"
            buttonStyle={{borderRadius: 30, paddingHorizontal: 30}}
            containerStyle={{marginTop: theme.spacing.xl}}
            icon={{type: 'ionicon', name: 'arrow-back', color: theme.colors.white}}>
            Trở về
          </Button>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 12,
    paddingVertical: 40,
  },
});

export default Feedback;
