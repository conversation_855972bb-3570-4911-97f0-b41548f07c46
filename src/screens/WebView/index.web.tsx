import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React, {useEffect} from 'react';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';

const WebViewScreen: React.FC<NativeStackScreenProps<RootStackParamList, 'WebView'>> = ({route, navigation}) => {
  useEffect(() => {
    if (route.params.url) {
      window.location.href = route.params.url;
    }
  }, [route.params]);
  return null;
};

export default WebViewScreen;
