import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React, {useCallback, useEffect, useRef} from 'react';
import {Linking, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import WebView, {WebViewMessageEvent} from 'react-native-webview';
import {ShouldStartLoadRequest} from 'react-native-webview/lib/WebViewTypes';
import WebviewScreenHeader from '~components/by-screens/WebView/WebViewScreenHeader';
import {useAuthActions} from '~hooks';
import {ACCESS_TOKEN_KEY, REFRESH_TOKEN_KEY} from '~utils/config';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import {ParsedWebViewMessage, WebViewMessageTypes} from '~utils/types/common';
import ee, {EventNames} from '~utils/events';
import {downloadImageWithPOST} from '~utils/helpers/downloadImage';
import {useToast} from 'react-native-toast-notifications';
import {checkAndRequestSavePhotoPermission} from '~utils/helpers/downloadImagePermissions';

const WebViewScreen: React.FC<NativeStackScreenProps<RootStackParamList, 'WebView'>> = ({route, navigation}) => {
  const insets = useSafeAreaInsets();
  const webviewRef = useRef<WebView>(null);
  const {authData} = useAuthActions();
  const toast = useToast();

  useEffect(() => {
    navigation.setOptions({
      headerShown: true,
      header: props => <WebviewScreenHeader {...props} />,
    });
  }, []);

  const getIncludeAuthScript = useCallback(() => {
    if (!authData.access_token) {
      return '';
    }
    const domainRegex = /^(?:https?:\/\/)?(?:[a-zA-Z0-9-]+\.)*thitruongsi\.com(?:\/[^\s]*)?/;
    if (!domainRegex.test(route.params?.url) && !__DEV__) {
      return '';
    }

    return `window.localStorage.setItem('${ACCESS_TOKEN_KEY}','${authData.access_token}');window.localStorage.setItem('${REFRESH_TOKEN_KEY}', '${authData.refresh_token}');`;
  }, [authData.access_token]);

  const handleOnShouldStartLoad = useCallback(
    (event: ShouldStartLoadRequest) => {
      const {url} = event;

      if (typeof event.isTopFrame === 'undefined' || event.isTopFrame) {
        if (route.params?.redirectNativeScreen) {
          if (url.startsWith('https://dropship.thitruongsi.com')) {
            setTimeout(() => {
              navigation.goBack();
              Linking.openURL(url.replace('https://dropship.thitruongsi.com', 'tts.com.dsbuyer://'));
            }, 300);
            return false;
          }
          return true;
        }
      }
      return true;
    },
    [route.params],
  );

  const handleMessage = useCallback(async (event: WebViewMessageEvent) => {
    try {
      const {data} = event.nativeEvent;
      const dataParsed: ParsedWebViewMessage = JSON.parse(data);
      switch (dataParsed.type) {
        case WebViewMessageTypes.Navigate:
          navigation.navigate(...dataParsed.data);
          break;

        case WebViewMessageTypes.Replace:
          navigation.replace(...dataParsed.data);
          break;
        case WebViewMessageTypes.Push:
          navigation.push(...dataParsed.data);
          break;

        case WebViewMessageTypes.PageTitle:
          navigation.setOptions({title: dataParsed.data});
          break;
        case WebViewMessageTypes.PopToTop:
          navigation.popToTop();
          break;

        case WebViewMessageTypes.Goback:
          try {
            navigation.goBack();
          } catch (error) {}
          break;

        case WebViewMessageTypes.OpenURL:
          Linking.openURL(dataParsed.data);
          break;

        case WebViewMessageTypes.OpenImageViewer:
          ee.emit(EventNames.OpenImageViewer, {
            newImages: dataParsed.data?.newImages.map((url: any) => ({
              url: url,
              props: {
                type: 'image',
              },
            })),
            newIndex: 0,
          });
          break;

        case WebViewMessageTypes.DownloadImagePOST:
          const id = toast.show('', {placement: 'bottom'});
          const canContinue = await checkAndRequestSavePhotoPermission();
          if (!canContinue) {
            toast.hide(id);
            toast.show('Bạn chưa cấp quyền lưu ảnh', {placement: 'bottom'});
            return;
          }
          try {
            await downloadImageWithPOST(dataParsed.data?.url, dataParsed.data?.body, dataParsed.data?.ext);
            toast.show('Đã lưu ảnh vào thư viện', {placement: 'bottom'});
          } catch (error) {}
          toast.hide(id);
          break;
        default:
          break;
      }
    } catch (error) {}
  }, []);

  return (
    <View style={{flex: 1, paddingBottom: insets.bottom}}>
      <WebView
        ref={webviewRef}
        decelerationRate="normal"
        style={{flex: 1}}
        allowFileAccess={true}
        pullToRefreshEnabled={true}
        onMessage={handleMessage}
        androidHardwareAccelerationDisabled
        startInLoadingState={true}
        allowsBackForwardNavigationGestures
        onShouldStartLoadWithRequest={handleOnShouldStartLoad}
        source={
          {
            uri: route.params.url,
            headers: {},
          } || undefined
        }
        injectedJavaScriptBeforeContentLoaded={`window.TTS_DROPSHIP_VERSION=3;${route.params?.includeAuth ? getIncludeAuthScript() : ''}true`}
        injectedJavaScript="window.ReactNativeWebView.postMessage(JSON.stringify({type:'PageTitle',data:document.title}));true;"
        onContentProcessDidTerminate={() => {
          if (webviewRef.current) {
            webviewRef.current.reload();
          }
        }}
      />
    </View>
  );
};

export default WebViewScreen;
