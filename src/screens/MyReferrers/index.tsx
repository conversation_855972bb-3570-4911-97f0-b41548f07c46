import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Text} from '@rneui/themed';
import React, {useCallback, useMemo} from 'react';
import {ActivityIndicator, FlatList, View, ListRenderItemInfo} from 'react-native';
import InvitedItem from '~components/by-screens/MyRefferers/InvitedItem';
import {useGetAffiliateStatistic, useGetMyReferrers} from '~utils/api/auth';
import theme from '~utils/config/themes/theme';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import {Referrer} from '~utils/types/user';

const MyReferrers: React.FC<NativeStackScreenProps<RootStackParamList, 'MyReferrers'>> = () => {
  const {data, isLoading, isFetchingNextPage, fetchNextPage, hasNextPage} = useGetMyReferrers({});
  const {data: affStats} = useGetAffiliateStatistic();

  const dataFlat = useMemo(() => {
    return data?.pages.flatMap(page => page.referrers) ?? [];
  }, [data]);

  const renderItem = useCallback(
    (item: ListRenderItemInfo<Referrer>) => {
      return <InvitedItem item={item.item} />;
    },
    [dataFlat],
  );

  const handleScrolledToBottom = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  return (
    <View style={{flex: 1}}>
      <FlatList
        data={dataFlat}
        renderItem={renderItem}
        ListHeaderComponent={
          <>
            <Text style={{paddingHorizontal: theme.spacing.m, fontSize: theme.typography.base, color: theme.colors.textLight, marginTop: theme.spacing.s, marginBottom: theme.spacing.m}}>
              Đã mời
              <Text style={{fontWeight: 'bold'}}> {affStats?.total_referrers} người</Text>
            </Text>
          </>
        }
        ListEmptyComponent={
          <>
            {!isLoading && <Text style={{textAlign: 'center', marginTop: theme.spacing.l}}>Chưa có bạn nào được mời</Text>}
            {isLoading && <ActivityIndicator size="small" color={theme.colors.primary} />}
          </>
        }
        onEndReached={handleScrolledToBottom}
      />
    </View>
  );
};

export default MyReferrers;
