import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Button, Text} from '@rneui/themed';
import React, {useCallback, useMemo} from 'react';
import {Image, Linking, ScrollView, StyleSheet, View} from 'react-native';
import {useGetRefCodeDetail} from '~utils/api/auth';
import theme from '~utils/config/themes/theme';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
// @ts-ignore
import LogoDropshipIcon from '~assets/logo-dropship-icon.png';
import {ScreenWidth} from '@rneui/base';
import {useIsMobile} from '~hooks';
import DownloadAppQR from '~components/shared/DownloadAppQR';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Clipboard from '@react-native-clipboard/clipboard';
import {useToast} from 'react-native-toast-notifications';
import {responsiveWidth} from '~components/shared/web/WebContainer';
import {Helmet} from 'react-helmet';
import {handleImageDomain} from '~utils/helpers/image';

const Invite: React.FC<NativeStackScreenProps<RootStackParamList, 'Invite'>> = ({route}) => {
  const {data} = useGetRefCodeDetail(route.params.ref_code);
  const isMobile = useIsMobile();
  const toast = useToast();

  const maxWidth = useMemo(() => {
    return Math.min(ScreenWidth, responsiveWidth.sm);
  }, []);

  const handleCopy = useCallback(() => {
    Clipboard.setString(data?.code ?? '');
    toast.show('Đã copy', {
      placement: 'bottom',
      duration: 2000,
    });
  }, [data]);

  const handleDownloadApp = useCallback(() => {
    Linking.openURL('https://ttsdropship.page.link/invite-install');
  }, []);

  const ogDescription = useMemo(() => {
    return 'Nhận thưởng ngay khi đăng nhập vào app. Bán hàng cộng tác viên không cần vốn, lợi nhuận cao.';
  }, [data]);

  const seoTitle = useMemo(() => {
    if (data?.user.full_name) {
      return `${data?.user.full_name} mời bạn cùng tham gia bán hàng CTV không vốn`;
    }
    return '';
  }, [data]);

  const canonicalURL = useMemo(() => {
    if (route.params.ref_code) {
      return `https://dropship.thitruongsi.com/invite?ref_code=${route.params.ref_code}`;
    }
  }, [route.params.ref_code]);

  const ogImage = useMemo(() => {
    return 'https://dropship.thitruongsi.com/src/assets/invite-background.jpeg';
  }, []);

  return (
    <>
      <Helmet>
        <meta charSet="UTF-8" />
        {ogDescription ? <meta name="description" content={ogDescription} /> : null}
        {ogDescription ? <meta property="og:description" content={ogDescription} /> : null}
        {seoTitle ? <meta property="og:title" content={seoTitle + ' - TTS Dropship'} /> : null}
        <meta property="og:site_name" content="Thị Trường Sỉ" />
        <meta property="og:locale" content="vi-VN" />
        <meta property="og:type" content="website" />
        {canonicalURL ? <meta property="og:url" content={canonicalURL} /> : null}
        {canonicalURL ? <link rel="canonical" href={canonicalURL} /> : null}
        {ogImage ? <meta property="og:image" content={ogImage} /> : null}
      </Helmet>
      {/* <Image source={InviteBackground} style={{width: maxWidth, height: (maxWidth * 400) / 600}} /> */}
      <ScrollView style={{paddingHorizontal: theme.spacing.m, paddingVertical: theme.spacing.l, backgroundColor: theme.colors.primary, flex: 1}}>
        <View style={{alignSelf: 'center', marginBottom: theme.spacing.l * 4}}>
          <Image source={LogoDropshipIcon} style={{width: 80, height: 80}} />
        </View>
        <View style={{backgroundColor: 'white', padding: theme.spacing.m, borderRadius: 12, position: 'relative'}}>
          <View style={{marginBottom: theme.spacing.l * 2}}>
            <Image
              source={{
                uri: handleImageDomain(data?.user?.avatar ?? ''),
              }}
              style={{
                width: 100,
                height: 100,
                borderRadius: 100,
                position: 'absolute',
                top: -50 - theme.spacing.m,
                left: (maxWidth - theme.spacing.m * 4) / 2 - 50,
                borderWidth: 2,
                borderColor: theme.colors.gray10,
              }}
            />
          </View>
          <View>
            <Text style={{fontWeight: 'bold', fontSize: theme.typography.lg2}}>{data?.user.full_name}</Text> tặng bạn gói quà 50K khi tham gia bán hàng cùng TTS Dropship
          </View>

          <Text style={{marginTop: theme.spacing.xl}}>🎁 Nhanh tay tham gia & nhận thưởng chỉ với 3 bước sau</Text>
          <View style={[{marginTop: theme.spacing.l}, theme.globalStyles.flexRow]}>
            <StepIcon step={1} />
            <Text style={styles.stepText}>Tải app TTS Dropship</Text>
            {isMobile && (
              <Button
                type="outline"
                size="sm"
                onPress={handleDownloadApp}
                containerStyle={{marginLeft: theme.spacing.l}}
                buttonStyle={{borderRadius: 100, paddingVertical: 0}}
                titleStyle={{fontSize: theme.typography.base}}
                iconRight
                icon={{type: 'ionicon', name: 'arrow-down-circle-outline', color: theme.colors.primary, size: 16}}>
                Tải app
              </Button>
            )}
          </View>
          <View style={[{marginTop: theme.spacing.l}, theme.globalStyles.flexRow, {alignItems: 'flex-start'}]}>
            <StepIcon step={2} />
            <View>
              <Text style={styles.stepText}>
                Đăng ký tài khoản <Text style={styles.subText}>(nhận ngay 10K)</Text>
              </Text>
              <View
                style={{
                  marginLeft: theme.spacing.s,

                  marginTop: theme.spacing.m,
                }}>
                <Text
                  style={{
                    fontSize: theme.typography.base,
                    color: theme.colors.textLight,
                  }}>
                  Nhớ nhập mã giới thiệu:{' '}
                </Text>
                <Text
                  style={{
                    fontSize: theme.typography.base,
                    color: theme.colors.text,
                  }}>
                  {data?.code}
                </Text>
                <Ionicons name="copy-outline" size={18} style={{marginLeft: theme.spacing.s}} onPress={handleCopy} />
              </View>
            </View>
          </View>
          <View style={[{marginTop: theme.spacing.l}, theme.globalStyles.flexRow]}>
            <StepIcon step={3} />
            <Text style={styles.stepText}>
              Bán hàng & nhận hoa hồng <Text style={styles.subText}>(thưởng thêm đến 40K)</Text>
            </Text>
          </View>

          {isMobile && (
            <Button onPress={handleDownloadApp} containerStyle={{alignSelf: 'center', marginTop: theme.spacing.xl}} size="sm" buttonStyle={{borderRadius: 100, paddingHorizontal: 20}}>
              Tải App ngay
            </Button>
          )}
        </View>
      </ScrollView>
      {!isMobile && <DownloadAppQR />}
    </>
  );
};

type StepIconProps = {
  step: number;
};

const StepIcon: React.FC<StepIconProps> = props => {
  return (
    <View style={styles.step}>
      <Text style={{color: '#fff', fontSize: theme.typography.md}}>{props.step}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  step: {
    width: 24,
    height: 24,
    backgroundColor: theme.colors.textLightest,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 30,
  },
  stepText: {
    fontSize: theme.typography.md,
    marginLeft: theme.spacing.s,
  },
  subText: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
    marginLeft: theme.spacing.xs,
  },
});

export default Invite;
