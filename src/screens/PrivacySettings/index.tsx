import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React from 'react';
import {View} from 'react-native';
import SettingItem from '~components/by-screens/AccountInfoSettings/SettingItem';
import PasswordChanger from '~components/by-screens/PrivacySettings/PasswordChanger';
import theme from '~utils/config/themes/theme';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';

const PrivacySettings: React.FC<NativeStackScreenProps<RootStackParamList, 'PrivacySettings'>> = ({navigation}) => {
  return (
    <View style={{flex: 1, paddingHorizontal: 12}}>
      <View style={{marginBottom: theme.spacing.l}}>
        <PasswordChanger />
      </View>
      <SettingItem title="Xóa tài khoản" destructive rounded description="Xóa tài khoản, ngưng sử dụng" onPress={() => navigation.navigate('AccountDeletion')} />
    </View>
  );
};

export default PrivacySettings;
