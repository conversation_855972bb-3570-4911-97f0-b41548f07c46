import React from 'react';
import {Helmet} from 'react-helmet';
import {View} from 'react-native';
import AppleIntroduce from '~components/by-screens/WholeFoodsLink/AppleIntroduce';
import CallToAction from '~components/by-screens/WholeFoodsLink/CallToAction';
import FeaturedProducts from '~components/by-screens/WholeFoodsLink/FeaturedProducts';
import SupplierProfile from '~components/by-screens/WholeFoodsLink/SupplierProfile';
import WhyChooseUs from '~components/by-screens/WholeFoodsLink/WhyChooseUs';
import {useExtendContainerWidth} from '~components/shared/web/WebContainer';
import theme from '~utils/config/themes/theme';

const WholeFoodsLink = () => {
  useExtendContainerWidth('lg');
  return (
    <>
      <Helmet>
        {/* @ts-ignore */}
        <link href="https://fonts.googleapis.com/css2?family=Lexend+Deca:wght@100..900&display=swap" rel="stylesheet" />
      </Helmet>
      <View>
        <AppleIntroduce />
        <FeaturedProducts />
        <SupplierProfile />
        <WhyChooseUs />
        <CallToAction />
      </View>
    </>
  );
};

export default WholeFoodsLink;
