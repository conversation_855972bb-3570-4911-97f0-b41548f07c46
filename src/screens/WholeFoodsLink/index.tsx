import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React, {useEffect} from 'react';
import {env} from '~utils/config';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';

const WholeFoodsLink: React.FC<NativeStackScreenProps<RootStackParamList, 'WholeFoodsLink'>> = ({navigation}) => {
  useEffect(() => {
    navigation.replace('WebView', {url: `${env.WEBVIEW_URL}/wholefoodslink`, includeAuth: true});
  }, []);

  return null;
};

export default WholeFoodsLink;
