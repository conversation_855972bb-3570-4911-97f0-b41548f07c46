import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import React, {useEffect} from 'react';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {Platform} from 'react-native';

// @ts-ignore
import Ionicons from 'react-native-vector-icons/dist/Ionicons';

import HomeScreen from '~screens/Home';
import OrdersScreen from '~screens/Orders';
import UserScreen from '~screens/User';
import theme from '~utils/config/themes/theme';
import {useAuthActions} from '~hooks';
import HomeTabbarButton from '~components/by-screens/Home/HomeTabbarButton';
import Inspiration from '~screens/Inspiration';

export type MainTabsScreenList = {
  Home: {onboard?: boolean; access_token?: string; refresh_token?: string; oauth_key?: string} | undefined;
  Wishlist: undefined;
  Order: {buyer_status?: string} | undefined;
  Account: undefined;
  Inspiration: {tabIndex?: number};
};

const Tab = createBottomTabNavigator<MainTabsScreenList>();

const TAB_HEIGHT = 54;

const MainTabsScreen = () => {
  const insets = useSafeAreaInsets();
  // const [isSavedProductDirty, setSavedProductDirty] = useState(false);
  const {isLoggedIn, openLoginRequiredModal} = useAuthActions();

  useEffect(() => {}, []);

  // const getInitialSavedProductDirty = useCallback(async () => {
  //   // AsyncStorage.get(ASYNC_STORAGE_KEYS.SAVED_PRODUCTS_DIRTY);
  // }, []);

  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: true,
        tabBarBadgeStyle: {
          fontSize: 12,
        },
        tabBarItemStyle: {
          height: TAB_HEIGHT - 4,
          overflow: 'hidden',
        },
        headerTitleStyle: {
          color: theme.colors.text,
        },
        tabBarHideOnKeyboard: Platform.OS === 'android',
        tabBarInactiveTintColor: theme.colors.text,
        tabBarActiveTintColor: theme.colors.primary,
        tabBarLabelStyle: {fontSize: 10},
        tabBarStyle: {height: TAB_HEIGHT + insets.bottom},
      }}>
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          headerTitle: 'TTS Dropship - Cùng bạn bán hàng',
          title: Platform.OS === 'web' ? 'TTS Dropship - Làm CTV bán hàng online không cần vốn' : 'TTS Dropship - Cùng bạn bán hàng',

          tabBarButton: props => <HomeTabbarButton {...props} />,
          tabBarLabel: 'Trang chủ',
          headerTintColor: theme.colors.textLight,
          tabBarIcon: ({focused, color}) => <Ionicons name={focused ? 'home' : 'home-outline'} color={color} size={24} />,
        }}
      />
      <Tab.Screen
        name="Inspiration"
        component={Inspiration}
        options={{
          title: 'Khám phá',
          tabBarIcon: ({focused, color}) => <Ionicons name={focused ? 'compass-sharp' : 'compass-outline'} color={color} size={24} />,
          tabBarLabel: 'Khám phá',
          headerShown: false,
          headerTransparent: true,
        }}
      />
      <Tab.Screen
        name="Order"
        component={OrdersScreen}
        listeners={{
          tabPress: e => {
            if (!isLoggedIn) {
              e.preventDefault();
              openLoginRequiredModal();
            }
          },
        }}
        options={{
          title: 'Đơn hàng',
          headerTintColor: theme.colors.textLight,
          tabBarIcon: ({focused, color}) => <Ionicons name={focused ? 'document-text' : 'document-text-outline'} color={color} size={24} />,
        }}
      />
      <Tab.Screen
        name="Account"
        component={UserScreen}
        options={{
          title: 'Tài khoản',
          headerTintColor: theme.colors.textLight,
          tabBarIcon: ({focused, color}) => <Ionicons name={focused ? 'person-circle-sharp' : 'person-circle-outline'} color={color} size={24} />,
          tabBarLabel: 'Tài khoản',
          headerShown: true,
        }}
      />
    </Tab.Navigator>
  );
};

export default MainTabsScreen;
