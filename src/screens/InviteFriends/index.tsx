import {Button, Input, Text} from '@rneui/themed';
import React, {useCallback, useMemo, useState} from 'react';
import {Linking, Platform, Pressable, ScrollView, Share, StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import {useGetAffiliateStatistic, useGetCurrentUser} from '~utils/api/auth';
import {useToast} from 'react-native-toast-notifications';
import Clipboard from '@react-native-clipboard/clipboard';
import RefCodeChangeDialog from '~components/by-screens/InviteFriends/RefCodeChangeDialog';
import LinkedRefCode from '~components/by-screens/InviteFriends/LinkedRefCode';
import {useAuthActions} from '~hooks';
import {env} from '~utils/config';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

const InviteFriends: React.FC<NativeStackScreenProps<RootStackParamList, 'InviteFriends'>> = ({navigation}) => {
  const {data} = useGetCurrentUser();
  const toast = useToast();
  const [refCodeChangeDialogOpen, setRefCodeChangeDialogOpen] = useState(false);
  const {isLoggedIn} = useAuthActions();
  const {data: affStatistic} = useGetAffiliateStatistic();
  const insets = useSafeAreaInsets();

  const handleViewProgramDetail = useCallback(() => {
    navigation.navigate('WebView', {
      url: 'https://go.thitruongsi.com/ds-mgm',
    });
  }, []);

  const handleNavigateMyReferrers = useCallback(() => {
    navigation.navigate('MyReferrers');
  }, []);

  const inviteURL = useMemo(() => {
    return `${env.WEBVIEW_URL}/invite?ref_code=${data?.ref_code}`;
  }, [data?.ref_code]);

  const handleShare = useCallback(() => {
    Share.share(
      {
        message: Platform.OS === 'ios' ? 'Gửi lời mời cho bạn bè' : inviteURL,
        url: inviteURL,
        title: '',
      },
      {
        tintColor: theme.colors.primary,
      },
    );
  }, []);

  const copy = useCallback(
    (text: string) => {
      Clipboard.setString(text);
      toast.show('Đã sao chép!', {
        placement: 'bottom',
        duration: 1000,
      });
    },
    [inviteURL],
  );

  const handlePreviewMGM = useCallback(() => {
    if (Platform.OS === 'web') {
      Linking.canOpenURL(inviteURL);
      return;
    }

    navigation.navigate('WebView', {
      url: inviteURL,
    });
  }, []);

  return (
    <ScrollView style={[styles.container, {paddingBottom: insets.bottom}]}>
      <View style={{backgroundColor: theme.colors.white, padding: 12, borderRadius: 12, marginBottom: theme.spacing.m}}>
        <View style={[theme.globalStyles.flexRow, {alignItems: 'flex-start', marginBottom: theme.spacing.s}]}>
          <Text style={{fontSize: theme.typography.base, marginRight: theme.spacing.xs}}>🎁</Text>
          <Text style={{fontSize: theme.typography.base, flex: 1}}>Nhận thưởng đến 50K cho mỗi bạn bè tham gia</Text>
        </View>
        <View style={[theme.globalStyles.flexRow, {alignItems: 'flex-start', marginBottom: theme.spacing.s}]}>
          <Text style={{fontSize: theme.typography.base, marginRight: theme.spacing.xs}}>🎁</Text>
          <Text style={{fontSize: theme.typography.base, flex: 1}}>Nhận thưởng thụ động 3% hoa hồng của bạn bè trong suốt 1 năm</Text>
        </View>

        <Button
          size="sm"
          type="clear"
          titleStyle={{fontSize: theme.typography.base, color: theme.colors.text, fontWeight: '600'}}
          icon={{
            type: 'ionicon',
            name: 'document-text-outline',
            size: 18,
          }}
          onPress={handleViewProgramDetail}>
          Xem chính sách giới thiệu
        </Button>
      </View>

      <View
        style={[
          {
            backgroundColor: theme.colors.white,
            padding: 12,
            paddingHorizontal: theme.spacing.s,
            borderRadius: 12,
          },
        ]}>
        <View style={[theme.globalStyles.flexRow, {marginBottom: theme.spacing.l}]}>
          <Text style={{fontWeight: 'bold', fontSize: theme.typography.base}}>Gửi link giới thiệu đến bạn bè</Text>
          <Ionicons name="settings-outline" style={{marginLeft: 'auto', paddingHorizontal: 8}} size={22} color={theme.colors.textLight} onPress={() => setRefCodeChangeDialogOpen(true)} />
        </View>
        <View>
          <Input
            containerStyle={{paddingHorizontal: 0, flexShrink: 1, marginRight: 0}}
            inputContainerStyle={{
              paddingVertical: 0,
              height: 40,
              backgroundColor: theme.colors.gray10,
              borderColor: theme.colors.gray10,
              borderRadius: 8,
            }}
            inputStyle={{color: theme.colors.text, padding: 0, height: 30, paddingVertical: 0, fontSize: theme.typography.base}}
            value={data?.ref_code}
            renderErrorMessage={false}
            editable={false}
            rightIcon={{
              type: 'ionicon',
              name: 'copy-outline',
              onPress: () => copy(data?.ref_code as string),
              size: 22,
              color: theme.colors.primary,
            }}
            label="Mã giới thiệu"
            labelStyle={{fontWeight: 'normal'}}
          />
          <View style={{flexDirection: 'row', alignItems: 'flex-end'}}>
            <Input
              containerStyle={{paddingHorizontal: 0, flexShrink: 1, marginRight: 0, marginTop: theme.spacing.m}}
              inputContainerStyle={{
                paddingVertical: 0,
                height: 40,
                backgroundColor: theme.colors.gray10,
                borderColor: theme.colors.gray10,
                borderRadius: 8,
              }}
              inputStyle={{color: theme.colors.text, padding: 0, height: 30, paddingVertical: 0, fontSize: theme.typography.base}}
              value={inviteURL}
              renderErrorMessage={false}
              editable={false}
              rightIcon={
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <Ionicons name="copy-outline" size={24} color={theme.colors.primary} onPress={() => copy(inviteURL)} />
                  <Button
                    size="sm"
                    titleStyle={{fontSize: theme.typography.base, color: theme.colors.text}}
                    icon={{
                      type: 'ionicon',
                      name: 'eye',
                      size: 14,
                      color: theme.colors.textLight,
                    }}
                    containerStyle={{marginLeft: 4}}
                    buttonStyle={{padding: 2, paddingVertical: 4, borderRadius: 8, backgroundColor: '#d8e2ec'}}
                    onPress={handlePreviewMGM}>
                    Xem
                  </Button>
                </View>
              }
              label="Link giới thiệu của bạn"
              labelStyle={{fontWeight: 'normal'}}
            />
          </View>
        </View>

        <Button
          containerStyle={{alignSelf: 'center', marginTop: theme.spacing.l}}
          size="sm"
          buttonStyle={{borderRadius: 30, borderColor: theme.colors.textLight}}
          icon={{type: 'ionicon', name: 'share-social', color: theme.colors.white, size: 20}}
          titleStyle={{fontSize: theme.typography.md, color: theme.colors.white, fontWeight: '500'}}
          onPress={handleShare}>
          Chia sẻ link
        </Button>
      </View>

      {/*  */}
      {/*  */}

      <Pressable
        onPress={handleNavigateMyReferrers}
        style={[
          {
            backgroundColor: theme.colors.white,
            padding: 16,
            paddingHorizontal: theme.spacing.m,
            borderRadius: 12,
            marginTop: theme.spacing.m,
          },
          theme.globalStyles.flexRow,
        ]}>
        <View style={[theme.globalStyles.flexRow]}>
          <Text>Mời thành công:</Text>
          <Text style={{color: theme.colors.primary, fontWeight: 'bold', marginLeft: theme.spacing.s}}>&nbsp;{affStatistic?.total_referrers} người</Text>
        </View>
        <Ionicons name="chevron-forward" color={theme.colors.textLight} size={18} style={{marginLeft: 'auto'}} />
      </Pressable>
      {/* 
      <View
        style={{
          backgroundColor: theme.colors.white,
          padding: 12,
          borderRadius: 12,
          marginTop: theme.spacing.m,
        }}>
        <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight, marginBottom: theme.spacing.s}}>Tài liệu liên quan</Text>
        <Text style={{fontSize: theme.typography.lg, fontWeight: '500'}}>Mời bạn cùng bán hàng - cùng nhận thưởng đến 100.000đ</Text>
        <Text numberOfLines={3} style={{fontSize: theme.typography.base, color: theme.colors.textLight, marginTop: theme.spacing.s}}>
          Nhận thưởng tức thì khi bạn bè tham gia bán hàng, cùng tiềm năng thu nhập thụ động không giới hạn đến từ hoa hồng của bạn bè bán được trong suốt quá trình tham gia.
        </Text>

        <Button
          type="clear"
          size="sm"
          containerStyle={{alignSelf: 'flex-end', marginTop: theme.spacing.m}}
          icon={{type: 'ionicon', name: 'chevron-forward', size: 18, color: theme.colors.primary}}
          iconRight
          onPress={handleViewProgramDetail}>
          Xem chi tiết
        </Button>
      </View> */}

      <RefCodeChangeDialog open={refCodeChangeDialogOpen} onClose={() => setRefCodeChangeDialogOpen(false)} />

      {isLoggedIn && <LinkedRefCode />}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: theme.spacing.m - 4,
    paddingVertical: theme.spacing.s,
  },
});

export default InviteFriends;
