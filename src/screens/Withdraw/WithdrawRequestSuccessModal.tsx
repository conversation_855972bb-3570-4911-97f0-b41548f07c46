import {ScreenHeight, ScreenWidth} from '@rneui/base';
import {Button, Text} from '@rneui/themed';
import React from 'react';
import {Image, Modal, StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';
// @ts-ignore
import SuccessIcon from '~assets/success-icon.png';
import {MyBank} from '~utils/types/common';

type WithdrawRequestSuccessModal = {
  open: boolean;
  onClose: () => void;
  withdrawAmount?: number;
  bankAccount?: MyBank;
};

const WithdrawRequestSuccessModal: React.FC<WithdrawRequestSuccessModal> = props => {
  return (
    <Modal onRequestClose={props.onClose} visible={props.open}>
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: theme.colors.gray10}}>
        <View style={styles.container}>
          <Image source={SuccessIcon} style={{width: 100, height: 100}} />
          <Text style={{fontSize: theme.typography.lg2, marginTop: theme.spacing.m}}><PERSON><PERSON><PERSON> cầu rút tiền thành công</Text>
          <Text style={styles.amount}>{props.withdrawAmount ? props.withdrawAmount.toLocaleString('vi-VN') : ''} VND</Text>

          {/* <View style={{alignSelf: 'flex-start', marginTop: theme.spacing.l}}>
            <Text style={styles.text}>
              Tên người nhận: &nbsp;
              <Text style={[styles.text, {textTransform: 'uppercase'}]}>{props.bankAccount?.name}</Text>
            </Text>
            <Text style={styles.text}>
              Số tài khoản: &nbsp;
              <Text style={[styles.text, {textTransform: 'uppercase'}]}>{props.bankAccount?.number}</Text>
            </Text>
            <Text style={styles.text}>
              Ngân hàng: &nbsp;
              <Text style={[styles.text, {textTransform: 'uppercase'}]}>{props.bankAccount?.bank.short_name}</Text>
            </Text>
          </View> */}

          <View style={{alignSelf: 'flex-start', marginTop: theme.spacing.l, borderBottomWidth: 1, borderBottomColor: theme.colors.gray30, paddingBottom: theme.spacing.l}}>
            <View style={styles.bankInfo__item}>
              <Text style={styles.bankInfo__field}>Tên người nhận:</Text>
              <Text style={styles.bankInfo__value}>{props.bankAccount?.name}</Text>
            </View>
            <View style={styles.bankInfo__item}>
              <Text style={styles.bankInfo__field}>Số tài khoản:</Text>
              <Text style={styles.bankInfo__value}>{props.bankAccount?.number}</Text>
            </View>
            <View style={styles.bankInfo__item}>
              <Text style={styles.bankInfo__field}>Ngân hàng:</Text>
              <Text style={styles.bankInfo__value}>{props.bankAccount?.bank.short_name}</Text>
            </View>
          </View>

          <View style={{marginTop: theme.spacing.l, borderBottomWidth: 1, borderBottomColor: theme.colors.gray20, paddingBottom: theme.spacing.l}}>
            <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight, alignSelf: 'flex-start'}}>Giao dịch của bạn sẽ được xử lý trong tối đa 1 ngày làm việc, cụ thể:</Text>
            <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight, marginTop: theme.spacing.m, alignSelf: 'flex-start'}}>
              - Yêu cầu trước 8 giờ sáng: nhận được trong cùng ngày làm việc (trừ thứ 7, chủ nhật & ngày lễ)
            </Text>
            <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight, marginTop: theme.spacing.s, alignSelf: 'flex-start'}}>
              - Yêu cầu sau 8 giờ sáng: nhận được trong ngày làm việc tiếp theo (trừ thứ 7, chủ nhật & ngày lễ)
            </Text>
          </View>

          <Text style={{marginTop: 'auto'}}>TTS Dropship - Cùng bạn bán hàng</Text>
          <View style={{marginTop: 'auto', width: '100%', flexDirection: 'row'}}>
            {/* <TouchableOpacity>
              <View style={{width: 90, alignItems: 'center'}}>
                <Ionicons name="camera-outline" size={24} color={theme.colors.primary} />
                <Text style={{fontSize: theme.typography.sm, color: theme.colors.textLight, textAlign: 'center'}}>Chụp màn hình</Text>
              </View>
            </TouchableOpacity> */}

            <Button containerStyle={{flex: 1}} size="sm" buttonStyle={{borderRadius: 100}} titleStyle={{fontSize: theme.typography.md}} onPress={props.onClose}>
              OK
            </Button>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    width: (ScreenWidth * 90) / 100,
    height: (ScreenHeight * 80) / 100,
    shadowColor: 'gray',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 5,
    borderRadius: 30,
    // justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: theme.spacing.l,
    paddingHorizontal: theme.spacing.l,
  },

  amount: {
    fontSize: theme.typography.lg2,
    color: theme.colors.primary,
    fontWeight: 'bold',
    marginTop: theme.spacing.s,
  },

  text: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
  },

  bankInfo__item: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: theme.spacing.s,
  },
  bankInfo__field: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
    marginLeft: theme.spacing.s,
    flexShrink: 0,
    width: 120,
  },
  bankInfo__value: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
    textTransform: 'uppercase',
  },
});

export default WithdrawRequestSuccessModal;
