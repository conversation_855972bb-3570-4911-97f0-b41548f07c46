import {Button, Input, Text} from '@rneui/themed';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {ActivityIndicator, KeyboardAvoidingView, Platform, Pressable, SafeAreaView, ScrollView, StyleSheet, View} from 'react-native';
import {getWithdrawalFee, useGetIncomeSummary, useGetMyBankAccounts, useWithdrawalRequestMutation} from '~utils/api/wallet';
import theme from '~utils/config/themes/theme';
import {formatPriceToString} from '~utils/helpers/price';
import {MyBank} from '~utils/types/common';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import BankAccountItem from '~components/by-screens/BankAccountList/BankAccountItem';
import BankAccountSelectModal from '~components/by-screens/Withdraw/BankAccountSelectModal';
import WithdrawRequestSuccessModal from './WithdrawRequestSuccessModal';
import {useToast} from 'react-native-toast-notifications';
import {useGetCurrentUser, useGetIdentityVerificationStatus} from '~utils/api/auth';
import IdentityVerificationRequired from '~components/by-screens/Withdraw/IdVerificationRequired';

const Withdraw: React.FC<NativeStackScreenProps<RootStackParamList, 'Withdraw'>> = ({navigation}) => {
  const {data: currentUser, refetch} = useGetCurrentUser();
  const {data} = useGetIncomeSummary();
  const {data: bankAccounts} = useGetMyBankAccounts();
  const [withdrawFeeLoading, setWithdrawLoading] = useState(false);
  const [withdrawFee, setWithdrawFee] = useState<number | undefined>();
  const [amount, setAmount] = useState<any>('');
  const [selectedBankAccount, setSelectedBankAccount] = useState<MyBank>();
  const [bankAccountSelectModalOpen, setBankAccountSelectModalOpen] = useState(false);
  const mutation = useWithdrawalRequestMutation();
  const [errors, setErrors] = useState<Partial<{amount: string | string[]}>>({});
  const [successModalOpen, setSuccessModalOpen] = useState(false);
  const toast = useToast();
  const {data: identityVerificationStatus} = useGetIdentityVerificationStatus();

  useEffect(() => {
    refetch();
    setWithdrawLoading(true);
    getWithdrawalFee()
      .then(fee => {
        setWithdrawFee(fee);
      })
      .finally(() => {
        setWithdrawLoading(false);
      });
  }, []);

  useEffect(() => {
    if (bankAccounts && bankAccounts.length && !selectedBankAccount) {
      setSelectedBankAccount(bankAccounts.find(acc => acc.default));
    }
  }, [bankAccounts]);

  const isWithdrawDisabled = useMemo(() => {
    return withdrawFeeLoading || !amount || !selectedBankAccount;
  }, [withdrawFeeLoading, amount, selectedBankAccount]);

  const handleChange = useCallback(
    (text: string) => {
      if (errors.amount) {
        setErrors(prev => ({...prev, amount: ''}));
      }
      setAmount(parseInt(text.replace(/\./g, ''), 10) || '');
    },
    [errors],
  );

  const suggestedAmount = useMemo(() => {
    return [500000, 1000000, 2000000, data?.can_withdrawl].filter((suggestAmount: any) => suggestAmount > 0 && suggestAmount <= (data?.can_withdrawl as number));
  }, [data]);

  const handleAddBankAccount = useCallback(() => {
    navigation.navigate('AddBankAccount');
  }, []);

  const handleBankAccountChange = useCallback((account: MyBank) => {
    setSelectedBankAccount(account);
  }, []);

  const isIdentityVerificationRequired = useMemo(() => {
    return currentUser?.idcard_verify && currentUser?.idcard_verify !== 'verified' && !identityVerificationStatus?.rollback;
  }, [currentUser?.idcard_verify, identityVerificationStatus?.rollback]);

  const handleSubmit = useCallback(async () => {
    try {
      if (isIdentityVerificationRequired) {
        throw new Error('Yêu cầu định danh tài khoản');
      }

      await mutation
        .mutateAsync({
          amount,
          bank_account_id: selectedBankAccount?.id as number,
        })
        .catch(err => {
          if (err.response?.data?.errors) {
            setErrors(err.response.data.errors);
          }

          throw err;
        });

      setSuccessModalOpen(true);
    } catch (error: any) {
      toast.show(error?.response?.data?.message || error?.message || 'Có lỗi xảy ra', {
        duration: 3000,
        placement: 'center',
        type: 'danger',
      });
    }
  }, [amount, selectedBankAccount, currentUser]);

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: theme.colors.white}}>
      <ScrollView style={styles.container} keyboardDismissMode="interactive" keyboardShouldPersistTaps="handled">
        {isIdentityVerificationRequired && <IdentityVerificationRequired />}
        <>
          <View style={[styles.row, {justifyContent: 'center', alignItems: 'flex-end', marginVertical: 50}]}>
            <Text style={{marginRight: theme.spacing.s, color: theme.colors.text, fontWeight: '500'}}>Số dư:</Text>
            <Text style={{fontSize: theme.typography.lg2, fontWeight: '500'}}>{formatPriceToString(data?.can_withdrawl ?? 0)}</Text>
          </View>
          <View style={{marginTop: theme.spacing.l}}>
            <Input
              label="Nhập số tiền cần rút"
              placeholder={'0.00'}
              containerStyle={{paddingHorizontal: 0, paddingVertical: 0}}
              value={formatPriceToString(amount, false, '')}
              onChangeText={handleChange}
              keyboardType="numeric"
              errorMessage={errors.amount as any}
              renderErrorMessage={Boolean(errors.amount)}
              errorStyle={{color: 'red'}}
            />
            <View style={{flexDirection: 'row', flexWrap: 'wrap'}}>
              {suggestedAmount.map((suggestAmount: any) => (
                <Pressable style={styles.suggestAmount} key={suggestAmount} onPress={() => setAmount(suggestAmount)}>
                  <Text style={{fontSize: 12, color: theme.colors.text}}>{formatPriceToString(suggestAmount)}</Text>
                </Pressable>
              ))}
            </View>
          </View>

          <View style={{marginTop: theme.spacing.l, flexDirection: 'row'}}>
            <Text style={{fontSize: theme.typography.md, color: theme.colors.textLight, fontWeight: '500'}}>Phí rút: </Text>
            {withdrawFeeLoading && <ActivityIndicator size={'small'} />}
            {!withdrawFeeLoading && withdrawFee === 0 ? <Text>Miễn phí</Text> : <Text>{formatPriceToString(withdrawFee ?? 0)}</Text>}
          </View>

          <View style={{marginTop: theme.spacing.l}}>
            <Text style={{fontSize: theme.typography.md, color: theme.colors.textLight, fontWeight: '500'}}>Tài khoản ngân hàng</Text>
            {selectedBankAccount && (
              <BankAccountItem
                item={selectedBankAccount}
                key={selectedBankAccount.id}
                rightAction={
                  <Button
                    type="clear"
                    buttonStyle={{paddingHorizontal: 0, paddingVertical: 0}}
                    titleStyle={{fontSize: theme.typography.base}}
                    containerStyle={{alignSelf: 'center'}}
                    onPress={() => setBankAccountSelectModalOpen(true)}>
                    Thay đổi
                  </Button>
                }
              />
            )}
            {!selectedBankAccount && !bankAccounts?.length && (
              <View style={{marginVertical: theme.spacing.m}}>
                <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight, textAlign: 'center'}}>Chưa có tài khoản ngân hàng nào</Text>
                <Button
                  icon={{type: 'ionicon', name: 'add-circle', size: 16, color: theme.colors.primary}}
                  size="sm"
                  type="clear"
                  titleStyle={{fontSize: theme.typography.base}}
                  onPress={handleAddBankAccount}>
                  Thêm tài khoản
                </Button>
              </View>
            )}
            {!selectedBankAccount && (bankAccounts?.length ?? 0) > 0 && (
              <View style={{marginVertical: theme.spacing.m}}>
                <Button
                  icon={{type: 'ionicon', name: 'add-circle', size: 16, color: theme.colors.primary}}
                  size="sm"
                  type="clear"
                  titleStyle={{fontSize: theme.typography.base}}
                  onPress={() => setBankAccountSelectModalOpen(true)}>
                  Chọn tài khoản
                </Button>
              </View>
            )}
          </View>
        </>
        {/* )} */}
      </ScrollView>
      <KeyboardAvoidingView style={{paddingHorizontal: 12}} behavior={Platform.OS === 'ios' ? 'padding' : 'height'} keyboardVerticalOffset={100}>
        <Button size="sm" disabled={isWithdrawDisabled} buttonStyle={{borderRadius: 12}} onPress={handleSubmit} loading={mutation.isLoading}>
          Yêu cầu rút tiền
        </Button>
      </KeyboardAvoidingView>

      <BankAccountSelectModal onSelect={handleBankAccountChange} open={bankAccountSelectModalOpen} onClose={() => setBankAccountSelectModalOpen(false)} selectedAccountId={selectedBankAccount?.id} />
      <WithdrawRequestSuccessModal
        onClose={() => {
          setSuccessModalOpen(false);
          setTimeout(() => {
            navigation.goBack();
          }, 100);
        }}
        open={successModalOpen}
        bankAccount={selectedBankAccount}
        withdrawAmount={amount}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    paddingVertical: theme.spacing.l,
    paddingHorizontal: theme.spacing.m,
    flex: 1,
  },
  row: {
    flexDirection: 'row',
  },
  suggestAmount: {
    paddingHorizontal: 8,
    paddingVertical: 8,
    backgroundColor: theme.colors.gray10,
    marginTop: theme.spacing.s,
    marginRight: theme.spacing.m,
    borderRadius: 12,
  },
});

export default Withdraw;
