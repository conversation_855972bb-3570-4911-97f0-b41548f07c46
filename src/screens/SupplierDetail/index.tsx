import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React, {useCallback, useEffect, useMemo, useRef} from 'react';
import {ActivityIndicator, FlatList, ListRenderItemInfo, NativeSyntheticEvent, View, NativeScrollEvent} from 'react-native';
import {useGetShop} from '~utils/api/shop';
import theme from '~utils/config/themes/theme';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import {useSearchProductsInfinite} from '~utils/api/product';
import {ProductRequestQuery, SearchProduct} from '~utils/types/product';
import ProductCard from '~components/shared/ProductCard';
import SupplierIntroduction from '~components/by-screens/SupplierDetail/SupplierIntroduction';
import SortBar from '~components/by-screens/SupplierDetail/SortBar';
import useSort from '~hooks/useSort';
import SupplierDetailHeader from '~components/by-screens/SupplierDetail/SupplierDetailHeader';
import ShopInfo from '~components/by-screens/ProductDetail/ShopInfo';
import ScrollToTop from '~components/shared/ScrollToTop';
import SEOMeta from '~components/by-screens/SupplierDetail/SEOMeta';

const SupplierDetail: React.FC<NativeStackScreenProps<RootStackParamList, 'SupplierDetail'>> = ({route, navigation}) => {
  const flatListRef = React.useRef<FlatList>(null);
  const {data, isLoading} = useGetShop(route.params.shopId);
  const {sortState, updateSort} = useSort({
    sort_by: route.params.sort_by || 'dropship_published_at',
    ascending: ['true', 'false'].includes(route.params.ascending as any) ? (route.params.ascending === 'true' ? true : false) : undefined,
  });
  const [filterState, setFilterState] = React.useState<ProductRequestQuery>(route.params);
  const headerChanged = useRef<Boolean>(false);

  useEffect(() => {
    navigation.setOptions({
      headerRight: props => <SupplierDetailHeader {...props} shopId={route.params.shopId} />,
    });
  }, [route.params.shopId]);

  useEffect(() => {
    if (data?.styles?.title?.color) {
      navigation.setOptions({
        headerTintColor: data?.styles?.title?.color,
      });
    }
  }, [navigation, data?.styles]);

  const {
    data: searchData,
    fetchNextPage,
    isLoading: isLoadingSearch,
  } = useSearchProductsInfinite(
    {
      filter_shop_id: data?.id,
      sort_by: sortState.sort_by,
      ascending: sortState.ascending,
      ...filterState,
    },
    {enabled: Boolean(data?.id)},
  );

  const updateFilter = useCallback((newFilter: ProductRequestQuery) => {
    setFilterState(newFilter);
  }, []);

  const topItems = useMemo(
    () => [
      data ? (
        <View style={{flex: 1, marginHorizontal: -12}}>
          <ShopInfo shop={data} paddingInsetTop />
        </View>
      ) : (
        <View style={{height: 120}} />
      ),
      <View />,
      Boolean(data?.dropship_introduction) ? (
        <View style={{flex: 1, marginHorizontal: -12, marginTop: theme.spacing.s, marginBottom: theme.spacing.m}}>
          <SupplierIntroduction shop={data} />
        </View>
      ) : (
        <View />
      ),
      <View />,
      <View style={{flex: 1, marginHorizontal: -12, marginBottom: theme.spacing.s}}>
        <SortBar updateFilter={updateFilter} disableScoreSort sortState={sortState} updateSort={updateSort} shopId={data?.id} />
      </View>,
      <View />,
    ],
    [data, sortState],
  );

  const dataFlat = useMemo(() => {
    return [...topItems, ...(searchData?.pages.flatMap(page => page.products) ?? [])] ?? topItems;
  }, [data, searchData]);

  const renderItem = useCallback(
    (item: ListRenderItemInfo<SearchProduct>) => {
      if (item.index < topItems.length) {
        return item.item;
      }
      return <ProductCard product={item.item} />;
    },
    [dataFlat, topItems],
  );

  const handleScrollToEnd = useCallback(() => {
    fetchNextPage();
  }, []);

  const scrollToTop = useCallback(() => {
    flatListRef.current?.scrollToOffset({animated: true, offset: 0});
  }, []);

  const handleScroll = useCallback((event: NativeSyntheticEvent<NativeScrollEvent>) => {
    if (event.nativeEvent.contentOffset.y > 100 && !headerChanged.current) {
      headerChanged.current = true;

      navigation.setOptions({
        // headerTransparent: false,
        headerTintColor: theme.colors.text,
        headerBackground: () => <View style={{backgroundColor: 'white', flex: 1}}></View>,
      });
    }
    if (event.nativeEvent.contentOffset.y < 100 && headerChanged.current) {
      headerChanged.current = false;

      navigation.setOptions({
        // headerTransparent: true,
        headerTintColor: data?.styles?.title?.color || theme.colors.text,
        headerBackground: () => null,
      });
    }
  }, []);

  return (
    <>
      <SEOMeta shop={data} />
      <View style={[{flex: 1}, !isLoading && dataFlat.length === topItems.length && {backgroundColor: theme.colors.white}]}>
        <FlatList
          ref={flatListRef}
          stickyHeaderIndices={[2]}
          renderItem={renderItem as any}
          numColumns={2}
          style={{flex: 1}}
          contentContainerStyle={{paddingHorizontal: 12}}
          onEndReached={handleScrollToEnd}
          data={dataFlat}
          ListFooterComponent={<>{isLoadingSearch && <ActivityIndicator />}</>}
          onScroll={handleScroll}
        />

        <ScrollToTop onPress={scrollToTop} />
      </View>
    </>
  );
};

export default SupplierDetail;
