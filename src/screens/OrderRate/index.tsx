import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React, {useEffect} from 'react';
import {ActivityIndicator, SafeAreaView, ScrollView} from 'react-native';
import RateItem from '~components/by-screens/OrderRate/RateItem';
import useRateStore from '~hooks/store/useRateStore';
import {useGetOrder} from '~utils/api/order';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import RateSubmit from '~components/by-screens/OrderRate/RateSubmit';
import OperationRate from '~components/by-screens/OrderRate/OperationRate';
import {useGetRatingDetail} from '~utils/api/review';

const OrderRate: React.FC<NativeStackScreenProps<RootStackParamList, 'OrderRate'>> = ({route}) => {
  const {data} = useGetOrder(route.params.orderId);
  const {data: rate} = useGetRatingDetail(String(route.params.orderId), 'order', {enabled: !!route.params.isUpdate});
  const rateStore = useRateStore();
  const [isInitialized, setIsInitialized] = React.useState(false);

  useEffect(() => {
    if (data?.id) {
      if (route.params.isUpdate && !rate) {
        return;
      }

      if (isInitialized) {
        return;
      }

      rateStore.initRate({
        action_type: 'order',
        action_id: String(data.id),
        anonymous: false,
        line_items: data.line_items.reduce(
          (a, c) => ({
            ...a,
            [c.variant_id]: {
              variant_id: c.variant_id,
              comment: '',
              photos: [],
              videos: [],
              quality_rate: 0,
              image_src: c.image_src,
              title: c.title,
              variant_title: c.variant_title,
            },
          }),
          {},
        ),
        delivery_service: 0,
        seller_service: 0,
        comment: '',
      });

      setIsInitialized(true);
    }
  }, [data?.id, rate, route.params.isUpdate]);

  if (!isInitialized) {
    return <ActivityIndicator />;
  }

  return (
    <SafeAreaView style={{flex: 1}}>
      <ScrollView style={{flex: 1}}>
        {data?.line_items.map(lineItem => (
          <RateItem onChange={rateStore.updateRateLineItem} lineItem={lineItem} key={lineItem.id} />
        ))}
        <OperationRate onChange={rateStore.updateRateStore} />
      </ScrollView>
      <RateSubmit />
    </SafeAreaView>
  );
};

export default OrderRate;
