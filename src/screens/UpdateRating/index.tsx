import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React, {useEffect} from 'react';
import {SafeAreaView, ScrollView} from 'react-native';
import OperationRate from '~components/by-screens/UpdateRating/OperationRate';
import RateItem from '~components/by-screens/UpdateRating/RateItem';
import RateSubmit from '~components/by-screens/UpdateRating/RateSubmit';
import useUpdateRateStore from '~hooks/store/useUpdateRateStore';
import {useGetOrder} from '~utils/api/order';
import {useGetRatingDetail} from '~utils/api/review';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';

const UpdateRating: React.FC<NativeStackScreenProps<RootStackParamList, 'UpdateRating'>> = ({route}) => {
  const updateRateStore = useUpdateRateStore();
  const {data} = useGetOrder(route.params.orderId);
  const {data: rate} = useGetRatingDetail(String(route.params.orderId), 'order', {enabled: !!route.params.isUpdate});

  const [isInitialized, setIsInitialized] = React.useState(false);

  useEffect(() => {
    if (data?.id && rate) {
      if (isInitialized) {
        return;
      }

      updateRateStore.initRate({
        ...rate,
        action_id: rate.action_id,
        action_type: rate.action_type,
        anonymous: rate.anonymous,
        comment: rate.comment,
        delivery_service: rate.delivery_service,
        seller_service: rate.delivery_service,
        line_items: data.line_items.reduce((a, c) => {
          const rateLineItem = rate.line_items.find(r => r.variant_id === c.variant_id);
          return {
            ...a,
            [c.variant_id]: {
              ...rateLineItem,
              image_src: c.image_src,
              title: c.title,
              variant_title: c.variant_title,
            },
          };
        }, {}),
      });
      setIsInitialized(true);
    }
  }, [data?.id, rate]);

  if (!isInitialized) {
    return null;
  }

  return (
    <SafeAreaView style={{flex: 1}}>
      <ScrollView style={{flex: 1}}>
        {Object.keys(updateRateStore.line_items).map(lineItemId => {
          return <RateItem onChange={updateRateStore.updateRateLineItem} lineItem={updateRateStore.line_items[lineItemId]} key={lineItemId} />;
        })}

        <OperationRate sellerService={updateRateStore.seller_service} deliveryService={updateRateStore.delivery_service} onChange={updateRateStore.updateRateStore} />
      </ScrollView>
      <RateSubmit />
    </SafeAreaView>
  );
};

export default UpdateRating;
