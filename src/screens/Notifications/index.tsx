import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Text} from '@rneui/themed';
import React, {useCallback, useEffect, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {NavigationState, SceneRendererProps, TabBar, TabView} from 'react-native-tab-view';
import NotificationList from '~components/by-screens/Notifications/NotificationList';
import {NotificationCategory, useGetNotificationCategories, useMarkAllSeenNotificationMutation} from '~utils/api/notifications';
import theme from '~utils/config/themes/theme';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import MarkAllReadButton from '~components/by-screens/Notifications/MarkAllReadButton';
import produce from 'immer';

type State = NavigationState<{
  key: string;
  title: string;
}>;

const Notifications: React.FC<NativeStackScreenProps<RootStackParamList, 'Notifications'>> = ({navigation}) => {
  const {data: notiCategories} = useGetNotificationCategories();
  const [index, setIndex] = useState(0);
  const [routes, setRoutes] = useState<any[]>([{key: 'all', title: 'Tất cả'}, ...(notiCategories || []).map(category => ({...category, key: category.type}))]);
  const mutation = useMarkAllSeenNotificationMutation();

  useEffect(() => {
    // navigation.setOptions({
    //   headerRight: MarkAllReadButton,
    // });

    mutation.mutate({ids: []});
  }, []);

  useEffect(() => {
    setRoutes([{key: 'all', title: 'Tất cả'}, ...(notiCategories || []).map(category => ({...category, key: category.type}))]);
  }, [notiCategories]);

  const renderTabBar = (tabbarProps: SceneRendererProps & {navigationState: State}) => (
    <TabBar
      {...tabbarProps}
      scrollEnabled
      indicatorStyle={styles.indicator}
      style={styles.tabbar}
      tabStyle={styles.tab}
      renderLabel={scene => (
        <View style={theme.globalStyles.flexRow}>
          <Text numberOfLines={1} style={{fontSize: theme.typography.base, color: scene.focused ? '#008060' : '#333', paddingHorizontal: 4}}>
            {scene.route.title}
          </Text>
          {Boolean((scene.route as any).unseen) && (
            <View style={{backgroundColor: theme.colors.red, ...theme.globalStyles.roundedFull, width: 5, height: 5, alignItems: 'center', justifyContent: 'center'}}>
              {/* <Text style={{fontSize: theme.typography.xs, color: theme.colors.white, fontWeight: 'bold'}}>{(scene.route as any).unseen > 1}</Text> */}
            </View>
          )}
        </View>
      )}
    />
  );

  const renderScene = useCallback(({route}: {route: (typeof routes)[0] & NotificationCategory}) => {
    switch (route.key) {
      default:
        return <NotificationList type={route.type} />;
    }
  }, []);

  const handleIndexChange = useCallback(
    (newIndex: number) => {
      setIndex(newIndex);
      if (routes[newIndex].unseen) {
        setRoutes(prev =>
          produce(prev, draft => {
            draft[newIndex].unseen = 0;
          }),
        );
      }
    },
    [routes],
  );

  return <TabView lazy renderTabBar={renderTabBar} navigationState={{index, routes}} renderScene={renderScene as any} onIndexChange={handleIndexChange} />;
};

const styles = StyleSheet.create({
  locationItem: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,.05)',
  },
  locationItem_text: {
    fontSize: 16,
  },
  tabbar: {
    backgroundColor: '#fff',
    borderBottomWidth: 2,
    borderBottomColor: 'rgba(0,0,0,.05)',
  },
  tab: {
    width: 'auto',
  },
  indicator: {
    backgroundColor: '#008060',
  },
  label: {
    fontSize: 16,
    fontWeight: '400',
    color: '#333',
    textTransform: 'none',
  },
});

export default Notifications;
