import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Button, Input} from '@rneui/base';
import React, {useCallback, useMemo, useState} from 'react';
import {Modal, Platform, ScrollView, StyleSheet, Text, View} from 'react-native';
import {SafeAreaView, useSafeAreaInsets} from 'react-native-safe-area-context';
import Select from '~components/shared/Form/Select';
import {LocationType} from '~components/shared/LocationBottomSheet';
import LocationTabView from '~components/shared/LocationTabView';
import useCreateOrderStore from '~hooks/store/useCreateOrderStore';
import {useCreateCustomerMutation} from '~utils/api/customer';
import {useGetLocations} from '~utils/api/location';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import Address from '~utils/types/address';
import Customer from '~utils/types/customer';
import customerValidate from '~utils/validate/customer';
import Ionicons from 'react-native-vector-icons/Ionicons';
import * as Sentry from '@sentry/react-native';
import theme from '~utils/config/themes/theme';
import {useSoftInputHeightChanged} from 'react-native-avoid-softinput';
import Animated, {useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';
import {useDisableAvoidSoftInput} from '~hooks/useAvoidSoftInput';
import {standardizePhone} from '~utils/helpers/phone';
import {useToast} from 'react-native-toast-notifications';
import AddressInputWithSearch from '~components/shared/Form/AddressInputWithSearch';

const CreateCustomerScreen: React.FC<NativeStackScreenProps<RootStackParamList, 'CreateCustomer'>> = ({route, navigation}) => {
  const [customer, setCustomer] = useState<Partial<Customer & {name: string}>>({});
  const [address, setAddress] = useState<Partial<Address>>({});
  const [submitLoading, setSubmitLoading] = useState(false);
  const mutation = useCreateCustomerMutation();
  const setNewOrderState = useCreateOrderStore(state => state.setNewOrderState);
  const {data} = useGetLocations();
  /* eslint-disable-next-line no-spaced-func */
  const [errors, setErrors] = useState<Partial<{[key in keyof (Customer & {name?: string})]: string}>>({});
  const [locationSelectOpen, setLocationSelectOpen] = useState(false);
  const [initialTab, setInitialTab] = useState(0);
  const insets = useSafeAreaInsets();
  const toast = useToast();

  useDisableAvoidSoftInput();

  const buttonContainerPaddingValue = useSharedValue(0);
  const buttonContainerAnimatedStyle = useAnimatedStyle(() => {
    return {
      paddingBottom: buttonContainerPaddingValue.value - insets.bottom + theme.spacing.s,
    };
  });

  useSoftInputHeightChanged(({softInputHeight}) => {
    if (Platform.OS === 'ios') {
      buttonContainerPaddingValue.value = withTiming(softInputHeight);
    }
  });

  const getCities = useMemo(() => (data || []).reduce<string[]>((acc, cur) => [...acc, ...Object.keys(cur)], []), [data]);
  const getProvinces = useMemo(() => {
    if (!address.city || !data) {
      return [];
    }

    for (const city of data) {
      if (Object.keys(city).includes(address.city)) {
        return Object.keys(city[address.city]);
      }
    }

    return [];
  }, [address.city, data]);

  const getWards = useMemo(() => {
    if (!data || !address.city || !address.province) {
      return [];
    }
    for (const city of data) {
      if (Object.keys(city).includes(address.city)) {
        return city[address.city][Object.keys(city[address.city]).filter(province => province === address.province)[0]] || [];
      }
    }
    return [];
  }, [data, address.city, address.province]);

  const handleLocationSelectPress = useCallback((type: LocationType) => {
    const tab = {
      city: 0,
      province: 1,
      ward: 2,
    };
    setInitialTab(tab[type]);
    setLocationSelectOpen(true);
  }, []);

  const handleCustomerChange = useCallback((field: keyof (Customer & {name: string}), value: any) => {
    setCustomer(prev => ({...prev, [field]: value}));
  }, []);

  const handleAddressChange = useCallback((field: keyof Address, value: any) => {
    setAddress(prev => ({...prev, [field]: value}));
  }, []);

  const handleAddress1Change = useCallback(
    (
      address1: string,
      additionalData?: {
        ward: string;
        city: string;
        province: string;
      },
    ) => {
      handleAddressChange('address1', address1);
      if (additionalData) {
        handleAddressChange('ward', additionalData.ward);
        handleAddressChange('city', additionalData.city);
        handleAddressChange('province', additionalData.province);
      }
    },
    [],
  );

  const handleSubmit = async () => {
    setSubmitLoading(true);
    setErrors({});
    try {
      let isValid = true;
      let nameSplits = customer.name?.replace(/^\s+|\s+$/gm, '').split(' ');
      const createCustomerData = {
        ...customer,
        first_name: nameSplits?.[0],
        last_name: nameSplits?.slice(1).join(' '),
        addresses: [address as Address],
        phone: customer.phone ? standardizePhone(customer.phone) : customer.phone,
      };
      await customerValidate.validate(createCustomerData, {abortEarly: false}).catch(err => {
        isValid = false;
        setErrors(err.inner.reduce((a: any, c: any) => ({...a, [c.path]: c.message}), {}));
        setSubmitLoading(false);
      });

      if (!isValid) {
        return;
      }

      const res = await mutation.mutateAsync({customer: createCustomerData});
      if (route.params?.isCreateOrder) {
        if (res?.default_address) {
          setNewOrderState('shipping_address', res.default_address);
        }
        navigation.popTo('OrderConfirm');
      } else {
        navigation.goBack();
      }
    } catch (error: any) {
      toast.show(error?.response?.data?.message || 'Tạo khách hàng thất bại', {
        type: 'danger',
        duration: 3000,
      });
      Sentry.captureMessage('Tao khach hang that bai');
    }
    setSubmitLoading(false);
  };

  const handleSelectDone = useCallback((add: Partial<Address>) => {
    setAddress(prev => ({...prev, ...add}));
    setLocationSelectOpen(false);
  }, []);

  return (
    <>
      <SafeAreaView edges={['bottom', 'left', 'right']} style={{flex: 1}}>
        <ScrollView
          style={styles.container}
          keyboardShouldPersistTaps="handled"
          contentInsetAdjustmentBehavior="always"
          contentInset={{
            bottom: 30,
          }}>
          <Input
            label="Tên người nhận"
            containerStyle={styles.formContainer}
            inputContainerStyle={styles.inputContainer}
            inputStyle={styles.input}
            labelStyle={styles.label}
            value={customer.name}
            onChangeText={text => handleCustomerChange('name', text)}
            errorMessage={errors.name}
          />
          <Input
            label="Số điện thoại"
            containerStyle={styles.formContainer}
            inputContainerStyle={styles.inputContainer}
            inputStyle={styles.input}
            labelStyle={styles.label}
            keyboardType="numeric"
            value={customer.phone}
            onChangeText={text => handleCustomerChange('phone', text)}
            errorMessage={errors.phone}
          />

          <AddressInputWithSearch label="Địa chỉ nhận hàng" address={address.address1} onDone={handleAddress1Change} errorMessage={(errors as any)['addresses[0].address1']} />
          <Select
            options={getCities}
            label="Tỉnh / thành phố"
            onPress={() => handleLocationSelectPress('city')}
            value={address.city}
            onChange={city => handleAddressChange('city', city)}
            errorMessage={(errors as any)['addresses[0].city']}
          />
          <Select
            options={getProvinces}
            label="Quận / huyện"
            onPress={() => handleLocationSelectPress('province')}
            value={address.province}
            onChange={province => handleAddressChange('province', province)}
            errorMessage={(errors as any)['addresses[0].province']}
          />
          <Select
            options={getWards}
            label="Phường / xã"
            onPress={() => handleLocationSelectPress('ward')}
            value={address.ward}
            onChange={ward => handleAddressChange('ward', ward)}
            errorMessage={(errors as any)['addresses[0].ward']}
          />
        </ScrollView>

        <Animated.View style={[buttonContainerAnimatedStyle]}>
          <Button
            title={'Tạo khách hàng'}
            buttonStyle={{backgroundColor: theme.colors.primary, borderRadius: 30}}
            containerStyle={{marginHorizontal: theme.spacing.m}}
            onPress={handleSubmit}
            loading={submitLoading}
          />
        </Animated.View>
      </SafeAreaView>

      {Platform.OS !== 'web' && (
        <Modal visible={locationSelectOpen} onRequestClose={() => setLocationSelectOpen(false)} animationType="slide">
          <View style={{flex: 1, paddingTop: insets.top, paddingBottom: insets.bottom}}>
            <View style={{paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: 'rgba(0,0,0,.08)', position: 'relative'}}>
              <View style={{position: 'absolute', left: 12, top: 4, zIndex: 100}}>
                <Ionicons name="close-circle" size={38} color="rgba(0,0,0,.5)" onPress={() => setLocationSelectOpen(false)} />
              </View>
              <Text style={{textAlign: 'center', fontSize: 18, fontWeight: '500', color: '#008060'}}>Chọn địa chỉ</Text>
              <View />
            </View>
            <LocationTabView address1={address.address1} initialTab={initialTab} onSelectDone={handleSelectDone} initialAddress={address} />
          </View>
        </Modal>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 8,
    paddingVertical: 20,

    backgroundColor: '#fff',
    position: 'relative',
    flex: 1,
  },
  inputContainer: {
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,.3)',
    borderRadius: 8,
    marginTop: 8,
    height: 46,
  },
  formContainer: {
    marginBottom: 0,
  },
  input: {
    padding: 12,
    fontSize: theme.typography.md,
  },
  label: {
    fontWeight: '500',
    fontSize: theme.typography.md,
    color: theme.colors.text,
  },
});

export default CreateCustomerScreen;
