import {But<PERSON>, ListItem} from '@rneui/themed';
import React, {useState} from 'react';
import {ScrollView, Text, View} from 'react-native';
import Hyperlink from 'react-native-hyperlink';
import {useChatWoot} from '~hooks';
import theme from '~utils/config/themes/theme';

const FAQ = () => {
  const [expandedIndex, setExpandedIndex] = useState(-1);
  const {openChatWoot} = useChatWoot();

  const questions = [
    {
      name: 'Giới thiệu về TTS Dropship',
      value: 'TTS Dropship là app bán hàng cộng tác viên có lợi nhuận tốt nhất thị trường, nhờ nguồn hàng tận xưởng hoặc từ các nhà cung cấp lớn, chuyên sản xuất và bỏ sỉ cho thị trường',
    },
    {
      name: 'Kiếm tiền trên TTS Dropship như thế nào?',
      value: 'Chỉ cần chọn bất kì sản phẩm nào có trên app và đăng bán lại ra thị trường với giá chênh lệch. Khi có khách mua hàng bạn sẽ quay lại app để tạo đơn hàng, nhận hoa hồng.',
    },
    {
      name: 'Hàng hóa trên TTS Dropship là từ đâu?',
      value: 'Hàng hóa trên TTS Dropship rất đa dạng, từ hàng nhập khẩu đến hàng nội địa. Nhà cung cấp trên TTS Dropship là những nguồn hàng hàng đầu Việt Nam, chuyên sản xuất, bỏ sỉ cho thị trường.',
    },
    {
      name: 'Rút tiền từ TTS Dropship như thế nào?',
      value: 'Sau 7 ngày từ ngày khách nhận hàng. Hoa hồng của bạn sẽ được đối soát và có thể rút.\n\nBạn chỉ cần thêm tài khoản ngân hàng và bấm rút tiền.',
    },
    {
      name: 'Nếu đặt hàng mà khách không nhận thì tôi có mất phí gì không ?',
      // value: 'Bạn sẽ chịu phí giao hàng tới khách và phí hoàn hàng về kho của nhà cung cấp nếu khách hàng không nhận hàng.',
      value: 'Hiện tại app TTS Dropship đang hỗ trợ chi phí hoàn hàng.',
    },
    {
      name: 'Chính sách đổi trả sản phẩm như thế nào ?',
      value: 'Xem tại chính sách đổi trả tại https://thitruongsi.com/pages/docs/dropship-chinh-sach-doi-tra-san-pham',
    },
  ];

  return (
    <ScrollView style={{flex: 1}}>
      {questions.map((question, index) => {
        const isExpanded = expandedIndex === index;
        return (
          <ListItem.Accordion
            bottomDivider
            key={index}
            onPress={() => setExpandedIndex(isExpanded ? -1 : index)}
            content={
              <>
                <ListItem.Content>
                  <ListItem.Title style={{fontWeight: '500'}}>{question.name}</ListItem.Title>
                </ListItem.Content>
              </>
            }
            isExpanded={isExpanded}>
            <ListItem>
              <ListItem.Content>
                <Hyperlink linkDefault linkStyle={{textDecorationLine: 'underline'}}>
                  <Text style={{color: theme.colors.text, fontSize: theme.typography.md}}>{question.value}</Text>
                </Hyperlink>
              </ListItem.Content>
            </ListItem>
          </ListItem.Accordion>
        );
      })}

      <View style={{padding: 40}}>
        <Text style={{textAlign: 'center', color: theme.colors.text}}>Không tìm thấy hỏi của bạn?</Text>
        <Button
          icon={{name: 'chatbubble-ellipses-sharp', color: theme.colors.text, type: 'ionicon', size: 24}}
          color={'secondary'}
          size="sm"
          titleStyle={{color: theme.colors.text}}
          containerStyle={{marginTop: theme.spacing.l}}
          buttonStyle={{borderRadius: 30}}
          onPress={openChatWoot}>
          Nhắn tin với hỗ trợ viên
        </Button>
      </View>
    </ScrollView>
  );
};

export default FAQ;
