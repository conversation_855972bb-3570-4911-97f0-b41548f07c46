import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Input, Text} from '@rneui/themed';
import React from 'react';
import {View} from 'react-native';
import theme from '~utils/config/themes/theme';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useDebounce} from '~hooks';
import OrderList from '~components/by-screens/Orders/OrderList';

const SearchOrders: React.FC<NativeStackScreenProps<RootStackParamList, 'SearchOrders'>> = () => {
  const [searchQuery, setSearchQuery] = React.useState('');
  const searchQueryDebounce = useDebounce(searchQuery, 400);

  return (
    <View style={{flex: 1}}>
      <View style={{backgroundColor: theme.colors.white, paddingVertical: theme.spacing.s, position: 'relative', paddingHorizontal: theme.spacing.m}}>
        <Input
          renderErrorMessage={false}
          placeholder="Mã đơn hàng, SĐT khách, tên khách"
          inputContainerStyle={{borderColor: theme.colors.gray20, backgroundColor: theme.colors.gray20, marginHorizontal: 0, paddingLeft: 48}}
          value={searchQuery}
          returnKeyType="search"
          onChangeText={setSearchQuery}
          autoFocus
          clearButtonMode="always"
        />
        <Ionicons name="search-outline" size={24} color={theme.colors.gray50} style={{position: 'absolute', top: 16, left: 36}} />
      </View>

      <View style={{flex: 1}}>
        {searchQueryDebounce ? (
          <OrderList query={{query: searchQueryDebounce}} />
        ) : (
          <View style={{flex: 1, alignItems: 'center', marginTop: theme.spacing.l * 4, paddingHorizontal: theme.spacing.m}}>
            <Text style={{textAlign: 'center'}}>Bạn có thể tìm kiếm theo 4 số cuối mã đơn hàng, số điện thoại khách hoặc tên khách</Text>
          </View>
        )}
      </View>
    </View>
  );
};

export default SearchOrders;
