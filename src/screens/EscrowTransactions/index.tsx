import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Text} from '@rneui/themed';
import React, {useCallback, useMemo} from 'react';
import {SafeAreaView, StyleSheet, View} from 'react-native';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import {NavigationState, SceneRendererProps, TabBar, TabView} from 'react-native-tab-view';
import EscrowTransactionList from '~components/by-screens/EscrowTransactions/EscrowTransactionList';

type State = NavigationState<{
  key: string;
  title: string;
}>;

const EscrowTransactions: React.FC<NativeStackScreenProps<RootStackParamList, 'EscrowTransactions'>> = () => {
  const [index, setIndex] = React.useState(0);

  const routes = useMemo(() => {
    return [{key: 'all', title: 'Tất cả'}];
  }, []);

  const renderTabBar = (tabbarProps: SceneRendererProps & {navigationState: State}) => (
    <TabBar
      {...tabbarProps}
      scrollEnabled
      indicatorStyle={styles.indicator}
      style={styles.tabbar}
      tabStyle={styles.tab}
      renderLabel={scene => (
        <View>
          <Text numberOfLines={1} style={{color: scene.focused ? '#008060' : '#333', paddingHorizontal: 4}}>
            {scene.route.title}
          </Text>
        </View>
      )}
    />
  );

  const renderScene = useCallback(() => {
    return <EscrowTransactionList />;
  }, []);

  const handleIndexChange = useCallback((newIndex: number) => {
    setIndex(newIndex);
  }, []);

  return (
    <SafeAreaView style={{flex: 1}}>
      <TabView lazy renderTabBar={renderTabBar} navigationState={{index, routes}} renderScene={renderScene} onIndexChange={handleIndexChange} />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  tabbar: {
    backgroundColor: '#fff',
    borderBottomWidth: 2,
    borderBottomColor: 'rgba(0,0,0,.05)',
  },
  tab: {
    width: 'auto',
  },
  indicator: {
    backgroundColor: '#008060',
  },
});

export default EscrowTransactions;
