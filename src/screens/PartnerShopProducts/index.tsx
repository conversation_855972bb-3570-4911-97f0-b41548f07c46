import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React from 'react';
import {FlatList} from 'react-native';
import ProductItem from '~components/by-screens/PartnerShopProducts/ProductItem';
import {useGetShopProductsInfinite} from '~utils/api/partner';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';

const PartnerShopProducts: React.FC<NativeStackScreenProps<RootStackParamList, 'PartnerShopProducts'>> = ({route}) => {
  const {data, fetchNextPage} = useGetShopProductsInfinite(route.params.partnerShopId);

  const dataFlatList = data?.pages.flatMap(page => page.products);

  return <FlatList data={dataFlatList} renderItem={({item}) => <ProductItem product={item} partnerShopId={route.params.partnerShopId} />} onEndReached={() => fetchNextPage()} />;
};

export default PartnerShopProducts;
