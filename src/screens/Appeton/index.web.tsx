import React from 'react';
import { useExtendContainerWidth } from '~components/shared/web/WebContainer';
import { sendWebViewEvent } from '~utils/helpers/webview';
import inInTTSDropshipWebview from '~utils/helpers/isInTTSDropshipWebview';
import { BillboardSlider } from '~components/by-screens/Page/Appeton/index.web';

const Appeton = () => {
  useExtendContainerWidth('lg');

  const handlePressCTA = () => {
    if (inInTTSDropshipWebview()) {
      sendWebViewEvent('Navigate', ['SupplierDetail', { shopId: 'new-retail-cpg', keyword: 'appeton' }]);
    } else {
      window.open('https://dropship.thitruongsi.com/supplier/new-retail-cpg?keyword=appeton');
    }
  };

  const scrollToHash = (hash: string) => {
    const element = document.getElementById(hash);
    if (element) {
      const headerOffset = 78;
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth',
      });
    }
  };

  return (
    <>
      <meta charSet="utf-8" />
      <meta httpEquiv="x-ua-compatible" content="ie=edge" />
      <meta name="description" content="Clinically proven to help skinny people gain weight healthily." />
      <meta name="keywords" content="" />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <link rel="icon" type="image/vnd.microsoft.icon" href="/static/appeton/images/favicon.ico" />
      <link rel="shortcut icon" type="image/x-icon" href="/static/appeton/images/favicon.ico" />
      <link rel="stylesheet" href="/static/appeton/css/theme-55c649182.css?v=1.0.1" type="text/css" media="all" />
      <link rel="stylesheet" type="text/css" href="/static/appeton/css/appeton.css?v=1.0.1" />
      <link rel="stylesheet" type="text/css" href="/static/appeton/css/icon-mind/style.css" />
      <link rel="stylesheet" type="text/css" href="/static/appeton/css/mobirise/style.css" />
      <link rel="stylesheet" type="text/css" href="/static/appeton/css/icon54/style.css" />
      <link rel="stylesheet" type="text/css" href="/static/appeton/css/icon54v4/style.css" />
      <link rel="stylesheet" type="text/css" href="/static/appeton/css/flaticon/style.css" />
      <link rel="stylesheet" type="text/css" href="/static/appeton/css/material_icon/style.css" />

      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" />
      <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet" />

      <meta property="og:type" content="product" />
      <meta property="og:title" content="Trở thành CTV APPETON WEIGHT GAIN trên TTS Dropship" />
      <meta property="og:site_name" content="Appeton" />
      <meta property="og:image" content="/static/appeton/images/appeton_hero.png" />

      <div id="product">
        <main>
          <header id="header">
            <div id="logo">
              <img className="logo img-responsive" src="/static/appeton/images/appeton_logo.jpg" alt="Appeton" />
              <img className="tts-logo img-responsive" src="/static/appeton/images/logo-dropship-icon.png" alt="TTS Dropship" />
            </div>
          </header>
          <aside id="notifications">
            <div className="container"></div>
          </aside>
          <section id="wrapper">
            <div id="content-wrapper">
              <div className="row" id="description">
                <div className="col-lg">
                  <div className="product-description">
                    <div
                      id="prodBackground"
                      style={{
                        backgroundImage: 'url("/static/appeton/images/appeton_hero.png")',
                        backgroundColor: 'rgba(0,0,0,.2)',
                      }}>
                      <img src="/static/appeton/images/appeton_hero.png" alt="" width={1484} />
                    </div>
                    <div className="container" id="brand-overview" style={{ background: '#ffffff' }}>
                      <div className="brand-overview-content">
                        <h2 className="montserrat-headline"><b>THƯƠNG HIỆU SỮA<br />HƠN 30 NĂM</b></h2>
                        <div>
                          <p>Appeton là thương hiệu của Malaysia - được sản xuất từ Pháp, chuyên về thực phẩm và thức uống bổ sung dinh dưỡng chất lượng cao, với hơn 30 năm kinh nghiệm trong lĩnh vực này.</p>
                        </div>

                        <div>
                          <video preload="metadata" style={{ maxWidth: '100%', height: 'auto' }} controls>
                            <source src="https://s3.thitruongsi.com/marketing/appeton_tvc.mp4#t=0.1" type="video/mp4" />
                            Your browser does not support HTML5 video.
                          </video>
                        </div>

                        <div className="mt-2">
                          <p>
                            Thương hiệu đã được tin dùng tại Việt Nam hơn 10 năm, hiện đang <b>được phân phối tại Nhà Thuốc Long Châu và nhiều đơn vị uy tín khác</b> (theo số đăng ký 03/NEW RETAIL CPG/2023).
                          </p>
                        </div>

                        <div className="mb-2">
                          <BillboardSlider images={["/static/appeton/images/appeton-vtv1.jpg", "/static/appeton/images/appeton_billboard.jpg", "/static/appeton/images/appeton_billboard_2.jpg", "/static/appeton/images/appeton_billboard_3.jpg"]} />
                        </div>
                      </div>
                    </div>
                    <div className="container" id="top-products" style={{ background: '#048e53', padding: '20px 0px' }}>
                      <h2 style={{ color: 'white' }} className="montserrat-headline font-weight-bold">SẢN PHẨM CỦA APPETON</h2>

                      <div className="d-flex">
                        <div className="col-md-6 mt-3 top-products-item">
                          <img className="img-fluid" src="/static/appeton/images/nutritions/Appeton-Weight-Gain-Adult-2.png" alt="" />
                          <div className="top-products-name">APPETON WEIGHT GAIN ADULT</div>
                          <div className="top-products-description">Dành cho nam & nữ từ 12 tuổi trở lên, sữa Weight Gain được đặc chế theo một công thức khoa học giàu protein giúp người gầy tăng cân hiệu quả và còn giúp người luyện tập thể hình, các vận động viên phát triển mô cơ, tăng cường sinh lực cơ bắp.</div>
                        </div>
                        <div className="col-md-6 mt-3 top-products-item">
                          <img className="img-fluid" src="/static/appeton/images/nutritions/appeton-weight-gain-junior.png" alt="" />
                          <div className="top-products-name">APPETON WEIGHT GAIN JUNIOR</div>
                          <div className="top-products-description">Dành cho trẻ từ 3-12 tuổi, giúp trẻ tăng cân khỏe mạnh và hoàn toàn tự nhiên. Sản phẩm được đặc chế theo một công thức khoa học giàu protein với chất lượng hội đủ 3 yếu tố then chốt giúp tăng cân hiệu quả. Ngoài ra sản phẩm được kết hợp dưỡng chất dễ tiêu hóa L-Protemax cần thiết cho mô phát triển, giúp trẻ ăn ngon, phát triển tốt chiều cao và tăng cường sức khỏe.</div>
                        </div>
                      </div>
                    </div>

                    <div className="mt-2" style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
                      <button className="seller-cta" onClick={handlePressCTA}>
                        XEM SẢN PHẨM
                        <i className="imind-arrow-right" />
                      </button>
                    </div>

                    <div className="container" id="seller-benefits" style={{ background: '#fff', padding: '20px 0px', minHeight: '80vh' }}>
                      <h2 style={{ padding: '20px 20px 5px 20px' }} className="montserrat-headline font-weight-bold">VÌ SAO BẠN NÊN TRỞ THÀNH CTV BÁN SỮA APPETON?</h2>
                      <div className="seller-benefits-content">
                        <div>
                          <h3 style={{ color: '#048e53' }}>⭐ Sản phẩm chất lượng</h3>
                          <p>
                            Appeton Weight Gain được sản xuất tại Pháp, sản phẩm trên TTS Dropship được phân phối bởi New Retail CPG (số đăng ký 03/NEW RETAIL CPG/2023). Sản phẩm được đảm bảo về chất lượng, hiệu quả và được chứng minh lâm sàng, giúp bạn tự tin để kinh doanh lâu dài.
                          </p>
                        </div>
                        <div>
                          <h3 style={{ color: '#048e53' }}>📙 Được đào tạo và hỗ trợ kinh doanh</h3>
                          <p>
                            TTS Dropship & nhãn hàng sẽ thường xuyên tổ chức các khóa đào tạo về kỹ năng bán hàng, kiến thức sản phẩm, cung cấp đầy đủ các tài liệu để bạn có thể tự tin tư vấn cho khách hàng, làm nền tảng phát triển công việc kinh doanh của bạn tốt hơn.
                          </p>
                        </div>
                        <div>
                          <h3 style={{ color: '#048e53' }}>🎁 Hoa hồng cao và nhiều phúc lợi</h3>
                          <p>
                            Sản phẩm Appeton trên TTS Dropship có hoa hồng cao, thường xuyên có các chương trình khuyến mãi, chương trình thưởng giúp bạn kinh doanh dễ dàng hơn & tăng thêm thu nhập.
                          </p>
                        </div>
                      </div>

                      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
                        <div>
                          <button className="seller-cta" onClick={handlePressCTA}>
                            BẮT ĐẦU BÁN HÀNG
                            <i className="imind-arrow-right" />
                          </button>
                        </div>

                        <div className="text-sm-center seller-cta-microcopy">đi đến gian hàng trên TTS Dropship</div>
                      </div>

                      <h3 style={{ padding: '40px 20px 0' }} className="montserrat-headline font-weight-bold">MỘT SỐ CÂU HỎI THƯỜNG GẶP</h3>
                      <div className="seller-benefits-content">
                        <div>
                          <h3 style={{ color: '#048e53' }}>TTS Dropship là gì?</h3>
                          <p>
                            TTS Dropship là nền tảng kinh doanh không cần vốn, không tồn kho & không lo xử lý giao hàng. Bạn chỉ cần tập trung đăng bán & tư vấn về sản phẩm để bán hàng.
                          </p>
                        </div>
                        <div>
                          <h3 style={{ color: '#048e53' }}>Sản phẩm Appeton có nguồn gốc như thế nào?</h3>
                          <p>
                            Appeton là thương hiệu đến từ Malaysia, được sản xuất tại Pháp & nhập khẩu bởi NEW RETAIL CPG (số đăng ký: 03/NEW RETAIL CPG/2023).
                          </p>
                        </div>
                        <div>
                          <h3 style={{ color: '#048e53' }}>Đơn hàng Appeton được xử lý như thế nào?</h3>
                          <p>
                            Các đơn hàng Appeton sẽ được xử lý bởi TTS Dropship, đóng gói & gửi từ kho của đối tác chuyên nghiệp giúp cho sản phẩm được đảm bảo về mặt chất lượng trong quá trình vận chuyển.
                          </p>
                        </div>
                      </div>

                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </main>
      </div>
      <div style={{ position: 'fixed', right: '12px', bottom: '12px', width: '50px', height: '50px' }}>
        <a href="https://zalo.me/g/rfwdrb274" target="_blank"><img src="/static/appeton/images/zalo_logo.svg" alt="zalo" /></a>
      </div>
    </>
  );
};

export default Appeton;
