import React, {useEffect} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {env} from '~utils/config';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';

const Appeton: React.FC<NativeStackScreenProps<RootStackParamList, 'Appeton'>> = ({navigation}) => {
  useEffect(() => {
    navigation.replace('WebView', {url: `${env.WEBVIEW_URL}/appeton`, includeAuth: true});
  }, []);

  return null;
};

export default Appeton;
