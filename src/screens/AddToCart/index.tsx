import React from 'react';
import {StyleSheet, View} from 'react-native';

import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import {useGetProductDetail} from '~utils/api/product';
import OptionSelect from '~components/by-screens/AddToCart/OptionSelect';

const AddToCart: React.FC<NativeStackScreenProps<RootStackParamList, 'AddToCart'>> = ({route}) => {
  const {data} = useGetProductDetail(route.params.productId);

  return <View style={styles.container}>{data && <OptionSelect product={data} selectedVariantId={route.params.selectedVariantId} />}</View>;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
});

export default AddToCart;
