import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React, {useEffect} from 'react';
import {env} from '~utils/config';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';

const ProductPickerGame: React.FC<NativeStackScreenProps<RootStackParamList, 'ProductPickerGame'>> = ({navigation}) => {
  useEffect(() => {
    navigation.replace('WebView', {
      url: `${env.WEBVIEW_URL}/product-picker-game`,
      includeAuth: true,
    });
  }, []);

  return null;
};

export default ProductPickerGame;
