import {Image} from '@rneui/themed';
import {StyleSheet, Text, TouchableOpacity, View, Animated} from 'react-native';
import React, {useEffect, useRef} from 'react';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {PickedProductType} from './index.web';

type PickedProductProps = {
  product: PickedProductType;
  handleGoToProduct: (productId: number) => void;
};

const PickedProduct = ({product, handleGoToProduct}: PickedProductProps) => {
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const glowAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Start the entrance animation sequence
    Animated.sequence([
      // First, show a pulsing glow
      Animated.timing(glowAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: false, // glow effect requires non-native driver
      }),

      // Then fade in the content with a magical scale appearance
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 900,
          useNativeDriver: true,
        }),
      ]),
    ]).start();

    // Run a continuous subtle pulsing glow after entrance
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: false, // glow animations require non-native driver
        }),
        Animated.timing(pulseAnim, {
          toValue: 0.3,
          duration: 1500,
          useNativeDriver: false,
        }),
      ]),
      {
        iterations: 3,
      },
    ).start(() => {
      Animated.timing(glowAnim, {
        toValue: 0,
        duration: 1000,
        useNativeDriver: false,
      }).start();
    });
  }, []);

  // Calculate the box shadow for the magical glow effect
  const glowIntensity = Animated.add(glowAnim, Animated.multiply(pulseAnim, 0.3));

  // Web-compatible styles for glow effect
  const webGlowStyle = {
    boxShadow: glowIntensity.interpolate({
      inputRange: [0, 1],
      outputRange: ['0px 0px 0px rgba(255, 220, 150, 0)', '0px 0px 15px rgba(255, 220, 150, 0.8)'],
    }),
  };

  // Calculate the halo color based on animation
  const haloColor = glowIntensity.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: ['rgba(255, 220, 150, 0)', 'rgba(255, 220, 150, 0.5)', 'rgba(255, 220, 150, 0.7)'],
  });

  return (
    <View style={styles.container}>
      {/* Outer glow halo effect */}
      <Animated.View
        style={[
          styles.halo,
          {
            opacity: glowIntensity.interpolate({
              inputRange: [0, 1],
              outputRange: [0, 0.8],
            }),
            backgroundColor: haloColor,
            transform: [{scale: Animated.add(1, Animated.multiply(pulseAnim, 0.05))}],
          },
        ]}
      />

      {/* Main content with fade and scale */}
      <Animated.View
        style={[
          styles.historyItem,
          {
            opacity: fadeAnim,
            transform: [{scale: scaleAnim}],
          },
          // @ts-ignore
          webGlowStyle, // Web-specific glow style
        ]}>
        <TouchableOpacity onPress={() => handleGoToProduct(product.product_id)} activeOpacity={0.8}>
          <Image source={{uri: product.image_thumb}} style={styles.historyItemImage} />
        </TouchableOpacity>
        <Text
          style={styles.historyItemText}
          numberOfLines={2}
          onPress={() => {
            handleGoToProduct(product.product_id);
          }}>
          {product.title}
        </Text>

        <Ionicons name="chevron-forward-outline" size={24} color={theme.colors.textLight} />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    paddingVertical: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  halo: {
    position: 'absolute',
    width: '100%',
    height: '120%',
    borderRadius: 12,
    alignSelf: 'center',
    zIndex: -1,
  },
  historyItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    gap: 10,
    backgroundColor: 'white',
    paddingHorizontal: 8,
    borderRadius: 8,
    zIndex: 2,
    width: '100%',
  },
  historyItemImage: {
    width: 50,
    height: 50,
    borderRadius: 5,
    resizeMode: 'cover',
    // @ts-ignore
    willChange: 'transform',
    transform: 'translateZ(0)',
    backfaceVisibility: 'hidden',
  },
  historyItemText: {
    flex: 1,
    fontSize: 14,
  },
  useSmallButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderRadius: 3,
  },
  useSmallButtonText: {
    color: 'white',
    fontSize: 12,
  },
});

export default React.memo(PickedProduct);
