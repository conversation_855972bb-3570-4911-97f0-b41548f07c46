import React, {useState, useEffect, useRef} from 'react';
import {View, Text, TouchableOpacity, StyleSheet, Animated, Easing, Image, ScrollView, Dimensions} from 'react-native';
import {useGetSmartCollection} from '~utils/api/product';
import {SearchProduct} from '~utils/types/product';
// @ts-ignore
import LuckyCatImage from '~assets/luckycat2.png';
import FloatingProducts from './FloatingProducts';
import inInTTSDropshipWebview from '~utils/helpers/isInTTSDropshipWebview';
import {sendWebViewEvent} from '~utils/helpers/webview';
import {WebViewMessageTypes} from '~utils/types/common';
import {useNavigation} from '~hooks';
import PickedProduct from './PickedProduct';

// Interface cho floating products
interface FloatingProduct {
  id: number;
  x: Animated.Value;
  y: Animated.Value;
  rotate: Animated.Value;
  size: number;
  speed: number;
  delay: number;
  imageUrl: string;
}

export type PickedProductType = SearchProduct & {
  picked_at: number;
};

function formatTime(seconds: number) {
  const mins = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  const formattedMins = String(mins).padStart(2, '0');
  const formattedSecs = String(secs).padStart(2, '0');

  return `${formattedMins}:${formattedSecs}`;
}

function test__setLatestSpinTime() {
  const millisecondsAgo = 14 * 60 * 1000 + 54 * 1000;
  const timeAgo = new Date(Date.now() - millisecondsAgo);
  localStorage.setItem('latestSpinTime', timeAgo.toISOString());
}

// Component chính của ứng dụng
const ProductPickerGame: React.FC = () => {
  const navigation = useNavigation();
  // State lưu trữ danh sách sản phẩm (giả định)
  const [products, setProducts] = useState<SearchProduct[]>([]);
  // State lưu trữ sản phẩm được chọn
  const [selectedProduct, setSelectedProduct] = useState<SearchProduct | null>(null);
  // State cho trạng thái quay
  const [isSpinning, setIsSpinning] = useState<boolean>(false);
  // State cho các sản phẩm đã chọn trước đó
  const [previousPicks, setPreviousPicks] = useState<PickedProductType[]>(JSON.parse(localStorage.getItem('previousPicks') || '[]'));
  // State cho các sản phẩm trôi nổi
  const [floatingProducts, setFloatingProducts] = useState<FloatingProduct[]>([]);
  // Animation values
  const spinValue = useRef(new Animated.Value(0)).current;
  const scaleValue = useRef(new Animated.Value(1)).current;
  const [latestSpinTime, setLatestSpinTime] = useState<string | null>(localStorage.getItem('latestSpinTime'));
  const [spinTimeRemaining, setSpinTimeRemaining] = useState<number>(0);
  const [spinCount, setSpinCount] = useState<number>(localStorage.getItem('spinCount') ? parseInt(localStorage.getItem('spinCount') || '0') : 3);
  // Ref cho kích thước màn hình
  const {width: screenWidth, height: screenHeight} = Dimensions.get('window');
  const {data} = useGetSmartCollection(
    'dropship-random',
    {limit: 15},
    {
      staleTime: 0,
    },
  );

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (latestSpinTime) {
      const spinTime = new Date(latestSpinTime).getTime();
      const now = new Date().getTime();
      const remaining = Math.max(0, (spinTime + 15 * 60 * 1000 - now) / 1000);
      setSpinTimeRemaining(remaining);
      if (remaining === 0 && spinCount <= 0) {
        setSpinCount(3);
      }

      if (remaining > 0) {
        interval = setInterval(() => {
          setSpinTimeRemaining(prev => {
            if (prev <= 1) {
              clearInterval(interval);
              setSpinCount(3);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      }
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [latestSpinTime]);

  useEffect(() => {
    if (data) {
      const products = data.pages.flatMap(page => page.products);
      setProducts(products);
      createFloatingProducts(products);
    }
  }, [data]);

  // Tạo các sản phẩm trôi nổi
  const createFloatingProducts = (products: SearchProduct[]) => {
    const floatingItems: FloatingProduct[] = products.map(product => {
      return {
        id: product.product_id,
        x: new Animated.Value(Math.random() * screenWidth),
        y: new Animated.Value(Math.random() * screenHeight),
        rotate: new Animated.Value(Math.random() * 360),
        size: 30 + Math.random() * 40,
        speed: 20000 + Math.random() * 15000,
        delay: Math.random() * 5000,
        imageUrl: product.image_thumb,
      };
    });

    setFloatingProducts(floatingItems);
  };

  // Animate các sản phẩm trôi nổi
  useEffect(() => {
    if (floatingProducts.length === 0) return;

    floatingProducts.forEach(product => {
      // Animate X position
      Animated.loop(
        Animated.sequence([
          Animated.timing(product.x, {
            toValue: Math.random() * screenWidth,
            duration: product.speed,
            delay: product.delay,
            useNativeDriver: true,
            easing: Easing.linear,
          }),
          Animated.timing(product.x, {
            toValue: Math.random() * screenWidth,
            duration: product.speed,
            useNativeDriver: true,
            easing: Easing.linear,
          }),
        ]),
      ).start();

      // Animate Y position
      Animated.loop(
        Animated.sequence([
          Animated.timing(product.y, {
            toValue: Math.random() * screenHeight,
            duration: product.speed * 1.2,
            delay: product.delay,
            useNativeDriver: true,
            easing: Easing.linear,
          }),
          Animated.timing(product.y, {
            toValue: Math.random() * screenHeight,
            duration: product.speed * 1.2,
            useNativeDriver: true,
            easing: Easing.linear,
          }),
        ]),
      ).start();

      // Animate rotation
      Animated.loop(
        Animated.timing(product.rotate, {
          toValue: 360,
          duration: 20000 + Math.random() * 10000,
          useNativeDriver: true,
          easing: Easing.linear,
        }),
      ).start();
    });
  }, [floatingProducts]);

  // Hàm chọn sản phẩm ngẫu nhiên
  const pickRandomProduct = () => {
    if (products.length === 0 || isSpinning) return;

    setIsSpinning(true);
    setSelectedProduct(null);

    // Animation xoay
    Animated.timing(spinValue, {
      toValue: 10, // Số vòng xoay
      duration: 3000,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: true,
    }).start();

    // Animation phóng to thu nhỏ
    Animated.sequence([
      Animated.timing(scaleValue, {
        toValue: 1.2,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(scaleValue, {
        toValue: 0.8,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(scaleValue, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();

    // Chọn sản phẩm sau khi animation hoàn tất
    setTimeout(() => {
      const randomIndex = Math.floor(Math.random() * products.length);
      const newPick = products[randomIndex];

      setSelectedProduct(newPick);
      setPreviousPicks(prev => [{...newPick, picked_at: new Date().getTime()}, ...prev].slice(0, 5));
      setIsSpinning(false);
      setSpinCount(spinCount - 1);
      if (spinCount - 1 === 0) {
        setLatestSpinTime(new Date().toISOString());
        localStorage.setItem('latestSpinTime', new Date().toISOString());
      }
      localStorage.setItem('previousPicks', JSON.stringify([{...newPick, picked_at: new Date().getTime()}, ...previousPicks]));

      localStorage.setItem('spinCount', (spinCount - 1).toString());

      // Reset animation
      spinValue.setValue(0);
    }, 3000);
  };

  // Hiệu ứng xoay
  const spin = spinValue.interpolate({
    inputRange: [0, 10],
    outputRange: ['0deg', '3600deg'],
  });

  const handleGoToProduct = (productId: number) => {
    if (inInTTSDropshipWebview()) {
      sendWebViewEvent(WebViewMessageTypes.Push, ['ProductDetail', {productId: productId}]);
    } else {
      navigation.push('ProductDetail', {productId: productId});
    }
  };

  return (
    <View style={styles.containerWrapper}>
      {/* Background với sản phẩm trôi nổi */}
      {/* <View style={styles.backgroundContainer}>{renderFloatingProducts()}</View> */}

      <FloatingProducts floatingProducts={floatingProducts} />

      <ScrollView style={styles.container}>
        <View style={styles.wheelContainer}>
          <Animated.View
            style={[
              styles.wheel,
              {
                transform: [{rotate: spin}, {scale: scaleValue}],
              },
            ]}>
            <Image source={LuckyCatImage} style={styles.wheelImage} />
            <Text style={styles.wheelText}>{isSpinning ? 'Đang quay...' : 'Sẵn sàng quay'}</Text>
          </Animated.View>

          <TouchableOpacity
            style={[styles.spinButton, (isSpinning || spinTimeRemaining > 0) && styles.spinningButton]}
            onPress={pickRandomProduct}
            disabled={isSpinning || products.length === 0 || spinTimeRemaining > 0}>
            {!spinTimeRemaining ? <Text style={styles.spinButtonText}>{isSpinning ? 'Đang quay...' : 'Quay ngay!'}</Text> : <Text style={styles.spinButtonText}>{formatTime(spinTimeRemaining)}</Text>}
          </TouchableOpacity>
          <Text style={styles.spinCount}>Lượt quay: {spinCount}</Text>
        </View>

        {previousPicks.length > 0 && (
          <View style={styles.historyContainer}>
            <Text style={styles.sectionTitle}>Sản phẩm được nhả vía:</Text>
            {previousPicks.map((product, index) => (
              <PickedProduct product={product} key={product.id + product.picked_at} handleGoToProduct={handleGoToProduct} />
            ))}
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  containerWrapper: {
    flex: 1,
    position: 'relative',
    overflow: 'hidden',
  },
  container: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  header: {
    alignItems: 'center',
    paddingVertical: 20,
    backgroundColor: '#4a6da7',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 5,
  },
  categoryContainer: {
    paddingHorizontal: 15,
    marginTop: 15,
  },
  categoryScroll: {
    flexDirection: 'row',
    marginTop: 10,
  },
  categoryButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
    backgroundColor: '#e0e0e0',
  },
  selectedCategory: {
    backgroundColor: '#4a6da7',
  },
  categoryText: {
    fontSize: 14,
    color: '#333',
  },
  selectedCategoryText: {
    color: 'white',
  },
  wheelContainer: {
    alignItems: 'center',
    marginTop: 20,
    paddingBottom: 20,
  },
  wheel: {
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    borderWidth: 5,
    borderColor: '#f0c419',
  },
  wheelImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    resizeMode: 'cover',
    // @ts-ignore
    willChange: 'transform',
    transform: 'translateZ(0)',
    backfaceVisibility: 'hidden',
  },
  wheelText: {
    marginTop: 10,
    fontSize: 16,
    fontWeight: 'bold',
  },
  spinButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 30,
    marginTop: 20,
    elevation: 3,
  },
  spinningButton: {
    backgroundColor: '#9E9E9E',
  },
  spinButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  resultContainer: {
    padding: 15,
    alignItems: 'center',
  },
  resultTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },

  useButton: {
    backgroundColor: '#2196F3',
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 5,
    alignSelf: 'flex-start',
    marginTop: 5,
  },

  historyContainer: {
    padding: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  statsContainer: {
    padding: 15,
    marginBottom: 20,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statItemText: {
    width: 100,
    fontSize: 14,
  },
  statBar: {
    flex: 1,
    height: 10,
    backgroundColor: '#e0e0e0',
    borderRadius: 5,
    marginHorizontal: 10,
  },
  statFill: {
    height: '100%',
    backgroundColor: '#FF9800',
    borderRadius: 5,
  },
  statCount: {
    width: 30,
    fontSize: 14,
    textAlign: 'right',
  },
  spinCount: {
    fontSize: 14,
    marginTop: 10,
  },
});

export default ProductPickerGame;
