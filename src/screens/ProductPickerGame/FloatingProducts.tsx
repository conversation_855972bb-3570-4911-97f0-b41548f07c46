import React from 'react';
import {Animated, StyleSheet} from 'react-native';
import {View, Image} from 'react-native';

type FloatingProduct = {
  id: number;
  x: Animated.Value;
  y: Animated.Value;
  rotate: Animated.Value;
  size: number;
  speed: number;
  delay: number;
  imageUrl: string;
};

function FloatingProducts({floatingProducts}: {floatingProducts: FloatingProduct[]}) {
  return (
    <View style={styles.backgroundContainer}>
      {floatingProducts.map(product => (
        <FloatingProduct product={product} key={product.id} />
      ))}
    </View>
  );
}

const FloatingProduct = ({product}: {product: FloatingProduct}) => {
  const rotateStr = product.rotate.interpolate({
    inputRange: [0, 360],
    outputRange: ['0deg', '360deg'],
  });
  return (
    <Animated.View
      key={product.id}
      style={[
        styles.floatingProduct,
        {
          width: product.size,
          height: product.size,
          transform: [{translateX: product.x}, {translateY: product.y}, {rotate: rotateStr}],
        },
      ]}>
      <Image source={{uri: product.imageUrl}} style={styles.floatingProductImage} />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
    backgroundColor: 'rgba(245, 245, 245, 0.9)',
  },
  floatingProduct: {
    position: 'absolute',
    borderRadius: 10,
    overflow: 'hidden',
    opacity: 0.4,
  },
  floatingProductImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
});

export default React.memo(FloatingProducts);
