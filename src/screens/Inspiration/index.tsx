import {BottomTabScreenProps} from '@react-navigation/bottom-tabs';
import {CompositeScreenProps, useIsFocused} from '@react-navigation/native';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import * as React from 'react';
import {Animated, StatusBar, StyleSheet, TouchableOpacity, View, useWindowDimensions} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {TabView, SceneMap} from 'react-native-tab-view';
import InspirationProducts from '~components/by-screens/Inspiration/InspirationProducts';
import InspirationSuppliers from '~components/by-screens/Inspiration/InspirationSuppliers';
import {MainTabsScreenList} from '~screens/MainTabs';
import theme from '~utils/config/themes/theme';
import AsyncStorage from '~utils/helpers/storage';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';

const renderScene = SceneMap({
  first: InspirationProducts,
  second: InspirationSuppliers,
});

type InspirationProps = CompositeScreenProps<BottomTabScreenProps<MainTabsScreenList, 'Inspiration'>, NativeStackScreenProps<RootStackParamList>>;

const Inspiration: React.FC<InspirationProps> = ({route}) => {
  const layout = useWindowDimensions();
  const insets = useSafeAreaInsets();
  const [isInitializing, setInitializing] = React.useState(true);
  const [index, setIndex] = React.useState(route.params?.tabIndex || 1);
  const [routes] = React.useState([
    {key: 'first', title: 'Sản phẩm'},
    {key: 'second', title: 'Nhà cung cấp'},
  ]);

  const isFocused = useIsFocused();

  React.useEffect(() => {
    if (typeof route.params?.tabIndex === 'undefined') {
      AsyncStorage.getItem('last_inspiration_index').then(value => {
        if (value !== null) {
          setIndex(parseInt(value));
        }
        setInitializing(false);
      });
    }
  }, []);

  const handleIndexChange = React.useCallback((index: number) => {
    setIndex(index);
    AsyncStorage.setItem('last_inspiration_index', index.toString());
  }, []);

  const renderTabBar = (props: any) => {
    const inputRange = props.navigationState.routes.map((x: any, i: any) => i);

    return (
      <View style={[styles.tabBar, {paddingTop: insets.top}]}>
        {props.navigationState.routes.map((route: any, i: any) => {
          const opacity = props.position.interpolate({
            inputRange,
            outputRange: inputRange.map((inputIndex: number) => (inputIndex === i ? 1 : 0.5)),
          });

          return (
            <TouchableOpacity style={styles.tabItem} onPress={() => handleIndexChange(i)}>
              <Animated.Text
                style={{
                  opacity,
                  fontSize: theme.typography.md,
                  fontWeight: '700',
                  color: theme.colors.text,
                }}>
                {route.title}
              </Animated.Text>
            </TouchableOpacity>
          );
        })}
      </View>
    );
  };

  if (isInitializing) {
    return null;
  }

  return (
    <View style={{flex: 1, backgroundColor: 'transparent'}}>
      {isFocused && <StatusBar backgroundColor={'black'} barStyle="light-content" />}

      <View style={{position: 'relative', flex: 1}}>
        <TabView navigationState={{index, routes}} renderTabBar={renderTabBar} renderScene={renderScene} onIndexChange={handleIndexChange} initialLayout={{width: layout.width}} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  tabBar: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255,255,255,.7)',
    position: 'absolute',
    zIndex: 100,
    width: '100%',
    justifyContent: 'center',
  },
  tabItem: {
    alignItems: 'center',
    padding: 20,
  },
});

export default Inspiration;
