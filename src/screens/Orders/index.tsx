import React, {useCallback, useEffect, useRef} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import type {BottomTabScreenProps} from '@react-navigation/bottom-tabs';
import {MainTabsScreenList} from '~screens/MainTabs';
import {NavigationState, SceneRendererProps, TabBar, TabBarItem, TabView} from 'react-native-tab-view';
import OrderList from '~components/by-screens/Orders/OrderList';
import {FlashList} from '@shopify/flash-list';
import Order from '~utils/types/order';
import {CompositeScreenProps, useScrollToTop} from '@react-navigation/native';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';

type State = NavigationState<{
  key: string;
  title: string;
}>;

type OrdersScreenProps = CompositeScreenProps<BottomTabScreenProps<MainTabsScreenList, 'Order'>, NativeStackScreenProps<RootStackParamList>>;

const OrdersScreen: React.FC<OrdersScreenProps> = ({navigation, route}) => {
  const orderListRef = useRef<FlashList<Order>[]>([]);
  const [index, setIndex] = React.useState(0);
  const [routes] = React.useState([
    {key: 'any', title: 'Tất cả'},
    {key: 'wait_confirm', title: 'Chờ xác nhận'},
    {key: 'wait_checkout', title: 'Chờ thanh toán'},
    {key: 'wait_pickup', title: 'Chờ lấy hàng'},
    {key: 'in_transit', title: 'Đang giao'},
    {
      key: 'delivered',
      title: 'Đã giao',
    },
    {key: 'wait_rate', title: 'Chờ đánh giá'},
    {key: 'rated', title: 'Đã đánh giá'},
    {key: 'cancelled', title: 'Đã hủy'},
  ]);

  useEffect(() => {
    if (route.params?.buyer_status) {
      const selectedTabIndex = routes.findIndex(tab => tab.key === route.params?.buyer_status);
      if (selectedTabIndex !== -1) {
        setTimeout(() => {
          setIndex(selectedTabIndex);
        }, 100);
      }
    }
  }, [route.params]);

  useScrollToTop({
    current: {
      scrollToTop() {
        const flatListRef = orderListRef.current[index];
        if (flatListRef && flatListRef.scrollToOffset) {
          flatListRef.scrollToOffset({offset: 0, animated: true});
        }
      },
    },
  });

  useEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <TouchableOpacity onPress={() => navigation.navigate('SearchOrders', {})}>
          <Ionicons name="search-outline" size={24} color={theme.colors.text} style={{marginRight: theme.spacing.s, paddingRight: theme.spacing.m, paddingHorizontal: 8, paddingVertical: 4}} />
        </TouchableOpacity>
      ),
    });
  }, [navigation]);

  const renderScene = useCallback((params: any) => {
    const routeIndex = routes.findIndex(route => route.key === params.route.key);
    switch (params.route.key) {
      default:
        return <OrderList query={{buyer_status: params.route.key}} ref={orderListRef} index={routeIndex} />;
    }
  }, []);

  const renderTabBar = (tabbarProps: SceneRendererProps & {navigationState: State}) => (
    <TabBar
      {...tabbarProps}
      scrollEnabled
      indicatorStyle={styles.indicator}
      style={styles.tabbar}
      tabStyle={styles.tab}
      renderTabBarItem={({key, ...props}) => <TabBarItem {...props} key={key} />}
      renderLabel={scene => (
        <View>
          <Text numberOfLines={1} style={{color: scene.focused ? '#008060' : '#333', paddingHorizontal: 4}}>
            {scene.route.title}
          </Text>
        </View>
      )}
    />
  );

  const handleIndexChange = useCallback((newIndex: number) => {
    setIndex(newIndex);
  }, []);

  return <TabView lazy renderTabBar={renderTabBar} navigationState={{index, routes}} renderScene={renderScene} onIndexChange={handleIndexChange} />;
};

const styles = StyleSheet.create({
  tabbar: {
    backgroundColor: '#fff',
    borderBottomWidth: 2,
    borderBottomColor: 'rgba(0,0,0,.05)',
  },
  tab: {
    width: 'auto',
  },
  indicator: {
    backgroundColor: '#008060',
  },
  label: {
    fontSize: 16,
    fontWeight: '400',
    color: '#333',
    textTransform: 'none',
  },
});

export default OrdersScreen;
