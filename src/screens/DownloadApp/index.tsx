import {ScreenWidth} from '@rneui/base';
import {Text} from '@rneui/themed';
import React, {useCallback, useMemo} from 'react';
import {Image, Linking, StyleSheet, TouchableOpacity, View} from 'react-native';
// @ts-ignore
import AppMockupPNG from '~assets/mockup-1000.png';
// @ts-ignore
import AppstorePNG from '~assets/appstore.png';
// @ts-ignore
import PlaystorePNG from '~assets/playstore.png';
import theme from '~utils/config/themes/theme';
import {responsiveWidth} from '~components/shared/web/WebContainer';

const DownloadApp = () => {
  const screenWidth = useMemo(() => Math.min(ScreenWidth, responsiveWidth.sm), []);

  const handleOpenAppStore = useCallback(() => {
    Linking.openURL('https://apps.apple.com/vn/app/tts-dropship/id6443614020');
  }, []);

  const handleOpenPlayStore = useCallback(() => {
    Linking.openURL('https://play.google.com/store/apps/details?id=com.tts.ds.buyer&hl=vi&gl=vi');
  }, []);

  return (
    <View style={{backgroundColor: '#dfdfdf', flex: 1}}>
      <Image source={AppMockupPNG} style={{width: screenWidth, height: (screenWidth * 251) / 300}} />

      <Text style={styles.h1}>Tải ứng dụng</Text>
      <Text style={styles.microcopy}>Đăng bán sản phẩm dễ dàng hơn</Text>

      <View style={[theme.globalStyles.flexRow, {justifyContent: 'center', marginTop: theme.spacing.l}]}>
        <TouchableOpacity onPress={handleOpenAppStore}>
          <Image source={AppstorePNG} style={{width: 160, height: 47, marginRight: theme.spacing.m}} />
        </TouchableOpacity>
        <TouchableOpacity onPress={handleOpenPlayStore}>
          <Image source={PlaystorePNG} style={{width: 160, height: 47}} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {},
  h1: {
    fontSize: theme.typography.lg2,
    textAlign: 'center',
    marginTop: theme.spacing.l,
  },
  microcopy: {
    fontSize: theme.typography.md,
    textAlign: 'center',
    color: theme.colors.textLight,
    marginTop: theme.spacing.s,
  },
});

export default DownloadApp;
