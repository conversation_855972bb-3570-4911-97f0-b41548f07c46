import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Button, Input} from '@rneui/base';
import React, {useCallback, useMemo, useState} from 'react';
import {Modal, Platform, ScrollView, StyleSheet, Text, View} from 'react-native';
import {SafeAreaView, useSafeAreaInsets} from 'react-native-safe-area-context';
import Select from '~components/shared/Form/Select';
import {LocationType} from '~components/shared/LocationBottomSheet';
import LocationTabView from '~components/shared/LocationTabView';
import useCreateOrderStore from '~hooks/store/useCreateOrderStore';
import {useCreateCustomerAddressMutation} from '~utils/api/customer';
import {useGetLocations} from '~utils/api/location';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import Address from '~utils/types/address';
import Customer from '~utils/types/customer';
import addressValidate from '~utils/validate/address';
import Ionicons from 'react-native-vector-icons/Ionicons';
import * as Sentry from '@sentry/react-native';
import {useToast} from 'react-native-toast-notifications';
import AddressInputWithSearch from '~components/shared/Form/AddressInputWithSearch';
import theme from '~utils/config/themes/theme';

const CreateCustomerAddress: React.FC<NativeStackScreenProps<RootStackParamList, 'CreateCustomerAddress'>> = ({route, navigation}) => {
  const [customer, setCustomer] = useState<Partial<Customer & {name: string}>>({});
  const [address, setAddress] = useState<Partial<Address>>({});
  const [submitLoading, setSubmitLoading] = useState(false);
  const mutation = useCreateCustomerAddressMutation();
  const setNewOrderState = useCreateOrderStore(state => state.setNewOrderState);
  const {data} = useGetLocations();
  /* eslint-disable-next-line no-spaced-func */
  const [errors, setErrors] = useState<Partial<{[key in keyof (Customer & {name?: string})]: string}>>({});
  const [locationSelectOpen, setLocationSelectOpen] = useState(false);
  const [initialTab, setInitialTab] = useState(0);
  const insets = useSafeAreaInsets();
  const toast = useToast();

  const getCities = useMemo(() => (data || []).reduce<string[]>((acc, cur) => [...acc, ...Object.keys(cur)], []), [data]);
  const getProvinces = useMemo(() => {
    if (!address.city || !data) {
      return [];
    }

    for (const city of data) {
      if (Object.keys(city).includes(address.city)) {
        return Object.keys(city[address.city]);
      }
    }

    return [];
  }, [address.city, data]);

  const getWards = useMemo(() => {
    if (!data || !address.city || !address.province) {
      return [];
    }
    for (const city of data) {
      if (Object.keys(city).includes(address.city)) {
        return city[address.city][Object.keys(city[address.city]).filter(province => province === address.province)[0]] || [];
      }
    }
    return [];
  }, [data, address.city, address.province]);

  const handleLocationSelectPress = useCallback((type: LocationType) => {
    const tab = {
      city: 0,
      province: 1,
      ward: 2,
    };
    setInitialTab(tab[type]);
    setLocationSelectOpen(true);
  }, []);

  const handleCustomerChange = useCallback((field: keyof (Customer & {name: string}), value: any) => {
    setCustomer(prev => ({...prev, [field]: value}));
  }, []);

  const handleAddressChange = useCallback((field: keyof Address, value: any) => {
    setAddress(prev => ({...prev, [field]: value}));
  }, []);

  const handleSubmit = async () => {
    setSubmitLoading(true);
    setErrors({});
    try {
      let isValid = true;
      let nameSplits = customer.name?.split(' ');
      const createCustomerData: Partial<Address> = {
        ...customer,

        first_name: nameSplits?.[0],
        last_name: nameSplits?.slice(1).join(' '),
        customer_id: route.params.customerId,
        ...address,
      };
      await addressValidate.validate(createCustomerData, {abortEarly: false}).catch(err => {
        isValid = false;
        setErrors(err.inner.reduce((a: any, c: any) => ({...a, [c.path]: c.message}), {}));
        setSubmitLoading(false);
      });

      if (!isValid) {
        return;
      }

      const res = await mutation.mutateAsync({address: createCustomerData});
      if (route.params?.isCreateOrder) {
        setNewOrderState('shipping_address', res);
        navigation.popTo('OrderConfirm');
      } else {
        navigation.goBack();
      }
    } catch (error: any) {
      Sentry.captureMessage('Tao dia chi that bai');
      toast.show(error?.response?.data?.message ?? 'Có lỗi xảy ra, vui lòng bấm nhắn tin để được hỗ trợ', {type: 'danger'});
    }
    setSubmitLoading(false);
  };

  const handleSelectDone = useCallback((add: Partial<Address>) => {
    setAddress(prev => ({...prev, ...add}));
    setLocationSelectOpen(false);
  }, []);

  const handleAddress1Change = useCallback(
    (
      address1: string,
      additionalData?: {
        ward: string;
        city: string;
        province: string;
      },
    ) => {
      handleAddressChange('address1', address1);
      if (additionalData) {
        handleAddressChange('ward', additionalData.ward);
        handleAddressChange('city', additionalData.city);
        handleAddressChange('province', additionalData.province);
      }
    },
    [],
  );

  return (
    <>
      <SafeAreaView edges={['bottom', 'left', 'right']} style={{flex: 1}}>
        <ScrollView style={styles.container}>
          <Input
            label="Họ và tên"
            containerStyle={styles.formContainer}
            inputContainerStyle={styles.inputContainer}
            inputStyle={styles.input}
            labelStyle={styles.label}
            value={customer.name}
            onChangeText={text => handleCustomerChange('name', text)}
            errorMessage={errors.name}
          />
          <Input
            label="Số điện thoại"
            containerStyle={styles.formContainer}
            inputContainerStyle={styles.inputContainer}
            inputStyle={styles.input}
            labelStyle={styles.label}
            keyboardType="numeric"
            value={customer.phone}
            onChangeText={text => handleCustomerChange('phone', text)}
            errorMessage={errors.phone}
          />
          <AddressInputWithSearch onDone={handleAddress1Change} address={address.address1} label="Địa chỉ nhận hàng" errorMessage={(errors as any).address1} />
          <Select
            options={getCities}
            label="Tỉnh / thành phố"
            onPress={() => handleLocationSelectPress('city')}
            value={address.city}
            onChange={city => handleAddressChange('city', city)}
            errorMessage={(errors as any).city}
          />
          <Select
            options={getProvinces}
            label="Quận / huyện"
            onPress={() => handleLocationSelectPress('province')}
            value={address.province}
            onChange={province => handleAddressChange('province', province)}
            errorMessage={(errors as any).province}
          />
          <Select
            options={getWards}
            label="Phường / xã"
            onPress={() => handleLocationSelectPress('ward')}
            value={address.ward}
            onChange={ward => handleAddressChange('ward', ward)}
            errorMessage={(errors as any).ward}
          />
          {/* 
          <Input
            label="Số nhà, tên đường"
            containerStyle={styles.formContainer}
            inputContainerStyle={styles.inputContainer}
            inputStyle={styles.input}
            labelStyle={styles.label}
            value={address.address1}
            onChangeText={text => handleAddressChange('address1', text)}
            errorMessage={(errors as any).address1}
            placeholder="Ví dụ: 28 Âu Cơ"
          /> */}
        </ScrollView>
        <Button title={'Tạo địa chỉ'} buttonStyle={{backgroundColor: '#008060'}} onPress={handleSubmit} loading={submitLoading} />
      </SafeAreaView>

      {Platform.OS !== 'web' && (
        <Modal visible={locationSelectOpen} onRequestClose={() => setLocationSelectOpen(false)} animationType="slide">
          <View style={{flex: 1, paddingTop: insets.top, paddingBottom: insets.bottom}}>
            <View style={{paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: 'rgba(0,0,0,.08)', position: 'relative'}}>
              <View style={{position: 'absolute', left: 12, top: 4, zIndex: 100}}>
                <Ionicons name="close-circle" size={38} color="rgba(0,0,0,.5)" onPress={() => setLocationSelectOpen(false)} />
              </View>
              <Text style={{textAlign: 'center', fontSize: 18, fontWeight: '500', color: '#008060'}}>Chọn địa chỉ</Text>
              <View />
            </View>
            <LocationTabView initialTab={initialTab} onSelectDone={handleSelectDone} initialAddress={address} />
          </View>
        </Modal>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 8,
    paddingVertical: 20,
    backgroundColor: '#fff',
    position: 'relative',
    flex: 1,
  },
  inputContainer: {
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,.3)',
    borderRadius: 8,
    marginTop: 8,
  },
  formContainer: {
    marginBottom: 0,
  },
  input: {
    padding: 12,
    fontSize: theme.typography.md,
  },
  label: {
    fontWeight: '500',
    fontSize: 18,
  },
});

export default CreateCustomerAddress;
