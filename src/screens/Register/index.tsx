import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Button, Text} from '@rneui/themed';
import React, {useEffect, useState} from 'react';
import {ActivityIndicator, Platform, StyleSheet, View} from 'react-native';
import {useAuthActions} from '~hooks';
import theme from '~utils/config/themes/theme';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useGetOAuthDrivers} from '~utils/api/auth';
import isInZaloMiniApp from '~utils/helpers/isInZaloMiniApp';
import ZaloMiniAppRegister from '~components/shared/ZaloMiniAppRegister';
// @ts-ignore
import ZaloIcon from '~assets/zalo.png';
// @ts-ignore
import GoogleIcon from '~assets/google.png';
// @ts-ignore
import FacebookIcon from '~assets/facebook.png';
// @ts-ignore
import AppleIcon from '~assets/apple.png';
import {QuickImage} from '~components/shared/QuickImage';
import {WINDOW_WIDTH} from '@gorhom/bottom-sheet';
import auth from '@react-native-firebase/auth';

export const OAUTH_BUTTON_WIDTH = Math.min(WINDOW_WIDTH * 0.8, 350);

export const DRIVERS_DATA = {
  facebook: {
    logo: FacebookIcon,
    name: 'Facebook',
  },
  zalo: {
    logo: ZaloIcon,
    name: 'Zalo',
  },
  google: {
    logo: GoogleIcon,
    name: 'Google',
  },
  apple: {
    logo: AppleIcon,
    name: 'Apple',
  },
};

const RegisterScreen: React.FC<NativeStackScreenProps<RootStackParamList, 'Register'>> = ({navigation}) => {
  const {isLoggingIn, openOAuth2InAppBrowser, openSSOAuth, isLoggedIn} = useAuthActions();
  const {data: drivers} = useGetOAuthDrivers();
  const [isZaloMiniApp] = useState(isInZaloMiniApp());

  useEffect(() => {
    if (Platform.OS !== 'web') {
      auth()
        .signOut()
        .catch(() => {});
    }
    if (isLoggedIn) {
      navigation.pop();
    }
  }, [isLoggedIn]);

  const handleOAuthPress = async (driver: string) => {
    openOAuth2InAppBrowser(driver);
  };

  const handleLoginPress = () => {
    navigation.replace('Login');
  };

  const driversByPlatform = Platform.OS === 'android' ? drivers.filter(d => d !== 'apple') : drivers;

  if (isZaloMiniApp) {
    return <ZaloMiniAppRegister />;
  }

  return (
    <View style={{flex: 1, backgroundColor: '#fff'}}>
      {isLoggingIn ? (
        <View style={styles.authLoading}>
          <ActivityIndicator size={30} />
          <Text>Đang đăng nhập</Text>
        </View>
      ) : (
        <View style={styles.container}>
          {/* <Image source={HappyRich} style={{width: 250, height: 167, marginBottom: theme.spacing.m}} /> */}
          <View style={{paddingHorizontal: theme.spacing.l}}>
            <Text style={{fontSize: theme.typography.lg2, textAlign: 'center', fontWeight: '500'}}>Đăng ký tài khoản TTS Dropship</Text>
            <Text style={{textAlign: 'center', marginTop: theme.spacing.s, color: theme.colors.textLight}}>Tạo đơn hàng và kiếm thu nhập không giới hạn. Hoàn toàn miễn phí</Text>
          </View>
          <View style={styles.actionContainer}>
            {/* {drivers?.[0] && DRIVERS_DATA[drivers?.[0] as keyof typeof DRIVERS_DATA] ? <OAuthButton driver={DRIVERS_DATA[drivers?.[0] as keyof typeof DRIVERS_DATA]} /> : null} */}
            {driversByPlatform?.map(driverName => {
              const driver = DRIVERS_DATA?.[driverName as keyof typeof DRIVERS_DATA];
              if (driver) {
                return <OAuthButton key={driverName} driver={driver} onPress={() => handleOAuthPress(driverName)} />;
              }
              return null;
            })}

            <Button
              buttonStyle={{
                borderColor: theme.colors.gray50,
                borderWidth: 1,
                borderRadius: 4,
                justifyContent: 'center',
                alignItems: 'center',
                paddingHorizontal: theme.spacing.m,
                paddingVertical: 0,
                width: OAUTH_BUTTON_WIDTH,
                height: 45,
              }}
              size="sm"
              onPress={() => openSSOAuth('register')}
              type="outline"
              containerStyle={{
                marginBottom: theme.spacing.m,
              }}>
              <Ionicons name="person-circle-outline" size={28} color={theme.colors.gray90} style={{marginRight: theme.spacing.s, borderRadius: 100, position: 'absolute', left: 10}} />
              <Text style={{fontSize: theme.typography.md}} numberOfLines={1}>
                Đăng ký bằng Email/SĐT
              </Text>
            </Button>
            <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 40, justifyContent: 'center'}}>
              <Text style={{fontSize: theme.typography.md}}>Đã có tài khoản?</Text>
              <Button type="clear" titleStyle={{color: theme.colors.primary, fontWeight: 'bold', fontSize: 16}} title="Đăng nhập" onPress={handleLoginPress} />
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

export const OAuthButton: React.FC<{driver: {logo: any; name: string}; labelHidden?: boolean; login?: boolean; onPress?: () => Promise<void>}> = props => {
  const [isLoading, setLoading] = useState(false);

  const handlePress = async () => {
    setLoading(true);
    await props.onPress?.();
    setLoading(false);
  };

  return (
    <Button
      buttonStyle={[
        {
          borderColor: theme.colors.gray50,
          borderWidth: 1,
          borderRadius: 4,
          justifyContent: 'center',
          // paddingHorizontal: theme.spacing.m,
          paddingVertical: 0,
          width: OAUTH_BUTTON_WIDTH,
          height: 44,
          position: 'relative',
        },
        props.labelHidden && {
          // paddingHorizontal: 0,
          width: 'auto',
          borderColor: theme.colors.white,
          paddingHorizontal: theme.spacing.s,
        },
      ]}
      onPress={handlePress}
      size="sm"
      type="outline"
      loading={isLoading}
      containerStyle={[
        {
          marginBottom: theme.spacing.m,
        },
        props.labelHidden && {alignSelf: 'flex-start'},
        isLoading && {alignItems: 'center'},
      ]}>
      <QuickImage
        source={props.driver.logo}
        style={[
          {width: 24, height: 24, borderRadius: 30, marginRight: theme.spacing.s, position: 'absolute', left: 12},
          props.labelHidden && {
            width: 35,
            height: 35,
          },
        ]}
      />
      {!props.labelHidden && (
        <Text style={{fontSize: theme.typography.md}}>
          {props.login ? 'Đăng nhập' : 'Đăng ký'} bằng {props.driver.name}
        </Text>
      )}
    </Button>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  actionContainer: {
    marginTop: 100,
  },
  authLoading: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 200,
  },
  loginReason: {
    fontSize: 16,
    marginTop: 8,
  },
});

export default RegisterScreen;
