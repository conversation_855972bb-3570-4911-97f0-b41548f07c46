import React from 'react';
import {ActivityIndicator, FlatList} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import NewUserTutorials from '~components/by-screens/Home/NewUserTutorials';
import WordpressPostCard from '~components/shared/WPPostCard';
import {useGetDocs} from '~utils/api/posts';

const Instruction = () => {
  const {data} = useGetDocs({per_page: 10, context: 'embed', doc_category: [26]});

  return (
    <SafeAreaView edges={['left', 'right', 'bottom']} style={{flex: 1}}>
      <FlatList
        style={{paddingVertical: 20, backgroundColor: 'white', flex: 1}}
        ListHeaderComponent={
          <>
            <NewUserTutorials />
          </>
        }
        ListEmptyComponent={<ActivityIndicator size={'small'} />}
        data={data}
        renderItem={item => <WordpressPostCard post={item.item} />}
        numColumns={1}
      />
    </SafeAreaView>
  );
};

export default Instruction;
