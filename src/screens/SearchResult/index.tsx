import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Button, Text} from '@rneui/themed';
import React, {useCallback, useMemo, useRef, useState} from 'react';
import {ActivityIndicator, FlatList, ListRenderItemInfo, Platform, Pressable, StyleSheet, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import Ionicons from 'react-native-vector-icons/Ionicons';
import theme from '~utils/config/themes/theme';
import {useSearchProductsInfinite} from '~utils/api/product';
import {ProductSortQuery, SearchProduct} from '~utils/types/product';
import ProductCard from '~components/shared/ProductCard';
import ListEmptyProduct from '~components/shared/ListEmptyProduct';
import useSort from '~hooks/useSort';
import ScrollToTop from '~components/shared/ScrollToTop';
import RelatedSupplier from '~components/by-screens/SearchResult/RelatedSupplier';

const SearchResult: React.FC<NativeStackScreenProps<RootStackParamList, 'SearchResult'>> = ({route, navigation}) => {
  const flatListRef = useRef<FlatList<any>>(null);
  const [searchValue] = useState(route.params.keyword);
  const insets = useSafeAreaInsets();
  const {sortState, updateSort} = useSort({
    sort_by: route.params.sort_by || '_score',
    ascending: ['true', 'false'].includes(route.params.ascending as any) ? (route.params.ascending === 'true' ? true : false) : undefined,
  });
  const {data, isLoading, fetchNextPage, isFetchingNextPage} = useSearchProductsInfinite({...route.params, sort_by: sortState.sort_by, ascending: sortState.ascending});

  const dataFlat = useMemo(() => {
    return data?.pages.flatMap(page => page.products) ?? [];
  }, [data]);

  const renderItem = useCallback((item: ListRenderItemInfo<SearchProduct>) => {
    return (
      <View style={styles.productItem}>
        <ProductCard product={item.item} />
      </View>
    );
  }, []);

  const getSelectedColor = useCallback(
    (sortBy: ProductSortQuery) => {
      return sortState.sort_by === sortBy ? theme.colors.primary : theme.colors.gray70;
    },
    [sortState.sort_by],
  );

  const getBorderBottomWidth = useCallback(
    (sortBy: ProductSortQuery) => {
      return sortState.sort_by === sortBy ? 2 : 0;
    },
    [sortState.sort_by],
  );

  const handleSortPress = useCallback(
    (sortBy: ProductSortQuery, ascending?: boolean) => {
      updateSort({sort_by: sortBy, ascending});
      if (Platform.OS === 'web') {
        navigation.navigate('SearchResult', {
          ...route.params,
          sort_by: sortBy,
          ascending: ascending,
        });
      }
    },
    [route.params, sortState],
  );

  const handleEndReached = () => {
    if (!isFetchingNextPage) {
      fetchNextPage();
    }
  };
  const handleSearchPress = useCallback(() => {
    navigation.navigate('Search', {keyword: route.params.keyword, shopId: route.params.filter_shop_id as any});
  }, [route.params]);

  const handleScrollToTop = useCallback(() => {
    flatListRef.current?.scrollToOffset({animated: true, offset: 0});
  }, []);

  return (
    <View style={[{flex: 1}, !isLoading && !dataFlat.length && {backgroundColor: theme.colors.white}]}>
      <View style={{paddingTop: insets.top, paddingBottom: 4, flexDirection: 'row', alignItems: 'center', paddingRight: 12, backgroundColor: '#fff', marginHorizontal: -4}}>
        <Ionicons name="chevron-back" size={28} style={{paddingHorizontal: 8, paddingVertical: 8}} color={theme.colors.textLight} onPress={navigation.goBack} />
        <Pressable
          onPress={handleSearchPress}
          style={{flex: 1, paddingVertical: 12, backgroundColor: theme.colors.gray20, borderBottomColor: '#fff', paddingHorizontal: theme.spacing.m, borderRadius: 8}}>
          <Text
            style={{
              fontSize: theme.typography.base,
            }}>
            {searchValue}
          </Text>
        </Pressable>
      </View>

      <View style={{flexDirection: 'row', backgroundColor: '#fff', paddingTop: 8, paddingBottom: 2, borderBottomColor: 'rgba(0,0,0,.1)', borderBottomWidth: 1, marginHorizontal: -4}}>
        <Button
          type="clear"
          containerStyle={styles.sortItem}
          title={'Liên quan'}
          titleProps={{
            allowFontScaling: false,
          }}
          buttonStyle={[styles.sortItem_btn, {borderBottomWidth: getBorderBottomWidth('_score')}]}
          titleStyle={[styles.sortItem_text, {color: getSelectedColor('_score')}]}
          onPress={() => handleSortPress('_score')}
        />
        <Button
          containerStyle={styles.sortItem}
          type="clear"
          title={'Mới nhất'}
          titleProps={{
            allowFontScaling: false,
          }}
          buttonStyle={[styles.sortItem_btn, {borderBottomWidth: getBorderBottomWidth('created_at')}]}
          titleStyle={[styles.sortItem_text, {color: getSelectedColor('created_at')}]}
          onPress={() => handleSortPress('created_at')}
        />
        <Button
          containerStyle={styles.sortItem}
          type="clear"
          title={'Bán chạy'}
          titleProps={{
            allowFontScaling: false,
          }}
          buttonStyle={[styles.sortItem_btn, {borderBottomWidth: getBorderBottomWidth('total_sales')}]}
          titleStyle={[styles.sortItem_text, {color: getSelectedColor('total_sales')}]}
          onPress={() => handleSortPress('total_sales')}
        />
        <Button
          type="clear"
          containerStyle={styles.sortItem}
          title={'Lợi nhuận'}
          titleProps={{
            allowFontScaling: false,
          }}
          buttonStyle={[styles.sortItem_btn, {borderBottomWidth: getBorderBottomWidth('dropship_profit')}]}
          titleStyle={[styles.sortItem_text, {color: getSelectedColor('dropship_profit')}]}
          onPress={() => handleSortPress('dropship_profit', typeof sortState.ascending !== 'undefined' && sortState.sort_by === 'dropship_profit' ? !sortState.ascending : false)}
          iconRight
          icon={
            <View style={{marginLeft: 4}}>
              {typeof sortState.ascending !== 'undefined' && sortState.sort_by === 'dropship_profit' ? (
                <>
                  {sortState.ascending === true && <Ionicons name="arrow-up-outline" size={18} color={theme.colors.primary} />}
                  {sortState.ascending === false && <Ionicons name="arrow-down-outline" size={18} color={theme.colors.primary} />}
                </>
              ) : (
                <>
                  <Ionicons name="caret-up" size={10} style={{padding: 0, marginBottom: -4}} color={theme.colors.gray60} />
                  <Ionicons name="caret-down" size={10} style={{padding: 0}} color={theme.colors.gray60} />
                </>
              )}
            </View>
          }
        />
        <Button
          containerStyle={styles.sortItem}
          type="clear"
          title={'Giá'}
          titleProps={{
            allowFontScaling: false,
          }}
          buttonStyle={[styles.sortItem_btn, {borderBottomWidth: getBorderBottomWidth('dropship_price'), borderRightWidth: 0}]}
          titleStyle={[styles.sortItem_text, {color: getSelectedColor('dropship_price')}]}
          iconRight
          icon={
            <View style={{marginLeft: 4}}>
              {typeof sortState.ascending !== 'undefined' && sortState.sort_by === 'dropship_price' ? (
                <>
                  {sortState.ascending === true && <Ionicons name="arrow-up-outline" size={18} color={theme.colors.primary} />}
                  {sortState.ascending === false && <Ionicons name="arrow-down-outline" size={18} color={theme.colors.primary} />}
                </>
              ) : (
                <>
                  <Ionicons name="caret-up" size={10} style={{padding: 0, marginBottom: -4}} color={theme.colors.gray60} />
                  <Ionicons name="caret-down" size={10} style={{padding: 0}} color={theme.colors.gray60} />
                </>
              )}
            </View>
          }
          onPress={() => handleSortPress('dropship_price', typeof sortState.ascending !== 'undefined' ? !sortState.ascending : true)}
        />
      </View>
      <FlatList
        ref={flatListRef}
        data={dataFlat}
        keyExtractor={item => item.id}
        renderItem={renderItem}
        numColumns={2}
        ListHeaderComponent={<>{!route.params.filter_shop_id && <RelatedSupplier keyword={route.params.keyword} />}</>}
        ListEmptyComponent={
          isLoading ? (
            // <View style={{marginVertical: 20}}>
            <ActivityIndicator color={theme.colors.primary} />
          ) : (
            // </View>
            <ListEmptyProduct content="Không tìm thấy sản phẩm nào, hãy thử thay đổi truy vấn và thử lại" />
          )
        }
        onEndReached={handleEndReached}
        columnWrapperStyle={{paddingHorizontal: 4}}
        ListFooterComponent={
          isFetchingNextPage ? (
            <View style={{flexDirection: 'row', justifyContent: 'center'}}>
              <ActivityIndicator color={theme.colors.primary} />
              <Text style={{marginLeft: 8}}>Đang tải thêm kết quả</Text>
            </View>
          ) : null
        }
      />
      <ScrollToTop onPress={handleScrollToTop} />
    </View>
  );
};

const styles = StyleSheet.create({
  productItem: {
    flex: 1,
    backgroundColor: '#fff',
    marginHorizontal: 4,
    marginVertical: 8,
    borderRadius: 12,
  },
  sortItem_text: {
    fontSize: 14,
    flexShrink: 0,
    color: theme.colors.gray70,
  },
  sortItem_btn: {
    paddingHorizontal: 0,
    // borderRightWidth: 1,
    // borderRightColor: 'rgba(0,0,0,.05)',
    paddingVertical: 8,
    borderBottomColor: theme.colors.primary,
    borderRadius: 0,
  },
  sortItem: {
    flex: 1,
    borderRadius: 0,
  },
});
export default SearchResult;
