import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Button, Text} from '@rneui/themed';
import {FlashList, ListRenderItemInfo} from '@shopify/flash-list';
import React, {useCallback, useMemo, useState} from 'react';
import {ActivityIndicator, RefreshControl, View} from 'react-native';
import CustomerItem from '~components/by-screens/Customers/CustomerItem';
import {useGetCustomersInfinite} from '~utils/api/customer';
import theme from '~utils/config/themes/theme';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import Customer from '~utils/types/customer';

const Customers: React.FC<NativeStackScreenProps<RootStackParamList, 'Customers'>> = ({navigation}) => {
  const {data, fetchNextPage, isFetchingNextPage, isLoading, refetch} = useGetCustomersInfinite();
  const [selectModeEnabled] = useState(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [isRefreshing, setRefreshing] = useState(false);

  const dataFlat = useMemo(() => {
    return data?.pages.flatMap(page => page.customers) ?? [];
  }, [data]);

  // useEffect(() => {
  //   if (dataFlat.length) {
  //     navigation.setOptions({
  //       headerRight: () => (
  //         <Button buttonStyle={{paddingVertical: 0}} title={selectModeEnabled ? 'Hủy' : 'Chọn'} type="clear" onPress={toggleSelectModeEnabled} titleStyle={{fontSize: theme.typography.md}} />
  //       ),
  //     });
  //     setSelectedIds([]);
  //   }
  // }, [selectModeEnabled, dataFlat]);

  const handleCustomerSelect = useCallback((customer: Customer) => {
    setSelectedIds(prev => {
      const isExist = prev.includes(customer.id);
      if (isExist) {
        return prev.filter(id => id !== customer.id);
      }

      return [...prev, customer.id];
    });
  }, []);

  // const toggleSelectModeEnabled = useCallback(() => {
  //   setSelectModeEnabled(prev => !prev);
  // }, []);

  const renderItem = useCallback(
    (item: ListRenderItemInfo<Customer>) => {
      return <CustomerItem customer={item.item} selectable={selectModeEnabled} selected={selectedIds.includes(item.item.id)} onSelect={handleCustomerSelect} />;
    },
    [data, selectedIds, selectModeEnabled],
  );

  const handleGoToCreateNewCustomer = useCallback(() => {
    navigation.navigate('CreateCustomer');
  }, []);

  const handleEndReached = () => {
    if (!isFetchingNextPage) {
      fetchNextPage();
    }
  };

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  }, [refetch]);

  return (
    <View style={{marginHorizontal: 12, flex: 1}}>
      <FlashList
        refreshControl={<RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />}
        data={dataFlat}
        ListHeaderComponent={
          <>
            <View style={{marginVertical: 20}}>
              <Button size="sm" buttonStyle={theme.globalStyles.rounded12} onPress={handleGoToCreateNewCustomer}>
                Tạo mới khách hàng
              </Button>
            </View>
          </>
        }
        ListEmptyComponent={<>{isLoading && <ActivityIndicator />}</>}
        keyExtractor={item => item.id}
        estimatedItemSize={104}
        renderItem={renderItem}
        onEndReached={handleEndReached}
        ListFooterComponent={
          isFetchingNextPage ? (
            <View style={{flexDirection: 'row', justifyContent: 'center'}}>
              <ActivityIndicator color={theme.colors.primary} />
              <Text style={{marginLeft: 8}}>Đang tải thêm kết quả</Text>
            </View>
          ) : dataFlat.length === 0 && !isLoading ? (
            <Text style={{textAlign: 'center', marginTop: 150}}>Chưa có khách hàng nào</Text>
          ) : null
        }
      />
    </View>
  );
};

export default Customers;
