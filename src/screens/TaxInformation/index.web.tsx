import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React from 'react';
import {View} from 'react-native';
import theme from '~utils/config/themes/theme';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import TaxInformationForm from './TaxInformationForm';

const TaxInformation: React.FC<NativeStackScreenProps<RootStackParamList, 'TaxInformation'>> = ({}) => {
  return (
    <View style={{paddingHorizontal: theme.spacing.m, backgroundColor: theme.colors.white, position: 'relative'}}>
      <TaxInformationForm />
    </View>
  );
};

export default TaxInformation;
