import React, {useEffect} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import {env} from '~utils/config';

const TaxInformation: React.FC<NativeStackScreenProps<RootStackParamList, 'TaxInformation'>> = ({navigation}) => {
  useEffect(() => {
    navigation.replace('WebView', {url: `${env.WEBVIEW_URL}/account/tax-information`, includeAuth: true});
  }, []);
  return null;
};

export default TaxInformation;
