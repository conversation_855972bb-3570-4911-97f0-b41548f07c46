import {Button, CheckBox, Input, Text} from '@rneui/themed';
import React, {useState} from 'react';
import {Modal, Pressable, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import LocationTabView from '~components/shared/LocationTabView';
import ModalScreenHeader from '~components/shared/ModalScreenHeader';
import theme from '~utils/config/themes/theme';
import Address from '~utils/types/address';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {updateTaxInformation} from '~utils/api/auth';
import taxInformationValidate from '~utils/validate/tax-information';
import {useToast} from 'react-native-toast-notifications';

const TaxInformationForm: React.FC<any> = ({}) => {
  const [taxType, setTaxType] = useState<'personal' | 'company' | 'household'>('personal');
  const [companyName, setCompanyName] = useState('');
  const [addressModalOpen, setAddressModalOpen] = useState(false);
  const [address, setAddress] = useState<Partial<Address>>({});
  const [taxEmail, setTaxEmail] = useState(['']);
  const [taxId, setTaxId] = useState('');
  const [submitLoading, setSubmitLoading] = useState(false);
  const [errors, setErrors] = useState<any>({});
  const toast = useToast();

  const handleSelectAddressDone = (address: Partial<Address>) => {
    setAddress(address);
    setAddressModalOpen(false);
  };

  const handleSetAddressDetail = (addressDetail: string) => {
    setAddress({...address, address1: addressDetail});
  };

  const handleChangeTaxEmail = (index: number, email: string) => {
    setTaxEmail(taxEmail.map((e, i) => (i === index ? email : e)));
  };

  const handleSubmit = async () => {
    setSubmitLoading(true);
    const taxInfo = {
      tax_id: taxId,
      tax_address: [address.address1, address.ward, address.city, address.province].filter(Boolean).join(', '),
      tax_name: companyName,
      tax_email: taxEmail,
      tax_type: taxType,
    };

    let isValid = true;

    console.log(taxInfo);

    await taxInformationValidate.validate(taxInfo, {abortEarly: false}).catch(err => {
      isValid = false;
      console.log(err.inner);
      setErrors(err.inner.reduce((a: any, c: any) => ({...a, [c.path]: c.message}), {}));
      setSubmitLoading(false);
    });

    if (!isValid) {
      return;
    }

    updateTaxInformation(taxInfo).then(() => {
      setSubmitLoading(false);
      toast.show('Cập nhật thành công');
    });
  };

  console.log(errors);

  return (
    <>
      <ScrollView style={styles.container}>
        <Text style={[styles.inputLabel]}>Loại hình kinh doanh</Text>

        <View style={styles.taxTypeContainer}>
          <CheckBox
            textStyle={styles.checkboxText}
            containerStyle={styles.checkboxContainer}
            title="Cá nhân"
            checkedIcon="radio-button-on"
            iconType="ionicon"
            size={20}
            uncheckedIcon="radio-button-off"
            checked={taxType === 'personal'}
            onPress={() => setTaxType('personal')}
          />
          <CheckBox
            textStyle={styles.checkboxText}
            containerStyle={styles.checkboxContainer}
            title="Hộ kinh doanh"
            checkedIcon="radio-button-on"
            iconType="ionicon"
            uncheckedIcon="radio-button-off"
            size={20}
            checked={taxType === 'household'}
            onPress={() => setTaxType('household')}
          />
          <CheckBox
            containerStyle={styles.checkboxContainer}
            textStyle={styles.checkboxText}
            title="Công ty"
            checkedIcon="radio-button-on"
            iconType="ionicon"
            uncheckedIcon="radio-button-off"
            size={20}
            checked={taxType === 'company'}
            onPress={() => setTaxType('company')}
          />
        </View>
        {/*  */}
        {taxType !== 'personal' && (
          <View style={styles.companyNameContainer}>
            <Input
              containerStyle={{paddingHorizontal: 0}}
              labelStyle={styles.inputLabel}
              label={`Tên ${taxType === 'company' ? 'công ty' : taxType === 'household' ? 'hộ kinh doanh' : 'cá nhân'}`}
              inputStyle={styles.input}
              placeholder={`Nhập tên ${taxType === 'company' ? 'công ty' : taxType === 'household' ? 'hộ kinh doanh' : 'cá nhân'}`}
              value={companyName}
              onChangeText={setCompanyName}
              renderErrorMessage={!!errors.tax_name}
              errorMessage={errors.tax_name}
            />
          </View>
        )}

        <View style={{marginTop: theme.spacing.m}}>
          <Text style={styles.inputLabel}>Địa chỉ đăng ký kinh doanh</Text>
          <TouchableOpacity onPress={() => setAddressModalOpen(true)} style={{paddingVertical: 10, paddingHorizontal: 16, borderWidth: 1, borderColor: 'rgba(0,0,0,.1)', borderRadius: 12}}>
            <Text style={{color: address.ward ? theme.colors.text : theme.colors.gray50, fontSize: theme.typography.base, fontWeight: '400'}}>
              {address.ward ? [address.city, address.province, address.ward].join(', ') : 'Tỉnh-Thành/Quận-Huyện/Phường-Xã'}
            </Text>
          </TouchableOpacity>

          {!!errors.tax_address && <Text style={{color: theme.colors.red, fontSize: theme.typography.sm, marginTop: theme.spacing.s}}>{errors.tax_address}</Text>}

          {!!address.ward && (
            <Input
              containerStyle={{paddingHorizontal: 0, marginTop: theme.spacing.m}}
              labelStyle={styles.inputLabel}
              label="Địa chỉ chi tiết"
              placeholder="Số nhà, tên đường"
              value={address.address1}
              inputStyle={styles.input}
              onChangeText={handleSetAddressDetail}
            />
          )}

          <Modal visible={addressModalOpen} onRequestClose={() => setAddressModalOpen(false)} animationType="slide">
            <ModalScreenHeader title="Chọn địa chỉ" onClose={() => setAddressModalOpen(false)} />
            <LocationTabView initialTab={0} onSelectDone={handleSelectAddressDone} initialAddress={address} />
          </Modal>
        </View>

        <View style={{marginTop: theme.spacing.m}}>
          <Text style={styles.inputLabel}>Email nhận hóa đơn điện tử</Text>
          {taxEmail.map((email, index) => (
            <View key={index} style={{flexDirection: 'row', alignItems: 'center', gap: 4}}>
              <Input
                onChangeText={text => handleChangeTaxEmail(index, text)}
                errorMessage={errors[`tax_email[${index}]`]}
                containerStyle={{paddingHorizontal: 0, flex: 1}}
                inputStyle={styles.input}
                labelStyle={styles.inputLabel}
                value={email}
                placeholder="Nhập email"
              />
              <Pressable onPress={() => setTaxEmail(taxEmail.filter((_, i) => i !== index))} style={{marginBottom: errors[`tax_email[${index}]`] ? theme.spacing.s + 14 : theme.spacing.s}}>
                <Ionicons name="close" size={24} color={theme.colors.textLight} />
              </Pressable>
            </View>
          ))}

          {taxEmail.length < 5 && (
            <Button
              size="sm"
              type="outline"
              onPress={() => setTaxEmail([...taxEmail, ''])}
              containerStyle={{alignSelf: 'flex-start'}}
              titleStyle={{color: theme.colors.text, fontSize: theme.typography.sm}}
              buttonStyle={{backgroundColor: theme.colors.white, borderColor: theme.colors.text, padding: 2, paddingLeft: 0}}
              icon={{type: 'ionicon', name: 'add', size: 16, color: theme.colors.text}}>
              Thêm Email ({taxEmail.length}/5)
            </Button>
          )}
        </View>

        <View style={{marginTop: theme.spacing.m}}>
          <Input
            value={taxId}
            onChangeText={setTaxId}
            label="Mã số thuế"
            placeholder="Nhập mã số thuế"
            labelStyle={styles.inputLabel}
            inputStyle={styles.input}
            containerStyle={{paddingHorizontal: 0}}
            errorMessage={errors.tax_id}
          />
        </View>
        <View style={{marginTop: theme.spacing.l}}>
          <Button title="Lưu" size="sm" onPress={handleSubmit} loading={submitLoading} disabled={submitLoading} />
        </View>
      </ScrollView>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: theme.spacing.m,
  },
  taxTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    flexWrap: 'wrap',
    backgroundColor: theme.colors.white,
  },
  checkboxContainer: {
    marginHorizontal: 0,
    margin: 0,
    padding: 0,
  },
  checkboxText: {
    fontSize: theme.typography.base,
    fontWeight: '400',
    color: theme.colors.text,
  },
  companyNameContainer: {
    marginTop: theme.spacing.m,
  },
  inputContainer: {
    marginHorizontal: 0,
  },
  inputLabel: {
    fontSize: theme.typography.base,
    fontWeight: '400',
    color: theme.colors.text,
    marginBottom: 4,
  },
  input: {
    fontSize: theme.typography.base,
    minHeight: 34,
  },
});

export default TaxInformationForm;
