import React, {useCallback, useMemo} from 'react';
import {Image, Pressable, StyleSheet, View} from 'react-native';
import {handleImageDomain} from '~utils/helpers/image';
import Order from '~utils/types/order';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {formatPriceToString} from '~utils/helpers/price';
import {useNavigation} from '~hooks';
import dayjs from 'dayjs';
import theme from '~utils/config/themes/theme';
import {getMarketPlaceLabel, getOrderStatusLabelColor} from '~utils/helpers/order';
import {Button, Text} from '@rneui/themed';

type OrderListItemProps = {
  order: Partial<Order>;
};

const OrderListItem: React.FC<OrderListItemProps> = props => {
  const navigation = useNavigation();

  const handlePress = useCallback(() => {
    navigation.push('OrderDetail', {orderId: props.order.id as number});
  }, [props.order.id]);

  const itemCount = useMemo(() => {
    return props.order.line_items?.length ?? 0 - 1;
  }, [props.order.id]);

  const marketPlace = useMemo(() => {
    try {
      return props.order.source_name?.split(':')[1] as any;
    } catch (error) {
      return '';
    }
  }, [props.order.id]);

  const handleRateOrder = useCallback(() => {
    navigation.push('OrderRate', {orderId: props.order.id!});
  }, [props.order.id]);

  const isShowRateOrder = useMemo(() => {
    if (props.order.shipment_status !== 'delivered' || props.order.rated_at) {
      return false;
    }
    const isExpired = dayjs(props.order.rated_at).isAfter(dayjs());

    if (isExpired) {
      return false;
    }

    return true;
  }, [props.order.rated_at, props.order.shipment_status, props.order.rate_expires_at]);

  const isShowViewRateDetail = useMemo(() => {
    return !!props.order.rated_at;
  }, [props.order.rated_at]);

  return (
    <View style={styles.container}>
      <Pressable onPress={handlePress} style={[styles.orderContainer, {opacity: props.order.status === 'cancelled' ? 0.7 : 1}]} accessible={false}>
        <View style={{flex: 1}}>
          <View style={{flexDirection: 'row'}}>
            <View style={{position: 'relative'}}>
              <Image
                style={{width: 70, height: 70, borderWidth: 1, borderColor: 'rgba(0,0,0,.07)', borderRadius: 8}}
                source={{
                  uri: handleImageDomain(props.order.line_items?.[0]?.image_src as string),
                }}
              />
              {itemCount > 1 && (
                <View style={{position: 'absolute', bottom: 0, right: 0}}>
                  <View style={{backgroundColor: theme.colors.primary, paddingHorizontal: theme.spacing.s, borderTopLeftRadius: 30, borderBottomRightRadius: 8}}>
                    <Text style={{fontSize: 11, color: theme.colors.white}}>+{itemCount - 1} sp</Text>
                  </View>
                </View>
              )}
            </View>

            <View style={{marginLeft: 8, flex: 1}}>
              <View style={{flexDirection: 'row'}}>
                <Text style={{fontWeight: '400', color: theme.colors.textLight, fontSize: theme.typography.base, marginBottom: 4}}>{props.order.name}</Text>

                {props.order.source_name === 'biolink' ? (
                  <View style={{marginLeft: theme.spacing.xs}}>
                    <Text style={{color: theme.colors.textLight, fontSize: theme.typography.base}}>(đơn từ Biolink)</Text>
                  </View>
                ) : null}
              </View>

              {props.order.customer?.id ? (
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <Ionicons name="person" size={12} color={theme.colors.textLight} />
                  <Text style={{marginLeft: 4, color: theme.colors.textLight, fontSize: theme.typography.base}}>{[props.order.customer?.first_name, props.order.customer?.last_name].join(' ')}</Text>
                </View>
              ) : null}

              {props.order.dropship_marketplace ? (
                <View>
                  <Text style={{color: theme.colors.textLight, fontSize: theme.typography.base}}>
                    {getMarketPlaceLabel(marketPlace)} - {props.order.source_identifier}
                  </Text>
                </View>
              ) : null}

              <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 4, marginBottom: 4}}>
                <Text style={[styles.price]}>{formatPriceToString(parseInt(props.order.total_price as string, 10))}</Text>
              </View>
            </View>
          </View>
          <View style={{flexDirection: 'row', justifyContent: 'space-between', marginTop: theme.spacing.s}}>
            <Text style={{fontSize: 14, color: 'rgba(0,0,0,.6)'}}>{dayjs(props.order.created_at).format('HH:mm')}</Text>
            <Text style={{fontSize: 14, fontWeight: 'bold', color: props.order.escrow_release_at ? theme.colors.primary : getOrderStatusLabelColor(props.order.status_label as string)}}>
              {props.order.escrow_release_at ? 'Đã đối soát' : props.order.status_label}
            </Text>
          </View>
        </View>

        <View style={{marginLeft: 'auto', alignSelf: 'center', paddingLeft: 12}}>
          <Ionicons name="chevron-forward" size={24} color="rgba(0,0,0,.2)" />
        </View>
      </Pressable>

      {isShowRateOrder && (
        <View style={styles.rateContainer}>
          <Text style={{fontSize: theme.typography.sm, color: theme.colors.pending}}>Đánh giá trước {dayjs(props.order.rate_expires_at).format('DD/MM/YYYY HH:mm')}</Text>
          <Button
            size="md"
            onPress={handleRateOrder}
            buttonStyle={{paddingVertical: theme.spacing.s}}
            containerStyle={{marginLeft: 'auto', marginHorizontal: theme.spacing.m, marginVertical: theme.spacing.s}}
            titleStyle={{fontSize: theme.typography.base, fontWeight: '600'}}>
            Đánh giá
          </Button>
        </View>
      )}
      {isShowViewRateDetail && (
        <View style={styles.rateContainer}>
          <View />
          <Button
            size="md"
            type="outline"
            onPress={() => navigation.navigate('RateDetail', {actionId: props.order.id?.toString()!})}
            buttonStyle={{paddingVertical: theme.spacing.s}}
            containerStyle={{marginHorizontal: theme.spacing.m, marginVertical: theme.spacing.s}}
            titleStyle={{fontSize: theme.typography.base, fontWeight: '600'}}>
            Xem đánh giá
          </Button>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,.07)',
    borderRadius: 12,
    marginBottom: 8,
  },
  orderContainer: {
    backgroundColor: '#fff',
    paddingHorizontal: 8,
    paddingVertical: 8,
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,.07)',
    borderRadius: 12,
  },
  text: {
    fontSize: 16,
  },
  price: {
    fontSize: theme.typography.md,
    fontWeight: '500',
    textAlign: 'right',
  },
  rateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: theme.spacing.s,
    // alignSelf: 'flex-end',
    backgroundColor: theme.colors.white,
  },
});

export default OrderListItem;
