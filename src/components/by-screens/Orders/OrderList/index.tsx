import {FlashList, ListRenderItemInfo} from '@shopify/flash-list';
import dayjs from 'dayjs';
import React, {useCallback, useMemo} from 'react';
import {useState} from 'react';
import {ActivityIndicator, RefreshControl, StyleSheet, Text, View} from 'react-native';
import useRefreshOnFocus from '~hooks/useRefreshOnFocus';
import {OrdersRequestQuery, useGetOrdersInfinite} from '~utils/api/order';
import theme from '~utils/config/themes/theme';
import Order from '~utils/types/order';
import OrderListItem from '../OrderListItem';

type OrderListProps = {
  query: OrdersRequestQuery;
  index?: number;
};

const OrderList = React.forwardRef<FlashList<Order>[], OrderListProps>((props, ref) => {
  const [isRefreshing, setRefreshing] = useState(false);
  const {data, isLoading, fetchNextPage, refetch, isFetchingNextPage} = useGetOrdersInfinite(props.query);

  useRefreshOnFocus(refetch);

  const dataFlat = useMemo(() => {
    return data?.pages.map(page => page.items).flat() ?? [];
  }, [data]);

  const renderItem = useCallback(
    (listItem: ListRenderItemInfo<Partial<Order>>) => {
      const previousItem = listItem.index > 0 ? dataFlat[listItem.index - 1] : undefined;
      const previousTime = previousItem ? dayjs(previousItem.created_at) : undefined;
      const currentTime = dayjs(listItem.item.created_at);
      const currentTimeString = currentTime.format('DD-MM-YYYY');
      const now = dayjs();

      let timeIndicator: React.ReactNode = null;

      if (listItem.index === 0) {
        if (currentTimeString === now.format('DD-MM-YYYY')) {
          timeIndicator = <Text style={styles.timeIndicator}>Hôm nay</Text>;
        } else {
          timeIndicator = <Text style={styles.timeIndicator}>{currentTimeString}</Text>;
        }
      } else {
        if (currentTimeString !== previousTime?.format('DD-MM-YYYY')) {
          timeIndicator = <Text style={styles.timeIndicator}>{currentTimeString}</Text>;
        }
      }
      return (
        <View style={{paddingHorizontal: 8}}>
          {timeIndicator}
          <OrderListItem order={listItem.item} />
        </View>
      );
    },
    [data],
  );

  const handleScrollToEnd = useCallback(() => {
    if (!isFetchingNextPage) {
      fetchNextPage();
    }
  }, [isFetchingNextPage]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  }, [refetch]);

  return (
    <FlashList
      ref={r => {
        if (ref) {
          if (typeof props.index !== 'undefined') {
            (ref as any).current[props.index] = r;
          }
        } else {
        }
      }}
      refreshControl={<RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />}
      contentContainerStyle={styles.container}
      ListEmptyComponent={<View style={{marginVertical: 40, alignItems: 'center'}}>{isLoading ? <ActivityIndicator /> : <Text style={styles.emptyText}>Không có đơn hàng nào</Text>}</View>}
      data={dataFlat}
      keyExtractor={item => String(item.id)}
      renderItem={renderItem}
      onEndReached={handleScrollToEnd}
      estimatedItemSize={166}
    />
  );
});

const styles = StyleSheet.create({
  container: {
    paddingBottom: 20,
    // flex: 1,
  },
  timeIndicator: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    // paddingTop: 16,
    fontSize: theme.typography.sm,
    color: theme.colors.textLight,
  },

  emptyText: {
    color: theme.colors.text,
  },
});

export default OrderList;
