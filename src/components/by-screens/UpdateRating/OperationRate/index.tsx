import {AirbnbRating, Text} from '@rneui/themed';
import React, {useCallback} from 'react';
import {Platform, StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';
// @ts-ignore
import AirBnBStar from '~assets/airbnbstar.png';
import {RateStoreType} from '~hooks/store/useRateStore';

type OperationRateProps = {
  sellerService: number;
  deliveryService: number;
  onChange: (field: keyof RateStoreType, value: any) => void;
};

const OperationRate: React.FC<OperationRateProps> = props => {
  const handleFinishRateSupplierService = (rate: number) => {
    props.onChange('seller_service', rate);
  };

  const handleFinishRateDeliveryService = (rate: number) => {
    props.onChange('delivery_service', rate);
  };

  return (
    <View style={styles.container}>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>
        <Text
          style={{
            fontSize: theme.typography.base,
          }}>
          Dịch vụ nhà cung cấp
        </Text>
        <AirbnbRating
          count={5}
          reviews={['Rất tệ', 'Tệ', 'Bình thường', 'Tốt', 'Rất tốt']}
          defaultRating={props.sellerService}
          starImage={Platform.OS === 'web' ? AirBnBStar : undefined}
          onFinishRating={handleFinishRateSupplierService}
          size={22}
          showRating={false}
          reviewSize={theme.typography.lg1}
        />
      </View>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>
        <Text style={{fontSize: theme.typography.base}}>Dịch vụ vận chuyển</Text>
        <AirbnbRating
          count={5}
          reviews={['Rất tệ', 'Tệ', 'Bình thường', 'Tốt', 'Rất tốt']}
          defaultRating={props.deliveryService}
          starImage={Platform.OS === 'web' ? AirBnBStar : undefined}
          onFinishRating={handleFinishRateDeliveryService}
          size={22}
          showRating={false}
          reviewSize={theme.typography.lg1}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: theme.spacing.s,
    backgroundColor: theme.colors.white,
    paddingHorizontal: theme.spacing.m,
    paddingVertical: theme.spacing.xs,
  },
});

export default OperationRate;
