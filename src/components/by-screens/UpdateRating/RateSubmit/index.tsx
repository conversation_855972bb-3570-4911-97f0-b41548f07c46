import {Button, Switch, Text} from '@rneui/themed';
import {useQueryClient} from '@tanstack/react-query';
import React from 'react';
import {ActivityIndicator, Modal, Platform, StyleSheet, View} from 'react-native';
import {useToast} from 'react-native-toast-notifications';
import {ValidationError} from 'yup';
import {useNavigation} from '~hooks';
import useUpdateRateStore from '~hooks/store/useUpdateRateStore';
import {addRatings, uploadAttachment} from '~utils/api/review';
import theme from '~utils/config/themes/theme';
import reviewValidate from '~utils/validate/review';

const RateSubmit = () => {
  const navigation = useNavigation();
  const updateRateStore = useUpdateRateStore();
  const queryClient = useQueryClient();

  const [isLoading, setLoading] = React.useState(false);
  const toast = useToast();

  const toggleRateAsAnons = (value: boolean) => {
    updateRateStore.updateRateStore('anonymous', value);
  };

  const handleSubmit = async () => {
    let isValid = true;
    // validate
    const tobeValidate = {
      line_items: Object.keys(updateRateStore.line_items).map(lineItemId => ({
        quality_rate: updateRateStore.line_items[lineItemId].quality_rate,
        comment: updateRateStore.line_items[lineItemId].comment,
      })),
      delivery_service: updateRateStore.delivery_service,
      seller_service: updateRateStore.seller_service,
    };
    await reviewValidate.validate(tobeValidate).catch((err: ValidationError) => {
      isValid = false;
      err.errors.forEach(errString => {
        toast.show(errString, {type: 'danger', duration: 3000, placement: 'center'});
      });
    });

    if (!isValid) {
      return;
    }
    // upload attachments
    setLoading(true);

    try {
      const promises = Object.keys(updateRateStore.line_items).map(async lineItemId => {
        try {
          let photos: string[] = updateRateStore.line_items[lineItemId].photos.map(photo => photo._id);
          let videos: string[] = updateRateStore.line_items[lineItemId].videos.map(video => video._id);
          const uploadAttachments = updateRateStore.line_items[lineItemId]._attachments?.map(async att => {
            const res = await uploadAttachment(att, Platform.OS === 'ios');
            if (res) {
              if (res.media_type === 'image') {
                photos.push(res.attachment_id);
              }
              if (res.media_type === 'video') {
                videos.push(res.attachment_id);
              }
            }
          });
          await Promise.all(uploadAttachments ?? []);
          // updateRateStore.updateRateLineItem(lineItemId, 'photos', photos);
          // updateRateStore.updateRateLineItem(lineItemId, 'videos', videos);
          const lineItem = {
            variant_id: updateRateStore.line_items[lineItemId].variant_id,
            comment: updateRateStore.line_items[lineItemId].comment,
            quality_rate: updateRateStore.line_items[lineItemId].quality_rate,
            photos: photos,
            videos: videos,
          };
          return lineItem;
        } catch (error) {
          return updateRateStore.line_items[lineItemId];
        }
      });

      const lineItems = await Promise.all(promises);

      const newRating = {
        _id: updateRateStore._id,
        id: updateRateStore.id,
        created_at: updateRateStore.created_at,
        action_type: 'order',
        action_id: updateRateStore.action_id,
        anonymous: updateRateStore.anonymous,
        line_items: lineItems as any,
        delivery_service: updateRateStore.delivery_service,
        seller_service: updateRateStore.seller_service,
        comment: updateRateStore.comment,
      };

      await addRatings(newRating);
      queryClient.invalidateQueries(['rating', updateRateStore.action_id, updateRateStore.action_type]);

      toast.show('Đã cập nhật đánh giá', {placement: 'center'});
      navigation.goBack();
    } catch (error: any) {
      toast.show(error?.response?.data?.message ?? 'Có lỗi xảy ra', {placement: 'center', type: 'danger'});
    }
    setLoading(false);
  };

  return (
    <View style={styles.container}>
      <View style={styles.anonsContainer}>
        <Switch
          trackColor={{
            false: theme.colors.gray30,
            true: theme.colors.primary,
          }}
          thumbColor={updateRateStore.anonymous ? theme.colors.primary : theme.colors.gray50}
          value={updateRateStore.anonymous}
          onValueChange={toggleRateAsAnons}
        />
        <Text style={{marginLeft: theme.spacing.s}}>Đánh giá ẩn danh</Text>
      </View>
      <Button
        size="sm"
        onPress={handleSubmit}
        buttonStyle={{
          borderRadius: 4,
        }}
        titleStyle={{
          fontSize: theme.typography.base,
          fontWeight: '600',
        }}>
        Gửi đánh giá
      </Button>

      <Modal transparent visible={isLoading}>
        <View style={styles.loadingModalContent}>
          <ActivityIndicator size={'large'} color={theme.colors.white} />
          <Text style={{fontSize: theme.typography.base, marginTop: theme.spacing.m, color: theme.colors.white}}>Đang gửi đánh giá</Text>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    paddingHorizontal: theme.spacing.m,
    paddingVertical: theme.spacing.xs,
  },
  anonsContainer: {
    paddingVertical: theme.spacing.xs,
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingModalContent: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default RateSubmit;
