import {Text} from '@rneui/themed';
import React from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {QuickImage} from '~components/shared/QuickImage';
import {RateLineItemAttachment} from '~utils/api/review';
import theme from '~utils/config/themes/theme';
import {handleImageDomain} from '~utils/helpers/image';
import Ionicons from 'react-native-vector-icons/Ionicons';

type UploadedAttachmentProps = {
  attachment: RateLineItemAttachment;
  onRemove: (mediaType: 'image' | 'video', attachmentId: string) => void;
};

const UploadedAttachment: React.FC<UploadedAttachmentProps> = props => {
  return (
    <TouchableOpacity style={styles.attachmentContainer}>
      {props.attachment.media_type === 'video' ? (
        <>
          <QuickImage
            style={styles.attachment}
            source={{
              uri: handleImageDomain(props.attachment.video_thumb_url!),
            }}
          />
          <View style={styles.durationContainer}>
            <Ionicons name="videocam" size={14} color={theme.colors.white} />
            {props.attachment.duration ? (
              <Text style={styles.duration}>
                {Math.floor((props.attachment.duration % 3600) / 60)
                  .toString()
                  .padStart(2, '0')}
                :{Math.floor((props.attachment.duration % 3600) % 60)}
              </Text>
            ) : null}
          </View>
        </>
      ) : null}

      {props.attachment.media_type === 'image' ? (
        <>
          <QuickImage
            style={styles.attachment}
            source={{
              uri: handleImageDomain(props.attachment.url),
            }}
          />
        </>
      ) : null}

      <TouchableOpacity style={styles.closeContainer} onPress={() => props.onRemove(props.attachment.media_type, props.attachment._id)}>
        <Ionicons name="close" size={16} color={theme.colors.white} />
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  attachment: {
    width: 65,
    height: 65,
    borderWidth: 1,
    borderColor: theme.colors.gray20,
    borderRadius: 4,
  },
  attachmentContainer: {
    marginRight: theme.spacing.s,
    position: 'relative',
  },
  durationContainer: {
    paddingHorizontal: theme.spacing.xs,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    flexDirection: 'row',
  },
  duration: {
    fontSize: theme.typography.xs,
    color: theme.colors.white,
    marginLeft: theme.spacing.xs,
  },

  closeContainer: {
    zIndex: 10,
    position: 'absolute',
    top: 0,
    right: 0,
    width: 25,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,.8)',
  },
});

export default UploadedAttachment;
