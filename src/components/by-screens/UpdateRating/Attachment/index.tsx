import React from 'react';
import {Image, StyleSheet, TouchableOpacity, View} from 'react-native';
import {Asset} from 'react-native-image-picker';
import {Video} from '~components/shared/Video';

import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {Text} from '@rneui/themed';

type AttachmentProps = {
  asset: Asset;
  onRemove: (asset: Asset) => void;
};

const Attachment: React.FC<AttachmentProps> = props => {
  return (
    <View style={styles.container}>
      {props.asset.duration ? (
        <Video
          paused
          style={styles.attachment}
          source={{
            uri: props.asset.uri,
          }}
        />
      ) : (
        <Image
          style={styles.attachment}
          source={{
            uri: props.asset.uri,
          }}
        />
      )}

      <TouchableOpacity style={styles.closeContainer} onPress={() => props.onRemove(props.asset)}>
        <Ionicons name="close" size={16} color={theme.colors.white} />
      </TouchableOpacity>

      {props.asset.duration ? (
        <View style={styles.durationContainer}>
          <Text style={styles.duration}>
            {/*  */}
            {Math.floor((props.asset.duration % 3600) / 60)
              .toString()
              .padStart(2, '0')}
            :{Math.floor((props.asset.duration % 3600) % 60)}
          </Text>
        </View>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginRight: theme.spacing.s,
    marginBottom: theme.spacing.xs,
    borderWidth: 1,
    borderColor: theme.colors.gray20,
    position: 'relative',
  },
  attachment: {
    width: 64,
    height: 64,
    borderRadius: 4,
  },
  closeContainer: {
    zIndex: 10,
    position: 'absolute',
    top: 0,
    right: 0,
    width: 25,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,.8)',
  },
  durationContainer: {
    backgroundColor: 'rgba(0,0,0,.8)',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingLeft: theme.spacing.xs,
  },
  duration: {
    color: theme.colors.white,
    fontSize: theme.typography.sm,
  },
});

export default Attachment;
