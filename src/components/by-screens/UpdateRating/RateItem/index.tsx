import {AirbnbRating, Input, Text} from '@rneui/themed';
import React, {useCallback, useState} from 'react';
import {Image, Platform, StyleSheet, TouchableOpacity, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import {handleImageDomain} from '~utils/helpers/image';
// @ts-ignore
import AirBnBStar from '~assets/airbnbstar.png';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {Asset, launchImageLibrary} from 'react-native-image-picker';
import Attachment from '../Attachment';
import {useDebounce, useUpdateEffect} from '~hooks';
import {RateOrderLineItem} from '~hooks/store/useUpdateRateStore';
import UploadedAttachment from '../UploadedAttachment';

type RateItemProps = {
  lineItem: RateOrderLineItem;
  onChange: (variantId: string, field: keyof RateOrderLineItem, value: any) => void;
};

const RateItem: React.FC<RateItemProps> = props => {
  const [rating, setRating] = useState(0);
  const [reviewText, setReviewText] = useState(props.lineItem.comment);
  const reviewTextDebounce = useDebounce(reviewText, 200);
  const [selectedAttachments, setSelectedAttachments] = useState<Asset[]>([]);

  useUpdateEffect(() => {
    props.onChange(props.lineItem.variant_id, '_attachments', selectedAttachments);
  }, [selectedAttachments]);

  useUpdateEffect(() => {
    props.onChange(props.lineItem.variant_id, 'comment', reviewTextDebounce);
  }, [reviewTextDebounce]);

  const handlePress = useCallback(async () => {
    const result = await launchImageLibrary({
      mediaType: Platform.OS === 'web' ? 'photo' : 'mixed',
      selectionLimit: 6 - selectedAttachments.length,
      maxWidth: 2048,
      quality: Platform.OS === 'ios' ? 1 : 0.9, // ios will compress later
    });
    if (result.assets && result.assets.length) {
      setSelectedAttachments([...selectedAttachments, ...result.assets]);
    }
  }, [selectedAttachments]);

  const onRemoveAttachments = useCallback((asset: Asset) => {
    setSelectedAttachments(atts => atts.filter(att => att.fileName !== asset.fileName));
  }, []);

  const handleFinishRating = (rating: number) => {
    setRating(rating);
    props.onChange(props.lineItem.variant_id, 'quality_rate', rating);
  };

  const handleRemoveUploadedAttachment = (mediaType: 'image' | 'video', attachmentId: string) => {
    props.onChange(
      props.lineItem.variant_id,
      mediaType === 'image' ? 'photos' : 'videos',
      props.lineItem[mediaType === 'image' ? 'photos' : 'videos'].filter(attachment => attachment._id !== attachmentId),
    );
  };
  return (
    <>
      <View style={styles.container}>
        <View style={{paddingVertical: theme.spacing.s, flexDirection: 'row', borderBottomWidth: 1, borderBottomColor: theme.colors.gray20, marginBottom: theme.spacing.m}}>
          <Image
            source={{
              uri: handleImageDomain(props.lineItem.image_src!),
            }}
            style={{width: 50, height: 50, borderRadius: theme.spacing.xs, marginRight: theme.spacing.s}}
          />
          <View style={{flex: 1}}>
            <Text style={{fontSize: theme.typography.base}} numberOfLines={2}>
              {props.lineItem.title}
            </Text>
            <Text style={{fontSize: theme.typography.base}}>{['default', 'default title'].includes(props.lineItem.variant_title!?.toLowerCase()) ? '' : props.lineItem.variant_title}</Text>
          </View>
        </View>

        <AirbnbRating
          count={5}
          starContainerStyle={{width: 180, justifyContent: 'space-between'}}
          reviews={['Rất tệ', 'Tệ', 'Bình thường', 'Tốt', 'Rất tốt']}
          defaultRating={props.lineItem.quality_rate}
          starImage={Platform.OS === 'web' ? AirBnBStar : undefined}
          onFinishRating={handleFinishRating}
          size={25}
          showRating={false}
          // reviewColor={getSelectedColor(rating)}
          // selectedColor={getSelectedColor(rating)}
          reviewSize={theme.typography.lg1}
        />
        <Text
          style={{
            textAlign: 'center',
            marginTop: theme.spacing.m,
            fontSize: theme.typography.sm,
            color: theme.colors.textLight,
          }}>
          Đánh giá sản phẩm này
        </Text>
        <View style={{marginTop: theme.spacing.m}}>
          <Input
            label="Viết đánh giá"
            labelStyle={{color: theme.colors.text, fontSize: theme.typography.base}}
            value={reviewText}
            onChangeText={setReviewText}
            inputContainerStyle={{borderBottomWidth: 1, paddingVertical: 0, borderRadius: 8}}
            containerStyle={{paddingHorizontal: 0}}
            placeholder="Ví dụ: khách khen, nên bán nha!"
            numberOfLines={5}
            style={{
              textAlignVertical: 'top',
            }}
            multiline
            inputStyle={{fontSize: theme.typography.md, minHeight: 60}}
          />
        </View>
        <View style={styles.attachmentsContainer}>
          {selectedAttachments.map((attachment, index) => (
            <Attachment key={attachment.fileName + ` ${index}`} asset={attachment} onRemove={onRemoveAttachments} />
          ))}

          {props.lineItem.videos.map(video => {
            return <UploadedAttachment attachment={video} key={video._id} onRemove={handleRemoveUploadedAttachment} />;
          })}
          {props.lineItem.photos.map(photo => {
            return <UploadedAttachment attachment={photo} key={photo._id} onRemove={handleRemoveUploadedAttachment} />;
          })}

          <TouchableOpacity
            onPress={handlePress}
            style={{
              width: 65,
              height: 65,
              backgroundColor: theme.colors.gray20,
              justifyContent: 'center',
              alignItems: 'center',
              borderRadius: 4,
            }}>
            <Ionicons name="camera-outline" size={30} color={theme.colors.text} />
            <Text
              style={{
                color: theme.colors.textLight,
                fontSize: theme.typography.sm,
                fontWeight: '600',
                textAlign: 'center',
                marginTop: theme.spacing.xs,
              }}>
              Chọn
            </Text>
          </TouchableOpacity>
        </View>
        <Text
          style={{
            color: theme.colors.textLight,
            fontSize: theme.typography.sm,
            marginTop: theme.spacing.xs,
          }}>
          Nên đính kèm ảnh sản phẩm thật, ảnh chụp màn hình feedback của khách hàng
        </Text>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: theme.spacing.m,
    backgroundColor: theme.colors.white,
    paddingHorizontal: theme.spacing.m,
    paddingVertical: theme.spacing.xs,
  },
  attachmentsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
});

export default RateItem;
