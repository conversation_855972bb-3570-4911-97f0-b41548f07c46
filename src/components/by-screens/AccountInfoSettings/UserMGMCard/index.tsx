import React, {useCallback, useRef, useState} from 'react';
import {ImageBackground, StyleSheet, View} from 'react-native';
import UserAvatar from '../UserAvatar';
import {useGetCurrentUser, useGetUserDropshipProfile} from '~utils/api/auth';
import theme from '~utils/config/themes/theme';
import {Button, CheckBox, Text} from '@rneui/themed';
import {captureRef} from 'react-native-view-shot';
import {savePicture} from '~utils/helpers/downloadImage';
import {useToast} from 'react-native-toast-notifications';
import Share from 'react-native-share';
import googleAnalytics from '~utils/helpers/analytics';
import {checkAndRequestSavePhotoPermission} from '~utils/helpers/downloadImagePermissions';

type UserMGMCardProps = {};

const UserMGMCard: React.FC<UserMGMCardProps> = props => {
  const {data} = useGetCurrentUser();
  const {data: dropshipProfile} = useGetUserDropshipProfile();
  const toast = useToast();
  const ref = useRef<any>();
  const [isHideInviteText, setIsHideInviteText] = useState(false);

  const handleToggleChecked = useCallback(() => {
    setIsHideInviteText(prev => !prev);
  }, []);

  const handleDownload = useCallback(async () => {
    googleAnalytics.logEvent('download_dropship_profile', {
      hide_invite_text: isHideInviteText,
    });

    const canContinue = await checkAndRequestSavePhotoPermission();

    if (!canContinue) {
      return;
    }

    captureRef(ref, {
      format: 'jpg',
      quality: 1,
    }).then(
      uri => {
        savePicture(uri).then(() => {
          toast.show('Đã lưu về máy');
        });
      },
      error => console.error('Oops, snapshot failed', error),
    );
  }, [ref, isHideInviteText]);

  const handleShare = useCallback(() => {
    googleAnalytics.logEvent('share_dropship_profile', {
      hide_invite_text: isHideInviteText,
    });

    captureRef(ref, {
      format: 'jpg',
      quality: 1,
    }).then(
      uri => {
        Share.open({
          url: uri,
        });
      },
      error => console.error('Oops, snapshot failed', error),
    );
  }, [ref, isHideInviteText]);

  return (
    <View style={styles.container}>
      <ImageBackground
        source={require('~assets/confetti_bg.png')}
        style={styles.imageContainer}
        ref={ref}
        imageStyle={{
          objectFit: 'fill',
        }}>
        {/* <View > */}
        <View style={{position: 'absolute', top: 0, left: 0, right: 0, display: 'flex', flexDirection: 'row', justifyContent: 'center', overflow: 'hidden'}}>
          <View style={{backgroundColor: theme.colors.primary, borderRadius: 12, paddingHorizontal: 8, paddingTop: 20, marginTop: -19, paddingBottom: 1}}>
            <Text style={{fontSize: theme.typography.base, color: theme.colors.white, fontWeight: '600'}}>Hồ sơ TTS Dropship</Text>
          </View>
        </View>
        <UserAvatar borderColor={dropshipProfile?.card_border_color} avatar={data?.avatar as string} />
        <Text style={{alignSelf: 'center', fontSize: theme.typography.lg1, color: theme.colors.text, fontWeight: 'bold', marginTop: theme.spacing.s}}>{data?.full_name}</Text>
        <Text style={{alignSelf: 'center', fontSize: theme.typography.lg, color: theme.colors.text, marginTop: theme.spacing.xs}}>{dropshipProfile?.title}</Text>
        {dropshipProfile && dropshipProfile.rank < 300 && (
          <Text style={{alignSelf: 'center', fontSize: theme.typography.base, color: theme.colors.textLight, marginTop: theme.spacing.xs}}>Xếp hạng: {dropshipProfile.rank}</Text>
        )}

        <View style={{alignSelf: 'center', alignItems: 'center', marginTop: theme.spacing.m}}>
          <Text>Doanh số</Text>
          <Text style={{fontSize: theme.typography.lg2, color: theme.colors.primary, fontWeight: 'bold'}}>{dropshipProfile?.sales?.toLocaleString('vi-VN')}</Text>

          <Text style={{marginTop: theme.spacing.s}}>Đơn hàng</Text>
          <Text style={{fontSize: theme.typography.lg2, color: theme.colors.primary, fontWeight: 'bold'}}>{dropshipProfile?.order_count?.toLocaleString('vi-VN')}</Text>
        </View>
        {/* </View> */}

        <View style={{marginTop: theme.spacing.l, paddingTop: theme.spacing.s, borderTopWidth: 1, borderTopColor: theme.colors.gray30, opacity: isHideInviteText ? 0 : 1}}>
          {/* @ts-ignore */}
          <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight}}>
            Tải app <Text style={{fontSize: theme.typography.base, color: theme.colors.text, fontWeight: 'bold'}}>TTS Dropship</Text> để kiếm tiền cùng {data?.first_name}. Nhập mã {data?.ref_code} để
            nhận 50K.
          </Text>
        </View>
      </ImageBackground>

      <View>
        <View>
          <CheckBox
            containerStyle={{backgroundColor: 'transparent', marginHorizontal: 0, paddingHorizontal: 0}}
            wrapperStyle={{marginHorizontal: 0, paddingHorizontal: 0}}
            style={{marginHorizontal: 0, paddingHorizontal: 0}}
            checked={isHideInviteText}
            title={'Ẩn lời mời Tải App'}
            textStyle={{color: theme.colors.white, fontSize: theme.typography.base, fontWeight: '500'}}
            size={20}
            iconType="ionicon"
            uncheckedIcon="square-outline"
            checkedIcon="checkbox-outline"
            checkedColor={theme.colors.white}
            uncheckedColor="white"
            onPress={handleToggleChecked}
          />
        </View>
      </View>

      <View style={{alignSelf: 'center', flexDirection: 'row'}}>
        <Button
          icon={{
            type: 'ionicon',
            name: 'download-outline',
            size: 16,
            color: theme.colors.text,
          }}
          titleStyle={{color: theme.colors.text, fontSize: theme.typography.base}}
          size="sm"
          buttonStyle={{backgroundColor: theme.colors.white, borderRadius: 8, padding: 6}}
          containerStyle={{marginTop: theme.spacing.m}}
          onPress={handleDownload}>
          Tải xuống
        </Button>
        <Button
          icon={{
            type: 'ionicon',
            name: 'share-social',
            size: 16,
            color: theme.colors.text,
          }}
          titleStyle={{color: theme.colors.text, fontSize: theme.typography.base}}
          size="sm"
          buttonStyle={{backgroundColor: theme.colors.white, borderRadius: 8, padding: 6}}
          containerStyle={{marginTop: theme.spacing.m, marginLeft: theme.spacing.m}}
          onPress={handleShare}>
          Chia sẻ
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.primary,
    padding: 20,
    marginTop: theme.spacing.m,
    borderRadius: 12,
  },
  imageContainer: {
    padding: theme.spacing.m,
    backgroundColor: 'white',
    borderRadius: 12,
    position: 'relative',
    paddingTop: theme.spacing.xl,
    borderWidth: 1,
    borderColor: theme.colors.primary,
    borderStyle: 'dotted',
  },
});

export default UserMGMCard;
