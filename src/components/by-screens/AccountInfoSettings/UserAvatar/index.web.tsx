import React, {useCallback} from 'react';
import {View} from 'react-native';
import {handleImageDomain} from '~utils/helpers/image';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {UserAvatarProps} from '.';
import {useUpdateUserInfoMutation} from '~utils/api/auth';
import {uploadImage} from '~utils/api/image';
import {Image} from '@rneui/themed';

const UserAvatar: React.FC<UserAvatarProps> = props => {
  const updateUserInfoMutation = useUpdateUserInfoMutation();

  const handleChangeAvatar = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    try {
      const file = e?.target?.files?.[0];
      if (file) {
        const uploadedImage = await uploadImage(file as any);
        if (uploadedImage) {
          await updateUserInfoMutation.mutateAsync({
            avatar: handleImageDomain(uploadedImage.src),
          });
        }
      }
    } catch (error) {}
  }, []);

  return (
    <label style={{alignSelf: 'center', marginTop: 30, position: 'relative', overflow: 'hidden', borderRadius: 100}} htmlFor="logo-input">
      <Image
        style={{width: 150, height: 150}}
        source={{
          uri: handleImageDomain(props.avatar),
        }}
      />
      <input id="logo-input" type="file" accept="image/*" style={{display: 'none'}} onChange={handleChangeAvatar} />
      <View style={{position: 'absolute', bottom: 0, backgroundColor: 'rgba(0,0,0,.8)', width: 150, height: 40, alignItems: 'center', justifyContent: 'center'}}>
        <Ionicons name="camera-outline" color={'#fff'} size={20} />
      </View>
    </label>
  );
};

export default UserAvatar;
