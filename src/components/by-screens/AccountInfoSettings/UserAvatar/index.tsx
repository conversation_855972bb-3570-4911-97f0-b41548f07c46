import React, {useCallback} from 'react';
import {Alert, Image, Pressable, View} from 'react-native';
import ImageCropPicker from 'react-native-image-crop-picker';
import {useUpdateUserInfoMutation} from '~utils/api/auth';
import {uploadImage} from '~utils/api/image';
import {handleImageDomain, imageToFile} from '~utils/helpers/image';

export type UserAvatarProps = {
  avatar: string;
  borderColor?: string;
};

const UserAvatar: React.FC<UserAvatarProps> = props => {
  const updateUserInfoMutation = useUpdateUserInfoMutation();

  const handleChangeAvatar = useCallback(async () => {
    try {
      const croppedImage = await ImageCropPicker.openPicker({
        cropping: true,
        width: 500,
        height: 500,
        cropperCircleOverlay: true,
        forceJpg: true,
      });
      const uploadedImage = await uploadImage(imageToFile(croppedImage));
      if (uploadedImage) {
        await updateUserInfoMutation.mutateAsync({
          avatar: handleImageDomain(uploadedImage.src),
        });
      }
    } catch (error: any) {
      if (error?.message === 'User did not grant library permission.') {
        Alert.alert('Không thể truy cập album ảnh', 'Bạn chưa cấp quyền truy cập vào hình ảnh, vui lòng cấp quyền ở phần Cài đặt và thử lại');
      }
    }
  }, []);

  const handleImagePress = useCallback(() => {
    return handleChangeAvatar();
  }, [props.avatar]);

  return (
    <View style={[{alignSelf: 'center', position: 'relative', overflow: 'hidden', borderRadius: 200}, props.borderColor ? {borderWidth: 2, borderColor: props.borderColor} : {}]}>
      <Pressable onPress={handleImagePress}>
        <Image style={{width: 80, height: 80}} source={{uri: handleImageDomain(props.avatar)}} />
      </Pressable>
    </View>
  );
};

export default UserAvatar;
