import {Text} from '@rneui/themed';
import React from 'react';
import {Pressable, StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';

type SettingItemProps = {
  title?: string;
  description?: string | React.ReactNode;
  onPress?: () => void;
  destructive?: boolean;
  rounded?: boolean;
};

const SettingItem: React.FC<SettingItemProps> = props => {
  return (
    <Pressable style={[styles.container, props.rounded && {borderRadius: 12}]} onPress={props.onPress}>
      <View>
        <Text style={[styles.title, props.destructive && {color: theme.colors.red}]}>{props.title}</Text>
        <Text style={styles.description}>{props.description}</Text>
      </View>
      <Ionicons name="chevron-forward" size={20} style={{marginLeft: 'auto'}} color={theme.colors.textLight} />
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    paddingHorizontal: 12,
    paddingVertical: 16,
    ...theme.globalStyles.flexRow,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray20,
  },
  title: {
    fontSize: theme.typography.md,
    fontWeight: '500',
    marginBottom: theme.spacing.xs,
  },
  description: {
    color: theme.colors.textLight,
    fontSize: theme.typography.base,
  },
});

export default SettingItem;
