import {CheckBox} from '@rneui/base';
import React from 'react';
import {Pressable, StyleSheet, View} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import ShippingRateItem from '../ShippingRate';
import {ShippingRateSelectModalProps} from '.';
import {Dimensions} from 'react-native';
import ReactDOM from 'react-dom';
import {Text} from '@rneui/themed';

const screenHeight = Dimensions.get('screen').height;

const ShippingRateSelectModal: React.FC<ShippingRateSelectModalProps> = props => {
  if (!props.open) {
    return null;
  }

  return ReactDOM.createPortal(
    <div style={{position: 'fixed', top: 0, left: 0, right: 0, bottom: 0, display: 'flex', flexDirection: 'column', backgroundColor: 'rgba(0,0,0,.2)'}}>
      <Pressable style={{flex: 1}} onPress={props.onClose} />
      <View style={styles.bottomSheet}>
        <View style={styles.bottomSheetHeader}>
          <Text />
          <Text style={styles.bottomSheetTitle}>Chọn đơn vị vận chuyển</Text>
          <Ionicons name="close-circle" size={40} color={'rgba(0,0,0,.2)'} onPress={props.onClose} />
        </View>
        <View style={styles.bottomSheetContent}>
          {props.rates.map(rate => (
            <View style={{flexDirection: 'row', alignItems: 'center'}} key={rate.service_code}>
              <CheckBox
                size={35}
                onPress={() => props.onSelect(rate)}
                checked={props.selectedShippingRate?.service_code === rate.service_code}
                iconType="ionicon"
                uncheckedIcon="radio-button-off-outline"
                checkedIcon="checkmark-circle"
                checkedColor="#008060"
                containerStyle={{margin: 0, padding: 0, backgroundColor: 'transparent'}}
                wrapperStyle={{marginLeft: -10, marginTop: 10, padding: 0}}
              />
              <View style={{flex: 1}}>
                <ShippingRateItem arrowHidden shippingRate={rate} key={rate.service_code} onPress={() => props.onSelect(rate)} />
              </View>
            </View>
          ))}
        </View>
      </View>
    </div>,
    document.querySelector('body') as any,
  );
};

const styles = StyleSheet.create({
  bottomSheet: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  bottomSheetHeader: {
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,.1)',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  bottomSheetTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  bottomSheetContent: {
    paddingHorizontal: 8,
    paddingVertical: 20,
    minHeight: 300,
    maxHeight: screenHeight - 200,
  },
});

export default ShippingRateSelectModal;
