import {CheckBox} from '@rneui/base';
import React, {useCallback, useEffect, useMemo, useRef} from 'react';
import {ListRenderItemInfo, View} from 'react-native';
import {ShippingRate} from '~utils/types/order';
import {BottomSheetBackdrop, BottomSheetFlatList, BottomSheetModal} from '@gorhom/bottom-sheet';
import ShippingRateItem from '../ShippingRate';
import {useReducedMotion} from 'react-native-reanimated';

export type ShippingRateSelectModalProps = {
  rates: ShippingRate[];
  selectedShippingRate?: ShippingRate;
  onSelect: (newShippingRate: ShippingRate) => void;
  open: boolean;
  onClose: () => void;
};

const ShippingRateSelectModal: React.FC<ShippingRateSelectModalProps> = props => {
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  const renderBackdrop = useCallback((bsProps: any) => <BottomSheetBackdrop {...bsProps} disappearsOnIndex={-1} appearsOnIndex={1} />, []);
  const snapPoints = useMemo(() => ['50%', '95%'], []);
  const reducedMotion = useReducedMotion();

  useEffect(() => {
    if (props.open) {
      bottomSheetRef.current?.present();
    } else {
      bottomSheetRef.current?.close();
    }
  }, [props.open]);

  const renderItem = useCallback(
    (item: ListRenderItemInfo<ShippingRate>) => {
      return (
        <View style={{flexDirection: 'row', alignItems: 'center', paddingHorizontal: 12}}>
          <CheckBox
            size={28}
            onPress={() => props.onSelect(item.item)}
            checked={props.selectedShippingRate?.service_code === item.item.service_code}
            iconType="ionicon"
            uncheckedIcon="radio-button-off-outline"
            checkedIcon="checkmark-circle"
            checkedColor="#008060"
            containerStyle={{margin: 0, padding: 0, backgroundColor: 'transparent'}}
            wrapperStyle={{marginLeft: -10, marginTop: 10, padding: 0}}
          />
          <View style={{flex: 1}}>
            <ShippingRateItem arrowHidden shippingRate={item.item} key={item.item.service_code} onPress={() => props.onSelect(item.item)} />
          </View>
        </View>
      );
    },
    [props.selectedShippingRate],
  );

  return (
    <BottomSheetModal
      animateOnMount={!reducedMotion}
      backdropComponent={renderBackdrop}
      ref={bottomSheetRef}
      index={0}
      snapPoints={snapPoints}
      enablePanDownToClose
      onDismiss={props.onClose}
      enableDismissOnClose>
      <BottomSheetFlatList data={props.rates} renderItem={renderItem} />
    </BottomSheetModal>
  );
};

export default ShippingRateSelectModal;
