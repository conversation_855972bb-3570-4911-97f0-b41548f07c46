import {Text} from '@rneui/themed';
import React, {useMemo} from 'react';
import {StyleSheet, View} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Card from '~components/shared/Card';
import useCreateOrderStore from '~hooks/store/useCreateOrderStore';
import theme from '~utils/config/themes/theme';
import {formatPriceToString} from '~utils/helpers/price';

const OrderSummary = () => {
  const orderByShopId = useCreateOrderStore(state => state.order_by_shop_id);

  const orders = useMemo(() => {
    try {
      return Object.keys(orderByShopId).map(shopId => orderByShopId[shopId]);
    } catch (error) {
      return [];
    }
  }, [orderByShopId]);

  const ordersSummary = useMemo(() => {
    let totalLineItemsPrice = 0;
    let totalSupplierPrice = 0;
    let totalShippingLinePrices = 0;
    let totalShippingLineDiscount = 0;
    let totalCommissionPromo = 0;
    let dropshipperShippingPrice = 0;
    for (let i = 0; i < orders.length; i++) {
      for (let j = 0; j < orders[i].line_items.length; j++) {
        totalLineItemsPrice += orders[i].line_items[j].price * orders[i].line_items[j].quantity;
        totalSupplierPrice += orders[i].line_items[j].dropship_price * orders[i].line_items[j].quantity;
      }
      totalShippingLinePrices +=
        orders[i].shipping_lines?.reduce((a1, c1) => {
          return a1 + c1.price;
        }, 0) ?? 0;
      totalShippingLineDiscount +=
        orders[i].price_rules?.reduce((a1, c1) => {
          if (c1.target_type === 'shipping_line') {
            let shippingPrice = orders[i].shipping_lines?.reduce((acc, cur) => acc + cur.price, 0) ?? 0;
            let discountPrice = Math.min(shippingPrice, Math.abs(c1.value));
            return a1 + -discountPrice;
          }
          return a1;
        }, 0) ?? 0;
      totalCommissionPromo +=
        orders[i].price_rules?.reduce((a1, c1) => {
          if (c1.target_type === 'commission') {
            if (c1.value_type === 'percentage') {
              const orderSubtotal = orders[i].line_items?.reduce((a2, c2) => a2 + c2.price * c2.quantity, 0) ?? 0;
              return a1 + (c1.value * orderSubtotal) / 100;
            }
            if (c1.value_type === 'fixed_amount') {
              return a1 + c1.value;
            }

            return a1;
          }
          return a1;
        }, 0) ?? 0;
      dropshipperShippingPrice += orders[i].shipping_lines?.reduce((acc, cur) => acc + (cur.__pay_amount || 0), 0) ?? 0;
    }

    return {
      totalLineItemsPrice,
      totalSupplierPrice,
      totalShippingLinePrices,
      totalShippingLineDiscount,
      totalCommissionPromo,
      dropshipperShippingPrice,
    };
  }, [orders]);

  const totalPrice = useMemo(() => {
    return ordersSummary.totalLineItemsPrice + ordersSummary.totalShippingLinePrices + ordersSummary.totalShippingLineDiscount - ordersSummary.dropshipperShippingPrice;
  }, [ordersSummary]);

  const totalProfit = useMemo(() => {
    return ordersSummary.totalLineItemsPrice - ordersSummary.totalSupplierPrice + ordersSummary.totalCommissionPromo - ordersSummary.dropshipperShippingPrice;
  }, [ordersSummary]);

  return (
    <>
      <Card
        title={
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Ionicons name="receipt-outline" size={18} style={{marginRight: 8}} color={theme.colors.text} />
            <Text style={{fontSize: theme.typography.lg, fontWeight: '500'}}>Chi tiết thanh toán</Text>
          </View>
        }>
        <View style={{paddingHorizontal: 8}}>
          <Text style={{color: theme.colors.textLight}}>Chi tiết thanh toán khách của bạn phải trả</Text>
          <View style={[{marginTop: theme.spacing.m}, theme.globalStyles.flexRow, {justifyContent: 'space-between'}]}>
            <Text style={styles.left}>Tổng tiền hàng</Text>
            <Text style={styles.right}>{formatPriceToString(ordersSummary.totalLineItemsPrice)}</Text>
          </View>
          <View style={[{marginTop: theme.spacing.s}, theme.globalStyles.flexRow, {justifyContent: 'space-between'}]}>
            <Text style={styles.left}>Tổng phí vận chuyển</Text>
            <Text style={styles.right}>{formatPriceToString(ordersSummary.totalShippingLinePrices)}</Text>
          </View>
          {ordersSummary.totalShippingLineDiscount !== 0 && (
            <View style={[{marginTop: theme.spacing.s}, theme.globalStyles.flexRow, {justifyContent: 'space-between'}]}>
              <Text style={styles.left}>Tổng khuyến mại</Text>
              <Text style={styles.right}>{formatPriceToString(ordersSummary.totalShippingLineDiscount)}</Text>
            </View>
          )}
          {ordersSummary.dropshipperShippingPrice > 0 && (
            <View style={[{marginTop: theme.spacing.s}, theme.globalStyles.flexRow, {justifyContent: 'space-between'}]}>
              <Text style={styles.left}>CTV tài trợ phí vận chuyển</Text>
              <Text style={[styles.right]}>{formatPriceToString(-ordersSummary.dropshipperShippingPrice)}</Text>
            </View>
          )}
          <View style={[{marginTop: theme.spacing.s}, theme.globalStyles.flexRow, {justifyContent: 'space-between'}]}>
            <Text style={[styles.left, {color: theme.colors.text}]}>Tổng thanh toán</Text>
            <Text style={[styles.right, {color: theme.colors.text}]}>{formatPriceToString(totalPrice)}</Text>
          </View>
        </View>
      </Card>

      <Card
        title={
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Ionicons name="receipt-outline" size={18} style={{marginRight: 8}} color={theme.colors.text} />
            <Text style={{fontSize: theme.typography.lg, fontWeight: '500'}}>Chi tiết hoa hồng</Text>
          </View>
        }>
        <View style={{paddingHorizontal: 8}}>
          {/* <Text style={{color: theme.colors.textLight}}>Chi tiết thanh toán khách của bạn phải trả</Text> */}
          <View style={[theme.globalStyles.flexRow, {justifyContent: 'space-between'}]}>
            <Text style={styles.left}>Tổng giá bán</Text>
            <Text style={styles.right}>{formatPriceToString(ordersSummary.totalLineItemsPrice)}</Text>
          </View>
          <View style={[{marginTop: theme.spacing.s}, theme.globalStyles.flexRow, {justifyContent: 'space-between'}]}>
            <Text style={styles.left}>Tổng giá nhà cung cấp</Text>
            <Text style={styles.right}>{formatPriceToString(ordersSummary.totalSupplierPrice)}</Text>
          </View>
          <View style={[{marginTop: theme.spacing.s}, theme.globalStyles.flexRow, {justifyContent: 'space-between'}]}>
            <Text style={styles.left}>Lợi nhuận bán hàng</Text>
            <Text style={styles.right}>{formatPriceToString(ordersSummary.totalLineItemsPrice - ordersSummary.totalSupplierPrice)}</Text>
          </View>

          {ordersSummary.dropshipperShippingPrice > 0 && (
            <View style={[{marginTop: theme.spacing.s}, theme.globalStyles.flexRow, {justifyContent: 'space-between'}]}>
              <Text style={styles.left}>CTV tài trợ phí vận chuyển</Text>
              <Text style={[styles.right, {color: theme.colors.red}]}>{formatPriceToString(-ordersSummary.dropshipperShippingPrice)}</Text>
            </View>
          )}

          {ordersSummary.totalCommissionPromo !== 0 && (
            <View style={[{marginTop: theme.spacing.s}, theme.globalStyles.flexRow, {justifyContent: 'space-between'}]}>
              <Text style={styles.left}>Tổng thưởng</Text>
              <Text style={[styles.right, {color: theme.colors.primary}]}>{formatPriceToString(ordersSummary.totalCommissionPromo)}</Text>
            </View>
          )}
          <View style={[{marginTop: theme.spacing.s}, theme.globalStyles.flexRow, {justifyContent: 'space-between'}]}>
            <Text style={[styles.left, {color: theme.colors.text}]}>Tổng lợi nhuận</Text>
            <Text style={[styles.right, {color: theme.colors.primary, fontWeight: 'bold'}]}>{formatPriceToString(totalProfit)}</Text>
          </View>
        </View>
      </Card>
    </>
  );
};

const styles = StyleSheet.create({
  container: {},
  left: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
  },
  right: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
  },
});
export default OrderSummary;
