import React, {useCallback} from 'react';
import {Pressable, View} from 'react-native';
import Card from '~components/shared/Card';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '~hooks';
import {useGetCustomersInfinite} from '~utils/api/customer';
import useCreateOrderStore from '~hooks/store/useCreateOrderStore';
import {Text, Button} from '@rneui/themed';
import theme from '~utils/config/themes/theme';
import {formatPhoneString} from '~utils/helpers/common';

const CustomerSection = () => {
  const navigation = useNavigation();
  const {data, isLoading} = useGetCustomersInfinite({query: ''});
  const shippingAddress = useCreateOrderStore(state => state.shipping_address);
  const isFromOtherMarketPlace = useCreateOrderStore(state => state.is_from_other_marketplace);

  const handlePress = useCallback(() => {
    if (!isLoading && !data?.pages[0]?.total) {
      navigation.navigate('CreateCustomer', {isCreateOrder: true});
    } else {
      navigation.navigate('CustomerSelect');
    }
  }, [isLoading, data]);

  if (isFromOtherMarketPlace) {
    return null;
  }

  return (
    <Card
      title={
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <Ionicons name="location-outline" size={18} style={{marginRight: 4}} color={theme.colors.text} />
          <Text style={{fontSize: theme.typography.lg, fontWeight: '500'}}>Địa chỉ nhận hàng</Text>
        </View>
      }>
      {shippingAddress ? (
        <Pressable
          accessible={false}
          onPress={handlePress}
          style={{paddingHorizontal: 16, paddingVertical: 8, flexDirection: 'row', alignItems: 'center', backgroundColor: '#f0fff6', borderRadius: 12, marginTop: -theme.spacing.s}}>
          <View style={{flex: 1}}>
            <Text style={{fontSize: theme.typography.md}}>
              {[shippingAddress.first_name, shippingAddress.last_name].filter(Boolean).join(' ')} | {formatPhoneString(shippingAddress.phone)}
            </Text>
            <Text style={{fontSize: theme.typography.md, marginTop: 4}}>{[shippingAddress.address1, shippingAddress.ward, shippingAddress.province, shippingAddress.city].join(', ')}</Text>
          </View>
          <View>
            <Ionicons name="chevron-forward" size={20} color={theme.colors.textLight} accessibilityLabel="Chọn khách hàng khác" />
          </View>
        </Pressable>
      ) : null}
      {!shippingAddress && (
        <View style={{flexDirection: 'row', justifyContent: 'center', marginTop: theme.spacing.s}}>
          <Button
            onPress={handlePress}
            color="primary"
            icon={{
              type: 'ionicon',
              name: 'add',
              size: 24,
              color: theme.colors.white,
            }}
            size="sm"
            title="Chọn người nhận"
            buttonStyle={{
              paddingHorizontal: 20,
              borderRadius: 30,
            }}
            titleStyle={{
              fontWeight: '500',
            }}
          />
        </View>
      )}
    </Card>
  );
};

export default CustomerSection;
