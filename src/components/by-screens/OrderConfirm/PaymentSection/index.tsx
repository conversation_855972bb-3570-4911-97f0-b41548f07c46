import {CheckBox} from '@rneui/base';
import React, {useCallback, useEffect, useState} from 'react';
import Card from '~components/shared/Card';
import useCreateOrderStore from '~hooks/store/useCreateOrderStore';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {StyleSheet, View} from 'react-native';
import {Text} from '@rneui/themed';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '~hooks';
import googleAnalytics from '~utils/helpers/analytics';

export const paymentMethods = [
  {value: 'bank_transfer', label: 'Chuyển khoản/Quét mã QR', isPopular: true},
  {value: 'zalopay_app', label: 'Ví ZaloPay', isPopular: false},
  {value: 'zalopay_atm', label: 'Thẻ ATM/Internet Banking', isPopular: false},
  {value: 'zalopay_cc', label: 'Visa, Master, JCB', isPopular: false},
];

const PaymentSection = () => {
  const navigation = useNavigation();
  const setNewOrderState = useCreateOrderStore(state => state.setNewOrderState);
  const paymentMethod = useCreateOrderStore(state => state.payment_method);
  const isFromOtherMarketPlace = useCreateOrderStore(state => state.is_from_other_marketplace);

  useEffect(() => {
    if (isFromOtherMarketPlace && paymentMethod == 'cod') {
      setNewOrderState('payment_method', null);
    }
  }, [isFromOtherMarketPlace]);

  const [isPrepay, setPrepay] = useState(false);

  const handleChange = useCallback((method: string) => {
    setNewOrderState('payment_method', method);

    // analytics
    googleAnalytics.logAddPaymentInfo({
      payment_type: method,
    });
  }, []);

  const handleHelpPrepayPress = useCallback(() => {
    navigation.push('WebView', {url: 'https://thitruongsi.com/pages/docs/huong-dan-tao-don-dropship#3-toc-title', title: 'Hướng dẫn tạo đơn hàng'});
  }, []);

  return (
    <Card
      title={
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <MaterialCommunityIcons name="credit-card-fast-outline" size={26} style={{marginRight: 8}} color={theme.colors.text} />
          <Text style={{fontSize: theme.typography.lg, fontWeight: '500'}}>Phương thức thanh toán</Text>
        </View>
      }>
      <CheckBox
        title={'Thanh toán khi nhận hàng (COD)'}
        size={28}
        onPress={() => {
          handleChange('cod');
          setPrepay(false);
        }}
        textStyle={{fontWeight: '400', fontSize: theme.typography.md, color: theme.colors.text}}
        checked={paymentMethod === 'cod'}
        iconType="ionicon"
        uncheckedIcon="radio-button-off-outline"
        checkedIcon="checkmark-circle"
        checkedColor="#008060"
        containerStyle={{margin: 0, padding: 0, backgroundColor: 'transparent'}}
        wrapperStyle={{marginRight: -10, marginBottom: 10, padding: 0}}
        disabled={isFromOtherMarketPlace}
      />
      {isFromOtherMarketPlace && (
        <View style={{paddingHorizontal: theme.spacing.m, backgroundColor: theme.colors.secondary, paddingVertical: theme.spacing.xs, borderRadius: 4, marginBottom: theme.spacing.m}}>
          <Text>COD không khả dụng với đơn hàng từ sàn</Text>
        </View>
      )}

      <CheckBox
        title={
          <View style={{paddingHorizontal: 12, flexDirection: 'row', alignItems: 'center'}}>
            <Text style={{marginRight: theme.spacing.s}}>Thanh toán trước</Text>
            <Ionicons name="help-circle-outline" size={18} color={theme.colors.primary} onPress={handleHelpPrepayPress} />
          </View>
        }
        size={28}
        onPress={() => {
          handleChange('');

          setPrepay(true);
        }}
        textStyle={{fontWeight: '400', fontSize: theme.typography.md, color: theme.colors.text}}
        checked={isPrepay}
        iconType="ionicon"
        uncheckedIcon="radio-button-off-outline"
        checkedIcon="checkmark-circle"
        checkedColor="#008060"
        containerStyle={{margin: 0, padding: 0, backgroundColor: 'transparent'}}
        wrapperStyle={{marginRight: -10, marginBottom: 10, padding: 0}}
      />

      {isPrepay && (
        <View style={{paddingLeft: 34}}>
          <Text style={{paddingLeft: 14, fontSize: theme.typography.base, color: theme.colors.textLight, marginBottom: theme.spacing.m}}>Chọn 1 trong các phương thức bên dưới</Text>
          {paymentMethods.map(method => (
            <CheckBox
              key={method.value}
              title={<PaymentMethodItemLabel method={method.label} isPopular={method.isPopular} />}
              size={24}
              onPress={() => handleChange(method.value)}
              textStyle={{fontWeight: '400', fontSize: theme.typography.md, color: theme.colors.text}}
              checked={method.value === paymentMethod}
              iconType="ionicon"
              uncheckedIcon="radio-button-off-outline"
              checkedIcon="checkmark-circle"
              checkedColor="#008060"
              containerStyle={{margin: 0, padding: 0, backgroundColor: 'transparent'}}
              wrapperStyle={{marginRight: -10, marginBottom: 10, padding: 0}}
            />
          ))}
        </View>
      )}
    </Card>
  );
};

const PaymentMethodItemLabel = ({method, isPopular}: {method: string; isPopular: boolean}) => {
  return (
    <View style={styles.labelContainer}>
      <Text style={styles.labelText}>{method}</Text>
      {isPopular && (
        <View style={styles.popularBadge}>
          <Text style={styles.popularBadgeText}>Phổ biến</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: theme.spacing.s,
  },
  labelText: {
    fontSize: theme.typography.md,
    fontWeight: '400',
    color: theme.colors.text,
  },
  popularBadge: {
    backgroundColor: theme.colors.primaryLight,
    borderRadius: 4,
    paddingHorizontal: 4,
    paddingVertical: 2,
    marginLeft: 4,
  },
  popularBadgeText: {
    fontSize: theme.typography.sm,
    fontWeight: '500',
    color: theme.colors.primary,
  },
});

export default PaymentSection;
