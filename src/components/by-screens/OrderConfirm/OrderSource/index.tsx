import {CheckBox, Input, Text} from '@rneui/themed';
import React, {useCallback, useMemo} from 'react';
import {Platform, View} from 'react-native';
import Card from '~components/shared/Card';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import useCreateOrderStore from '~hooks/store/useCreateOrderStore';
import {MarketPlace} from '~utils/types/order';

type OrderSourceProps = {};

const OrderSource: React.FC<OrderSourceProps> = props => {
  const setNewOrderState = useCreateOrderStore(state => state.setNewOrderState);
  const sourceName = useCreateOrderStore(state => state.source_name);
  const marketPlace = useCreateOrderStore(state => state.market_place);
  const sourceIdentifier = useCreateOrderStore(state => state.source_identifier);
  const isFromOtherMarketPlace = useCreateOrderStore(state => state.is_from_other_marketplace);

  const baseSourceName = useMemo(() => {
    return Platform.OS === 'web' ? 'dropship_web' : 'dropship_app';
  }, []);

  const marketPlaceSources = useMemo(
    () => [
      {value: 'shopee', label: 'Shopee'},
      {value: 'lazada', label: 'Lazada'},
      {value: 'tiktokshop', label: 'Tiktok Shop'},
      {value: 'other', label: 'Khác'},
    ],
    [baseSourceName],
  );

  const handleChangeMarketPlace = useCallback(
    (newMarketPlace: MarketPlace | null) => {
      setNewOrderState('source_name', newMarketPlace ? `${baseSourceName}:${newMarketPlace}` : baseSourceName);
      setNewOrderState('market_place', newMarketPlace);
    },
    [baseSourceName],
  );

  const handleChangeSourceIdentifier = useCallback((text: string) => {
    setNewOrderState('source_identifier', text);
  }, []);

  const handleChangeIsFromOtherMarketPlace = useCallback((newValue: boolean) => {
    setNewOrderState('is_from_other_marketplace', newValue);
  }, []);

  return (
    <Card title="Nguồn đơn">
      <CheckBox
        title={'Đơn thường'}
        size={24}
        onPress={() => {
          handleChangeMarketPlace(null);
          handleChangeIsFromOtherMarketPlace(false);
        }}
        textStyle={{fontWeight: '400', fontSize: theme.typography.md, color: theme.colors.textLight}}
        checked={sourceName === baseSourceName && isFromOtherMarketPlace === false}
        iconType="ionicon"
        uncheckedIcon="radio-button-off-outline"
        checkedIcon="checkmark-circle"
        checkedColor={theme.colors.primary}
        containerStyle={{margin: 0, padding: 0, backgroundColor: 'transparent'}}
        wrapperStyle={{marginRight: -10, marginBottom: 10, padding: 0}}
      />

      <CheckBox
        title={
          <View style={{paddingHorizontal: 12, flexDirection: 'row', alignItems: 'center'}}>
            <Text style={{marginRight: theme.spacing.s, color: theme.colors.textLight}}>Đơn từ các sàn (Shopee, Lazada...)</Text>
            <Ionicons name="help-circle-outline" size={18} color={theme.colors.primary} onPress={() => {}} />
          </View>
        }
        size={24}
        onPress={() => {
          // handleChangeMarketPlace(baseSourceName);
          handleChangeIsFromOtherMarketPlace(true);
        }}
        textStyle={{fontWeight: '400', fontSize: theme.typography.md, color: theme.colors.text}}
        checked={isFromOtherMarketPlace}
        iconType="ionicon"
        uncheckedIcon="radio-button-off-outline"
        checkedIcon="checkmark-circle"
        checkedColor="#008060"
        containerStyle={{margin: 0, padding: 0, backgroundColor: 'transparent'}}
        wrapperStyle={{marginRight: -10, marginBottom: 10, padding: 0}}
      />
      {isFromOtherMarketPlace && (
        <View style={{paddingLeft: 34}}>
          <Text style={{paddingLeft: 14, fontSize: theme.typography.base, color: theme.colors.textLight, marginBottom: theme.spacing.m}}>Đơn của bạn từ sàn nào?</Text>
          <View style={{flexDirection: 'row', flexWrap: 'wrap'}}>
            {marketPlaceSources.map(mp => (
              <CheckBox
                key={mp.value}
                title={mp.label}
                size={24}
                onPress={() => handleChangeMarketPlace(mp.value as any)}
                textStyle={{fontWeight: '400', fontSize: theme.typography.base, color: theme.colors.text}}
                checked={mp.value === marketPlace}
                iconType="ionicon"
                uncheckedIcon="radio-button-off-outline"
                checkedIcon="checkmark-circle"
                checkedColor="#008060"
                containerStyle={{margin: 0, padding: 0, backgroundColor: 'transparent'}}
                wrapperStyle={{marginRight: -10, marginBottom: 10, padding: 0}}
              />
            ))}
          </View>
          {/*  */}
          {sourceName !== baseSourceName ? (
            <Input
              containerStyle={{marginTop: theme.spacing.s}}
              placeholder="Mã đơn hàng"
              placeholderTextColor={theme.colors.textLightest}
              label="Mã đơn hàng"
              value={sourceIdentifier}
              onChangeText={handleChangeSourceIdentifier}
              labelStyle={{fontWeight: 'normal', fontSize: theme.typography.base}}
            />
          ) : null}
        </View>
      )}
    </Card>
  );
};

export default OrderSource;
