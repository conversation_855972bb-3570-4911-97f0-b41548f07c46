import {Input} from '@rneui/base';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {ActivityIndicator, StyleSheet, View} from 'react-native';
import Card from '~components/shared/Card';
import useCreateOrderStore from '~hooks/store/useCreateOrderStore';
import {CartItem} from '~utils/types/cart';
import {ShippingLine, ShippingRate} from '~utils/types/order';
import ShippingRateItem from '../ShippingRate';
import ShippingRateSelectModal from '../ShippingRateSelectModal';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {shippingRateToShippingLine} from '~utils/helpers/order';
import {Button, CheckBox, Text} from '@rneui/themed';
import theme from '~utils/config/themes/theme';
import OrderedItem from './OrderedItem';
import * as Sentry from '@sentry/react-native';
import {makeRequestCreator} from '~utils/api/axios';
import {FirebaseAnalyticsTypes} from '@react-native-firebase/analytics';
import googleAnalytics from '~utils/helpers/analytics';
import {formatPriceToString} from '~utils/helpers/price';
import AppliedVoucher from './AppliedVoucher';
import {ShopPriceRule} from '~utils/api/voucher';
import Address from '~utils/types/address';
import {useUpdateEffect} from '~hooks';
// import Ionicons from 'react-native-vector-icons/Ionicons';

type OrderByShopProps = {
  shopId: string;
  order: {[key: string]: any};
};

const OrderByShop: React.FC<OrderByShopProps> = props => {
  const shippingAddress: Address | null = useCreateOrderStore(state => state.shipping_address);
  const paymentMethod = useCreateOrderStore(state => state.payment_method);
  const setOrderByShopIdState = useCreateOrderStore(state => state.setOrderByShopIdState);
  const isFromOtherMarketPlace = useCreateOrderStore(state => state.is_from_other_marketplace);

  const [shippingRateLoading, setShippingRateLoading] = useState(false);
  const [shippingRates, setShippingRates] = useState([]);
  const [selectedShippingRate, setSelectedShippingRate] = useState<ShippingRate | undefined>();
  const [shippingRateModalOpen, setShippingRateModalOpen] = useState(false);
  const [shippingRateError, setShippingRateError] = useState('');
  const cancellable = useRef(makeRequestCreator());

  useEffect(() => {
    setOrderByShopIdState(props.shopId, 'shipping_lines', [shippingRateToShippingLine(selectedShippingRate, shippingAddress?.phone as string)].filter(Boolean));

    if (selectedShippingRate) {
      // analytics
      const addShippingInfoParams: FirebaseAnalyticsTypes.AddShippingInfoEventParameters = {
        shipping_tier: selectedShippingRate.service_name,
        currency: 'VND',
        value: selectedShippingRate.total_price,
        items:
          props.order?.line_items?.map((lineItem: CartItem) => ({
            item_id: lineItem.product_id,
            item_name: lineItem.product_title,
            quantity: lineItem.quantity,
            price: lineItem.price,
            item_variant: lineItem.variant_title,
            item_category: lineItem.product_type?.split(' > ')[0],
            item_category2: lineItem.product_type.split(' > ')[1],
          })) ?? [],
      };
      googleAnalytics.logAddShippingInfo(addShippingInfoParams);
    }
  }, [selectedShippingRate]);

  useUpdateEffect(() => {
    if (isFromOtherMarketPlace) {
      setSelectedShippingRate(undefined);
    }
  }, [isFromOtherMarketPlace]);

  useEffect(() => {
    if (!isFromOtherMarketPlace) {
      runGetShippingRate();
    }
  }, [shippingAddress?.id, paymentMethod, props.order.line_items, isFromOtherMarketPlace]);

  const runGetShippingRate = useCallback(() => {
    if (shippingAddress?.id) {
      setShippingRateLoading(true);
      setShippingRateError('');
      setSelectedShippingRate(undefined);

      cancellable
        .current('/v2/order/api/shipping_rates.json', {
          rate: {
            destination: {
              customer_address_id: shippingAddress.id,
            },
            items: props.order.line_items?.map((lineItem: CartItem) => ({
              product_id: lineItem.product_id,
              variant_id: lineItem.variant_id,
              quantity: lineItem.quantity,
              dropship_selling_price: lineItem.dropship_selling_price,
              dropship: true,
            })),
            origin: {
              shop_id: props.shopId,
            },
            cod: paymentMethod === 'cod',
          },
        })
        .then(res => {
          if (res) {
            setShippingRates(res.data.rates);
            setSelectedShippingRate(res.data.rates[0]);
            setShippingRateLoading(false);
          }
        })
        .catch(err => {
          setShippingRateError(err.response?.data?.message ?? 'Tìm nhà vận chuyển thất bại');
          Sentry.captureMessage('Tim nha van chuyen that bai');
          setShippingRateLoading(false);
        });
    }
  }, [shippingAddress?.id, paymentMethod, props.order, props.shopId]);

  const openShippingRateModal = useCallback(() => {
    setShippingRateModalOpen(true);
  }, []);

  const closeShippingRateModal = useCallback(() => {
    setShippingRateModalOpen(false);
  }, []);

  const handleShippingRateSelect = useCallback((shippingRate: ShippingRate) => {
    setSelectedShippingRate(shippingRate);
    setTimeout(() => {
      closeShippingRateModal();
    }, 100);
  }, []);

  const vendorName = useMemo(() => {
    return props.order?.line_items?.[0]?.vendor ?? '';
  }, []);

  const totalLineItemsPrice = useMemo(() => {
    try {
      return (props.order?.line_items as CartItem[]).reduce((a, c) => {
        return a + c.dropship_selling_price * c.quantity;
      }, 0);
    } catch (error) {
      return 0;
    }
  }, [props.order]);

  const totalSupplierPrice = useMemo(() => {
    try {
      return (props.order?.line_items as CartItem[]).reduce((a, c) => {
        return a + c.dropship_price * c.quantity;
      }, 0);
    } catch (error) {
      return 0;
    }
  }, [props.order]);

  const totalShippingPrice = useMemo(() => {
    try {
      return (props.order?.shipping_lines as ShippingLine[]).reduce((a, c) => {
        return a + c.price;
      }, 0);
    } catch (error) {
      return 0;
    }
  }, [props.order]);
  //

  const shippingLinePriceRules = useMemo(() => {
    return props.order.price_rules?.filter((rule: ShopPriceRule) => rule.target_type === 'shipping_line') ?? [];
  }, [props.order]);

  const totalDiscount = useMemo(() => {
    return props.order.price_rules?.reduce((a1: any, c1: any) => {
      if (c1.target_type === 'shipping_line') {
        //
        let shippingPrice = props.order.shipping_lines?.reduce((acc: any, cur: any) => acc + cur.price, 0) ?? 0;
        let discountPrice = Math.min(shippingPrice, Math.abs(c1.value));
        return a1 + -discountPrice;
      }
      return a1;
    }, 0);
  }, [props.order]);

  const handleToggleShipmentPaybyDropshipper = useCallback(
    (isPaybyDropshipper: boolean) => {
      // @ts-ignore
      setSelectedShippingRate(prev => ({...prev, __pay_by: isPaybyDropshipper ? 'dropshipper' : null, __pay_amount: isPaybyDropshipper ? totalShippingPrice + totalDiscount : null}));
    },
    [totalShippingPrice, totalDiscount],
  );

  const isShowPaybyDropshipperOption = useMemo(() => {
    return totalShippingPrice + totalDiscount > 0;
  }, [totalShippingPrice, totalDiscount, totalSupplierPrice, totalLineItemsPrice]);

  const isPaybyDropshipperDisabled = useMemo(() => {
    if (!isShowPaybyDropshipperOption) {
      return false;
    }

    return totalLineItemsPrice - totalSupplierPrice < totalShippingPrice + totalDiscount;
  }, [isShowPaybyDropshipperOption, totalLineItemsPrice, totalSupplierPrice, totalShippingPrice, totalDiscount]);

  return (
    <>
      <Card
        title={
          <View style={[theme.globalStyles.flexRow, {alignItems: 'center'}]}>
            <MaterialCommunityIcons name="storefront-outline" size={18} color={theme.colors.textLight} />
            <Text style={{marginLeft: 4, color: theme.colors.textLight}}>{vendorName}</Text>
          </View>
        }>
        {props.order.line_items?.map((lineItem: CartItem) => (
          <OrderedItem lineItem={lineItem} key={lineItem.id} />
        ))}

        {!isFromOtherMarketPlace && (
          <>
            <View
              style={{
                marginTop: 8,
                paddingVertical: 12,
                paddingHorizontal: 8,
                borderTopWidth: 1,
                borderTopColor: 'rgba(0,0,0,.05)',
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <MaterialCommunityIcons name="truck-fast-outline" size={20} color={theme.colors.text} />
                <Text style={{fontSize: theme.typography.md, marginLeft: 8}}>Nhà vận chuyển</Text>
              </View>
              {!shippingAddress && <Text style={[styles.text, {color: theme.colors.textLight}]}>Chưa có người nhận</Text>}
            </View>
            {shippingRateLoading && (
              <View style={{marginVertical: 20, alignItems: 'center'}}>
                <ActivityIndicator color={'#008060'} />
              </View>
            )}
            {Boolean(shippingRateError) && (
              <View style={[theme.globalStyles.flexRow]}>
                <Text style={{color: theme.colors.red, fontSize: theme.typography.base, marginHorizontal: theme.spacing.s, flex: 1}}>{shippingRateError}</Text>
                <Button type="clear" titleStyle={{color: theme.colors.blue, fontSize: theme.typography.md, textDecorationLine: 'underline'}} onPress={runGetShippingRate}>
                  Thử lại
                </Button>
              </View>
            )}
            {selectedShippingRate && <ShippingRateItem onPress={openShippingRateModal} shippingRate={selectedShippingRate} priceRules={shippingLinePriceRules} />}
          </>
        )}

        {isShowPaybyDropshipperOption ? (
          <CheckBox
            containerStyle={{backgroundColor: 'transparent', marginHorizontal: 0, paddingHorizontal: 0}}
            wrapperStyle={{marginHorizontal: 0, paddingHorizontal: 0, alignItems: 'flex-start'}}
            style={{marginHorizontal: 0, paddingHorizontal: 0}}
            checked={!!selectedShippingRate?.__pay_by}
            disabled={isPaybyDropshipperDisabled}
            // @ts-ignore
            title={
              <View style={{marginLeft: theme.spacing.s}}>
                <Text style={{fontSize: theme.typography.base, color: isPaybyDropshipperDisabled ? theme.colors.textLightest : theme.colors.text}}>
                  Chịu phí ship ({formatPriceToString(totalShippingPrice + totalDiscount)}) thay cho khách (trừ vào lợi nhuận)
                </Text>
                {isPaybyDropshipperDisabled && <Text style={{fontSize: theme.typography.base, color: theme.colors.pending}}>Lợi nhuận không đủ</Text>}
              </View>
            }
            textStyle={{color: theme.colors.text, fontSize: theme.typography.base, fontWeight: '400'}}
            size={20}
            iconType="ionicon"
            uncheckedIcon={'square-outline'}
            checkedIcon="checkbox-outline"
            checkedColor={theme.colors.text}
            uncheckedColor={isPaybyDropshipperDisabled ? theme.colors.textLightest : theme.colors.text}
            onPress={() => handleToggleShipmentPaybyDropshipper(!selectedShippingRate?.__pay_by)}
          />
        ) : null}

        <AppliedVoucher shopId={props.shopId} />

        <View
          style={{
            marginVertical: theme.spacing.m,
            borderTopWidth: 1,
            borderTopColor: 'rgba(0,0,0,.05)',
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
          <Text style={{fontSize: theme.typography.base, marginRight: 80}}>Ghi chú</Text>
          <Input
            onChangeText={text => setOrderByShopIdState(props.shopId, 'note', text)}
            placeholder="Ghi chú cho người bán..."
            containerStyle={{flex: 1}}
            inputStyle={{paddingHorizontal: 12, fontSize: 14, textAlign: 'right'}}
            inputContainerStyle={{borderBottomWidth: 0, backgroundColor: theme.colors.white, borderRadius: 30}}
            renderErrorMessage={false}
          />
        </View>
        <View style={[theme.globalStyles.flexRow, {alignItems: 'center', justifyContent: 'flex-end'}]}>
          <Text style={{fontSize: theme.typography.base}}>Tổng tiền</Text>
          <Text style={[{marginRight: theme.spacing.l, fontSize: theme.typography.lg, color: theme.colors.primary, marginLeft: theme.spacing.m}]}>
            {formatPriceToString(totalLineItemsPrice + (selectedShippingRate?.__pay_by === 'dropshipper' ? 0 : totalShippingPrice + totalDiscount))}
          </Text>
        </View>
      </Card>

      <ShippingRateSelectModal onClose={closeShippingRateModal} rates={shippingRates} open={shippingRateModalOpen} selectedShippingRate={selectedShippingRate} onSelect={handleShippingRateSelect} />
    </>
  );
};

const styles = StyleSheet.create({
  imageThumb: {
    width: 70,
    height: 70,
    borderRadius: 4,
    borderColor: 'rgba(0,0,0,.05)',
    borderWidth: 1,
    marginRight: 8,
  },
  lineItem: {
    marginTop: 16,
    flexDirection: 'row',
  },
  lineItem__productTitle: {
    // fontWeight: '500',
    fontSize: theme.typography.md,
    color: theme.colors.text,
  },
  lineItem__variantTitle: {
    fontSize: theme.typography.base,
    marginTop: 4,
    color: theme.colors.textLight,
  },
  text: {
    fontSize: theme.typography.base,
  },
  price: {
    fontWeight: '500',
    fontSize: theme.typography.md,
    color: theme.colors.text,
  },
});

export default OrderByShop;
