import {Text} from '@rneui/themed';
import React, {useEffect, useMemo} from 'react';
import {StyleSheet, View} from 'react-native';
import useCreateOrderStore from '~hooks/store/useCreateOrderStore';
import {useGetShopPriceRules} from '~utils/api/voucher';
import theme from '~utils/config/themes/theme';

type ShopVoucherProps = {
  shopId: string;
};

const AppliedVoucher: React.FC<ShopVoucherProps> = props => {
  const order = useCreateOrderStore(state => state.order_by_shop_id[props.shopId]);
  const setOrderByShopIdState = useCreateOrderStore(state => state.setOrderByShopIdState);

  const prerequisiteSubtotalPrice = useMemo(() => {
    try {
      return (
        order.line_items?.reduce((acc: any, cur: any) => {
          return acc + parseInt(cur.price, 10) * cur.quantity;
        }, 0) ?? 0
      );
    } catch (error) {
      return 0;
    }
  }, [order]);

  const {data: shippingLinePriceRules} = useGetShopPriceRules({
    shop_id: props.shopId,
    prerequisite_subtotal_price: prerequisiteSubtotalPrice,
    order: 'asc',
    sort_by: 'value',
    target_type: 'shipping_line',
  });

  const {data: commissionPriceRules} = useGetShopPriceRules({
    shop_id: props.shopId,
    prerequisite_subtotal_price: prerequisiteSubtotalPrice,
    order: 'desc',
    sort_by: 'value',
    target_type: 'commission',
  });

  const matchedShippingLinesPriceRule = useMemo(() => {
    return shippingLinePriceRules?.[0];
  }, [shippingLinePriceRules]);

  const matchedCommissionPriceRule = useMemo(() => {
    return commissionPriceRules?.[0];
  }, [commissionPriceRules]);

  const matchedPriceRules = useMemo(() => {
    return [matchedShippingLinesPriceRule, matchedCommissionPriceRule].filter(Boolean);
  }, [matchedShippingLinesPriceRule, matchedCommissionPriceRule]);

  useEffect(() => {
    setOrderByShopIdState(props.shopId, 'price_rules', matchedPriceRules.length ? matchedPriceRules : []);
  }, [matchedPriceRules, props.shopId, order.shipping_lines, order.line_items]);

  if (!matchedPriceRules.length) {
    return null;
  }
  return (
    <>
      {matchedShippingLinesPriceRule ? (
        <View style={styles.container}>
          <Text style={styles.text}>
            🎁 Đã áp dụng <Text style={[{fontStyle: 'italic'}, styles.text]}>{matchedShippingLinesPriceRule?.title}</Text>
          </Text>
        </View>
      ) : null}

      {matchedCommissionPriceRule ? (
        <View style={styles.container}>
          <Text style={styles.text}>
            🎁 Đã áp dụng <Text style={[{fontStyle: 'italic'}, styles.text]}>{matchedCommissionPriceRule?.title}</Text>
          </Text>
        </View>
      ) : null}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: theme.colors.gray10,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 4,
  },
  text: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    // marginRight: 80,
    flex: 1,
  },
});

export default AppliedVoucher;
