import {Text} from '@rneui/themed';
import React, {useMemo} from 'react';
import {Image, StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import {handleImageDomain} from '~utils/helpers/image';
import {formatPriceToString} from '~utils/helpers/price';
import {CartItem} from '~utils/types/cart';

type OrderedItemProps = {
  lineItem: CartItem;
};

const OrderedItem: React.FC<OrderedItemProps> = props => {
  const isDefaultVariant = useMemo(() => {
    return ['default', 'default title'].includes(props.lineItem.variant_title.toLowerCase());
  }, [props.lineItem]);

  return (
    <View key={props.lineItem.id} style={styles.lineItem}>
      <Image style={styles.imageThumb} source={{uri: handleImageDomain(props.lineItem.image)}} />
      <View style={{flex: 1}}>
        <Text style={styles.lineItem__productTitle} numberOfLines={1}>
          {props.lineItem.product_title}
        </Text>
        {props.lineItem.options_with_values && !isDefaultVariant && (
          <Text style={styles.lineItem__variantTitle}>
            {Object.keys(props.lineItem.options_with_values)
              .map(key => `${key}: ${props.lineItem.options_with_values[key]}`)
              .join(', ')}
          </Text>
        )}
        <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 4, marginLeft: 4}}>
          <Text style={styles.price}>
            {formatPriceToString(props.lineItem.dropship_selling_price)} <Text style={[styles.text, {fontWeight: '400', color: 'rgba(0,0,0,.5)'}]}>/ {props.lineItem.unit_name}</Text>
          </Text>
          <Text style={[styles.text, {marginLeft: 20}]}>
            SL: {props.lineItem.quantity} {props.lineItem.unit_name}
          </Text>
        </View>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  imageThumb: {
    width: 60,
    height: 60,
    borderRadius: 8,
    borderColor: 'rgba(0,0,0,.05)',
    borderWidth: 1,
    marginRight: 8,
  },
  lineItem: {
    marginBottom: theme.spacing.m,
    flexDirection: 'row',
  },
  lineItem__productTitle: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
  },
  lineItem__variantTitle: {
    marginTop: 4,
    fontSize: theme.typography.sm,
    color: theme.colors.text,
  },
  text: {
    fontSize: theme.typography.base,
  },
  price: {
    fontWeight: '500',
    fontSize: theme.typography.base,
    color: theme.colors.text,
  },
});

export default OrderedItem;
