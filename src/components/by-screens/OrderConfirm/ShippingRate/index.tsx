import React, {useMemo} from 'react';
import {Pressable, StyleSheet, View} from 'react-native';
import {ShippingRate} from '~utils/types/order';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {formatPriceToString} from '~utils/helpers/price';
import dayjs from 'dayjs';
import {Text} from '@rneui/themed';
import theme from '~utils/config/themes/theme';
import {ShopPriceRule} from '~utils/api/voucher';

type ShippingRateItemProps = {
  shippingRate: ShippingRate;
  arrowHidden?: boolean;
  onPress?: () => void;
  priceRules?: ShopPriceRule[];
};

const ShippingRateItem: React.FC<ShippingRateItemProps> = props => {
  const minDeliveryDate = useMemo(() => {
    return `${dayjs(props.shippingRate.min_delivery_date).format('DD/MM')}`;
  }, [props.shippingRate.min_delivery_date]);

  const maxDeliveryDate = useMemo(() => {
    return `${dayjs(props.shippingRate.max_delivery_date).format('DD/MM')}`;
  }, [props.shippingRate.max_delivery_date]);

  const totalDiscountValue = useMemo(() => {
    return (
      props.priceRules?.reduce((a, c) => {
        return a + c.value;
      }, 0) ?? 0
    );
  }, [props.priceRules, props.shippingRate]);

  const hasDiscount = totalDiscountValue !== 0;

  return (
    <Pressable
      style={[styles.selectedShippingRate, props.arrowHidden ? {backgroundColor: '#fff', borderBottomWidth: 1, borderBottomColor: 'rgba(0,0,0,.05)', marginTop: 0, paddingVertical: 20} : {}]}
      onPress={props.onPress}
      accessible={false}>
      <View style={[theme.globalStyles.flexRow, {justifyContent: 'space-between', alignItems: 'flex-start'}]}>
        <Text style={[styles.text, {flex: 1, marginRight: 50}]}>{props.shippingRate.service_name}</Text>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <View>
            <Text style={[styles.text, {marginRight: 8}, hasDiscount && {color: theme.colors.textLight, textDecorationLine: 'line-through'}]}>
              {formatPriceToString(props.shippingRate.total_price)}
            </Text>
            {hasDiscount && <Text style={[styles.text]}>{formatPriceToString(Math.max(props.shippingRate.total_price + totalDiscountValue, 0))}</Text>}
          </View>
          {!props.arrowHidden && <Ionicons name="chevron-forward" size={20} color={theme.colors.textLight} accessibilityLabel="Chọn nhà vận chuyển khác" />}
        </View>
      </View>
      <Text style={[styles.text2, {marginTop: 4}]}>Nhận hàng dự kiến: {minDeliveryDate === maxDeliveryDate ? maxDeliveryDate : `${minDeliveryDate} - ${maxDeliveryDate}`}</Text>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  selectedShippingRate: {
    backgroundColor: '#f0fff6',
    paddingHorizontal: 8,
    paddingVertical: 12,
    borderRadius: 4,
  },
  text: {
    fontSize: theme.typography.md,
  },
  text2: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
  },
});
export default ShippingRateItem;
