import {ScreenHeight} from '@rneui/base';
import {Button, CheckBox, Text} from '@rneui/themed';
import React, {useCallback} from 'react';
import {Pressable, StyleSheet, View} from 'react-native';
import Modal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import BankAccountItem from '~components/by-screens/BankAccountList/BankAccountItem';
import {useGetMyBankAccounts} from '~utils/api/wallet';
import theme from '~utils/config/themes/theme';
import {MyBank} from '~utils/types/common';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '~hooks';

type BankAccountSelectModalProps = {
  open: boolean;
  onClose: () => void;
  selectedAccountId?: number;
  onSelect: (account: MyBank) => void;
};

const BankAccountSelectModal: React.FC<BankAccountSelectModalProps> = props => {
  const insets = useSafeAreaInsets();
  const {data} = useGetMyBankAccounts();
  const navigation = useNavigation();

  const handleAccountSelect = useCallback((account: MyBank) => {
    if (typeof props.onSelect === 'function') {
      props.onSelect(account);
    }
    setTimeout(() => {
      props.onClose();
    }, 100);
  }, []);

  const handleAddBankAccount = useCallback(() => {
    props.onClose();
    navigation.navigate('AddBankAccount');
  }, []);

  return (
    <Modal isVisible={props.open} style={{margin: 0}} swipeDirection={['down', 'left', 'right']} onSwipeComplete={props.onClose} onBackdropPress={props.onClose} onBackButtonPress={props.onClose}>
      <Pressable style={{flex: 1, paddingTop: insets.top}} onPress={props.onClose}>
        <View style={[styles.modalContent, {paddingBottom: theme.spacing.m + insets.bottom}]} onStartShouldSetResponder={() => true}>
          <View style={{marginBottom: theme.spacing.s, position: 'relative'}}>
            <View style={{position: 'absolute', top: -8, left: 0, zIndex: 1}}>
              <Ionicons name="close-circle" size={34} color={theme.colors.gray50} onPress={props.onClose} />
            </View>
            <Text style={{textAlign: 'center', fontWeight: '600', fontSize: theme.typography.lg}}>Chọn tài khoản</Text>
            <View style={{position: 'absolute', top: 0, right: 0, zIndex: 1}}>
              <Button type="clear" titleStyle={{fontSize: theme.typography.md, color: theme.colors.blue}} size="sm" buttonStyle={{padding: 0}} onPress={handleAddBankAccount}>
                Thêm
              </Button>
            </View>
          </View>

          {data?.map(account => (
            <Pressable key={account.id} onPress={() => handleAccountSelect(account)}>
              <BankAccountItem
                item={account}
                leftAction={
                  <CheckBox
                    size={28}
                    onPress={() => handleAccountSelect(account)}
                    checked={props.selectedAccountId === account.id}
                    iconType="ionicon"
                    uncheckedIcon="radio-button-off-outline"
                    checkedIcon="checkmark-circle"
                    checkedColor="#008060"
                    containerStyle={{margin: 0, padding: 0, backgroundColor: 'transparent'}}
                    wrapperStyle={{marginLeft: -10, marginTop: 10, padding: 0}}
                  />
                }
              />
            </Pressable>
          ))}
        </View>
      </Pressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContent: {
    backgroundColor: theme.colors.white,
    marginTop: 'auto',
    minHeight: ScreenHeight * 0.5,
    padding: 12,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
});

export default BankAccountSelectModal;
