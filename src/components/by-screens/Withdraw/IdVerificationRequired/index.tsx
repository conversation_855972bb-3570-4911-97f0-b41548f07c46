import {Button, Text} from '@rneui/themed';
import React from 'react';
import {View} from 'react-native';
import {useNavigation} from '~hooks';
import theme from '~utils/config/themes/theme';

const IdentityVerificationRequired = () => {
  const navigation = useNavigation();

  const handlePressVerify = () => {
    navigation.navigate('IdentityVerification', {msg: 'Vui lòng định danh tài khoản trước khi rút tiền'});
  };

  return (
    <View style={{backgroundColor: theme.colors.secondary, paddingHorizontal: theme.spacing.s, paddingVertical: theme.spacing.s, borderRadius: 8}}>
      <Text style={{fontSize: theme.typography.lg, marginBottom: theme.spacing.xs}}>Y<PERSON>u cầu định danh tài khoản</Text>
      <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight}}><PERSON><PERSON>n có thể rút tiền khi tài khoản đã định danh.</Text>

      <View style={{marginTop: theme.spacing.m, alignItems: 'flex-start'}}>
        <Button onPress={handlePressVerify} buttonStyle={{borderRadius: 100, paddingVertical: 6}} titleStyle={{fontSize: theme.typography.md}} size="sm">
          Định danh ngay
        </Button>
      </View>
    </View>
  );
};

export default IdentityVerificationRequired;
