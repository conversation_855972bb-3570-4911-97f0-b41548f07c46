import {useRoute} from '@react-navigation/native';
import {Text} from '@rneui/themed';
import React, {useCallback, useMemo} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {SortParams} from '~hooks/useSort';
import theme from '~utils/config/themes/theme';
import {ProductRequestQuery, ProductSortQuery} from '~utils/types/product';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {Menu, MenuOption, MenuOptions, MenuTrigger} from 'react-native-popup-menu';
import SupplierCategoriesModal from '../SupplierCategoriesModal';
import {AggregationBucket} from '~utils/types/common';
import {handleImageDomain} from '~utils/helpers/image';
import {QuickImage} from '~components/shared/QuickImage';

type SortBarProps = {
  sortState: SortParams;
  updateSort: (newSortState: SortParams) => void;
  updateFilter: (newFilterState: ProductRequestQuery) => void;
  disableScoreSort?: boolean;
  shopId?: string;
};

const SortBar: React.FC<SortBarProps> = ({sortState, updateSort, disableScoreSort, shopId, updateFilter}) => {
  const route = useRoute();
  const [isCategoriesModalVisible, setIsCategoriesModalVisible] = React.useState(false);
  const [selectedCategory, setSelectedCategory] = React.useState<AggregationBucket | null>(null);

  const getSelectedColor = useCallback(
    (sortBy: ProductSortQuery) => {
      return sortState.sort_by === sortBy ? theme.colors.primary : theme.colors.textLight;
    },
    [sortState.sort_by],
  );

  const getBorderBottomWidth = useCallback(
    (sortBy: ProductSortQuery) => {
      return sortState.sort_by === sortBy ? 1 : 0;
    },
    [sortState.sort_by],
  );

  const handleSortPress = useCallback(
    (sortBy: ProductSortQuery, ascending?: boolean) => {
      updateSort({sort_by: sortBy, ascending});
    },
    [route.params, sortState],
  );

  const getSortLabel = useMemo(() => {
    const sortLabel = {
      _score: 'Liên quan',
      dropship_published_at: 'Mới nhất',
      total_sales: 'Bán chạy',
      dropship_profit: 'Lợi nhuận',
      dropship_price: 'Giá',
    };

    // @ts-ignore
    return sortLabel[sortState.sort_by] || 'Sắp xếp';
  }, [sortState.sort_by]);

  const isShowAscIcon = useMemo(() => {
    return ['dropship_price', 'dropship_profit'].includes(sortState.sort_by ?? '');
  }, [sortState.sort_by]);

  const handleSelectCategory = useCallback((item: AggregationBucket) => {
    setSelectedCategory(item);
    setIsCategoriesModalVisible(false);
    setTimeout(() => {
      updateFilter({filter_category_lv2: item.key});
    }, 500);
  }, []);

  const handleClearCategory = useCallback(() => {
    setSelectedCategory(null);
    updateFilter({filter_category_lv2: undefined});
  }, []);

  return (
    <View>
      <View style={{flexDirection: 'row', backgroundColor: theme.colors.white, paddingHorizontal: 8, paddingVertical: 4}}>
        <Menu>
          <MenuTrigger>
            <View style={[theme.globalStyles.flexRow, {borderWidth: 1, padding: 2, paddingHorizontal: 6, borderColor: theme.colors.textLight, borderRadius: 100}]}>
              {!isShowAscIcon && <Ionicons name="swap-vertical" size={14} color={theme.colors.text} />}
              <Text style={{fontSize: theme.typography.base, marginLeft: theme.spacing.xs}}>{getSortLabel}</Text>
              {isShowAscIcon && <Ionicons name={sortState.ascending ? 'arrow-up' : 'arrow-down'} size={14} color={theme.colors.text} />}
            </View>
          </MenuTrigger>
          <MenuOptions
            customStyles={{
              optionText: {
                paddingVertical: 2,
                color: theme.colors.text,
              },
              optionWrapper: {
                paddingVertical: 2,
              },
            }}>
            {!disableScoreSort && <MenuOption onSelect={() => alert(`Save`)} text="Liên quan" />}

            <MenuOption onSelect={() => handleSortPress('dropship_published_at')} text="Mới nhất" />
            <MenuOption onSelect={() => handleSortPress('total_sales')} text="Bán chạy" />
            <MenuOption
              onSelect={() => handleSortPress('dropship_profit', typeof sortState.ascending !== 'undefined' && sortState.sort_by === 'dropship_profit' ? !sortState.ascending : false)}
              text="Lợi nhuận"
            />
            <MenuOption onSelect={() => handleSortPress('dropship_price', typeof sortState.ascending !== 'undefined' ? !sortState.ascending : true)} text="Giá" />
          </MenuOptions>
        </Menu>
        <TouchableOpacity style={[theme.globalStyles.flexRow, {marginLeft: theme.spacing.s}]} onPress={() => setIsCategoriesModalVisible(true)}>
          {!selectedCategory ? (
            <>
              <Ionicons name="grid-outline" size={14} color={theme.colors.text} />
              <Text style={{fontSize: theme.typography.base, marginLeft: theme.spacing.xs}}>Danh mục</Text>
            </>
          ) : (
            <>
              <View style={[theme.globalStyles.flexRow, {backgroundColor: theme.colors.gray20, paddingLeft: theme.spacing.s, borderRadius: 100}]}>
                <QuickImage
                  source={{
                    uri: handleImageDomain(selectedCategory.image!),
                  }}
                  style={{width: 20, height: 20, borderRadius: theme.spacing.xs}}
                />
                <Text style={{fontSize: theme.typography.base, marginRight: theme.spacing.xs}}>{selectedCategory.title}</Text>

                <Ionicons name="close-circle" size={18} color={theme.colors.textLight} onPress={handleClearCategory} />
              </View>
            </>
          )}
        </TouchableOpacity>
      </View>

      {shopId && <SupplierCategoriesModal onSelectCategory={handleSelectCategory} shopId={shopId} isVisible={isCategoriesModalVisible} onClose={() => setIsCategoriesModalVisible(false)} />}
    </View>
  );
};

const styles = StyleSheet.create({});

export default SortBar;
