import {SCREEN_WIDTH} from '@gorhom/bottom-sheet';
import {Text} from '@rneui/themed';
import React from 'react';
import {ActivityIndicator, ScrollView, View} from 'react-native';
import Modal from 'react-native-modal';
import {useGetSearchAggregations} from '~utils/api/product';
import theme from '~utils/config/themes/theme';
import SupplierCategoryItem from './SupplierCategoryItem';
import {AggregationBucket} from '~utils/types/common';

type SupplierCategoriesModalProps = {
  isVisible: boolean;
  onClose: () => void;
  shopId: string;
  onSelectCategory: (category: AggregationBucket) => void;
};

const SupplierCategoriesModal: React.FC<SupplierCategoriesModalProps> = props => {
  const {data, isLoading} = useGetSearchAggregations({filter_shop_id: props.shopId});

  return (
    <Modal
      animationIn={'fadeInRight'}
      animationOut={'fadeOutRight'}
      isVisible={props.isVisible}
      onSwipeComplete={props.onClose}
      onBackdropPress={props.onClose}
      style={{alignItems: 'flex-end', margin: 0}}>
      <View style={{flex: 1, margin: 0, width: Math.min(SCREEN_WIDTH, 728) * 0.8, marginHorizontal: 0, backgroundColor: theme.colors.white, paddingTop: theme.spacing.l}}>
        <Text style={{textAlign: 'center', fontSize: theme.typography.lg, fontWeight: '500', marginTop: theme.spacing.l, marginBottom: theme.spacing.m}}>Danh mục sản phẩm</Text>
        <ScrollView style={{flex: 1}} showsVerticalScrollIndicator={false}>
          {isLoading && <ActivityIndicator />}

          {data?.filter_category_lv2.buckets.map(item => (
            <SupplierCategoryItem onPress={props.onSelectCategory} item={item} key={item.key} />
          ))}
        </ScrollView>
      </View>
    </Modal>
  );
};

export default SupplierCategoriesModal;
