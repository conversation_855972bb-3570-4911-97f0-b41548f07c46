import {Text} from '@rneui/themed';
import React, {useCallback} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {QuickImage} from '~components/shared/QuickImage';
import theme from '~utils/config/themes/theme';
import {handleImageDomain} from '~utils/helpers/image';
import {AggregationBucket} from '~utils/types/common';
import Ionicons from 'react-native-vector-icons/Ionicons';

type SupplierCategoryItemProps = {
  item: AggregationBucket;
  onPress: (item: AggregationBucket) => void;
};

const SupplierCategoryItem: React.FC<SupplierCategoryItemProps> = props => {
  const handlePress = useCallback(() => {
    props.onPress(props.item);
  }, [props.item.key]);

  return (
    <TouchableOpacity onPress={handlePress} style={styles.container}>
      {props.item.image ? (
        <QuickImage
          source={{
            uri: handleImageDomain(props.item.image),
          }}
          style={{width: 40, height: 40, borderWidth: 1, borderColor: theme.colors.gray10}}
        />
      ) : null}
      <Text style={{color: theme.colors.text, flex: 1, marginHorizontal: theme.spacing.s, fontSize: theme.typography.base, fontWeight: '500'}} numberOfLines={2}>
        {props.item.title}
      </Text>
      <View style={{marginLeft: 'auto', flexDirection: 'row', alignItems: 'center'}}>
        <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight}}>{props.item.doc_count}</Text>
        <Ionicons name="chevron-forward" size={16} color={theme.colors.textLight} />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.s,
    paddingHorizontal: theme.spacing.s,
  },
});

export default SupplierCategoryItem;
