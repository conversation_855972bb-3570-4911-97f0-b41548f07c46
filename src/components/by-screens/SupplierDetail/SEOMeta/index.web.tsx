import React, {useMemo} from 'react';
import {SEOMetaProps} from '.';
import {Helmet} from 'react-helmet';
import {formatPriceToString} from '~utils/helpers/price';
import {handleImageDomain} from '~utils/helpers/image';

const SEOMeta: React.FC<SEOMetaProps> = props => {
  const ogDescription = useMemo(() => {
    return props.shop?.name ? `Nhà cung cấp ${props.shop.name} trên TTS Dropship` : '';
  }, [props.shop?.name]);

  const seoTitle = useMemo(() => {
    return props.shop?.name ? `${props.shop.name}` : '';
  }, [props.shop?.name]);

  const ogImage = useMemo(() => {
    return props.shop?.avatar ? handleImageDomain(props.shop.avatar) : '';
  }, [props.shop?.avatar]);

  return (
    // @ts-ignore
    <Helmet>
      <meta charSet="UTF-8" />
      <title>{seoTitle} - TTS Dropship</title>
      {ogDescription ? <meta name="description" content={ogDescription} /> : null}
      {ogDescription ? <meta property="og:description" content={ogDescription} /> : null}
      {seoTitle ? <meta property="og:title" content={seoTitle + ' - TTS Dropship'} /> : null}
      <meta property="og:site_name" content="TTS Dropship" />
      <meta property="og:locale" content="vi-VN" />
      <meta property="og:type" content="website" />
      {ogImage ? <meta property="og:image" content={ogImage} /> : null}
      {/* preload og image */}
    </Helmet>
  );
};

export default SEOMeta;
