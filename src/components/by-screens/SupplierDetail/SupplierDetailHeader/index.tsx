import {HeaderButtonProps} from '@react-navigation/elements';
import React, {useCallback} from 'react';
import {Platform, Share} from 'react-native';
import {Menu, MenuOption, MenuOptions, MenuTrigger, renderers} from 'react-native-popup-menu';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '~hooks';
import {env} from '~utils/config';
import theme from '~utils/config/themes/theme';

const {Popover} = renderers;

type SupplierDetailHeaderProps = {
  shopId: string;
} & HeaderButtonProps;

const SupplierDetailHeader: React.FC<SupplierDetailHeaderProps> = props => {
  const navigation = useNavigation();

  const handlePress = useCallback(() => {
    navigation.navigate('Search', {shopId: props.shopId});
  }, [props.shopId]);

  const handleShare = useCallback(() => {
    const supplierUrl = `${env.WEBVIEW_URL}/supplier/${props.shopId}`;
    Share.share({
      message: Platform.OS === 'ios' ? '' : supplierUrl,
      url: supplierUrl,
    });
  }, [props.shopId]);

  return (
    <>
      <Ionicons name="search-outline" size={24} color={props.tintColor || theme.colors.text} style={{paddingHorizontal: 12, paddingVertical: 4}} onPress={handlePress} />
      {Platform.OS !== 'web' && (
        <Menu style={{}} renderer={Popover} rendererProps={{placement: 'bottom', anchorStyle: {backgroundColor: '#f5f5f5'}}}>
          <MenuTrigger>
            <Ionicons name="ellipsis-vertical-outline" size={22} color={props.tintColor || theme.colors.text} style={{padding: 4}} />
          </MenuTrigger>
          <MenuOptions
            customStyles={{
              optionText: {color: '#333'},
              optionsContainer: {
                backgroundColor: '#f5f5f5',
                borderRadius: 8,
              },
              optionWrapper: {
                padding: 12,
                borderBottomColor: 'rgba(0,0,0,.1)',
                borderBottomWidth: 1,
              },
            }}>
            <MenuOption text="Chia sẻ" onSelect={handleShare} />
          </MenuOptions>
        </Menu>
      )}
    </>
  );
};

export default SupplierDetailHeader;
