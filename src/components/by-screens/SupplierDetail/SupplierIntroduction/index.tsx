import React from 'react';
import {StyleSheet, View} from 'react-native';
import ReadMoreText from '~components/shared/ReadMoreText';
import theme from '~utils/config/themes/theme';
import {ShopV1} from '~utils/types/shop';

type SupplierIntroductionProps = {
  shop?: ShopV1;
};

const SupplierIntroduction: React.FC<SupplierIntroductionProps> = props => {
  return (
    <View style={styles.container}>
      <ReadMoreText
        text={props.shop?.dropship_introduction ?? ''}
        textStyle={{
          color: theme.colors.textLight,
        }}
        numberOfLines={3}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    padding: 12,
    marginBottom: 8,
  },
  text: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
  },
});

export default SupplierIntroduction;
