import {useRoute} from '@react-navigation/native';
import {But<PERSON>} from '@rneui/themed';
import React, {useCallback} from 'react';
import {Platform, StyleSheet, View} from 'react-native';
import {useNavigation} from '~hooks';
import {SortParams} from '~hooks/useSort';
import theme from '~utils/config/themes/theme';
import {ProductSortQuery} from '~utils/types/product';
import Ionicons from 'react-native-vector-icons/Ionicons';

type SortBarProps = {
  sortState: SortParams;
  onChange: (newSort: SortParams) => void;
};

const SortBar: React.FC<SortBarProps> = props => {
  const navigation = useNavigation();
  const route = useRoute();

  const getSelectedColor = useCallback(
    (sortBy: ProductSortQuery) => {
      return props.sortState.sort_by === sortBy ? theme.colors.primary : theme.colors.text;
    },
    [props.sortState.sort_by],
  );

  const getBorderBottomWidth = useCallback(
    (sortBy: ProductSortQuery) => {
      return props.sortState.sort_by === sortBy ? 2 : 0;
    },
    [props.sortState.sort_by],
  );

  const handleSortPress = useCallback(
    (sortBy: ProductSortQuery, ascending?: boolean) => {
      props.onChange({sort_by: sortBy, ascending});
      if (Platform.OS === 'web') {
        navigation.navigate('CategoryDetail', {
          ...route.params,
          sort_by: sortBy,
          ascending: ascending,
        } as any);
      }
    },
    [route.params, props.sortState],
  );
  return (
    <View style={{flex: 1, flexDirection: 'row', backgroundColor: '#fff', paddingTop: 8, paddingBottom: 2, borderBottomColor: 'rgba(0,0,0,.1)', borderBottomWidth: 1}}>
      <Button
        type="clear"
        containerStyle={styles.sortItem}
        title={'Liên quan'}
        buttonStyle={[styles.sortItem_btn, {borderBottomWidth: getBorderBottomWidth('up_at')}]}
        titleStyle={[styles.sortItem_text, {color: getSelectedColor('up_at')}]}
        onPress={() => handleSortPress('up_at')}
      />
      <Button
        containerStyle={styles.sortItem}
        type="clear"
        title={'Mới nhất'}
        buttonStyle={[styles.sortItem_btn, {borderBottomWidth: getBorderBottomWidth('created_at')}]}
        titleStyle={[styles.sortItem_text, {color: getSelectedColor('created_at')}]}
        onPress={() => handleSortPress('created_at')}
      />
      <Button
        containerStyle={styles.sortItem}
        type="clear"
        title={'Bán chạy'}
        buttonStyle={[styles.sortItem_btn, {borderBottomWidth: getBorderBottomWidth('total_sales')}]}
        titleStyle={[styles.sortItem_text, {color: getSelectedColor('total_sales')}]}
        onPress={() => handleSortPress('total_sales')}
      />
      <Button
        type="clear"
        containerStyle={styles.sortItem}
        title={'Lợi nhuận'}
        buttonStyle={[styles.sortItem_btn, {borderBottomWidth: getBorderBottomWidth('dropship_profit')}]}
        titleStyle={[styles.sortItem_text, {color: getSelectedColor('dropship_profit')}]}
        onPress={() => handleSortPress('dropship_profit', typeof props.sortState.ascending !== 'undefined' && props.sortState.sort_by === 'dropship_profit' ? !props.sortState.ascending : false)}
        iconRight
        icon={
          <View style={{marginLeft: 4}}>
            {typeof props.sortState.ascending !== 'undefined' && props.sortState.sort_by === 'dropship_profit' ? (
              <>
                {props.sortState.ascending === true && <Ionicons name="arrow-up-outline" size={18} color={theme.colors.primary} />}
                {props.sortState.ascending === false && <Ionicons name="arrow-down-outline" size={18} color={theme.colors.primary} />}
              </>
            ) : (
              <>
                <Ionicons name="caret-up" size={10} style={{padding: 0, marginBottom: -4}} color={theme.colors.gray60} />
                <Ionicons name="caret-down" size={10} style={{padding: 0}} color={theme.colors.gray60} />
              </>
            )}
          </View>
        }
      />
      <Button
        containerStyle={styles.sortItem}
        type="clear"
        title={'Giá'}
        buttonStyle={[styles.sortItem_btn, {borderBottomWidth: getBorderBottomWidth('dropship_price'), borderRightWidth: 0}]}
        titleStyle={[styles.sortItem_text, {color: getSelectedColor('dropship_price')}]}
        iconRight
        icon={
          <View style={{marginLeft: 4}}>
            {typeof props.sortState.ascending !== 'undefined' && props.sortState.sort_by === 'dropship_price' ? (
              <>
                {props.sortState.ascending === true && <Ionicons name="arrow-up-outline" size={18} color={theme.colors.primary} />}
                {props.sortState.ascending === false && <Ionicons name="arrow-down-outline" size={18} color={theme.colors.primary} />}
              </>
            ) : (
              <>
                <Ionicons name="caret-up" size={10} style={{padding: 0, marginBottom: -4}} color={theme.colors.gray60} />
                <Ionicons name="caret-down" size={10} style={{padding: 0}} color={theme.colors.gray60} />
              </>
            )}
          </View>
        }
        onPress={() => handleSortPress('dropship_price', typeof props.sortState.ascending !== 'undefined' && props.sortState.sort_by === 'dropship_price' ? !props.sortState.ascending : true)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  productItem: {
    flex: 1,
    backgroundColor: '#fff',
    marginHorizontal: 4,
    marginVertical: 8,
    borderRadius: 12,
  },
  sortItem_text: {
    fontSize: 14,
    flexShrink: 0,
    color: theme.colors.text,
  },
  sortItem_btn: {
    paddingHorizontal: 0,
    paddingVertical: 8,
    borderBottomColor: theme.colors.primary,
    borderRadius: 0,
  },
  sortItem: {
    borderRadius: 0,
    flex: 1,
    // paddingHorizontal: 20,
  },
});

export default SortBar;
