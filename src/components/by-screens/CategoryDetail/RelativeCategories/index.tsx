import {ScreenWidth} from '@rneui/base';
import {Text} from '@rneui/themed';
import React, {useMemo} from 'react';
import {Image, Pressable, View} from 'react-native';
import {responsiveWidth} from '~components/shared/web/WebContainer';
import theme from '~utils/config/themes/theme';
import Category from '~utils/types/category';
import {useLinkProps} from '@react-navigation/native';

type RelativeCategoriesProps = {
  relativeCategories: Partial<Category>[];
};

const RelativeCategories: React.FC<RelativeCategoriesProps> = props => {
  return (
    <View style={{flex: 1, flexDirection: 'row', flexWrap: 'wrap', paddingTop: theme.spacing.m, paddingLeft: 8, backgroundColor: '#fff'}}>
      {(props.relativeCategories ?? []).map(c => (
        <RelativeCategoryItem category={c} key={c.id} />
      ))}
    </View>
  );
};

const RelativeCategoryItem = ({category}: {category: Partial<Category>}) => {
  const {onPress, ...linkProps} = useLinkProps({
    href: `/cat/${category.slug}/${category.category_id}`,
    screen: 'CategoryDetail',
    params: {categoryId: category.category_id, slug: category.slug},
  });

  const screenWidth = useMemo(() => {
    return Math.min(ScreenWidth, responsiveWidth.sm);
  }, []);

  return (
    <Pressable onPress={onPress} {...linkProps} style={{padding: 8, backgroundColor: '#fff', marginRight: 8, marginBottom: 8, borderRadius: 12, alignItems: 'center', width: (screenWidth - 60) / 4}}>
      <Image
        style={{width: 50, height: 50, borderRadius: 100}}
        source={{uri: `https://imgcdn.thitruongsi.com/tts/rs:fit:200:200:1:1/g:sm/plain/https://files.thitruongsi.com/assets/category/thumbs/${category.thumb_image_src}`}}
      />
      <Text style={{textAlign: 'center', marginTop: 8, fontSize: theme.typography.sm}}>{category.title}</Text>
    </Pressable>
  );
};

export default RelativeCategories;
