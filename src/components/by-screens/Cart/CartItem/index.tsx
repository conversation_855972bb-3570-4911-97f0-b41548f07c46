import React, {useCallback, useMemo, useState} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {handleImageDomain} from '~utils/helpers/image';
import {formatPriceToString} from '~utils/helpers/price';
import {CartItem as CartItemType} from '~utils/types/cart';
import {Button, CheckBox, Text} from '@rneui/themed';
import {useNavigation} from '~hooks';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {ListItem} from '@rneui/themed';
import {useAddToCartMutation} from '~utils/api/cart';
import {useGetCurrentUser} from '~utils/api/auth';
import triggerHapticFeedback from '~utils/helpers/haptic';
import theme from '~utils/config/themes/theme';
import {FirebaseAnalyticsTypes} from '@react-native-firebase/analytics';
import googleAnalytics from '~utils/helpers/analytics';
import {QuickImage} from '~components/shared/QuickImage';

type CartItemProps = {
  item: CartItemType;
  checked: boolean;
  onCheckedChange: (cartItem: CartItemType, newChecked: boolean) => void;
};

const CartItem: React.FC<CartItemProps> = props => {
  const navigation = useNavigation();
  const [isPriceDetailExpanded, setPriceDetailExpanded] = useState(false);
  const {data: user} = useGetCurrentUser();

  const addToCartMutation = useAddToCartMutation();

  const togglePriceDetailExpand = useCallback(() => {
    setPriceDetailExpanded(prev => !prev);
  }, []);

  const handlePress = useCallback(() => {
    navigation.navigate('ProductDetail', {productId: props.item.product_id});
  }, []);

  const handleRemoveFromCart = useCallback(() => {
    if (props.checked) {
      props.onCheckedChange(props.item, false);
    }

    addToCartMutation.mutateAsync({
      items: [
        {
          note: '',
          dropship_selling_price: props.item.dropship_selling_price,
          quantity: 0,
          variant_id: props.item.variant_id,
        },
      ],
      user_id: user?.id as string,
    });

    // analytics
    const removeFromCartParams: FirebaseAnalyticsTypes.RemoveFromCartEventParameters = {
      currency: 'VND',
      value: props.item.dropship_selling_price * props.item.quantity,
      items: [
        {
          item_name: props.item.product_title,
          item_id: props.item.product_id,
          item_variant: props.item.variant_title,
          price: props.item.dropship_selling_price,
          quantity: props.item.quantity,
          item_category: props.item.product_type?.split(' > ')[0],
          item_category2: props.item.product_type?.split(' > ')[1],
        },
      ],
    };
    googleAnalytics.logRemoveFromCart(removeFromCartParams);
  }, [user, props.item, props.checked, props.onCheckedChange]);

  const isDefaultVariant = useMemo(() => {
    return ['default', 'default title'].includes(props.item.variant_title.toLowerCase());
  }, [props.item]);

  const handleGoToEditCart = useCallback(() => {
    navigation.push('AddToCart', {productId: props.item.product_id, selectedVariantId: props.item.variant_id});
  }, [props.item]);

  return (
    <ListItem.Swipeable
      onTouchStart={e => {
        e.stopPropagation();
      }}
      containerStyle={{paddingHorizontal: 0}}
      rightContent={reset => (
        <Button
          loading={addToCartMutation.isLoading}
          onPress={() => {
            handleRemoveFromCart();
            reset();
          }}
          icon={{name: 'trash-outline', color: 'white', type: 'ionicon'}}
          buttonStyle={{backgroundColor: '#FE2C55'}}
          containerStyle={{alignSelf: 'center', marginTop: 20}}
        />
      )}>
      <ListItem.Content style={{flexDirection: 'row', alignItems: 'flex-start'}}>
        <CheckBox
          size={28}
          onPress={() => {
            props.onCheckedChange(props.item, !props.checked);
            triggerHapticFeedback('rigid');
          }}
          checked={props.checked}
          iconType="ionicon"
          uncheckedIcon="radio-button-off"
          checkedIcon="checkmark-circle"
          checkedColor="#008060"
          containerStyle={{margin: 0, padding: 0, backgroundColor: 'transparent', paddingHorizontal: 12, paddingVertical: 12}}
          wrapperStyle={{padding: 0}}
        />
        <View style={styles.container}>
          <View style={{flexDirection: 'row'}}>
            <TouchableOpacity onPress={handlePress}>
              <QuickImage
                style={styles.thumb}
                source={{
                  uri: handleImageDomain(props.item.image),
                }}
              />
            </TouchableOpacity>
            <View style={{padding: 4, flex: 1}}>
              <TouchableOpacity onPress={handlePress}>
                <Text style={{fontSize: theme.typography.md, marginBottom: 4}} numberOfLines={1}>
                  {props.item.product_title}
                </Text>
                <Text style={{fontSize: theme.typography.base, marginBottom: 4, color: theme.colors.textLight}}>
                  {props.item.options_with_values &&
                    !isDefaultVariant &&
                    Object.keys(props.item.options_with_values)
                      .map(key => `${key}: ${props.item.options_with_values[key]}`)
                      .join(', ')}
                </Text>
              </TouchableOpacity>
              <View style={{marginTop: 'auto', flexDirection: 'row', alignItems: 'center'}}>
                <View style={[styles.flexRow, {alignItems: 'center'}]}>
                  <Text style={{fontSize: theme.typography.md}}>SL: {props.item.quantity}</Text>
                  <Button size="sm" type="clear" titleStyle={{color: theme.colors.blue, fontSize: theme.typography.md}} buttonStyle={{paddingVertical: 0}} onPress={handleGoToEditCart}>
                    Sửa
                  </Button>
                </View>
                <TouchableOpacity onPress={togglePriceDetailExpand} style={{flexDirection: 'row', alignItems: 'center', marginLeft: 'auto', paddingRight: theme.spacing.m}}>
                  <Text style={{marginLeft: 'auto', fontSize: theme.typography.md}}>{formatPriceToString(props.item.dropship_selling_price)}</Text>
                  <Ionicons name={isPriceDetailExpanded ? 'chevron-up-outline' : 'chevron-down-outline'} size={16} color="rgba(0,0,0,.4)" />
                </TouchableOpacity>
              </View>
            </View>
          </View>
          {isPriceDetailExpanded && (
            <View style={[styles.priceDetail]}>
              <View style={[styles.flexRow, {justifyContent: 'space-between'}]}>
                <Text style={[styles.text2, styles.textGray]}>Giá nhà cung cấp</Text>
                <Text style={[styles.text2, styles.textGray]}>{formatPriceToString(props.item.dropship_price)}</Text>
              </View>
              <View style={[styles.flexRow, {justifyContent: 'space-between', marginTop: 4}]}>
                <Text style={[styles.text2, styles.textGray]}>Giá bán của bạn</Text>
                <Text style={[styles.text2]}>{formatPriceToString(props.item.dropship_selling_price)}</Text>
              </View>
              <View style={[styles.flexRow, {justifyContent: 'space-between', marginTop: 4}]}>
                <Text style={[styles.text2, styles.textGray]}>Lợi nhuận</Text>
                <Text style={[styles.text2, {color: '#008060'}]}>{formatPriceToString((props.item.dropship_selling_price - props.item.dropship_price) * props.item.quantity)}</Text>
              </View>
            </View>
          )}
        </View>
      </ListItem.Content>
    </ListItem.Swipeable>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    flex: 1,
  },
  thumb: {
    width: 70,
    height: 70,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,.07)',
    marginRight: 8,
    borderRadius: 8,
  },
  priceDetail: {
    backgroundColor: 'rgba(255, 241, 220,.5)',
    paddingHorizontal: 20,
    paddingVertical: 12,
    marginTop: 8,
    borderRadius: 12,
  },
  flexRow: {
    flexDirection: 'row',
  },
  text2: {
    fontSize: theme.typography.base,
  },
  textGray: {
    color: theme.colors.text,
    marginBottom: 4,
  },
});

export default React.memo(CartItem);
