import React, {useCallback, useMemo} from 'react';
import {Pressable, StyleSheet, View} from 'react-native';
import {CartItem as CartItemType} from '~utils/types/cart';
import CartItem from '../CartItem';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {CheckBox} from '@rneui/base';
import {Text} from '@rneui/themed';
import theme from '~utils/config/themes/theme';
import triggerHapticFeedback from '~utils/helpers/haptic';
import {useNavigation} from '~hooks';

type CartItemByShopProps = {
  shopId: string;
  items: CartItemType[];
  selectedItemIds: string[];
  onCheckedChange: (cartItem: CartItemType, newChecked: boolean) => void;
  onCheckedAllChange: (isCheckedAll: boolean, items: CartItemType[]) => void;
};

const CartItemByShop: React.FC<CartItemByShopProps> = props => {
  const navigation = useNavigation();

  const vendor = useMemo(() => {
    const firstItem = props.items[0];
    if (firstItem) {
      return (
        <Pressable style={[theme.globalStyles.flexRow, {flex: 1, marginRight: theme.spacing.m}]} onPress={() => navigation.navigate('SupplierDetail', {shopId: firstItem.shop_id})}>
          <MaterialCommunityIcons name="storefront-outline" size={24} color={theme.colors.text} />
          <Text style={styles.vendorText}>{firstItem.vendor}</Text>
        </Pressable>
      );
    }
    return null;
  }, [props.items]);

  const selectedItems = useMemo(() => props.items.map(item => props.selectedItemIds.includes(item.variant_id)), [props.items, props.selectedItemIds]);

  const isCheckedAll = useMemo(() => selectedItems.every(checked => checked), [selectedItems]);

  const handleCheckAllPress = useCallback(() => {
    props.onCheckedAllChange(!isCheckedAll, props.items);
    triggerHapticFeedback('rigid');
  }, [isCheckedAll]);

  return (
    <View style={styles.container}>
      <View style={{flexDirection: 'row', alignItems: 'center'}}>
        <CheckBox
          checked={isCheckedAll}
          onPress={handleCheckAllPress}
          size={28}
          iconType="ionicon"
          uncheckedIcon="radio-button-off"
          checkedIcon="checkmark-circle"
          checkedColor="#008060"
          containerStyle={{margin: 0, marginHorizontal: 0, padding: 0, backgroundColor: 'transparent', paddingHorizontal: 12}}
          wrapperStyle={{padding: 0, marginHorizontal: 0}}
        />
        {vendor}
      </View>

      {props.items.map((item, index) => (
        <CartItem item={item} key={item.id} checked={selectedItems[index]} onCheckedChange={props.onCheckedChange} />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    paddingRight: 12,
    paddingVertical: 8,
    marginBottom: theme.spacing.m,
  },
  vendorText: {
    fontSize: 16,
    // fontWeight: '500',
    marginLeft: 8,
    color: theme.colors.text,
  },
});
export default CartItemByShop;
