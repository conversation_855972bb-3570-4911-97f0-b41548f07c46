import React, {useCallback, useEffect} from 'react';
import {Button} from '@rneui/themed';
import theme from '~utils/config/themes/theme';
import {useNavigation} from '~hooks';
import {Animated} from 'react-native';

type CartBackButtonProps = {};

const CartBackButton: React.FC<CartBackButtonProps> = () => {
  const navigation = useNavigation();
  //   slide right animation
  const positionX = React.useRef(new Animated.Value(-30)).current;
  //    opacity animation interpolation
  const opacity = positionX.interpolate({
    inputRange: [-30, -10],
    outputRange: [0.5, 1],
    extrapolate: 'clamp',
  });

  useEffect(() => {
    slideIn();
  }, []);

  const slideIn = useCallback(() => {
    Animated.spring(positionX, {
      toValue: -10,

      //   speed: 300,
      delay: 500,
      speed: 1,
      bounciness: 0.5,
      useNativeDriver: true,
    }).start();
  }, []);

  return (
    <Animated.View
      style={[
        {
          transform: [{translateX: positionX}],
          opacity,
        },
      ]}>
      <Button
        onPress={() => {
          navigation.goBack();
        }}
        icon={{
          type: 'ionicon',
          name: 'chevron-back',
          size: 24,
          color: theme.colors.blue,
        }}
        size="sm"
        buttonStyle={{paddingHorizontal: 0}}
        titleStyle={{color: theme.colors.blue}}
        type="clear">
        Mua thêm
      </Button>
    </Animated.View>
  );
};

export default CartBackButton;
