import React, {useEffect, useRef, useState} from 'react';
import {Animated} from 'react-native';
import theme from '~utils/config/themes/theme';
import {CartItem} from '~utils/types/cart';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {Dialog} from '@rneui/themed';
import {useAddToCartMutation} from '~utils/api/cart';

type CartRemoveButtonProps = {
  selectedCartItems: CartItem[];
  onRemoveSuccess: () => void;
  isCheckout: boolean;
};

const CartRemoveButton: React.FC<CartRemoveButtonProps> = props => {
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const scale = useRef(new Animated.Value(0)).current;
  const mutation = useAddToCartMutation();

  useEffect(() => {
    if (props.selectedCartItems?.length > 0 && !props.isCheckout) {
      Animated.spring(scale, {
        toValue: 1,
        bounciness: 14,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.spring(scale, {
        toValue: 0,
        useNativeDriver: true,
      }).start();
    }
  }, [props.selectedCartItems?.length, props.isCheckout]);

  const showConfirmDialog = () => {
    setConfirmDialogVisible(true);
  };

  const hideConfirmDialog = () => {
    setConfirmDialogVisible(false);
  };
  const handleRemoveFromCart = async () => {
    await mutation.mutateAsync({
      items: props.selectedCartItems.map(item => ({
        quantity: 0,
        dropship_selling_price: item.price,
        variant_id: item.variant_id,
        note: '',
      })),
    });

    props.onRemoveSuccess();

    hideConfirmDialog();
  };

  return (
    <>
      <Animated.View style={[theme.globalStyles.flexRow, {transform: [{scale: scale}]}, {alignSelf: 'flex-start', justifyContent: 'center', marginRight: theme.spacing.m}]}>
        <Ionicons name="trash" size={24} color={theme.colors.red} onPress={showConfirmDialog} />
      </Animated.View>

      <Dialog isVisible={confirmDialogVisible}>
        <Dialog.Title
          title="Xóa khỏi giỏ hàng?"
          titleStyle={{
            color: theme.colors.text,
          }}
        />

        <Dialog.Actions>
          <Dialog.Button title={'XÓA'} titleStyle={{color: theme.colors.primary}} onPress={handleRemoveFromCart} loading={mutation.isLoading} />
          <Dialog.Button title={'TRỞ VỀ'} titleStyle={{color: theme.colors.textLight}} onPress={hideConfirmDialog} onPressIn={hideConfirmDialog} />
        </Dialog.Actions>
      </Dialog>
    </>
  );
};

export default CartRemoveButton;
