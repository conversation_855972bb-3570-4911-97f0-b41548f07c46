import {Button, Dialog, Input, Text} from '@rneui/themed';
import React, {useState} from 'react';
import {View} from 'react-native';
import Card from '~components/shared/Card';
import theme from '~utils/config/themes/theme';

const TaxCodeInput = () => {
  const [isDialogVisible, setIsDialogVisible] = useState(false);

  const handleSubmit = () => {
    setIsDialogVisible(true);
  };

  return (
    <Card title="Mã số thuế" rounded>
      <Text
        style={{
          color: theme.colors.pending,
          fontSize: theme.typography.base,
        }}>
        Chưa có mã số thuế
      </Text>

      <View style={{alignItems: 'flex-start', marginTop: theme.spacing.m}}>
        <Button size="sm" titleStyle={{fontSize: theme.typography.base}} onPress={handleSubmit}>
          Cập nhật
        </Button>
      </View>

      <TaxCodeInputDialog isVisible={isDialogVisible} onClose={() => setIsDialogVisible(false)} onSubmit={() => setIsDialogVisible(false)} />
    </Card>
  );
};

const TaxCodeInputDialog: React.FC<{
  isVisible: boolean;
  onClose: () => void;
  onSubmit: () => void;
}> = ({isVisible, onClose, onSubmit}) => {
  const [taxCode, setTaxCode] = useState('');
  const [errorMsg, setErrorMsg] = useState('');

  const handleSubmit = () => {
    setErrorMsg('');

    if (taxCode.length === 0) {
      setErrorMsg('Vui lòng nhập mã số thuế');
      return;
    }

    onSubmit();
  };

  return (
    <Dialog isVisible={isVisible}>
      <Dialog.Title
        title="Mã số thuế"
        titleStyle={{
          color: theme.colors.text,
          fontWeight: '500',
        }}
      />
      <Input
        containerStyle={{
          paddingHorizontal: 0,
        }}
        placeholder="Vui lòng nhập mã số thuế"
        inputStyle={{fontSize: theme.typography.base, paddingHorizontal: 0}}
        value={taxCode}
        onChangeText={setTaxCode}
        errorMessage={errorMsg}
      />

      <Dialog.Actions>
        <Button title="Cập nhật" onPress={handleSubmit} size="sm" type="clear" titleStyle={{color: theme.colors.primary}} />
        <Button title="Trở về" onPress={onClose} size="sm" containerStyle={{marginRight: theme.spacing.s}} type="clear" titleStyle={{color: theme.colors.gray70}} />
      </Dialog.Actions>
    </Dialog>
  );
};

export default TaxCodeInput;
