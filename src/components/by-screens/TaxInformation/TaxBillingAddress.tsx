import {Button, Dialog, Input, Text} from '@rneui/themed';
import React, {useState} from 'react';
import {View} from 'react-native';
import Card from '~components/shared/Card';
import theme from '~utils/config/themes/theme';

const TaxBillingAddress = () => {
  const [isDialogVisible, setIsDialogVisible] = useState(false);

  return (
    <Card title="Địa chỉ nhận hóa đơn" rounded>
      <Text style={{color: theme.colors.pending, fontSize: theme.typography.base}}>Chưa có địa chỉ</Text>
      {/* write more */}

      <View style={{alignItems: 'flex-start', marginTop: theme.spacing.m}}>
        <Button size="sm" titleStyle={{fontSize: theme.typography.base}} onPress={() => setIsDialogVisible(true)}>
          Cập nhật
        </Button>
      </View>

      <TaxBillingAddressDialog isVisible={isDialogVisible} onClose={() => setIsDialogVisible(false)} onSubmit={() => setIsDialogVisible(false)} />
    </Card>
  );
};

const TaxBillingAddressDialog: React.FC<{
  isVisible: boolean;
  onClose: () => void;
  onSubmit: () => void;
}> = ({isVisible, onClose, onSubmit}) => {
  const [address, setAddress] = useState('');
  const [zipcode, setZipcode] = useState('');
  const [addressError, setAddressError] = useState('');
  const [zipcodeError, setZipcodeError] = useState('');

  const handleSubmit = () => {
    setAddressError('');
    setZipcodeError('');

    if (address.length === 0) {
      setAddressError('Vui lòng nhập địa chỉ');
      return;
    }
    if (zipcode.length === 0) {
      setZipcodeError('Vui lòng nhập zipcode');
      return;
    }
    onSubmit();
  };

  return (
    <Dialog isVisible={isVisible}>
      <Dialog.Title
        title="Địa chỉ nhận hóa đơn"
        titleStyle={{
          color: theme.colors.text,
          fontWeight: '500',
          fontSize: theme.typography.md,
        }}
      />
      <Input
        containerStyle={{
          paddingHorizontal: 0,
        }}
        label="Địa chỉ"
        labelStyle={{color: theme.colors.text, fontSize: theme.typography.base}}
        placeholder="Vui lòng nhập địa chỉ"
        inputStyle={{fontSize: theme.typography.base, paddingHorizontal: 0}}
        value={address}
        onChangeText={setAddress}
        errorMessage={addressError}
      />
      <Input
        containerStyle={{
          paddingHorizontal: 0,
        }}
        label="Zipcode"
        keyboardType="numeric"
        placeholder="Ví dụ: 700000"
        inputStyle={{fontSize: theme.typography.base, paddingHorizontal: 0}}
        value={zipcode}
        onChangeText={setZipcode}
        errorMessage={zipcodeError}
        labelStyle={{color: theme.colors.text, fontSize: theme.typography.base}}
      />

      <Dialog.Actions>
        <Button title="Cập nhật" onPress={handleSubmit} size="sm" type="clear" titleStyle={{color: theme.colors.primary}} />
        <Button title="Trở về" onPress={onClose} size="sm" containerStyle={{marginRight: theme.spacing.s}} type="clear" titleStyle={{color: theme.colors.gray70}} />
      </Dialog.Actions>
    </Dialog>
  );
};

export default TaxBillingAddress;
