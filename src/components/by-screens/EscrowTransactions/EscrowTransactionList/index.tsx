import {FlashList, ListRenderItemInfo} from '@shopify/flash-list';
import React, {useCallback, useMemo, useState} from 'react';
import {ActivityIndicator, StyleSheet, View} from 'react-native';
import {useGetEscrowTransactions} from '~utils/api/wallet';
import {EscrowTransaction} from '~utils/types/finance';
import EscrowTransactionItem from '~components/by-screens/AccountAndPayment/EscrowTransactionItem';
import theme from '~utils/config/themes/theme';
import EndReachedIndicator from '~components/shared/EndReachedIndicator';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {Text} from '@rneui/themed';

const EscrowTransactionList = () => {
  const {data, fetchNextPage, isFetchingNextPage, hasNextPage, isLoading} = useGetEscrowTransactions({
    status: ['order_created', 'payment_received'],
    limit: 20,
    page: 1,
    type: 'commission',
  });
  const [showEndReached, setShowEndReached] = useState(false);

  const dataFlat = useMemo(() => {
    return data?.pages.flatMap(page => page.transactions) ?? [];
  }, [data]);

  const renderItem = useCallback(
    (item: ListRenderItemInfo<EscrowTransaction>) => {
      return (
        <View style={styles.item}>
          <EscrowTransactionItem transaction={item.item} />
        </View>
      );
    },
    [dataFlat],
  );
  const handleEndReached = useCallback(() => {
    if (!hasNextPage) {
      setShowEndReached(true);
      return;
    }
    if (!isFetchingNextPage) {
      fetchNextPage();
    }
  }, [fetchNextPage, isFetchingNextPage, hasNextPage]);

  return (
    <FlashList
      renderItem={renderItem}
      data={dataFlat}
      estimatedItemSize={91}
      onEndReached={handleEndReached}
      ListFooterComponent={<>{showEndReached && dataFlat.length >= 10 && <EndReachedIndicator />}</>}
      ListEmptyComponent={
        isLoading ? (
          <ActivityIndicator />
        ) : (
          <View style={{marginVertical: theme.spacing.xl, alignItems: 'center'}}>
            <Ionicons name="document-text-outline" size={60} color={theme.colors.textLightest} />
            <Text style={{textAlign: 'center', marginTop: theme.spacing.s, color: theme.colors.textLightest}}>Chưa có giao dịch nào</Text>
          </View>
        )
      }
    />
  );
};

const styles = StyleSheet.create({
  item: {
    backgroundColor: theme.colors.white,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
});

export default EscrowTransactionList;
