import {Button, Input} from '@rneui/themed';
import React, {useCallback, useState} from 'react';
import {View} from 'react-native';
import {useToast} from 'react-native-toast-notifications';
import Card from '~components/shared/Card';
import {useUpdateUserInfoMutation} from '~utils/api/auth';
import theme from '~utils/config/themes/theme';
import {passwordChangeValidate} from '~utils/validate/common';

const PasswordChanger = () => {
  const updateUserInfoMutation = useUpdateUserInfoMutation();
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [newPasswordConfirm, setNewPasswordConfirm] = useState('');
  const [errors, setErrors] = useState<any>({});

  const [currentPasswordVisibility, setCurrentPasswordVisibility] = useState(false);
  const [newPasswordVisibility, setNewPasswordVisibility] = useState(false);
  const [newPasswordConfirmVisibility, setNewPasswordConfirmVisibility] = useState(false);
  const toast = useToast();

  const handleSubmit = useCallback(async () => {
    try {
      setErrors({});
      let isValid = true;

      await passwordChangeValidate
        .validate(
          {
            old_password: currentPassword,
            password: newPassword,
            confirm_password: newPasswordConfirm,
          },
          {abortEarly: false},
        )
        .catch(err => {
          isValid = false;
          setErrors(err.inner.reduce((a: any, c: any) => ({...a, [c.path]: c.message}), {}));
        });

      if (isValid) {
        await updateUserInfoMutation
          .mutateAsync({
            old_password: currentPassword,
            password: newPassword,
            confirm_password: newPasswordConfirm,
          })
          .catch(err => {
            if (err?.response?.data.error === 'Mật khẩu cũ không hợp lệ.') {
              setErrors({
                old_password: err.response.data.message,
              });
            }
            throw err;
          });

        toast.show('Đổi mật khẩu thành công', {
          placement: 'bottom',
          duration: 1000,
          type: 'success',
        });
        setCurrentPassword('');
        setNewPassword('');
        setNewPasswordConfirm('');
      }
    } catch (error) {}
  }, [currentPassword, newPassword, newPasswordConfirm]);

  const toggleCurrentPasswordVisibility = useCallback(() => {
    setCurrentPasswordVisibility(prev => !prev);
  }, []);

  const toggleNewPasswordVisibility = useCallback(() => {
    setNewPasswordVisibility(prev => !prev);
  }, []);

  const toggleNewPasswordConfirmVisibility = useCallback(() => {
    setNewPasswordConfirmVisibility(prev => !prev);
  }, []);

  return (
    <Card title="Thay đổi mật khẩu" rounded>
      <Input
        label="Mật khẩu cũ"
        containerStyle={{marginBottom: theme.spacing.m}}
        value={currentPassword}
        onChangeText={setCurrentPassword}
        errorMessage={errors.old_password}
        autoComplete="off"
        secureTextEntry={!currentPasswordVisibility}
        rightIcon={{
          type: 'ionicon',
          name: currentPasswordVisibility ? 'eye-off' : 'eye',
          size: 20,
          color: theme.colors.textLight,
          onPress: toggleCurrentPasswordVisibility,
        }}
      />
      <Input
        label="Mật khẩu mới"
        containerStyle={{marginBottom: theme.spacing.m}}
        value={newPassword}
        onChangeText={setNewPassword}
        errorMessage={errors.password}
        rightIcon={{
          type: 'ionicon',
          name: newPasswordVisibility ? 'eye-off' : 'eye',
          size: 20,
          color: theme.colors.textLight,
          onPress: toggleNewPasswordVisibility,
        }}
        secureTextEntry={!newPasswordVisibility}
        autoComplete="off"
      />
      <Input
        label="Nhập lại mật khẩu"
        value={newPasswordConfirm}
        onChangeText={setNewPasswordConfirm}
        errorMessage={errors.confirm_password}
        rightIcon={{
          type: 'ionicon',
          name: newPasswordConfirmVisibility ? 'eye-off' : 'eye',
          size: 20,
          color: theme.colors.textLight,
          onPress: toggleNewPasswordConfirmVisibility,
        }}
        secureTextEntry={!newPasswordConfirmVisibility}
        autoComplete="off"
      />

      <View style={[theme.globalStyles.flexRow, {marginTop: theme.spacing.l, justifyContent: 'flex-end', marginRight: 8}]}>
        <Button size="sm" buttonStyle={{paddingHorizontal: 40, borderRadius: 30}} disabled={!newPasswordConfirm} onPress={handleSubmit} loading={updateUserInfoMutation.isLoading}>
          Lưu
        </Button>
      </View>
    </Card>
  );
};

export default PasswordChanger;
