import {ScreenWidth} from '@rneui/base';
import React, {useMemo} from 'react';
import {StyleSheet, Text, View} from 'react-native';
// @ts-ignore
import WhyChooseUsPNG from '~/assets/cpgfresh/whychooseus.png';
import {QuickImage} from '~components/shared/QuickImage';
import {responsiveWidth} from '~components/shared/web/WebContainer';
import {useIsMobile} from '~hooks';
import theme from '~utils/config/themes/theme';
import {PureCTA} from '../CallToAction';

const WhyChooseUs = () => {
  const isMobile = useIsMobile();

  const maxWidth = Math.min(ScreenWidth, responsiveWidth.lg);
  const maxImageWidth = useMemo(() => {
    return isMobile ? maxWidth : maxWidth / 2;
  }, []);

  return (
    <View style={styles.container}>
      <Text style={styles.headerTitle}>4 Lý Do <PERSON>ạn <PERSON>ê<PERSON> Trở Thành Cộng Tác Viên <PERSON>ô<PERSON> Na<PERSON>!</Text>

      <View style={{flexDirection: isMobile ? 'column' : 'row', flexWrap: 'wrap'}}>
        <QuickImage
          source={WhyChooseUsPNG}
          style={{
            width: maxImageWidth - theme.spacing.m * 2,
            height: (maxImageWidth - theme.spacing.m * 2) * 1.49,
            alignSelf: 'center',
            marginTop: theme.spacing.m,
            flexShrink: 0,
            marginRight: isMobile ? 0 : theme.spacing.m,
          }}
        />
        <View style={{flex: 1}}>
          <FeatureBlock headerText="1. Sản phẩm chất lượng" contentText={'Sản phẩm CPG được đảm bảo về chất lượng cao, hiệu quả và được chứng minh lâm sàng. Giúp bạn tự tin để kinh doanh lâu dài.'} />
          <FeatureBlock
            headerText="2. Được đào tạo và hỗ trợ kinh doanh"
            contentText={
              'TTS Dropship sẽ thường xuyên tổ chức các khóa đào tạo về kỹ năng bán hàng, kiến thức sản phẩm. Ngoài ra bạn còn được cập nhật các chương trình khuyến mại từ nhãn hàng để bán hàng dễ dàng hơn.'
            }
          />
          <FeatureBlock headerText="3. Hoa hồng và phúc lợi cao" contentText={'Sản phẩm trên TTS Dropship sẽ được chiết khấu cao, thường xuyên có các chương trình thưởng.'} />

          {!isMobile && (
            <View style={{alignSelf: 'center', marginTop: theme.spacing.m}}>
              <PureCTA />
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

const FeatureBlock: React.FC<{headerText: string; contentText: string}> = props => {
  return (
    <View style={styles.featureBlockContainer}>
      <Text style={styles.featureBlockHeaderText}>{props.headerText}</Text>
      <Text style={styles.featureBlockContentText}>{props.contentText}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 42,
    paddingHorizontal: theme.spacing.m,
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: '600',
    fontFamily: `"Lexend Deca", sans-serif`,
    color: '#23A00B',
    textAlign: 'center',
  },

  featureBlockContainer: {
    padding: 15,
    borderLeftColor: '#23A00B',
    borderLeftWidth: 2,
    marginTop: theme.spacing.m,
  },
  featureBlockHeaderText: {
    fontSize: 20,
    fontWeight: '600',
    fontFamily: `"Lexend Deca", sans-serif`,
    color: '#181725',
  },
  featureBlockContentText: {
    fontSize: 14,
    fontFamily: `"Lexend Deca", sans-serif`,
    color: '#454F5B',
    marginTop: theme.spacing.s,
  },
});

export default WhyChooseUs;
