import {useLinkProps} from '@react-navigation/native';
import {Text, ThemeContext} from '@rneui/themed';
import React, {useCallback, useMemo} from 'react';
import {StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import SwiperFlatList from 'react-native-swiper-flatlist';

// @ts-ignore
import AppleProduct1 from '~/assets/cpgfresh/apple-product1.png';
import {useGetSmartCollection} from '~utils/api/product';
import ProductCard from '~components/shared/ProductCard';
import {ScreenWidth} from '@rneui/base';
import {responsiveWidth} from '~components/shared/web/WebContainer';

const FeaturedProducts = () => {
  const {onPress, ...linkProps} = useLinkProps({to: `/supplier/wholefoodslink`});
  const {data} = useGetSmartCollection('cpg-fresh', {limit: 6});

  const dataFlat = useMemo(() => {
    return data?.pages?.flatMap(page => page.products) ?? [];
  }, [data]);

  const itemWidth = useMemo(() => {
    return Math.min(ScreenWidth, responsiveWidth.sm) / 2.4;
  }, []);

  const getItemLayout = useCallback(
    (__data: any, itemIndex: number) => {
      return {
        length: itemWidth,
        offset: itemWidth * itemIndex,
        index: itemIndex,
      };
    },
    [itemWidth],
  );

  const renderItem = useCallback(({item}: any) => {
    console.log(item);
    return (
      <View
        style={{
          width: itemWidth,
          marginTop: theme.spacing.m,
          borderWidth: 1,
          borderColor: theme.colors.gray20,
          backgroundColor: theme.colors.white,
          marginLeft: theme.spacing.m,
        }}>
        <ProductCard product={item} showPriceOnGuest hideProvince />
      </View>
    );
  }, []);

  return (
    <View style={styles.container}>
      <View style={{paddingHorizontal: 12}}>
        <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingBottom: 12, borderBottomWidth: 1, borderBottomColor: '#5EB14E'}}>
          <Text style={styles.headerTitle}>Sản phẩm</Text>
          <View>
            <Text {...linkProps} style={styles.headerViewMore}>
              Xem tất cả
            </Text>
          </View>
        </View>
      </View>
      {/*  */}

      <SwiperFlatList
        getItemLayout={getItemLayout}
        data={dataFlat}
        renderItem={renderItem}
        style={{flexGrow: 0}}
        showPagination
        paginationDefaultColor={theme.colors.gray60}
        paginationStyleItemActive={{
          width: 16,
        }}
        paginationActiveColor="#76B70D"
        paginationStyle={{
          marginBottom: theme.spacing.l * 2,
        }}
        paginationStyleItem={{
          width: 6,
          height: 6,
          marginHorizontal: 2,
        }}
      />
    </View>
  );
};

const FeatureProduct = () => {
  return (
    <View style={styles.container}>
      <View style={{paddingHorizontal: 12}}>
        <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingBottom: 12, borderBottomWidth: 1, borderBottomColor: '#5EB14E'}}>
          <Text style={styles.headerTitle}>Sản phẩm</Text>

          <View>
            <Text style={styles.headerViewMore}>Xem tất cả</Text>
          </View>
        </View>
      </View>
      {/*  */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    minHeight: 500,
    backgroundColor: '#FFFDE7',
    paddingVertical: 24,
  },
  headerTitle: {
    color: '#212B36',
    fontWeight: '600',
    fontSize: 20,
    fontFamily: `"Lexend Deca", sans-serif`,
  },
  headerViewMore: {
    color: '#5EB14E',
    fontWeight: '500',
    fontSize: theme.typography.md,
    padding: 2,
    paddingHorizontal: 4,
  },
});

export default FeaturedProducts;
