import {Text} from '@rneui/themed';
import React, {useMemo} from 'react';
import {StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';
// @ts-ignore
import WholeFoodsLinkFresh from '~/assets/cpgfresh/wholefoodslink-fresh.png';
import {QuickImage} from '~components/shared/QuickImage';
import {ScreenWidth} from '@rneui/base';
import {responsiveWidth} from '~components/shared/web/WebContainer';
import {useIsMobile} from '~hooks';

const SupplierProfile = () => {
  const isMobile = useIsMobile();
  const maxWidth = useMemo(() => {
    return Math.min(ScreenWidth, responsiveWidth.lg);
  }, []);

  const maxImageWidth = isMobile ? maxWidth - theme.spacing.m * 2 : maxWidth / 2 - theme.spacing.m * 2;

  return (
    <View style={styles.container}>
      <Text style={styles.headerTitle}>Về WFL Fresh - Trái cây nhập khẩu từ Úc</Text>
      <View style={{alignSelf: 'center', flexDirection: 'row', flexWrap: 'wrap'}}>
        <QuickImage source={WholeFoodsLinkFresh} style={{width: maxImageWidth, height: maxImageWidth}} />

        <Text style={styles.text}>
          WFL Fresh không chỉ đơn thuần là một thương hiệu trái cây, mà còn là một cam kết về sự tận hưởng và sự khám phá. Chúng tôi mang đến cho bạn những loại trái cây đa dạng, từ cam, quýt, lê,
          táo, đến dứa, nho, mận và nhiều hơn thế nữa.
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  headerTitle: {
    textAlign: 'center',
    fontSize: 32,
    fontWeight: '600',
    fontFamily: `"Lexend Deca", sans-serif`,
    color: theme.colors.white,
    marginBottom: theme.spacing.l,
  },
  container: {
    backgroundColor: '#5EB14E',
    paddingVertical: 32,
    paddingHorizontal: theme.spacing.m,
  },
  text: {
    marginTop: theme.spacing.l,
    textAlign: 'center',
    color: theme.colors.white,
    fontFamily: `"Lexend Deca", sans-serif`,
    fontSize: theme.typography.md,
    flex: 1,
  },
});

export default SupplierProfile;
