import {ScreenWidth} from '@rneui/base';
import {Button} from '@rneui/themed';
import React from 'react';
import {View} from 'react-native';
import {responsiveWidth} from '~components/shared/web/WebContainer';
import {useIsMobile} from '~hooks';
import theme from '~utils/config/themes/theme';

export const PureCTA = () => {
  const isMobile = useIsMobile();
  const maxWidth = isMobile ? ScreenWidth : 350;
  return (
    <Button
      titleStyle={{
        fontSize: theme.typography.md,
        fontFamily: `"Lexend Deca", sans-serif`,
      }}
      buttonStyle={{backgroundColor: '#5EB14E', borderRadius: 100, maxWidth: maxWidth}}>
      Bắt Đầu Bán Hàng
    </Button>
  );
};

const CallToAction = () => {
  const isMobile = useIsMobile();

  if (!isMobile) {
    return null;
  }

  return (
    <>
      <View style={{height: 100}} />
      {/* @ts-ignore */}
      <View style={{position: 'fixed', bottom: 0, left: 0, right: 0, paddingHorizontal: 15, backgroundColor: theme.colors.white, paddingVertical: 12}}>
        <PureCTA />
      </View>
    </>
  );
};

export default CallToAction;
