import {Text} from '@rneui/themed';
import React, {useCallback, useMemo} from 'react';
import {StyleSheet, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import theme from '~utils/config/themes/theme';
import SwiperFlatList from 'react-native-swiper-flatlist';

import {ListRenderItemInfo} from 'react-native';
import {QuickImage} from '~components/shared/QuickImage';
import {ScreenWidth} from '@rneui/base';
import {responsiveWidth} from '~components/shared/web/WebContainer';
import {useIsMobile} from '~hooks';
import {PureCTA} from '../CallToAction';

// @ts-ignore
import AppleTopSlider from '~assets/cpgfresh/apple-top-slider-1.jpg';
// @ts-ignore
import CherryTopSlider from '~assets/cpgfresh/cherry-top-slider.jpg';
// @ts-ignore
import PearTopSlider from '~assets/cpgfresh/pear-top-slider.jpg';

const AppleIntroduce = () => {
  const isMobile = useIsMobile();

  const data = useMemo(() => {
    return [
      {
        source: AppleTopSlider,
      },
      {
        source: CherryTopSlider,
      },
      {
        source: PearTopSlider,
      },
    ];
  }, []);

  const maxImageWidth = useMemo(() => {
    return Math.min(ScreenWidth, responsiveWidth.lg);
  }, []);

  const imageWidth = useMemo(() => {
    return maxImageWidth - 24;
  }, [maxImageWidth]);

  const renderItem = useCallback(({item}: ListRenderItemInfo<any>) => {
    return <QuickImage source={item.source} style={{width: imageWidth * 0.8, height: imageWidth * 0.8 * 0.4, borderRadius: 12, marginRight: theme.spacing.s}} />;
  }, []);

  const getItemLayout = useCallback(
    (__data: any, itemIndex: number) => {
      return {
        length: imageWidth,
        offset: imageWidth * itemIndex,
        index: itemIndex,
      };
    },
    [imageWidth],
  );

  return (
    <View style={{backgroundColor: '#76B70D'}}>
      <LinearGradient locations={[0.52, 0.81]} style={{flex: 1, paddingBottom: theme.spacing.l * 2}} colors={['#76B70D', '#FBF6C5']}>
        <View style={styles.headerTitleContainer}>
          <Text style={styles.headerTitle}>Trái cây sạch{isMobile ? '\n' : ' - '} Tươi Ngon Từ Vườn</Text>
          <Text style={styles.subTitle}>Trái cây nhập khẩu cao cấp từ Hàn Quốc, Úc, Mỹ, NewZealand.</Text>
        </View>

        {isMobile ? (
          <SwiperFlatList
            getItemLayout={getItemLayout}
            data={data}
            renderItem={renderItem}
            showPagination
            paginationDefaultColor={theme.colors.gray60}
            paginationStyleItemActive={{
              width: 16,
            }}
            paginationActiveColor="#76B70D"
            paginationStyle={
              {
                // marginBottom: -theme.spacing.m * 2,
              }
            }
            paginationStyleItem={{
              width: 6,
              height: 6,
              marginHorizontal: 2,
            }}
          />
        ) : (
          <View style={{alignSelf: 'center'}}>
            <QuickImage source={AppleTopSlider} style={{width: imageWidth + 24 - 12, height: imageWidth * 0.4, borderRadius: 4}} />
            <View style={{flexDirection: 'row', alignItems: 'center', marginTop: theme.spacing.s}}>
              <QuickImage source={CherryTopSlider} style={{width: imageWidth * 0.5, height: imageWidth * 0.5 * 0.4, borderRadius: 4, marginRight: theme.spacing.s}} />
              <QuickImage source={PearTopSlider} style={{width: imageWidth * 0.5, height: imageWidth * 0.5 * 0.4, borderRadius: 4}} />
            </View>
          </View>
        )}

        {!isMobile && (
          <View style={{alignSelf: 'center', marginTop: theme.spacing.m}}>
            <PureCTA />
          </View>
        )}
      </LinearGradient>

      <View style={{flexDirection: 'row', flexWrap: 'wrap'}}>
        <ProductSpecial
          // imageSource={Apple4}
          imageSource={{uri: 'https://newretailcpg.vn/wp-content/uploads/2024/05/Artboard-1d-1024x683.png'}}
          text="CAM NAVEL ÚC KHÔNG HẠT"
          subText={`🍃Chưa bao giờ mua Cam Úc chất lượng lại dễ dàng đến thế. Cam vỏ mỏng mọng đến từng tép, ngon ngọt mà giá lại cực kì tốt${'\n'}🍃Cam Navel có vị ngọt và thơm – hương vị rất đặc trưng & riêng biệt của loại quả này.${'\n'}🍃Siêu nhiều Vitamin C, chỉ cần ăn một quả cũng đáp ứng được đầy đủ nhu cầu vitamin C hàng ngày.`}
        />
        <ProductSpecial
          // imageSource={Apple5}
          imageSource={{uri: 'https://newretailcpg.vn/wp-content/uploads/2024/05/Artboard-1a-1024x683.png'}}
          text="CHERRY TASMANIA ÚC"
          // subText="Táo Organic Juliet Pháp túi 1kg là một loại táo hữu cơ được canh tác hoàn toàn theo phương pháp hữu cơ. Với hình dáng tròn bầu xinh xắn, vỏ táo màu đỏ sậm trên nền vàng nhạt, táo Juliet trông rất đẹp mắt."
          subText={`🍃Cherry Tasmania ngon ngọt, mọng nước vừa cập bến CPG Fresh cho mọi khách yêu thả ga lựa chọn.${'\n'}🍃Tươi xanh cuốn cành, cực đẹp, trái cứng giòn ngọt RẤT ĐẬM VỊ. Thùng 1kg, 2kg, hoặc 5kg — SIÊU NGON.${'\n'}🍃Ngoài ra, quả cherry chứa nhiều vitamin và chất chống oxi hóa, rất tốt cho trẻ nhỏ, người già và phụ nữ. Đặc biệt là nó rất thích hợp với người bị bệnh tiểu đường.
          `}
        />
        <ProductSpecial
          // imageSource={Apple5}
          imageSource={{uri: 'https://newretailcpg.vn/wp-content/uploads/2024/05/Artboard-1b-1024x683.png'}}
          text="TÁO STORY PHÁP"
          // subText="Táo Organic Juliet Pháp túi 1kg là một loại táo hữu cơ được canh tác hoàn toàn theo phương pháp hữu cơ. Với hình dáng tròn bầu xinh xắn, vỏ táo màu đỏ sậm trên nền vàng nhạt, táo Juliet trông rất đẹp mắt."
          subText={`🍃Hòa mình vào câu chuyện đầy mê hoặc của Táo Story, một kiệt tác thiên nhiên thực sự.\n🍃Được trồng trong những vườn cây táo tuyệt đẹp của Pháp, những trái táo này là biểu tượng cho di sản phong phú và sự khéo léo tỉ mỉ trong việc trồng trái cây của người Pháp.`}
        />
        <ProductSpecial
          // imageSource={Apple5}
          imageSource={{uri: 'https://newretailcpg.vn/wp-content/uploads/2024/05/Artboard-1c-1024x683.png'}}
          text="NHO MẪU ĐƠN HÀN QUỐC"
          // subText="Táo Organic Juliet Pháp túi 1kg là một loại táo hữu cơ được canh tác hoàn toàn theo phương pháp hữu cơ. Với hình dáng tròn bầu xinh xắn, vỏ táo màu đỏ sậm trên nền vàng nhạt, táo Juliet trông rất đẹp mắt."
          subText={`🍃Nho nhập khẩu trực tiếp từ các vườn nho ở vùng Sangju và Gimcheon của Hàn Quốc.\n🍃Được trồng bằng phương pháp hữu cơ và canh tác hoàn toàn trong nhà kính, đảm bảo an toàn và chất lượng.`}
        />
      </View>
    </View>
  );
};

const ProductSpecial: React.FC<{imageSource?: any; text?: string; subText?: string}> = props => {
  const isMobile = useIsMobile();
  const maxWidth = Math.min(ScreenWidth, responsiveWidth.lg);
  const maxImageWidth = isMobile ? maxWidth : maxWidth / 2;
  return (
    <View style={{backgroundColor: theme.colors.white, maxWidth: maxImageWidth, paddingBottom: theme.spacing.m}}>
      <View style={{paddingHorizontal: 15, paddingVertical: 12}}>
        <QuickImage source={props.imageSource} style={{width: maxImageWidth - 30, height: (maxImageWidth - 15) * 0.6}} />
        <View style={{padding: 16}}>
          <Text style={styles.text}>{props.text}</Text>
          <Text style={styles.subText}>{props.subText}</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  headerTitleContainer: {
    padding: theme.spacing.m,
    marginTop: 104,
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: '500',
    color: theme.colors.white,
    fontFamily: `"Lexend Deca", sans-serif`,
    textAlign: 'center',
  },
  subTitle: {
    fontSize: 20,
    color: theme.colors.white,
    fontFamily: `"Lexend Deca", sans-serif`,
    textAlign: 'center',
    fontWeight: '300',
    marginTop: theme.spacing.s,
  },
  text: {
    fontSize: 20,
    color: '#212B36',
    fontFamily: `"Lexend Deca", sans-serif`,
    textAlign: 'center',
    fontWeight: '500',
    marginTop: theme.spacing.s,
  },
  subText: {
    fontSize: theme.typography.base,
    color: '#454F5B',
    fontFamily: `"Lexend Deca", sans-serif`,
    // textAlign: 'center',
    marginTop: theme.spacing.s,
  },
});

export default AppleIntroduce;
