import {Button, Text} from '@rneui/themed';
import React, {useCallback} from 'react';
import {Linking, View} from 'react-native';
// @ts-ignore
import FacebookIcon from '~assets/facebook.png';
import {QuickImage} from '~components/shared/QuickImage';
import theme from '~utils/config/themes/theme';

const FacebookGroupInvite = () => {
  const handleJoinFacebookGroup = useCallback(() => {
    Linking.openURL('https://www.facebook.com/groups/ttsdropship');
  }, []);

  return (
    <View style={{marginTop: 0}}>
      <View style={{flexDirection: 'row', backgroundColor: theme.colors.white, padding: theme.spacing.m - 4, borderRadius: 12, alignItems: 'center'}}>
        <QuickImage source={FacebookIcon} style={{width: 30, height: 30, borderRadius: 100, marginRight: theme.spacing.s}} />
        <View style={{flex: 1, flexDirection: 'row', alignItems: 'center'}}>
          <Text style={{flex: 1, fontSize: theme.typography.base}}>Lập nghiệp cùng TTS Dropship (Official Group)</Text>
          <Button
            size="sm"
            titleStyle={{fontSize: theme.typography.base, fontWeight: '600'}}
            buttonStyle={{backgroundColor: '#0866ff', borderRadius: 8, paddingVertical: 4}}
            containerStyle={{alignSelf: 'flex-end'}}
            onPress={handleJoinFacebookGroup}>
            Tham gia
          </Button>
        </View>
      </View>
      <Text style={{marginTop: theme.spacing.s, fontSize: theme.typography.sm}}>Vào nhóm Facebook để giao lưu, học hỏi từ các cộng tác viên trên toàn quốc.</Text>
    </View>
  );
};

export default FacebookGroupInvite;
