import React, {useCallback, useEffect, useState} from 'react';
import {Platform, View} from 'react-native';
import AppRating from '~components/shared/AppRating';
import {useNavigation} from '~hooks';
import {handleRateApp} from '~utils/helpers/common';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {ASYNC_STORAGE_KEYS} from '~utils/config';
import Animated, {SlideInDown} from 'react-native-reanimated';
import theme from '~utils/config/themes/theme';
import {createFeedback} from '~utils/api/analytics';

const InAppRating = () => {
  const [showRate, setShowRate] = useState(false);
  const [isSuccess, setSuccess] = useState(false);
  const navigation = useNavigation();

  useEffect(() => {
    checkShowRateApp();
  }, []);

  const checkShowRateApp = useCallback(async () => {
    try {
      // eslint-disable-next-line no-extra-boolean-cast
      setShowRate(!Boolean(await AsyncStorage.getItem(ASYNC_STORAGE_KEYS.APP_RATED)));
    } catch (error) {}
  }, []);

  const handleRate = useCallback((star: number) => {
    if (star <= 3) {
      setTimeout(() => {
        navigation.navigate('Feedback', {initialStar: star});
      }, 300);
    } else if (star === 5 && Platform.OS !== 'web') {
      handleRateApp();
    }
    setShowRate(prev => !prev);
    setSuccess(true);
    AsyncStorage.setItem(ASYNC_STORAGE_KEYS.APP_RATED, String(star));
    createFeedback({
      features: 'dropship-app',
      response: star,
      feedback: '',
    });
  }, []);

  return (
    <>
      {showRate && (
        <View>
          <AppRating horizontal onChange={handleRate} title="Đánh giá ứng dụng" showRating={false} />
        </View>
      )}
      {isSuccess && (
        <Animated.Text style={{marginTop: theme.spacing.m}} entering={SlideInDown}>
          Cảm ơn bạn đã đánh giá ứng dụng!. Chúng tôi sẽ liên tục ghi nhận ý kiến đánh giá và cải tiến ứng dụng để phục vụ bạn tốt hơn
        </Animated.Text>
      )}
    </>
  );
};

export default InAppRating;
