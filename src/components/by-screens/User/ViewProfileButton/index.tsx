import {Button} from '@rneui/themed';
import React, {useCallback, useMemo} from 'react';
import {useNavigation} from '~hooks';
import {useGetUserDropshipProfile} from '~utils/api/auth';
import theme from '~utils/config/themes/theme';

const ViewProfileButton = () => {
  const navigation = useNavigation();
  const {data} = useGetUserDropshipProfile();

  const goToUserSettings = useCallback(() => {
    navigation.navigate('AccountInfoSettings');
  }, []);

  const isImpressedProfile = useMemo(() => {
    let rank = data?.rank ?? 0;
    return rank > 0 && rank < 200;
  }, [data]);

  return (
    <Button
      title={isImpressedProfile ? `🌟 Hồ sơ top ${data?.rank} ` : 'Xem hồ sơ của bạn'}
      type="clear"
      buttonStyle={[{padding: 0, paddingHorizontal: 0, paddingVertical: 4}, isImpressedProfile && {backgroundColor: theme.colors.white, paddingHorizontal: 4, paddingVertical: 1, borderRadius: 100}]}
      titleStyle={[{fontSize: theme.typography.base, color: theme.colors.textLight}, isImpressedProfile && {color: theme.colors.primary, fontWeight: '500'}]}
      iconRight={true}
      icon={{type: 'ionicon', name: 'chevron-forward', size: 14, color: isImpressedProfile ? theme.colors.primary : theme.colors.textLight}}
      containerStyle={{marginTop: isImpressedProfile ? theme.spacing.xs : 0}}
      onPress={goToUserSettings}
    />
  );
};

export default ViewProfileButton;
