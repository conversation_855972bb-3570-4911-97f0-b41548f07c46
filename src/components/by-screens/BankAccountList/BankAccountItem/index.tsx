import {Button, Text} from '@rneui/themed';
import React from 'react';
import {Image, StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import {MyBank} from '~utils/types/common';

type BankAccountItemProps = {
  item: MyBank;
  onRemoveRequest?: (bank: MyBank) => void;
  rightAction?: React.ReactNode;
  leftAction?: React.ReactNode;
};

const BankAccountItem: React.FC<BankAccountItemProps> = props => {
  return (
    <View style={styles.container}>
      {props.leftAction && props.leftAction}
      <Image source={{uri: props.item.bank.logo}} style={{width: 70, height: 25}} />
      <View style={{flex: 1}}>
        <Text style={styles.text}>{props.item.name}</Text>
        <View>
          <Text style={[{color: theme.colors.textLight, marginTop: 8}, styles.text]}>
            {props.item.bank.short_name} - {props.item.number}
          </Text>
        </View>
      </View>
      <View style={{marginLeft: 'auto', flexShrink: 0, alignSelf: 'center'}}>
        {props.onRemoveRequest && (
          <Button
            onPress={() => props.onRemoveRequest?.(props.item)}
            type="clear"
            icon={{
              type: 'ionicon',
              name: 'trash',
              size: 18,
              color: theme.colors.textLight,
            }}
            size="sm"
          />
        )}
        {props.rightAction && props.rightAction}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    padding: theme.spacing.m,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.m,
  },
  text: {
    fontSize: theme.typography.sm,
  },
});

export default BankAccountItem;
