import {Button, Text} from '@rneui/themed';
import React, {useCallback} from 'react';
import {StyleSheet, View} from 'react-native';
import {useNavigation} from '~hooks';
import theme from '~utils/config/themes/theme';

type FourStepsOfMoneyProps = {
  onCTAPress: () => void;
};

const FourStepsOfMoney: React.FC<FourStepsOfMoneyProps> = props => {
  const navigation = useNavigation();

  const goToFAQ = useCallback(() => {
    navigation.navigate('FAQ');
  }, []);

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>Bán hàng trong 4 bước</Text>
      <View style={{marginTop: theme.spacing.l, flexDirection: 'row'}}>
        <StepIcon step={1} />
        <View style={{marginLeft: theme.spacing.m, flex: 1}}>
          <Text style={{fontWeight: '600', fontSize: theme.typography.md, color: theme.colors.text}}>Chọn sản phẩm</Text>
          <Text style={{fontSize: theme.typography.base, color: theme.colors.text, marginTop: theme.spacing.s}}>
            Khám phá hơn 30.000 sản phẩm giá sỉ trên TTS Dropship và chọn ra sản phẩm phù hợp để bán.
          </Text>
        </View>
      </View>
      {/*  */}
      <View style={{marginTop: theme.spacing.l, flexDirection: 'row'}}>
        <StepIcon step={2} />
        <View style={{marginLeft: theme.spacing.m, flex: 1}}>
          <Text style={{fontWeight: '600', fontSize: theme.typography.md, color: theme.colors.text}}>Đăng bán bất cứ đâu</Text>
          <Text style={{fontSize: theme.typography.base, color: theme.colors.text, marginTop: theme.spacing.s}}>Đăng bán sản phẩm lên các trang mạng xã hội hoặc trang web bán hàng của bạn.</Text>
        </View>
      </View>
      {/*  */}
      <View style={{marginTop: theme.spacing.l, flexDirection: 'row'}}>
        <StepIcon step={3} />
        <View style={{marginLeft: theme.spacing.m, flex: 1}}>
          <Text style={{fontWeight: '600', fontSize: theme.typography.md, color: theme.colors.text}}>Chốt khách, tạo đơn</Text>
          <Text style={{fontSize: theme.typography.base, color: theme.colors.text, marginTop: theme.spacing.s}}>Tạo đơn hàng trên TTS Dropship để nhà cung cấp giao hàng tới khách.</Text>
        </View>
      </View>
      {/*  */}
      <View style={{marginTop: theme.spacing.l, flexDirection: 'row'}}>
        <StepIcon step={4} />
        <View style={{marginLeft: theme.spacing.m, flex: 1}}>
          <Text style={{fontWeight: '600', fontSize: theme.typography.md, color: theme.colors.text}}>Nhận hoa hồng, rút tiền</Text>
          <Text style={{fontSize: theme.typography.base, color: theme.colors.text, marginTop: theme.spacing.s}}>
            Nhận hoa hồng khi sản phẩm tới tay khách hàng và rút tiền về tài khoản ngân hàng của bạn.
          </Text>
        </View>
      </View>

      <Button
        type="clear"
        onPress={goToFAQ}
        containerStyle={{alignSelf: 'center', marginTop: theme.spacing.l}}
        buttonStyle={{paddingHorizontal: 0}}
        titleStyle={{color: theme.colors.text, fontSize: theme.typography.md, textDecorationLine: 'underline'}}>
        Xem câu hỏi thường gặp &raquo;
      </Button>
    </View>
  );
};

type StepIconProps = {
  step: number;
};

const StepIcon: React.FC<StepIconProps> = props => {
  return (
    <View style={styles.step}>
      <Text style={{color: '#fff', fontSize: theme.typography.md}}>{props.step}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: theme.colors.secondary,
  },
  flexRow: {
    flexDirection: 'row',
  },
  heading: {
    fontSize: theme.typography.lg2,
    color: theme.colors.text,
    textAlign: 'center',
    fontWeight: '500',
    marginTop: theme.spacing.m,
    marginBottom: theme.spacing.m,
    textTransform: 'uppercase',
  },
  step: {
    width: 24,
    height: 24,
    backgroundColor: theme.colors.text,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 30,
  },
});

export default FourStepsOfMoney;
