import {Text} from '@rneui/themed';
import React, {useCallback} from 'react';
import {Pressable, StyleSheet} from 'react-native';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '~hooks';

const SearchInput = () => {
  const navigation = useNavigation();

  const handlePress = useCallback(() => {
    navigation.navigate('Search');
  }, []);
  return (
    <Pressable style={styles.container} onPress={handlePress}>
      <Ionicons name="search" size={22} color={theme.colors.textLight} />
      <Text style={[styles.text, {marginLeft: theme.spacing.s}]}>Bạn muốn bán gì?</Text>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: theme.spacing.m,
    marginTop: theme.spacing.m,
    backgroundColor: theme.colors.gray10,
    padding: 14,
    borderRadius: 30,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: theme.colors.gray30,
  },
  text: {
    fontSize: theme.typography.lg,
    color: theme.colors.textLight,
  },
});

export default SearchInput;
