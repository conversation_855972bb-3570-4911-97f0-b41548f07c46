import React, {useRef} from 'react';
import {ScrollView, StyleSheet, View} from 'react-native';
import {trackViewTutorial, Tutorial, useGetTutorials} from '~utils/api/tutorials';
import TutorialItem from './TutorialItem';
import Video from 'react-native-video';
import Modal from 'react-native-modal';
import {WINDOW_HEIGHT} from '@gorhom/bottom-sheet';
import {useNavigation} from '~hooks';
import Ionicons from 'react-native-vector-icons/Ionicons';
import theme from '~utils/config/themes/theme';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

const NewUserTutorials = () => {
  const {data} = useGetTutorials();
  const [modalOpen, setModalOpen] = React.useState(false);

  const [selectedVideoUrl, setSelectedVideoUrl] = React.useState('');
  const videoPlayerRef = useRef(null);
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();

  // const [tutorialsWatched, setTutorialsWatched] = React.useState([]);

  // useEffect(() => {
  //   AsyncStorage.getItem(ASYNC_STORAGE_KEYS.TUTORIALS_WATCHED, (error, result) => {
  //     if (result) {
  //       const parsedTutorialsWatched = JSON.parse(result);
  //       if (parsedTutorialsWatched?.length) {
  //         setTutorialsWatched(parsedTutorialsWatched);
  //       }
  //     }
  //   });
  // }, []);

  const handleTutorialPress = (item: Tutorial) => {
    trackViewTutorial(item.id);

    if (item.type === 'video') {
      setSelectedVideoUrl(item.url);
      setModalOpen(true);

      return;
    }

    if (item.type === 'link') {
      navigation.push('WebView', {url: item.url, includeAuth: true, redirectNativeScreen: true});
    }
  };

  const videoHeight = WINDOW_HEIGHT - 200;
  const videoWidth = (videoHeight * 9) / 16;

  const handleCloseModal = () => {
    setModalOpen(false);
    setSelectedVideoUrl('');
    // @ts-ignore
    videoPlayerRef?.current?.seek(0);
  };
  return (
    <>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {data?.map(item => (
          <TutorialItem key={item.id} item={item} onPress={handleTutorialPress} />
        ))}
      </ScrollView>

      <Modal isVisible={modalOpen} swipeDirection={['down', 'up']} onSwipeComplete={handleCloseModal} style={styles.videoContainerStyle}>
        <View style={[styles.modalHeader, {top: insets.top + 4}]}>
          <Ionicons name="close" size={40} color={theme.colors.white} onPress={handleCloseModal} />
        </View>
        {selectedVideoUrl && (
          <Video
            controls
            ref={videoPlayerRef}
            ignoreSilentSwitch="ignore"
            style={{width: videoWidth, height: videoHeight}}
            source={{
              uri: selectedVideoUrl,
            }}
          />
        )}
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {},
  modalHeader: {
    position: 'absolute',
    top: 4,
    left: 0,
    right: 8,
    alignItems: 'flex-end',
  },
  videoContainerStyle: {
    margin: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default NewUserTutorials;
