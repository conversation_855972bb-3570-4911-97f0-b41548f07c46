import {SCREEN_WIDTH} from '@gorhom/bottom-sheet';
import {Text} from '@rneui/themed';
import React, {useCallback} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {Tutorial} from '~utils/api/tutorials';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {QuickImage} from '~components/shared/QuickImage';

type TutorialItemProps = {
  item: Tutorial;
  onPress: (item: Tutorial) => void;
};

const TutorialItem: React.FC<TutorialItemProps> = ({item, onPress}) => {
  const handlePress = useCallback(() => {
    onPress(item);
  }, [item]);

  return (
    <TouchableOpacity style={styles.container} onPress={handlePress}>
      <QuickImage
        source={{
          uri: `https://imgcdn.thitruongsi.com/tts/rs:fit:400:0:1:1/g:sm/plain/${item.thumbnail}`,
        }}
        style={styles.thumb}
      />

      <LinearGradient
        style={styles.linearGradient}
        colors={[
          'rgba(0,0,0,0)',
          'rgba(0,0,0,0.0029348165)',
          'rgba(0,0,0,0.01134518)',
          'rgba(0,0,0,0.02464)',
          'rgba(0,0,0,0.0422282)',
          'rgba(0,0,0,0.06351835)',
          'rgba(0,0,0,0.08792)',
          'rgba(0,0,0,0.11484165)',
          'rgba(0,0,0,0.1436918)',
          'rgba(0,0,0,0.17388)',
          'rgba(0,0,0,0.20481475)',
          'rgba(0,0,0,0.23590525)',
          'rgba(0,0,0,0.26656)',
          'rgba(0,0,0,0.2961882)',
          'rgba(0,0,0,0.35419835)',
          'rgba(0,0,0,0.45)',
        ]}
        locations={[0, 0.1179, 0.2138, 0.2912, 0.3534, 0.4037, 0.4456, 0.4824, 0.5176, 0.5544, 0.5963, 0.6466, 0.7088, 0.7862, 0.8821, 1]}
        start={{x: 0.5, y: 0}}
        end={{x: 0.5, y: 1}}></LinearGradient>

      <View style={styles.titleOverlay}>
        {item.type === 'video' && <Ionicons name="videocam" size={12} color={theme.colors.white} style={styles.videoIcon} />}
        <Text style={styles.title}>{item.title}</Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {marginLeft: theme.spacing.s, borderRadius: 12, marginVertical: 12, borderWidth: 2, borderColor: theme.colors.gray20, position: 'relative'},
  thumb: {
    width: SCREEN_WIDTH * 0.28,
    height: SCREEN_WIDTH * 0.28 * (16 / 9),
    borderRadius: 12,
  },
  title: {color: theme.colors.white, fontSize: theme.typography.base, fontWeight: 'bold'},
  titleOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 4,
    height: SCREEN_WIDTH * 0.28 * (16 / 9) * 0.4,
    backgroundColor: 'rgba(0, 0, 0, 0.45)',
    justifyContent: 'flex-end',
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
  },
  linearGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: SCREEN_WIDTH * 0.28 * (16 / 9) * 0.4,
    justifyContent: 'flex-end',
  },
  videoIcon: {},
});

export default TutorialItem;
