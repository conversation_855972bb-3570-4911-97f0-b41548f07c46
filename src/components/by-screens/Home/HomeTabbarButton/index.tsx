// write tabbar button empty component

import {BottomTabBarButtonProps} from '@react-navigation/bottom-tabs';
import {Text} from '@rneui/themed';
import React, {useCallback, useEffect, useState} from 'react';
import {Animated, TouchableOpacity} from 'react-native';
import ee, {EventNames} from '~utils/events';
import Ionicons from 'react-native-vector-icons/Ionicons';
import theme from '~utils/config/themes/theme';
import {useUpdateEffect} from '~hooks';

type HomeTabbarButtonProps = BottomTabBarButtonProps & {};

const HomeTabbarButton: React.FC<HomeTabbarButtonProps> = props => {
  // slide up animation
  const positionY = React.useRef(new Animated.Value(50)).current;
  const [isShowScrollToTopButton, setShowScrollToTopButton] = useState(false);

  useUpdateEffect(() => {
    if (!props.accessibilityState?.selected) {
      handleHideScrollToTopButton();
    } else {
      handleShowScrollToTopButton();
    }
  }, [props.accessibilityState?.selected]);

  useEffect(() => {
    ee.on(EventNames.HomePageUpper200Scroll, handleShowScrollToTopButton);
    ee.on(EventNames.HomePageLower200Scroll, handleHideScrollToTopButton);
    return () => {
      ee.off(EventNames.HomePageUpper200Scroll, handleShowScrollToTopButton);
      ee.off(EventNames.HomePageLower200Scroll, handleHideScrollToTopButton);
    };
  }, []);

  const slideUp = () => {
    Animated.timing(positionY, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };
  const slideDown = () => {
    Animated.timing(positionY, {
      toValue: 50,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setShowScrollToTopButton(false);
    });
  };

  const handleShowScrollToTopButton = useCallback(() => {
    setShowScrollToTopButton(true);
    slideUp();
  }, []);

  const handleHideScrollToTopButton = useCallback(() => {
    slideDown();
  }, []);

  if (!isShowScrollToTopButton) {
    return <TouchableOpacity {...props} />;
  }

  return (
    <TouchableOpacity {...props}>
      <Animated.View
        style={[
          {justifyContent: 'center', alignItems: 'center'},
          {
            transform: [{translateY: positionY}],
          },
        ]}>
        <Ionicons name="chevron-up-circle" size={32} color={theme.colors.primary} />
        <Text style={{fontSize: 12, color: theme.colors.primary}}>Lên trên</Text>
      </Animated.View>
    </TouchableOpacity>
  );
};

export default HomeTabbarButton;
