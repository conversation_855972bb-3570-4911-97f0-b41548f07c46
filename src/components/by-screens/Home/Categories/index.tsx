import {SCREEN_WIDTH} from '@gorhom/bottom-sheet';
import {useLinkProps} from '@react-navigation/native';
import {Text} from '@rneui/themed';
import React from 'react';
import {Pressable, ScrollView, StyleSheet, View} from 'react-native';
import {QuickImage} from '~components/shared/QuickImage';
import {useGetCategories} from '~utils/api/category';
import theme from '~utils/config/themes/theme';
import Category from '~utils/types/category';

const Categories = () => {
  const {data} = useGetCategories();

  return (
    <View style={{marginTop: theme.spacing.m, marginLeft: theme.spacing.m}}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={{height: 220, flexWrap: 'wrap'}}>
          {(data || []).map(category => (
            <CategoryItem category={category} key={category.id} />
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

const CategoryItem = ({category}: {category: Partial<Category>}) => {
  const {onPress, ...linkProps} = useLinkProps({
    href: `/cat/${category.slug}/${category.category_id}`,
    screen: 'CategoryDetail',
    params: {categoryId: category.category_id, slug: category.slug},
  });

  return (
    <Pressable key={category.id} style={styles.categoryItem} onPress={onPress} {...linkProps}>
      <QuickImage
        source={{uri: `https://imgcdn.thitruongsi.com/tts/rs:fit:200:200:1:1/g:sm/plain/https://files.thitruongsi.com/assets/category/thumbs/${category.thumb_image_src}`}}
        style={{width: 40, height: 40, borderRadius: 12}}
      />
      <Text style={{textAlign: 'center', marginTop: theme.spacing.s, fontSize: theme.typography.base}}>{category.title}</Text>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  categoryItem: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 100,
    backgroundColor: theme.colors.white,
    borderRadius: 12,
    marginRight: 8,
    marginBottom: 8,
    padding: 12,
    width: Math.min(SCREEN_WIDTH / 3.8, 150),
  },
});

export default Categories;
