import React, {useCallback, useEffect} from 'react';
import {Animated, StyleSheet, View} from 'react-native';
import {useNavigation} from '~hooks';
import theme from '~utils/config/themes/theme';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {Text} from '@rneui/themed';

const Introduction = () => {
  const navigation = useNavigation();
  // bounce animation
  const animatedValue = React.useRef(new Animated.Value(1)).current;

  useEffect(() => {
    setTimeout(() => {
      Animated.timing(animatedValue, {
        toValue: 1.5,
        useNativeDriver: true,
        duration: 200,
      }).start(() => {
        Animated.timing(animatedValue, {
          toValue: 1,
          useNativeDriver: true,
          duration: 200,
        }).start();
      });
    }, 1000);
  }, []);

  const animateBounce = useCallback(() => {
    Animated.timing(animatedValue, {
      toValue: 1.5,
      useNativeDriver: true,
      duration: 200,
    }).start(() => {
      Animated.timing(animatedValue, {
        toValue: 1,
        useNativeDriver: true,
        duration: 200,
      }).start();
    });
  }, []);

  useEffect(() => {
    navigation.setOptions({
      headerShadowVisible: false,
      headerTintColor: '#fff',
      headerStyle: {
        backgroundColor: theme.colors.primary,
      },
    });
  }, []);
  return (
    <View style={styles.container}>
      <Text style={[styles.headerText]}>Bán hàng</Text>
      <Text style={[styles.headerText, {fontWeight: '600'}]}>không cần vốn</Text>
      {/* <Text style={styles.subHeaderText}>Nền tảng bán hàng cộng tác viên có lợi nhuận cao nhất.</Text> */}
      <View style={{flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'space-around', marginTop: theme.spacing.xl, marginBottom: theme.spacing.l, flexWrap: 'wrap'}}>
        <View style={{width: 100, alignItems: 'center'}}>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Animated.View
              // bounce animation
              style={{
                transform: [{scale: animatedValue}],
              }}>
              <MaterialCommunityIcons name="trophy-variant" size={18} color={'yellow'} onPress={animateBounce} suppressHighlighting />
            </Animated.View>
            <Text style={{marginLeft: 8, fontSize: theme.typography.lg4, fontWeight: 'bold', color: theme.colors.white}}>1</Text>
          </View>
          <Text
            style={{
              textAlign: 'center',
              color: theme.colors.white,
            }}>
            Lợi nhuận cao nhất thị trường
          </Text>
        </View>
        {/*  */}
        <View style={{width: 100, alignItems: 'center'}}>
          <Text style={{fontSize: theme.typography.lg4, fontWeight: 'bold', color: theme.colors.white}}>100</Text>
          <Text
            style={{
              textAlign: 'center',
              color: theme.colors.white,
            }}>
            Rút tiền chỉ từ 100K
          </Text>
        </View>
        {/*  */}
        <View style={{width: 100, alignItems: 'center'}}>
          <MaterialCommunityIcons name="currency-usd-off" style={{width: 40, height: 42}} size={38} color={theme.colors.white} suppressHighlighting />
          <Text
            style={{
              textAlign: 'center',
              color: theme.colors.white,
            }}>
            Hoàn toàn miễn phí
          </Text>
        </View>
        {/*  */}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 12,
    paddingTop: 60,
    backgroundColor: theme.colors.primary,
  },
  headerText: {
    fontSize: 40,
    fontWeight: '400',
    color: '#fff',
    letterSpacing: 0,
  },
  subHeaderText: {
    // marginTop: 20,
    fontSize: theme.typography.lg1,
    color: '#fff',
    fontWeight: '300',
  },
});

export default Introduction;
