import React, {useEffect} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {useNavigation} from '~hooks';
import theme from '~utils/config/themes/theme';

const IntroductionLg = () => {
  const navigation = useNavigation();

  useEffect(() => {
    navigation.setOptions({
      headerStyle: {
        backgroundColor: theme.colors.white,
      },
      headerTintColor: theme.colors.text,
    });
  }, []);

  return (
    <>
      <View style={styles.container}>
        <Text style={[styles.headerText]}>Làm CTV Bán Hàng Online</Text>
        <Text style={[styles.headerText, {fontWeight: '600'}]}>không cần vốn</Text>

        <Text style={styles.subHeaderText}>Trở thành cộng tác viên bán hàng cho các nguồn hàng lớn hàng đầu Việt Nam.</Text>
      </View>
      <View style={{alignItems: 'center', justifyContent: 'center', maxWidth: 728, alignSelf: 'center'}}>
        <View style={{width: 100, borderTopWidth: 2, borderTopColor: theme.colors.textLight}} />
        <blockquote style={{fontSize: theme.typography.lg, color: theme.colors.textLight}}>
          TTS Dropship là nền tảng bán hàng cộng tác viên dành cho mọi người (giáo viên, học sinh sinh viên, nhân viên văn phòng, KOC, KOL...). Chúng tôi cung cấp nguồn hàng đa dạng, giá tốt để ai
          cũng có thể bắt đầu bán hàng online một cách dễ dàng, kiếm được thu nhập cao.
        </blockquote>
        <View style={{width: 100, borderTopWidth: 2, borderTopColor: theme.colors.textLight}} />
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 12,
    paddingVertical: 100,
    backgroundColor: theme.colors.white,
  },
  headerText: {
    fontSize: 40,
    fontWeight: '400',
    color: theme.colors.primary,
    letterSpacing: 0,
  },
  subHeaderText: {
    marginTop: 20,
    fontSize: theme.typography.lg1,
    color: theme.colors.primary,
    fontWeight: '300',
  },
});

export default IntroductionLg;
