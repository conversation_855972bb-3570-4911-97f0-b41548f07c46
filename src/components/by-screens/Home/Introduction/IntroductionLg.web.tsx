import React, {useEffect} from 'react';
import {Image, StyleSheet, Text, View} from 'react-native';
import {useNavigation} from '~hooks';
import theme from '~utils/config/themes/theme';

// @ts-ignore
import HomeHero from '~assets/home-hero.png';

const IntroductionLg = () => {
  const navigation = useNavigation();

  useEffect(() => {
    navigation.setOptions({
      headerStyle: {
        backgroundColor: theme.colors.white,
      },
      headerTintColor: theme.colors.text,
    });
  }, []);

  return (
    <>
      <View style={styles.container}>
        <View style={{flex: 1, marginRight: theme.spacing.m * 4}}>
          <h1
            style={{
              fontSize: 40,
              fontWeight: '400',
              color: theme.colors.primary,
              letterSpacing: 0,
            }}>
            Làm CTV Bán Hàng Online
          </h1>
          <Text style={[styles.headerText, {fontWeight: '600'}]}>không cần vốn</Text>
          <Text style={styles.subHeaderText}>Trở thành cộng tác viên bán hàng cho các nguồn hàng lớn hàng đầu Việt Nam.</Text>
          <Text style={{fontSize: theme.typography.sm, color: theme.colors.textLight, marginTop: theme.spacing.m}}>
            TTS Dropship là nền tảng bán hàng cộng tác viên dành cho mọi người (giáo viên, học sinh sinh viên, nhân viên văn phòng, KOC, KOL...).
          </Text>
          <Text style={{fontSize: theme.typography.sm, color: theme.colors.textLight, marginTop: theme.spacing.s}}>
            Chúng tôi cung cấp nguồn hàng đa dạng, giá tốt để ai cũng có thể bắt đầu bán hàng online một cách dễ dàng, kiếm được thu nhập cao.
          </Text>
        </View>
        <View>
          <Image source={HomeHero} style={{width: 400, height: 450, marginBottom: theme.spacing.m}} />
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 12,
    paddingVertical: 60,
    backgroundColor: theme.colors.white,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  headerText: {
    fontSize: 40,
    fontWeight: '400',
    color: theme.colors.primary,
    letterSpacing: 0,
  },
  subHeaderText: {
    marginTop: 20,
    fontSize: theme.typography.lg1,
    color: theme.colors.primary,
    fontWeight: '300',
  },
});

export default IntroductionLg;
