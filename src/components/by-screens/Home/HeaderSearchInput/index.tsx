import {Text} from '@rneui/themed';
import React, {useCallback, useEffect, useRef} from 'react';
import {Animated, Pressable, StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '~hooks';

const HeaderSearchInput = () => {
  const navigation = useNavigation();
  const translateYAnim = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    Animated.timing(translateYAnim, {
      toValue: 0,
      useNativeDriver: true,
      duration: 300,
    }).start();
  }, []);

  const handlePress = useCallback(() => {
    navigation.navigate('Search');
  }, []);

  return (
    <View style={{overflow: 'hidden', marginBottom: 4}}>
      <Animated.View style={{transform: [{translateY: translateYAnim}]}}>
        <Pressable style={styles.container} onPress={handlePress}>
          <Ionicons name="search" size={18} color={theme.colors.gray50} />
          <Text style={[styles.text, {marginLeft: theme.spacing.s}]}>Bạn muốn bán gì?</Text>
        </Pressable>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.gray20,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    width: '100%',
    minWidth: 250,
  },
  text: {
    fontSize: theme.typography.md,
    fontWeight: '400',

    color: theme.colors.gray60,
  },
});

export default HeaderSearchInput;
