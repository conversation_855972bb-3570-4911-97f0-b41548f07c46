import {Text} from '@rneui/themed';
import React, {useCallback} from 'react';
import {Pressable, StyleSheet} from 'react-native';
import theme from '~utils/config/themes/theme';
import {ProductCollection} from '~utils/types/product';
import {ScreenWidth} from '@rneui/base';
import {QuickImage} from '~components/shared/QuickImage';
import {useNavigation} from '~hooks';
import {responsiveWidth} from '~components/shared/web/WebContainer';

type CollectionProps = {
  collection: ProductCollection;
};

const Collection: React.FC<CollectionProps> = props => {
  const navigation = useNavigation();

  const handleCollectionPress = useCallback(() => {
    navigation.navigate('CollectionDetail', {
      collectionId: props.collection.collection_id,
    });
  }, [props.collection.collection_id]);

  return (
    <Pressable style={styles.container} onPress={handleCollectionPress} accessible={false}>
      <QuickImage source={{uri: props.collection.image}} style={styles.collectionThumb} />
      <Text style={styles.title}>{props.collection.title}</Text>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    padding: 12,
    marginRight: theme.spacing.m,
    borderRadius: 20,
    width: Math.min(ScreenWidth, responsiveWidth.sm) / 2.6,
    borderWidth: 1,
    borderColor: theme.colors.gray10,
    alignItems: 'center',
  },
  title: {
    marginTop: theme.spacing.m,
    paddingHorizontal: 8,
    fontSize: theme.typography.md,
    fontWeight: '500',
    textAlign: 'center',
  },
  collectionThumb: {
    width: Math.min(ScreenWidth, responsiveWidth.sm) / 2.6 - 24,
    height: Math.min(ScreenWidth, responsiveWidth.sm) / 2.6 - 24,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: theme.colors.gray20,
  },
});

export default Collection;
