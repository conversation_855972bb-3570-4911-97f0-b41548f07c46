import React, {useCallback} from 'react';
import {Text} from '@rneui/themed';
import {Pressable, ScrollView, StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import {ProductCollection} from '~utils/types/product';
import Ionicons from 'react-native-vector-icons/Ionicons';
import ProductCard from '~components/shared/ProductCard';
import {ScreenWidth} from '@rneui/base';
import {useNavigation} from '~hooks';
import {responsiveWidth} from '~components/shared/web/WebContainer';
import LinearGradient from 'react-native-linear-gradient';

export const mockupCollectionStyle = {
  styles: {
    bg: {
      gradientColors: ['#469c4b', '#65ae17'],
    },
    title: {
      color: theme.colors.white,
    },
  },
};

type CarouselProductsCollectionProps = {
  collection: ProductCollection;
};

const CarouselProductsCollection: React.FC<CarouselProductsCollectionProps> = props => {
  const navigation = useNavigation();

  // props.collection.styles = mockupCollectionStyle.styles;

  const handlePress = useCallback(() => {
    navigation.navigate('CollectionDetail', {
      collectionId: props.collection.handle,
    });
  }, [props.collection.collection_id]);

  return (
    <CollectionContainer collection={props.collection}>
      <View style={styles.container}>
        <Pressable style={[theme.globalStyles.flexRow, {alignItems: 'center', paddingBottom: theme.spacing.m}]} onPress={handlePress}>
          <Text style={[styles.title, props.collection.styles?.title]}>{props.collection.title}</Text>
          <Ionicons name="arrow-forward" size={22} color={props.collection.styles?.title?.color || theme.colors.text} style={{marginLeft: 'auto', paddingRight: 20}} />
        </Pressable>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={{marginLeft: -5}}>
          {props.collection.products.map(product => (
            <View style={styles.productCardContainer} key={product.id}>
              <ProductCard product={product} titleMaxLines={1} />
            </View>
          ))}
        </ScrollView>
      </View>
    </CollectionContainer>
  );
};

const CollectionContainer: React.FC<{collection: ProductCollection} & React.PropsWithChildren> = props => {
  if (props.collection.styles?.bg?.gradientColors && props.collection.styles?.bg?.gradientColors.length > 0) {
    return (
      <LinearGradient start={{x: 0, y: 0.2}} colors={props.collection.styles.bg.gradientColors} style={styles.linearGradient}>
        {props.children}
      </LinearGradient>
    );
  }

  return props.children;
};

const styles = StyleSheet.create({
  linearGradient: {
    flex: 1,
    paddingLeft: 15,
    marginLeft: -theme.spacing.m,
  },
  container: {
    marginTop: theme.spacing.m,
  },
  title: {
    fontSize: theme.typography.lg,
    fontWeight: '500',
  },
  productCardContainer: {
    maxWidth: Math.min(ScreenWidth, responsiveWidth.sm) / 2.3,
    marginRight: 0,
  },
});

export default CarouselProductsCollection;
