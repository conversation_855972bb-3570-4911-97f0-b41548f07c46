import {Text} from '@rneui/themed';
import React from 'react';
import {View} from 'react-native';
import {ProductCollection} from '~utils/types/product';

type InfiniteProductsCollectionProps = {
  collection: ProductCollection;
};

const InfiniteProductsCollection: React.FC<InfiniteProductsCollectionProps> = props => {
  return (
    <View>
      <Text>{props.collection.title}</Text>
    </View>
  );
};

export default InfiniteProductsCollection;
