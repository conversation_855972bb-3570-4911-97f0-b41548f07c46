import {Text} from '@rneui/themed';
import React from 'react';
import {ScrollView, StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import {ProductCollection} from '~utils/types/product';
import Collection from './Collection';

type CarouselCollectionProps = {
  collection: ProductCollection;
};

const CarouselCollection: React.FC<CarouselCollectionProps> = props => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>{props.collection.title}</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {props.collection.collections?.map(collection => (
          <Collection collection={collection} key={collection.collection_id} />
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: theme.spacing.m,
  },
  title: {
    fontSize: theme.typography.lg,
    fontWeight: '500',
    marginBottom: theme.spacing.m,
  },
});

export default CarouselCollection;
