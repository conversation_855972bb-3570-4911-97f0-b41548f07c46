import React, {useCallback, useImperativeHandle} from 'react';
import {StyleSheet, View} from 'react-native';
import {useGetHomeCollections} from '~utils/api/product';
import theme from '~utils/config/themes/theme';
import {ProductCollection} from '~utils/types/product';
import CarouselCollection from './CarouselCollection';
import CarouselProductsCollection from './CarouselProductsCollection';
import {QueryObserverResult} from '@tanstack/react-query';

type HomeCollectionsProps = {
  onReceiveInfiniteProductsCollectionId: (collectionId: string) => void;
};

export type HomeCollectionsRef = {
  refetch: () => Promise<QueryObserverResult<ProductCollection[], unknown>>;
};

const HomeCollections = React.forwardRef<HomeCollectionsRef, HomeCollectionsProps>((props, ref) => {
  const {data, refetch} = useGetHomeCollections();

  useImperativeHandle(ref, () => ({
    refetch,
  }));

  const renderCollections = useCallback(
    (collection: ProductCollection) => {
      switch (collection.template) {
        case 'carousel_products':
          return <CarouselProductsCollection collection={collection} key={collection.collection_id} />;
        case 'carousel_collections':
          return <CarouselCollection collection={collection} key={collection.collection_id} />;

        case 'infinity_products':
          setTimeout(() => {
            props.onReceiveInfiniteProductsCollectionId(collection.collection_id);
          }, 1000);
          return null;

        default:
          return null;
      }
    },
    [data],
  );

  return <View style={styles.container}>{data?.map(renderCollections)}</View>;
});

const styles = StyleSheet.create({
  container: {
    marginTop: theme.spacing.m,
    marginLeft: theme.spacing.m,
  },
});

export default HomeCollections;
