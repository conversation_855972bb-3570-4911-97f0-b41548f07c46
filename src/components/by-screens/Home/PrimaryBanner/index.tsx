import {ScreenWidth} from '@rneui/base';
import React, {useCallback, useMemo} from 'react';
import {Linking, ListRenderItemInfo, Platform, Pressable, View} from 'react-native';
import SwiperFlatList from 'react-native-swiper-flatlist';
import {useGetPromotedSlider} from '~utils/api/banner';
import theme from '~utils/config/themes/theme';
import {AdsContent} from '~utils/types/common';
import {QuickImage} from '~components/shared/QuickImage';
import {responsiveWidth} from '~components/shared/web/WebContainer';
import {useNavigation} from '~hooks';

const PrimaryBanner = () => {
  const {data} = useGetPromotedSlider();
  const navigation = useNavigation();

  const imageWidth = useMemo(() => {
    return Math.min(ScreenWidth, responsiveWidth.sm) - 24;
  }, []);

  const getItemLayout = useCallback(
    (__data: any, itemIndex: number) => {
      return {
        length: imageWidth,
        offset: imageWidth * itemIndex,
        index: itemIndex,
      };
    },
    [imageWidth],
  );

  const handleImagePress = useCallback((link: string) => {
    navigation.navigate('WebView', {url: link, redirectNativeScreen: true});
  }, []);

  const renderItem = useCallback(({item}: ListRenderItemInfo<AdsContent>) => {
    return (
      <Pressable onPress={() => handleImagePress(item.link)}>
        <QuickImage source={{uri: item.banner}} style={{width: imageWidth, height: imageWidth * 0.4, borderRadius: 12}} />
      </Pressable>
    );
  }, []);

  return (
    <View style={{paddingHorizontal: 12, marginVertical: 12}}>
      <SwiperFlatList
        getItemLayout={getItemLayout}
        ListEmptyComponent={<View style={{width: imageWidth, height: imageWidth * 0.4, borderRadius: 12, backgroundColor: theme.colors.gray20}} />}
        autoplay
        autoplayDelay={5}
        autoplayLoop
        showPagination={Platform.OS !== 'web'}
        data={data}
        paginationDefaultColor={theme.colors.gray50}
        renderItem={renderItem}
        paginationStyle={{
          marginBottom: -4,
        }}
        paginationStyleItem={{
          width: 5,
          height: 5,
          marginHorizontal: 4,
        }}
      />
    </View>
  );
};

export default PrimaryBanner;
