import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import Card from '~components/shared/Card';
import theme from '~utils/config/themes/theme';
import {Referrer} from '~utils/types/user';
// @ts-ignore
import CoinIcon from '~assets/coin.png';
import {QuickImage} from '~components/shared/QuickImage';
import {ThemeContext} from '@rneui/themed';

const SaleProgress: React.FC<{refferal: Referrer}> = ({refferal}) => {
  return (
    <Card>
      <QuickImage source={CoinIcon} style={{width: 40, height: 40, alignSelf: 'center'}} />
      <Text style={{paddingHorizontal: theme.spacing.l, textAlign: 'center', marginBottom: theme.spacing.l}}>Hãy hỗ trợ {refferal.user_name} có đơn hàng để cùng nhận thưởng nhé</Text>

      <Step
        stepNumber={1}
        title={
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Text style={{fontWeight: 'bold', color: theme.colors.text, marginRight: theme.spacing.xs}}>Đ<PERSON>n hàng đầu tiên từ 179K</Text>
            <QuickImage source={CoinIcon} style={styles.coinIcon} />
          </View>
        }
        description={
          <View>
            <Text>Bạn sẽ nhận được: 20.000đ</Text>
            <Text>{refferal.user_name} sẽ nhận được: 20.000đ</Text>
          </View>
        }
      />

      <Step
        stepNumber={2}
        title={
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Text style={{fontWeight: 'bold', color: theme.colors.text, marginRight: theme.spacing.xs}}>Đơn hàng thứ hai từ 179K</Text>
            <QuickImage source={CoinIcon} style={styles.coinIcon} />
          </View>
        }
        description={
          <View>
            <Text>Bạn sẽ nhận được: 30.000đ</Text>
            <Text>{refferal.user_name} sẽ nhận được: 20.000đ</Text>
          </View>
        }
      />

      <Step
        stepNumber={3}
        title={
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Text style={{fontWeight: 'bold', color: theme.colors.text, marginRight: theme.spacing.xs}}>Đơn thứ 3 trở đi</Text>
            <QuickImage source={CoinIcon} style={styles.coinIcon} />
          </View>
        }
        description={
          <View>
            <Text>Bạn sẽ nhận được 3% hoa hồng trong suốt 1 năm</Text>
          </View>
        }
      />
    </Card>
  );
};

const Step: React.FC<{stepNumber: number; title: React.ReactNode | string; description: React.ReactNode | string}> = ({stepNumber, title, description}) => {
  return (
    <View style={styles.step}>
      <View style={{flexDirection: 'row', alignItems: 'flex-start'}}>
        <View style={styles.stepNumber}>
          <Text style={styles.stepNumberText}>{stepNumber}</Text>
        </View>
        <View>
          {title}
          {description}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  coinIcon: {
    width: 20,
    height: 20,
  },
  stepNumber: {
    width: 20,
    height: 20,
    borderRadius: 15,
    backgroundColor: theme.colors.textLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.s,
  },
  stepNumberText: {
    color: theme.colors.white,
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  step: {
    marginTop: theme.spacing.m,
  },
  stepTitle: {
    fontSize: theme.typography.base,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  stepDescription: {
    fontSize: 14,
  },
  stepIcon: {
    width: 20,
    height: 20,
  },
});

export default SaleProgress;
