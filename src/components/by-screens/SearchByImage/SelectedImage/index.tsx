import {Button, Image} from '@rneui/themed';
import React, {useEffect, useRef} from 'react';
import {Animated, StyleSheet} from 'react-native';
import {openCropper} from 'react-native-image-crop-picker';
import {Asset} from 'react-native-image-picker';
import theme from '~utils/config/themes/theme';

type SelectedImageProps = {
  index: number;
  asset: Asset;
  onImageCropped: (newAsset: Asset) => void;
  onPress: () => void;
  isSelected: boolean;
};

const SelectedImage: React.FC<SelectedImageProps> = props => {
  const opacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(opacity, {
      toValue: 1,
      useNativeDriver: true,
      duration: 500,
    }).start();
  }, []);

  const handleCropImage = async () => {
    const cropped = await openCropper({
      path: props.asset?.uri!,
      mediaType: 'photo',
      loadingLabelText: '<PERSON><PERSON> tải ',
      cropperToolbarTitle: 'Cắt ảnh',
      cropperChooseText: '<PERSON><PERSON><PERSON>',
      cropperCancelText: 'Hủy',
      freeStyleCropEnabled: true,
    }).catch(err => {});
    if (cropped) {
      props.onImageCropped({
        ...props.asset,
        uri: cropped.path,
        type: cropped.mime,
        width: cropped.width,
        height: cropped.height,
        fileSize: cropped.size,
        fileName: cropped.filename,
      });
    }
  };

  return (
    <Animated.View style={[styles.container, {opacity: opacity}]}>
      <Image
        onPress={props.onPress}
        source={{
          uri: props.asset.uri,
        }}
        style={[{width: 50, height: 50, borderRadius: 10, borderWidth: 2, borderColor: theme.colors.white}, props.isSelected && {borderWidth: 2, borderColor: theme.colors.primary}]}
      />

      <Button
        icon={{
          type: 'ionicon',
          name: 'crop',
          size: 14,
          color: theme.colors.gray70,
        }}
        size="sm"
        containerStyle={{marginTop: 4}}
        buttonStyle={{padding: 2, paddingHorizontal: 2, backgroundColor: theme.colors.gray20, borderRadius: 8}}
        titleStyle={{fontSize: theme.typography.sm, color: theme.colors.text, fontWeight: '600'}}
        onPress={handleCropImage}>
        {props.index === 0 ? 'Cắt' : ''}
      </Button>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginRight: theme.spacing.s,
    alignItems: 'center',
    marginBottom: theme.spacing.s,
  },
});

export default SelectedImage;
