import {Image} from '@rneui/themed';
import React, {useEffect, useRef} from 'react';
import {Animated, StyleSheet} from 'react-native';
import {VectorSearchObject} from '~utils/api/product';
import theme from '~utils/config/themes/theme';

type SelectedObjectProps = {
  index: number;
  object: VectorSearchObject;
  onPress: () => void;
  isSelected: boolean;
};

const SelectedObject: React.FC<SelectedObjectProps> = props => {
  const opacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(opacity, {
      toValue: 1,
      useNativeDriver: true,
      duration: 500,
    }).start();
  }, []);

  return (
    <Animated.View style={[styles.container, {opacity: opacity}]}>
      <Image
        onPress={props.onPress}
        source={{
          uri: props.object.img_url,
        }}
        style={[{width: 50, height: 50, borderRadius: 10, borderWidth: 2, borderColor: theme.colors.white}, props.isSelected && {borderWidth: 2, borderColor: theme.colors.primary}]}
      />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginRight: theme.spacing.s,
    alignItems: 'center',
  },
});

export default SelectedObject;
