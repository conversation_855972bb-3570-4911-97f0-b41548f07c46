import {ScreenWidth} from '@rneui/base';
import React from 'react';
import {Pressable, StyleSheet, TouchableOpacity, View} from 'react-native';
import {QuickImage} from '~components/shared/QuickImage';
import {ProductImage, ProductVideo} from '~utils/types/product';
import {CheckBox} from '@rneui/themed';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import theme from '~utils/config/themes/theme';
import {MediaItemType} from '~screens/DownloadProductMedia';

type MediaItemProps = {
  item: MediaItemType;
  checked: boolean;
  onChange: (isChecked: boolean, imageId: string) => void;
  onViewPress: (imageId: string) => void;
};

const MediaItem: React.FC<MediaItemProps> = props => {
  const handleChangeChecked = () => {
    props.onChange(!props.checked, props.item.item.id);
  };

  return (
    <View style={styles.container}>
      <QuickImage
        source={{
          uri: props.item.type === 'image' ? (props.item.item as ProductImage).src! : (props.item.item as ProductVideo).thumb_url!,
        }}
        style={styles.image}
      />

      {props.item.type === 'video' && (
        <View style={styles.videoOverlay}>
          <MaterialCommunityIcons name="play-circle-outline" color={theme.colors.white} size={30} />
        </View>
      )}

      <Pressable onPress={handleChangeChecked} style={[styles.backdrop, props.checked && {backgroundColor: 'rgba(0,0,0,0.2)'}]} />

      <View style={styles.checkbox}>
        <CheckBox
          checked={props.checked}
          size={26}
          onPress={handleChangeChecked}
          iconType="ionicon"
          uncheckedIcon="square-outline"
          checkedIcon="checkbox"
          checkedColor="#008060"
          uncheckedColor="#008060"
          containerStyle={{margin: 0, padding: 0, borderRadius: 5, marginHorizontal: 0, zIndex: 2}}
          wrapperStyle={{padding: 0, paddingVertical: 0, paddingHorizontal: 0, marginHorizontal: 0}}
          style={{
            margin: 0,
            marginHorizontal: 0,
            padding: 0,
          }}
        />
      </View>
      <TouchableOpacity style={styles.openLightboxContainer} onPress={() => props.onViewPress(props.item.item.id)}>
        <View style={styles.openLightboxIcon}>
          <MaterialCommunityIcons name="arrow-expand" color={theme.colors.white} size={14} />
        </View>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: ScreenWidth / 3,
    height: ScreenWidth / 3,
    position: 'relative',
    borderWidth: 1,
    borderColor: theme.colors.white,
  },
  image: {
    width: ScreenWidth / 3 - 2,
    height: ScreenWidth / 3 - 2,
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  checkbox: {
    position: 'absolute',
    top: 3,
    right: -6,
    zIndex: 2,
  },
  openLightboxContainer: {
    position: 'absolute',
    bottom: 1,
    right: 1,
    width: 25,
    height: 25,
    justifyContent: 'flex-end',
    // borderTopLeftRadius: 8,
    alignItems: 'flex-end',
    zIndex: 2,
  },
  openLightboxIcon: {
    backgroundColor: 'rgba(0,0,0,0.3)',
    width: 20,
    height: 20,
    justifyContent: 'center',
    borderTopLeftRadius: 8,
    alignItems: 'center',
  },
});

export default MediaItem;
