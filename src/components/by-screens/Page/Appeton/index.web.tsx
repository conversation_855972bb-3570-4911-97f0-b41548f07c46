import React from "react";
import Slider from "react-slick";

type BillboardSliderProps = {
  images: string[];
};

export const BillboardSlider:React.FC<BillboardSliderProps> = ({images}) => {
  var settings = {
    dots: true,
    arrows: false,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1
  };

  return (
    // @ts-ignore
    <Slider {...settings}>
      {/* @ts-ignore */}
      {images.map((image: string, index: React.Key) => (
        <img className="img-responsive" src={image} key={index} />
      ))}
    </Slider>
  );
}