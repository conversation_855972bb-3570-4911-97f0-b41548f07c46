import {BottomSheet} from '@rneui/base';
import {Image, Text} from '@rneui/themed';
import React, {useCallback, useEffect, useState} from 'react';
import {Pressable, StyleSheet, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import theme from '~utils/config/themes/theme';
import ee, {EventNames} from '~utils/events';
import {handleImageDomain} from '~utils/helpers/image';
import {FavoriteShop} from '~utils/types/shop';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useUpdateFollowShopMutation} from '~utils/api/shop';

const FavoriteShopBottomSheet = () => {
  const [open, setOpen] = useState(false);
  const insets = useSafeAreaInsets();
  const [shop, setShop] = useState<FavoriteShop | null | undefined>(null);
  const mutation = useUpdateFollowShopMutation();

  useEffect(() => {
    ee.on(EventNames.OpenFavoriteShopBottomSheet, handleOpen);

    return () => {
      ee.off(EventNames.OpenFavoriteShopBottomSheet, handleOpen);
    };
  }, []);

  const handleOpen = useCallback((shopInfo?: FavoriteShop) => {
    setShop(shopInfo);
    setOpen(true);
  }, []);

  const handleClose = useCallback(() => {
    setOpen(false);
  }, []);

  const handleUnfollowShop = useCallback(() => {
    if (shop) {
      mutation.mutateAsync({
        shopId: shop.id,
        followState: false,
      });
    }

    handleClose();
  }, [shop]);

  return (
    <BottomSheet isVisible={open} onBackdropPress={handleClose}>
      <View style={[styles.container, {paddingBottom: insets.bottom + 20}]}>
        {shop ? (
          <View style={styles.shopContainer}>
            <Image source={{uri: handleImageDomain(shop.avatar)}} style={styles.shopAvatar} />
            <View>
              <Text style={styles.shopName}>{shop.name}</Text>
            </View>
          </View>
        ) : null}

        <Pressable style={styles.listItem} onPress={handleUnfollowShop}>
          <MaterialCommunityIcons name="bookmark-off" size={25} color={theme.colors.text} />
          <Text style={styles.listItemText}>Bỏ theo dõi</Text>
        </Pressable>
      </View>
    </BottomSheet>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: theme.spacing.l,
    paddingTop: theme.spacing.m,
    backgroundColor: theme.colors.white,
  },

  shopContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: theme.spacing.m,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray20,
  },
  shopAvatar: {
    width: 40,
    height: 40,
    borderRadius: 100,
    marginRight: theme.spacing.s,
  },
  shopName: {
    fontSize: theme.typography.md,
    fontWeight: '500',
  },
  listItem: {
    flexDirection: 'row',
    paddingVertical: theme.spacing.m,
    alignItems: 'center',
  },
  listItemText: {
    fontSize: theme.typography.lg,
    marginLeft: theme.spacing.s,
  },
});

export default FavoriteShopBottomSheet;
