import {Text} from '@rneui/themed';
import React, {useCallback} from 'react';
import {Pressable, StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import {handleImageDomain} from '~utils/helpers/image';
import Ionicons from 'react-native-vector-icons/Ionicons';
import ee, {EventNames} from '~utils/events';
import {useNavigation} from '~hooks';
import {QuickImage} from '~components/shared/QuickImage';

type FavoriteShopItemProps = {
  shop: any;
};

const FavoriteShopItem: React.FC<FavoriteShopItemProps> = props => {
  const navigation = useNavigation();
  const handleUnFollowPress = useCallback(() => {
    ee.emit(EventNames.OpenFavoriteShopBottomSheet, props.shop);
  }, []);

  const handleNavigate = useCallback(() => {
    if (props.shop) {
      navigation.navigate('SupplierDetail', {shopId: props.shop.slug});
    }
  }, [props.shop]);

  return (
    <Pressable style={styles.container} onPress={handleNavigate}>
      <QuickImage source={{uri: handleImageDomain(props.shop.avatar)}} style={styles.avatar} />
      <View style={{flex: 1}}>
        <Text style={styles.shopName}>{props.shop.name}</Text>
        <Text style={styles.shopAddress}>{props.shop.shortAddress}</Text>
      </View>
      <Ionicons
        name="ellipsis-horizontal"
        size={24}
        style={{alignSelf: 'center', marginLeft: 'auto', paddingHorizontal: theme.spacing.m, paddingVertical: theme.spacing.s}}
        color={theme.colors.textLight}
        onPress={handleUnFollowPress}
      />
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    padding: theme.spacing.s,
    paddingVertical: theme.spacing.m,
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray10,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 100,
    borderWidth: 1,
    borderColor: theme.colors.gray10,
    marginRight: theme.spacing.s,
  },
  shopName: {
    fontSize: theme.typography.md,
    fontWeight: '500',
    marginTop: theme.spacing.s,
  },
  shopAddress: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
  },
});

export default FavoriteShopItem;
