import produce from 'immer';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {Animated, Image, Pressable, ScrollView, StyleSheet, TextInput, View} from 'react-native';
import {getResizedImageUrl, handleImageDomain} from '~utils/helpers/image';
import {formatPriceToString} from '~utils/helpers/price';
import Product from '~utils/types/product';
import Variant from '~utils/types/variant';
import {hasVariants} from '~utils/helpers/product';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useAddToCartMutation} from '~utils/api/cart';
import {useGetCurrentUser} from '~utils/api/auth';
import {useAuthActions, useNavigation} from '~hooks';
import theme from '~utils/config/themes/theme';
import {useToast} from 'react-native-toast-notifications';
import * as Sentry from '@sentry/react-native';
import {FirebaseAnalyticsTypes} from '@react-native-firebase/analytics';
import {Input, Text, Button} from '@rneui/themed';
import Ionicons from 'react-native-vector-icons/Ionicons';
import ee, {EventNames} from '~utils/events';
import googleAnalytics from '~utils/helpers/analytics';

type OptionSelectProps = {
  product: Product;
  selectedVariantId?: string;
};

const getPreselectedVariantId = (selectedVariantId: string | undefined, product: Product, fallback: Variant | null): Variant | null => {
  if (selectedVariantId) {
    const variant = product.variants.find(v => v.id === selectedVariantId);
    return variant ?? null;
  }

  return fallback;
};

const OptionSelect: React.FC<OptionSelectProps> = props => {
  const priceInputRef = useRef<TextInput>(null);
  const isMultiVariants = useMemo(() => {
    return hasVariants(props.product);
  }, [props.product.id]);

  const {openLoginRequiredModal} = useAuthActions();
  const [selectedVariant, setSelectedVariant] = useState<Variant | null>(
    getPreselectedVariantId(props.selectedVariantId, props.product, isMultiVariants && props.product.variants.length > 1 ? null : props.product.variants[0]),
  );
  const [selectedOptions, setSelectedOptions] = useState<string[]>(selectedVariant ? ([selectedVariant.option1, selectedVariant.option2].filter(Boolean) as any) : []);
  const [quantity, setQuantity] = useState(1);
  const [sellingPrice, setSellingPrice] = useState(props.product.market_price);
  const insets = useSafeAreaInsets();
  const addToCartMutation = useAddToCartMutation();
  const {data: user} = useGetCurrentUser();
  const navigation = useNavigation();
  const [errors, setErrors] = useState<string[]>([]);
  const toast = useToast();
  const [selectedImageThumbnail, setSelectedImageThumbail] = useState<string | null>(null);
  const supplierPriceScale = useRef(new Animated.Value(1)).current;

  // optimize UX: auto select option has one value
  useEffect(() => {
    if (!selectedOptions.length) {
      const hasOneValueOption = props.product.options.findIndex(option => option.values.length === 1);
      if (hasOneValueOption !== -1) {
        let options = [];
        options[hasOneValueOption] = props.product.options[hasOneValueOption].values[0];

        setSelectedOptions(options);
      }
    }
  }, [props.product.id]);

  useEffect(() => {
    let variantTitle = selectedOptions.join(' / ');
    const variant = props.product.variants.find(v => v.title === variantTitle);
    if (variant) {
      let prevVariant = selectedVariant;
      if (prevVariant) {
        if (prevVariant.market_price !== variant.market_price) {
          setSellingPrice(variant.market_price);
          triggerSupplierPriceEffect();
        }
      } else {
        if (sellingPrice !== variant.market_price) {
          setSellingPrice(variant.market_price);
          triggerSupplierPriceEffect();
        }
      }
      setSelectedVariant(variant);
    }
  }, [selectedOptions]);

  const triggerSupplierPriceEffect = useCallback(() => {
    Animated.timing(supplierPriceScale, {
      duration: 150,
      toValue: 1.1,
      useNativeDriver: true,
    }).start(() => {
      Animated.timing(supplierPriceScale, {
        toValue: 1,
        useNativeDriver: true,
        duration: 150,
      }).start();
    });
  }, []);

  const handleOptionNameClick = useCallback((optionIndex: number, optionName: string, variantImage?: string | null) => {
    setSelectedOptions(prev =>
      produce(prev, draft => {
        draft[optionIndex] = optionName;
      }),
    );
    if (variantImage) {
      setSelectedImageThumbail(getResizedImageUrl(variantImage, 320, 320));
    }
  }, []);

  const handleAddToCart = useCallback(async () => {
    if (!user || !selectedVariant) {
      openLoginRequiredModal();

      return;
    }

    let validateErrs: string[] = [];

    setErrors([]);

    if (sellingPrice < selectedVariant.dropship_price) {
      validateErrs.push(`Giá bán phải cao hơn giá nhà cung cấp (${formatPriceToString(selectedVariant.dropship_price)})`);
    }

    if (validateErrs.length > 0) {
      setErrors(validateErrs);
      return;
    }

    try {
      await addToCartMutation
        .mutateAsync({
          user_id: user.id,
          items: [
            {
              dropship_selling_price: sellingPrice,
              quantity,
              variant_id: selectedVariant.id,
              note: '',
            },
          ],
        })
        .catch(err => {
          toast.show(err?.message, {
            type: 'danger',
            dangerColor: theme.colors.red,
          });

          Sentry.captureMessage('Them vao gio that bai');

          throw err;
        });

      // analytics
      const analyticsCartItems = [
        {
          quantity,
          price: sellingPrice,
          item_name: selectedVariant.product_title,
          item_id: selectedVariant.product_id,
          item_category: selectedVariant?.product_type?.split(' > ')[0],
          item_category2: selectedVariant?.product_type?.split(' > ')[1],
          item_variant: selectedVariant?.title,
        },
      ];
      const addToCardParams: FirebaseAnalyticsTypes.AddToCartEventParameters = {
        currency: 'VND',
        value: sellingPrice * quantity,
        items: analyticsCartItems,
      };
      const viewCartParams: FirebaseAnalyticsTypes.ViewCartEventParameters = {
        currency: 'VND',
        value: sellingPrice * quantity,
        items: analyticsCartItems,
      };

      googleAnalytics.logAddToCart(addToCardParams);
      googleAnalytics.logViewCart(viewCartParams);

      if (props.selectedVariantId) {
        navigation.replace('Cart');

        return;
      }

      navigation.replace('Cart', {
        preSelectedVariantIds: [selectedVariant.id],
        productId: selectedVariant.product_id,
      });
    } catch (error) {
      Sentry.captureMessage('Them vao gio that bai');
    }
  }, [selectedVariant, quantity, sellingPrice, user, props.selectedVariantId]);

  const getThumbnail = useMemo(() => {
    if (selectedVariant && selectedVariant.image_id) {
      const image = props.product.images.find(img => img.id === selectedVariant.image_id);
      if (image) {
        return handleImageDomain(getResizedImageUrl(image.src, 320, 320));
      }
    }

    return selectedImageThumbnail || handleImageDomain(getResizedImageUrl(props.product.image?.src, 320, 320));
  }, [props.product, selectedVariant, selectedImageThumbnail]);

  const imageViewerImages = useMemo(() => {
    return props.product.images.map(image => ({
      url: handleImageDomain(image.src),
      props: {
        type: 'image',
        productId: image.product_id,
      },
    }));
  }, [props.product.images]);

  const handleImagePress = useCallback(() => {
    let newIndex = 0;
    if (selectedImageThumbnail) {
      const imageIndex = props.product.images.findIndex(img => img.src === selectedImageThumbnail);
      if (imageIndex !== -1) {
        newIndex = imageIndex;
      }
    } else if (selectedVariant && selectedVariant.image_id) {
      const imageIndex = props.product.images.findIndex(img => img.id === selectedVariant.image_id);

      if (imageIndex !== -1) {
        newIndex = imageIndex;
      }
    }

    ee.emit(EventNames.OpenImageViewer, {newImages: imageViewerImages, newIndex});
  }, [props.product, selectedVariant, imageViewerImages, selectedImageThumbnail]);

  const handleSellingPriceChange = useCallback((priceString: string) => {
    setSellingPrice(parseInt(priceString.replace(/\./g, ''), 10) || ('' as any));
  }, []);

  const handleChangeQuantity = useCallback((text: string) => {
    setQuantity(parseInt(text, 10) || ('' as any));
  }, []);

  const handleAddQuantity = useCallback(() => {
    // @ts-ignore
    setQuantity(prev => parseInt(prev + 1, 10));
  }, []);

  const handleReduceQuantity = useCallback(() => {
    // @ts-ignore
    setQuantity(prev => (prev ? parseInt(prev - 1, 10) : prev));
  }, []);

  const getVariantImage = useCallback(
    (optionName: string, optionIndex: number) => {
      const firstVariant = props.product.variants.find(variant => variant[`option${optionIndex + 1}` as keyof Variant] === optionName);
      if (firstVariant?.image_id) {
        const imageIndex = props.product.images.findIndex(img => img.id === firstVariant.image_id);
        if (imageIndex !== -1) {
          return handleImageDomain(getResizedImageUrl(props.product.images[imageIndex].src, 100, 100));
        }
      }

      return null;
    },
    [props.product],
  );

  const handleViewSizeChart = useCallback(() => {
    ee.emit(EventNames.OpenImageViewer, {
      newImages: [
        {
          url: handleImageDomain(props.product.size_chart?.src as string),
          props: {
            type: 'image',
            productId: props.product.id,
          },
        },
      ],
      newIndex: 0,
    });
  }, [props.product.size_chart, props.product.id]);

  const handlePriceInputRefFocus = useCallback(() => {
    if (priceInputRef.current) {
      priceInputRef.current.focus();
    }
  }, []);

  return (
    <View style={styles.container}>
      <ScrollView style={{flex: 1}} showsVerticalScrollIndicator={false}>
        <View style={{paddingHorizontal: 4, paddingBottom: 12, flexDirection: 'row', alignItems: 'flex-start'}}>
          <Pressable onPress={handleImagePress} style={{position: 'relative', marginRight: theme.spacing.s}}>
            <Image
              style={{width: 100, height: 100, borderWidth: 1, borderColor: theme.colors.gray20, borderRadius: 12}}
              source={{
                uri: getThumbnail,
              }}
            />

            <View
              style={{
                backgroundColor: 'rgba(0,0,0,.7)',
                position: 'absolute',
                right: 4,
                top: 4,
                borderRadius: 100,
              }}>
              <Ionicons name="expand" size={14} style={{padding: 2}} color={theme.colors.white} />
            </View>
          </Pressable>
          {!selectedVariant && (
            <View style={{flex: 1}}>
              <Text style={{fontSize: theme.typography.base, marginBottom: 4}} numberOfLines={3}>
                {props.product.title}
              </Text>
              <Text style={[{fontSize: theme.typography.base}]}>
                <Text style={{fontSize: theme.typography.base, color: 'rgba(0,0,0,.5)'}}>Giá nhà cung cấp:</Text> {formatPriceToString(props.product.dropship_price)}
              </Text>
            </View>
          )}
          {selectedVariant && (
            <View style={{flex: 1}}>
              <Text style={{fontSize: theme.typography.base, marginBottom: 4, color: isMultiVariants ? theme.colors.textLight : theme.colors.text}} numberOfLines={3}>
                {selectedVariant.product_title}
              </Text>
              {isMultiVariants && <Text style={{marginBottom: 4}}>{selectedVariant.title === 'Default Title' ? '' : selectedVariant.title}</Text>}
              <Animated.Text style={[{color: theme.colors.textLight, fontSize: theme.typography.base}, {transform: [{scale: supplierPriceScale}]}]}>
                <Text style={{color: 'rgba(0,0,0,.5)', fontSize: theme.typography.base}}>Giá nhà cung cấp:</Text> {formatPriceToString(selectedVariant.dropship_price)}
              </Animated.Text>
            </View>
          )}
        </View>

        {isMultiVariants && (
          <View style={{paddingTop: 20}}>
            {props.product.options.map((option, index) => {
              return (
                <View style={{paddingHorizontal: 4, marginBottom: theme.spacing.m}} key={option.id}>
                  <View style={[theme.globalStyles.flexRow]}>
                    <Text style={{fontSize: theme.typography.base}}>{option.display_name}: </Text>
                    {Boolean(props.product.size_chart) && index === 0 && (
                      <Button
                        type="clear"
                        size="sm"
                        icon={{
                          type: 'ionicon',
                          name: 'chevron-forward',
                          size: 16,
                          color: theme.colors.blue,
                        }}
                        containerStyle={{marginLeft: 'auto'}}
                        iconRight
                        titleStyle={{
                          fontSize: theme.typography.base,
                          color: theme.colors.blue,
                        }}
                        onPress={handleViewSizeChart}>
                        Xem bảng quy đổi kích cỡ
                      </Button>
                    )}
                  </View>
                  <View style={{flexDirection: 'row', marginTop: 8, flexWrap: 'wrap'}}>
                    {option.values.map(optionName => {
                      let isSelected = selectedOptions[index] === optionName;
                      let nextIndex = index === 0 ? 1 : 0;
                      let hasVariant = true;
                      let variantImage = option.display_name?.toLowerCase().includes('màu') ? getVariantImage(optionName, index) : null;

                      if (selectedOptions[nextIndex]) {
                        const variant = props.product.variants.find(v => {
                          let optionArr = [];
                          optionArr[index] = optionName;
                          optionArr[nextIndex] = selectedOptions[nextIndex];
                          return v.title === optionArr.join(' / ');
                        });

                        if (!variant) {
                          hasVariant = false;
                        }
                      }
                      return (
                        <Pressable onPress={hasVariant ? () => handleOptionNameClick(index, optionName, variantImage) : undefined} key={optionName}>
                          <View
                            style={{
                              marginBottom: 8,
                              marginRight: 8,
                              paddingVertical: 2,
                              paddingHorizontal: 8,
                              backgroundColor: hasVariant ? (isSelected ? theme.colors.white : theme.colors.gray10) : theme.colors.gray30,
                              opacity: hasVariant ? 1 : 0.5,
                              borderRadius: 4,
                              borderWidth: 1,
                              borderColor: isSelected ? '#008060' : theme.colors.gray10,
                              flexDirection: 'row',
                              alignItems: 'center',
                              minWidth: 60,
                              justifyContent: 'center',
                            }}>
                            {variantImage ? <Image source={{uri: variantImage}} style={{width: 30, height: 30, borderRadius: 4}} /> : null}
                            <Text style={{paddingVertical: 6, fontSize: theme.typography.base, marginLeft: 4, color: isSelected ? theme.colors.primary : theme.colors.text}}>{optionName}</Text>
                          </View>
                        </Pressable>
                      );
                    })}
                  </View>
                </View>
              );
            })}
          </View>
        )}

        <View
          style={{
            paddingVertical: 20,
            paddingBottom: insets.bottom + 20,
          }}>
          {errors.length > 0 && (
            <View>
              {errors.map(errorText => (
                <Text style={{color: theme.colors.red}} key={errorText}>
                  {errorText}
                </Text>
              ))}
            </View>
          )}

          <View>
            <View style={[theme.globalStyles.flexRow, {alignItems: 'flex-start', marginBottom: theme.spacing.m}]}>
              <Text style={{fontSize: theme.typography.base, marginTop: theme.spacing.s, width: 120}}>Số lượng</Text>
              {selectedVariant?.inventory_quantity === 0 ? (
                <Text style={[{fontSize: theme.typography.base, marginTop: theme.spacing.s, color: theme.colors.red}]}>Hết hàng</Text>
              ) : (
                <Input
                  keyboardType="numeric"
                  value={String(quantity)}
                  inputStyle={{maxWidth: 52, textAlign: 'center', fontSize: theme.typography.lg, padding: 0, margin: 0}}
                  containerStyle={{width: 150}}
                  inputContainerStyle={{padding: 0, margin: 0, height: 40}}
                  rightIcon={{type: 'ionicon', name: 'add-circle', size: 26, color: theme.colors.textLight, onPress: handleAddQuantity}}
                  leftIcon={{type: 'ionicon', name: 'remove-circle', size: 26, color: theme.colors.textLight, onPress: handleReduceQuantity}}
                  onChangeText={handleChangeQuantity}
                  renderErrorMessage={false}
                />
              )}
            </View>
            {selectedVariant && (
              <View style={[{backgroundColor: theme.colors.secondary, padding: 8, borderRadius: 12, marginBottom: theme.spacing.m}]}>
                <Text style={{fontSize: theme.typography.base}}>
                  Bạn được bán với giá quy định từ{' '}
                  <Text style={[theme.globalStyles.fontBold, styles.text]}>{formatPriceToString(selectedVariant?.dropship_selling_price_min || selectedVariant?.dropship_price)}</Text> đến{' '}
                  <Text style={[theme.globalStyles.fontBold, styles.text]}>{formatPriceToString(selectedVariant?.market_price ?? 0)}</Text>
                </Text>
              </View>
            )}
            <View style={[theme.globalStyles.flexRow, {alignItems: 'flex-start'}]}>
              <Text style={{fontSize: theme.typography.base, marginTop: theme.spacing.s, width: 120}}>Giá bán của bạn (mỗi {props.product.unit_name?.toLowerCase()})</Text>
              <Input
                keyboardType="numeric"
                value={formatPriceToString(sellingPrice, false)}
                inputStyle={{maxWidth: 95, textAlign: 'center', fontSize: theme.typography.lg, padding: 0, margin: 0}}
                inputContainerStyle={{padding: 0, margin: 0, height: 40, borderColor: theme.colors.primary, borderWidth: 1, borderBottomWidth: 1}}
                containerStyle={{width: 150}}
                ref={priceInputRef as any}
                rightIcon={
                  <Pressable onPress={handlePriceInputRefFocus}>
                    <Text style={{color: theme.colors.textLight}}>đ</Text>
                  </Pressable>
                }
                onChangeText={handleSellingPriceChange}
                // renderErrorMessage={false}
              />
            </View>
          </View>

          <View style={styles.cartSummary}>
            <View style={{justifyContent: 'flex-end', flexDirection: 'row', marginBottom: theme.spacing.xs}}>
              <View style={{marginRight: 20}}>
                <Text style={[styles.text, {width: 130}]}>Tổng giá bán</Text>
              </View>
              <View style={{width: 130}}>
                <Text>{formatPriceToString(sellingPrice * quantity)}</Text>
              </View>
            </View>
            <View style={{justifyContent: 'flex-end', flexDirection: 'row'}}>
              <View style={[{marginRight: 20}]}>
                <Text style={[styles.text, {width: 130}]}>Tổng lợi nhuận</Text>
              </View>
              <View style={{width: 130}}>
                <Text style={[{color: theme.colors.primary}]}>
                  {formatPriceToString(Math.max(sellingPrice * quantity - (selectedVariant?.dropship_price ?? props.product.dropship_price) * quantity, 0))}
                </Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>

      <View style={{flexDirection: 'row', alignItems: 'center'}}>
        <Button
          size="sm"
          disabled={!selectedVariant || !quantity || selectedVariant?.inventory_quantity === 0 || addToCartMutation.isLoading}
          color={theme.colors.primary}
          containerStyle={{flex: 1, marginRight: 8}}
          iconRight
          icon={{type: 'ionicon', name: 'chevron-forward', size: 20, color: !selectedVariant || !quantity ? theme.colors.gray50 : theme.colors.white}}
          buttonStyle={{
            borderRadius: 100,
          }}
          loading={addToCartMutation.isLoading}
          onPress={handleAddToCart}>
          {props.selectedVariantId ? 'Cập nhật giỏ' : 'Thêm vào giỏ'}
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 12,
    flex: 1,
  },
  cartSummary: {
    // backgroundColor: theme.colors.secondary,
    paddingHorizontal: 8,
    paddingVertical: 8,
    marginTop: theme.spacing.l,
    borderRadius: 8,
    alignItems: 'flex-start',
    borderWidth: 1,
    borderColor: theme.colors.textLight,
    borderStyle: 'dashed',
  },
  text: {
    fontSize: theme.typography.base,
  },
  textLight: {
    color: theme.colors.textLight,
  },
  textBold: {},
});

export default OptionSelect;
