import React, {useCallback, useMemo} from 'react';
import {Linking, Pressable, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import dayjs from 'dayjs';
import {useNavigation} from '~hooks';
import {QuickImage} from '~components/shared/QuickImage';
import {handleImageDomain} from '~utils/helpers/image';
import theme from '~utils/config/themes/theme';
import {Referrer} from '~utils/types/user';
// @ts-ignore
import ZaloIcon from '~assets/zalo.png';
// @ts-ignore
import CoinIcon from '~assets/coin.png';

type InvitedItemProps = {
  item: Referrer;
};

const InvitedItem: React.FC<InvitedItemProps> = ({item}) => {
  const navigation = useNavigation();

  const handleZaloIconPress = useCallback(() => {
    Linking.openURL(`https://zalo.me/${item.user_phone}`);
  }, [item.user_phone]);

  const handleGoToReferralDetail = useCallback(() => {
    navigation.push('ReferralDetail', {refferal: item});
  }, [navigation, item]);

  const formattedDate = useMemo(() => dayjs(item.created_at).format('DD/MM/YYYY'), [item.created_at]);

  const onboardStatusButton = useMemo(
    () =>
      item.dropship_order_count < 1 ? (
        <TouchableOpacity onPress={handleGoToReferralDetail} style={[styles.onboardButton, styles.onboardingButton]}>
          <Text style={styles.onboardingText}>Chưa có đơn</Text>
          <QuickImage source={CoinIcon} style={styles.coinIcon} />
        </TouchableOpacity>
      ) : (
        <TouchableOpacity onPress={handleGoToReferralDetail} style={[styles.onboardButton, styles.successOnboardButton]}>
          <Text style={styles.successOnboardText}>Đã có đơn</Text>
        </TouchableOpacity>
      ),
    [item.dropship_order_count, handleGoToReferralDetail],
  );

  return (
    <Pressable style={styles.referrerItem} onPress={handleGoToReferralDetail}>
      <Text style={styles.dateText}>{formattedDate}</Text>
      <View style={styles.contentContainer}>
        <View style={styles.userInfoContainer}>
          <QuickImage
            source={{
              uri: handleImageDomain(item.avatar),
            }}
            style={styles.avatar}
          />
          <Text style={styles.userName}>{item.user_name}</Text>
        </View>
        <View style={styles.actionsContainer}>
          <TouchableOpacity onPress={handleZaloIconPress}>
            <QuickImage source={ZaloIcon} style={styles.zaloIcon} />
          </TouchableOpacity>
          {onboardStatusButton}
        </View>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  referrerItem: {
    paddingHorizontal: theme.spacing.m,
    paddingVertical: theme.spacing.m,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray20,
    backgroundColor: theme.colors.white,
  },
  userName: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
  },
  coinIcon: {
    width: 22,
    height: 22,
    marginLeft: theme.spacing.xs,
  },
  zaloIcon: {
    width: 24,
    height: 24,
  },
  avatar: {
    width: 30,
    height: 30,
    borderRadius: 30,
    marginRight: theme.spacing.s,
  },
  dateText: {
    marginBottom: theme.spacing.s,
    fontSize: theme.typography.sm,
    color: theme.colors.textLight,
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  userInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  onboardButton: {
    marginLeft: theme.spacing.m,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 1,
    paddingHorizontal: 2,
    borderRadius: 20,
  },
  onboardingButton: {
    // backgroundColor: '#f5f0e1',
  },
  successOnboardButton: {
    backgroundColor: theme.colors.primaryLight,
    paddingHorizontal: 8,
  },
  onboardingText: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
    fontWeight: 'bold',
  },
  successOnboardText: {
    fontSize: theme.typography.base,
    color: theme.colors.primary,
    fontWeight: 'bold',
  },
});

export default InvitedItem;
