import {Button, Dialog, Text} from '@rneui/themed';
import React, {useCallback, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import Order from '~utils/types/order';
import dayjs from 'dayjs';
import {useAddToCartMutation} from '~utils/api/cart';
import {useGetCurrentUser} from '~utils/api/auth';
import {useNavigation} from '~hooks';
import {FirebaseAnalyticsTypes} from '@react-native-firebase/analytics';
import googleAnalytics from '~utils/helpers/analytics';
import {useToast} from 'react-native-toast-notifications';

type OrderCancelledInfoProps = {
  order: Order;
};

const OrderCancelledInfo: React.FC<OrderCancelledInfoProps> = props => {
  const mutation = useAddToCartMutation();
  const {data: user} = useGetCurrentUser();
  const navigation = useNavigation();
  const [dialogVisible, setDialogVisible] = useState(false);
  const toast = useToast();

  const cancalledByName = props.order.cancelled_by === 'buyer' ? 'Bạn' : props.order.cancelled_by === 'seller' ? 'Nhà cung cấp' : props.order.cancelled_by === 'admin' ? 'Admin' : 'Người bán';

  const handleRepurchase = useCallback(async () => {
    setDialogVisible(true);
  }, []);

  const handleSubmitRepurchase = useCallback(async () => {
    try {
      if (user?.id) {
        await mutation.mutateAsync({
          user_id: user.id,
          items: props.order.line_items.map(lineItem => ({
            dropship_selling_price: Number(lineItem.price),
            quantity: lineItem.quantity,
            variant_id: lineItem.variant_id,
            note: '',
          })),
        });

        // analytics
        const analyticsCartItems = props.order.line_items.map(lineItem => ({
          quantity: lineItem.quantity,
          price: Number(lineItem.price),
          item_name: lineItem.title,
          item_id: lineItem.product_id,
          item_category: lineItem.product_type?.split(' > ')[0],
          item_category2: lineItem.product_type?.split(' > ')[1],
          item_variant: lineItem.variant_title,
        }));

        const totalCartValue = analyticsCartItems.reduce((a, c) => a + c.price * c.quantity, 0);
        const addToCardParams: FirebaseAnalyticsTypes.AddToCartEventParameters = {
          currency: 'VND',
          value: totalCartValue,
          items: analyticsCartItems,
        };
        const viewCartParams: FirebaseAnalyticsTypes.ViewCartEventParameters = {
          currency: 'VND',
          value: totalCartValue,
          items: analyticsCartItems,
        };

        googleAnalytics.logAddToCart(addToCardParams);
        googleAnalytics.logViewCart(viewCartParams);

        setDialogVisible(false);

        setTimeout(() => {
          navigation.navigate('Cart', {
            preSelectedVariantIds: props.order.line_items.map(lineItem => lineItem.variant_id),
          });
        }, 200);
      }
    } catch (error: any) {
      toast.show(error?.response?.data?.message ?? ('Có lỗi xảy ra' as string), {
        placement: 'center',
        type: 'danger',
      });
    }
  }, [props.order, user]);
  return (
    <View style={styles.container}>
      <Text style={styles.titleStyle}>Đơn hàng đã hủy</Text>
      <Text style={[styles.cancelReason, {marginTop: theme.spacing.s}]}>
        {cancalledByName} đã hủy đơn hàng này vào lúc {dayjs(props.order.cancelled_at).format('HH:mm DD/MM/YYYY')}
      </Text>
      <Text style={styles.cancelReason}>
        Lý do hủy: <Text style={[styles.cancelReason, styles.cancelReasonItalic]}>{props.order.cancel_reason}</Text>
      </Text>

      <View style={{alignSelf: 'flex-end', marginTop: theme.spacing.m}}>
        <Button
          onPress={handleRepurchase}
          size="sm"
          buttonStyle={{backgroundColor: theme.colors.white, borderRadius: 30, paddingVertical: 4, paddingHorizontal: 20}}
          titleStyle={{color: theme.colors.text, fontSize: theme.typography.base}}
          icon={{
            type: 'ionicon',
            name: 'reload',
            size: 14,
          }}
          loading={mutation.isLoading}
          loadingProps={{
            color: theme.colors.primary,
          }}>
          Đặt lại
        </Button>
      </View>

      {/* @ts-ignore */}
      <Dialog isVisible={dialogVisible}>
        <Dialog.Title
          title="Thêm vào giỏ?"
          titleStyle={{
            color: theme.colors.text,
          }}
        />

        <Text style={styles.textNormal}>Thêm vào giỏ các sản phẩm tương tự</Text>

        {/* @ts-ignore */}
        <Dialog.Actions>
          <Dialog.Button title={'ĐỒNG Ý'} titleStyle={{color: theme.colors.primary}} onPress={handleSubmitRepurchase} loading={mutation.isLoading} />
          <Dialog.Button title={'TRỞ VỀ'} titleStyle={{color: theme.colors.textLight}} onPress={() => setDialogVisible(false)} />
        </Dialog.Actions>
      </Dialog>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: theme.spacing.m,
    backgroundColor: theme.colors.gray20,
  },
  titleStyle: {
    fontSize: theme.typography.base,
    fontWeight: 'bold',
    color: theme.colors.red,
  },
  cancelReason: {
    marginTop: theme.spacing.xs,
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
  },
  cancelReasonItalic: {
    fontStyle: 'italic',
  },
  textNormal: {
    fontSize: theme.typography.base,
  },
});

export default OrderCancelledInfo;
