import {SCREEN_HEIGHT} from '@gorhom/bottom-sheet';
import {DialogActions} from '@rneui/base/dist/Dialog/Dialog.Actions';
import {Button, CheckBox, Dialog, Input, Text} from '@rneui/themed';
import React, {useCallback, useMemo, useState} from 'react';
import {ScrollView, StyleSheet} from 'react-native';
import {useCancelOrderMutation} from '~utils/api/order';
import theme from '~utils/config/themes/theme';
import Order from '~utils/types/order';

type OrderCancelButtonProps = {
  order: Order;
  onCancelSucess?: () => void;
};

const OrderCancelButton: React.FC<OrderCancelButtonProps> = props => {
  const mutation = useCancelOrderMutation();
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [selectedReasonIndex, setSelectedReasonIndex] = useState(-1);
  const [otherReasonText, setOtherReasonText] = useState('');
  const [errorMsg, setErrorMsg] = useState('');

  const handleCheck = useCallback((index: number) => {
    setSelectedReasonIndex(index);
  }, []);

  const handleOpenDialog = useCallback(() => {
    setConfirmDialogOpen(true);
  }, []);

  const handleCloseDialog = useCallback(() => {
    setConfirmDialogOpen(false);
  }, []);

  const isOtherReasonSelected = useMemo(() => {
    return selectedReasonIndex === -1 ? false : props.order.buyer_cancel_order_reasons[selectedReasonIndex] === 'Lý do khác';
  }, [selectedReasonIndex]);

  const handleSubmit = useCallback(async () => {
    if (isOtherReasonSelected && !otherReasonText) {
      setErrorMsg('Vui lòng nhập lý do');

      return;
    }
    if (selectedReasonIndex === -1) {
      setErrorMsg('Vui lòng chọn lý do');
      return;
    }

    try {
      await mutation
        .mutateAsync({
          orderId: props.order.id,
          reason: isOtherReasonSelected ? otherReasonText : props.order.buyer_cancel_order_reasons[selectedReasonIndex],
        })
        .catch(err => {
          throw err;
        });

      if (props.onCancelSucess) {
        props.onCancelSucess();
      }
    } catch (error) {}

    handleCloseDialog();
  }, [errorMsg, selectedReasonIndex, isOtherReasonSelected, otherReasonText, props.order]);

  if (!props.order.buyer_cancellable) {
    return null;
  }

  return (
    <>
      <Button
        size="sm"
        containerStyle={{marginTop: 100, paddingHorizontal: 12, alignSelf: 'center'}}
        buttonStyle={{borderRadius: 12, borderColor: 'red'}}
        titleStyle={{color: theme.colors.red, fontWeight: '600', fontSize: theme.typography.md}}
        onPress={handleOpenDialog}
        type="clear">
        Hủy đơn hàng
      </Button>

      <Dialog isVisible={confirmDialogOpen} onBackdropPress={handleCloseDialog} onRequestClose={handleCloseDialog}>
        <Dialog.Title title="Vui lòng chọn lý do hủy đơn" />
        <ScrollView style={{maxHeight: SCREEN_HEIGHT * 0.5}}>
          {props.order.buyer_cancel_order_reasons?.map((reason, index) => {
            return (
              <CheckBox
                wrapperStyle={{paddingHorizontal: 0}}
                key={reason}
                title={reason}
                iconType="ionicon"
                uncheckedIcon="square-outline"
                checkedIcon="checkbox"
                checkedColor="#008060"
                uncheckedColor={theme.colors.text}
                containerStyle={{backgroundColor: 'white', borderWidth: 0}}
                textStyle={styles.checkBoxTitle}
                checked={selectedReasonIndex === index}
                onPress={() => handleCheck(index)}
              />
            );
          })}
          {isOtherReasonSelected && <Input onChangeText={setOtherReasonText} multiline numberOfLines={5} inputContainerStyle={{paddingTop: 20}} autoFocus />}
        </ScrollView>
        {Boolean(errorMsg) && <Text style={{color: theme.colors.red}}>{errorMsg}</Text>}
        <DialogActions>
          <Dialog.Button title="TRỞ VỀ" onPress={handleCloseDialog} />
          <Dialog.Button title="HỦY ĐƠN HÀNG" titleStyle={{color: theme.colors.red}} onPress={handleSubmit} />
        </DialogActions>
      </Dialog>
    </>
  );
};

const styles = StyleSheet.create({
  checkBoxTitle: {
    fontSize: theme.typography.base,
    fontWeight: '400',
    color: theme.colors.text,
  },
});

export default OrderCancelButton;
