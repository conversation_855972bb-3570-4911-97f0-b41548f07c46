import {Button, Text} from '@rneui/themed';
import React, {useCallback} from 'react';
import {Alert, StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import Order from '~utils/types/order';
import {useAddToCartMutation} from '~utils/api/cart';
import {useGetCurrentUser} from '~utils/api/auth';
import {useNavigation} from '~hooks';
import {FirebaseAnalyticsTypes} from '@react-native-firebase/analytics';
import googleAnalytics from '~utils/helpers/analytics';
import dayjs from 'dayjs';

type OrderCompletedProps = {
  order: Order;
};

const OrderCompleted: React.FC<OrderCompletedProps> = props => {
  const mutation = useAddToCartMutation();
  const {data: user} = useGetCurrentUser();
  const navigation = useNavigation();

  const handleRepurchase = useCallback(async () => {
    Alert.alert('Thêm vào giỏ', 'Thêm vào giỏ các sản phẩm tương tự?', [
      {
        text: 'Không',
        style: 'destructive',
      },
      {
        text: 'Đồng ý',
        onPress: async () => {
          try {
            if (user?.id) {
              await mutation.mutateAsync({
                user_id: user.id,
                items: props.order.line_items.map(lineItem => ({
                  dropship_selling_price: Number(lineItem.price),
                  quantity: lineItem.quantity,
                  variant_id: lineItem.variant_id,
                  note: '',
                })),
              });

              // analytics
              const analyticsCartItems = props.order.line_items.map(lineItem => ({
                quantity: lineItem.quantity,
                price: Number(lineItem.price),
                item_name: lineItem.title,
                item_id: lineItem.product_id,
                item_category: lineItem.product_type?.split(' > ')[0],
                item_category2: lineItem.product_type?.split(' > ')[1],
                item_variant: lineItem.variant_title,
              }));

              const totalCartValue = analyticsCartItems.reduce((a, c) => a + c.price * c.quantity, 0);
              const addToCardParams: FirebaseAnalyticsTypes.AddToCartEventParameters = {
                currency: 'VND',
                value: totalCartValue,
                items: analyticsCartItems,
              };
              const viewCartParams: FirebaseAnalyticsTypes.ViewCartEventParameters = {
                currency: 'VND',
                value: totalCartValue,
                items: analyticsCartItems,
              };

              googleAnalytics.logAddToCart(addToCardParams);
              googleAnalytics.logViewCart(viewCartParams);

              navigation.navigate('Cart', {
                preSelectedVariantIds: props.order.line_items.map(lineItem => lineItem.variant_id),
              });
            }
          } catch (error) {}
        },
      },
    ]);
  }, [props.order]);

  return (
    <View style={styles.container}>
      <Text style={styles.titleStyle}>Đơn hàng thành công</Text>

      {props.order.escrow_release_at ? (
        <Text style={styles.text}>
          Đã đối soát thành công vào <Text style={[styles.text, styles.textItalic]}>{dayjs(props.order.escrow_release_at).format('DD/MM/YYYY HH:mm')}</Text>
        </Text>
      ) : null}
      {!props.order.escrow_release_at ? (
        <Text style={styles.text}>
          Ngày đối soát dự kiến: <Text style={[styles.text, styles.textItalic]}>{dayjs(props.order.estimated_escrow_release_at).format('DD/MM/YYYY HH:mm')}</Text>
        </Text>
      ) : null}

      <View style={{alignSelf: 'flex-end', marginTop: theme.spacing.m, flexDirection: 'row'}}>
        <Button
          onPress={handleRepurchase}
          size="sm"
          buttonStyle={{backgroundColor: theme.colors.white, borderRadius: 30, paddingVertical: 4, paddingHorizontal: 20}}
          titleStyle={{color: theme.colors.text, fontSize: theme.typography.base}}
          icon={{
            type: 'ionicon',
            name: 'reload',
            size: 14,
          }}
          loading={mutation.isLoading}
          loadingProps={{
            color: theme.colors.primary,
          }}>
          Tạo lần nữa
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: theme.spacing.m,
    backgroundColor: theme.colors.gray20,
  },
  titleStyle: {
    fontSize: theme.typography.base,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  text: {
    marginTop: theme.spacing.xs,
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
  },
  textItalic: {
    fontStyle: 'italic',
  },
});

export default OrderCompleted;
