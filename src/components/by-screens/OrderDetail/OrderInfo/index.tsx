import Clipboard from '@react-native-clipboard/clipboard';
import {Text} from '@rneui/themed';
import dayjs from 'dayjs';
import React, {useCallback, useMemo} from 'react';
import {StyleSheet, View} from 'react-native';
import {useToast} from 'react-native-toast-notifications';
import Ionicons from 'react-native-vector-icons/Ionicons';
import theme from '~utils/config/themes/theme';
import {getMarketPlaceBrandColor, getMarketPlaceLabel, getOrderStatusLabelColor} from '~utils/helpers/order';
import Order from '~utils/types/order';

type OrderInfoProps = {
  order: Order;
};

const OrderInfo: React.FC<OrderInfoProps> = props => {
  const toast = useToast();

  const handleCopyOrderName = useCallback(() => {
    Clipboard.setString(props.order?.name as string);
    toast.show('Đã sao chép mã đơn hàng', {placement: 'center', duration: 1000});
  }, [props.order]);

  const marketPlaceLabel = useMemo(() => {
    if (props.order.dropship_marketplace) {
      return getMarketPlaceLabel(props.order.source_name?.split(':')[1] as any);
    }
    return '';
  }, [props.order]);

  const handleCopyOrderIdentifier = useCallback(() => {
    Clipboard.setString(props.order?.name as string);
    toast.show('Đã sao chép mã đơn hàng', {placement: 'center', duration: 1000});
  }, [props.order]);

  return (
    <View style={styles.card}>
      {props.order.dropship_marketplace ? (
        <>
          <View style={{borderRadius: 4, marginBottom: theme.spacing.s, backgroundColor: getMarketPlaceBrandColor(props.order.source_name?.split(':')[1] as any)}}>
            <Text style={{color: theme.colors.white, paddingHorizontal: 6, paddingVertical: 2, fontSize: theme.typography.base}}>Đơn {marketPlaceLabel}</Text>
          </View>
          <View style={{flexDirection: 'row'}}>
            <Text>
              Mã đơn {marketPlaceLabel}: {props.order.source_identifier}
            </Text>
            <Ionicons name="copy-outline" size={18} color={theme.colors.textLight} onPress={handleCopyOrderIdentifier} style={{marginLeft: theme.spacing.s}} />
          </View>
        </>
      ) : (
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <Text style={[styles.text2, {fontWeight: 'bold', marginRight: 8}]}>Đơn hàng {props.order.name}</Text>
          <Ionicons name="copy-outline" size={18} color={theme.colors.primary} onPress={handleCopyOrderName} />
        </View>
      )}

      <Text style={[styles.text3, {marginTop: 4}]}>Ngày tạo: {dayjs(props.order.created_at).format('DD/MM/YYYY HH:mm')}</Text>
      <Text style={[styles.text3, {marginTop: 4}]}>
        Trạng thái: <Text style={{marginTop: 20, fontSize: 14, fontWeight: 'bold', color: getOrderStatusLabelColor(props.order.status_label as string)}}>{props.order.status_label}</Text>
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    paddingHorizontal: 8,
    paddingVertical: 20,
    backgroundColor: '#fff',
    alignItems: 'flex-start',
  },
  text2: {
    fontSize: theme.typography.md,
  },
  text3: {
    fontSize: 16,
  },
});

export default OrderInfo;
