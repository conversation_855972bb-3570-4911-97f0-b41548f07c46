import React, {useCallback, useMemo} from 'react';
import {Platform, Pressable, StyleSheet, View} from 'react-native';
import Card from '~components/shared/Card';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Ionicons from 'react-native-vector-icons/Ionicons';
import theme from '~utils/config/themes/theme';
import {Button, Text} from '@rneui/themed';
import Hyperlink from 'react-native-hyperlink';
import Order from '~utils/types/order';
import {useNavigation} from '~hooks';
import dayjs from 'dayjs';
import VerticalStepper from '~components/shared/VerticalStepper';
import Clipboard from '@react-native-clipboard/clipboard';
import {useToast} from 'react-native-toast-notifications';
import {formatPhoneString} from '~utils/helpers/common';
import {FulfillmentEventDescription} from '~screens/FulfillmentEvents';
import {getOrderBeforeFulfillmentEvents} from '~utils/helpers/order';
import {useGetFulfillmentEvents} from '~utils/api/order';
import useRefreshOnFocus from '~hooks/useRefreshOnFocus';

import PhoneHyperLink from '~utils/helpers/phone-hyperlink';

type ShippingTrackingProps = {
  order: Order;
};

const ShippingTracking: React.FC<ShippingTrackingProps> = props => {
  const navigation = useNavigation();
  const {data: fulfillmentEvents, refetch: refetchFulfillments} = useGetFulfillmentEvents(props.order.id);
  useRefreshOnFocus(refetchFulfillments);
  const toast = useToast();

  const fulfillment = useMemo(() => {
    return props.order?.fulfillments?.[0];
  }, [props.order]);

  const handleGoToFulfillmentEvents = useCallback(() => {
    navigation.navigate('FulfillmentEvents', {orderId: props.order.id});
  }, [props.order.id]);

  const handleCopyTrackingNumberLink = useCallback(() => {
    Clipboard.setString(fulfillment?.tracking_url ?? '');
    toast.show('Đã sao chép', {placement: 'center', duration: 1000});
  }, [fulfillment?.tracking_url]);

  const formatedPhone = useMemo(() => {
    return props.order?.shipping_address?.phone ? formatPhoneString(props.order?.shipping_address?.phone) : '';
  }, [props.order?.shipping_address?.phone]);

  const steps = [
    ...(fulfillmentEvents ?? []).map(fulfillmentEvent => ({
      title: fulfillmentEvent.status_text,
      description: <FulfillmentEventDescription event={fulfillmentEvent} />,
      isActive: false,
    })),
    ...getOrderBeforeFulfillmentEvents(props.order).map(event => ({
      title: event.title,
      description: (
        <View>
          {Boolean(event.happened_at) && <Text style={{color: theme.colors.textLight, fontSize: theme.typography.sm}}>{dayjs(event.happened_at).format('DD-MM-YYYY HH:mm')}</Text>}
          {Boolean(event.message) && <Text style={{color: theme.colors.textLight, fontSize: theme.typography.base, marginTop: 8}}>{event.message}</Text>}
        </View>
      ),
    })),
  ];

  const shippingLine = useMemo(() => {
    return props.order?.shipping_lines?.[0];
  }, [props.order]);

  const minDeliveryDate = useMemo(() => {
    try {
      if (shippingLine) {
        return `${dayjs(shippingLine.estimated_delivery_at_min).format('DD/MM')}`;
      }
    } catch (error) {}
  }, [shippingLine]);

  const maxDeliveryDate = useMemo(() => {
    try {
      if (shippingLine) {
        return `${dayjs(shippingLine.estimated_delivery_at_max).format('DD/MM')}`;
      }
    } catch (error) {}
  }, [shippingLine]);

  if (props.order.dropship_marketplace) {
    return null;
  }

  return (
    <>
      <Card>
        <View style={{width: '100%', flexDirection: 'row', alignItems: 'center', marginTop: -theme.spacing.s, marginBottom: theme.spacing.s}}>
          <MaterialCommunityIcons name="truck-fast-outline" size={20} style={{marginRight: 4}} color={theme.colors.text} />
          <Text style={{fontSize: theme.typography.lg}}>Theo dõi vận chuyển</Text>
          <Button
            size="sm"
            title="XEM"
            type="clear"
            titleStyle={{fontSize: theme.typography.md, color: theme.colors.blue}}
            containerStyle={{marginLeft: 'auto'}}
            onPress={handleGoToFulfillmentEvents}
          />
        </View>
        {/*  */}
        <Pressable onPress={handleGoToFulfillmentEvents} style={{paddingBottom: 20, borderBottomWidth: 1, borderBottomColor: 'rgba(0,0,0,0.1)', marginBottom: 10}}>
          {Boolean(fulfillment?.tracking_url) && (
            <View style={{flexDirection: 'row', marginTop: 4, alignItems: 'flex-start'}}>
              <Text style={[styles.textContent, {marginRight: 8}]}>Link vận đơn: </Text>
              <View style={{flex: 1, flexDirection: 'row'}}>
                <Hyperlink
                  linkDefault={Platform.OS === 'web'}
                  onPress={
                    Platform.OS === 'web'
                      ? undefined
                      : url =>
                          navigation.navigate('WebView', {
                            url,
                            title: `${shippingLine?.pass_code ? `👉 Bốn số cuối: ${shippingLine.pass_code} 👈` : 'Tra cứu vận đơn'}`,
                          })
                  }
                  style={{flex: 1}}
                  linkStyle={{color: theme.colors.blue, textDecorationLine: 'underline'}}>
                  <Text style={[styles.textContent]} numberOfLines={1}>
                    {fulfillment?.tracking_url}
                  </Text>
                </Hyperlink>
                <Ionicons name="copy-outline" size={18} color={theme.colors.textLight} style={{paddingHorizontal: 12}} onPress={handleCopyTrackingNumberLink} />
              </View>
            </View>
          )}

          <View style={{flexDirection: 'row', marginTop: 4, alignItems: 'flex-start'}}>
            <Text style={[styles.textContent, {marginRight: 8}]}>Đơn vị vận chuyển: </Text>
            <View style={{flex: 1}}>
              <Text style={styles.textContent}>{fulfillment?.tracking_company ?? shippingLine?.source}</Text>
            </View>
          </View>

          <View style={{flexDirection: 'row', marginTop: 4, alignItems: 'center'}}>
            <Text style={[styles.textContent, {marginRight: 8}]}>Nhận hàng dự kiến: </Text>
            <Text style={styles.textContent}>
              {fulfillment?.estimated_delivery_at
                ? dayjs(fulfillment.estimated_delivery_at).format('DD-MM-YYYY HH:mm (dddd)')
                : `${minDeliveryDate === maxDeliveryDate ? `${minDeliveryDate}` : `${minDeliveryDate} - ${maxDeliveryDate}`}`}
            </Text>
          </View>

          <View style={{marginTop: 20}}>
            <VerticalStepper steps={[steps[0]]} destructive={props.order.status === 'cancelled'} />
          </View>
        </Pressable>
        {/*  */}

        <View style={{flexDirection: 'row', alignItems: 'center', marginBottom: 8}}>
          <Ionicons name="location-outline" size={16} style={{marginRight: 4}} color={theme.colors.text} />
          <Text style={{fontSize: theme.typography.lg}}>Địa chỉ nhận hàng</Text>
        </View>
        <View style={{paddingHorizontal: theme.spacing.s, alignItems: 'flex-start'}}>
          <Text selectable style={[styles.text3, styles.textGray, {marginBottom: 2}]}>
            {props.order.shipping_address?.name}
          </Text>
          <PhoneHyperLink linkStyle={{color: theme.colors.blue}}>
            <Text style={[styles.text3, styles.textGray]}>{formatedPhone}</Text>
          </PhoneHyperLink>
          <Text selectable style={[styles.text3, styles.textGray, {marginTop: 2}]}>
            {[props.order.shipping_address?.address1, props.order.shipping_address?.ward, props.order.shipping_address?.province, props.order.shipping_address?.city].join(', ')}
          </Text>
        </View>
      </Card>
    </>
  );
};

const styles = StyleSheet.create({
  textContent: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
  },
  text3: {
    fontSize: 16,
  },
  textGray: {
    color: theme.colors.textLight,
  },
});

export default ShippingTracking;
