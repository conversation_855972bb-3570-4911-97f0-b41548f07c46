import {Text} from '@rneui/themed';
import React, {useCallback, useMemo, useState} from 'react';
import {Platform, StyleSheet, View} from 'react-native';
import Card from '~components/shared/Card';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

import Order from '~utils/types/order';
import {getMarketPlaceLabel} from '~utils/helpers/order';
import {useToast} from 'react-native-toast-notifications';
import Clipboard from '@react-native-clipboard/clipboard';
import Hyperlink from 'react-native-hyperlink';
import {useNavigation} from '~hooks';
import BillOfLadingUpload from './BillOfLadingUpload';
import BillOfLadingUpdateDialog from './BillOfLadingUpdateDialog';

type MarketPlaceOrderProgressProps = {
  order: Order;
};

const MarketPlaceOrderProgress: React.FC<MarketPlaceOrderProgressProps> = props => {
  const toast = useToast();
  const [updateTrackingNumberDialogOpen, setUpdateTrackingNumberDialogOpen] = useState(false);
  const navigation = useNavigation();

  const isPaidComplete = useMemo(() => {
    return props.order.financial_status === 'paid';
  }, [props.order]);

  const isConfirmComplete = useMemo(() => {
    return props.order.confirm_status === 'confirmed';
  }, [props.order]);

  const isSubmitTrackingCodeActive = useMemo(() => {
    return isPaidComplete && isConfirmComplete;
  }, [isPaidComplete, isConfirmComplete]);

  const billOfLading = useMemo(() => {
    return props.order.shipping?.bill_of_lading;
  }, [props.order.shipping]);

  const handleCopyTrackingCode = useCallback(() => {
    Clipboard.setString(billOfLading?.bill_of_lading as string);
    toast.show('Đã sao chép', {placement: 'center', duration: 1000});
  }, [billOfLading]);

  return (
    <Card title="Tiến trình đơn">
      <OrderProgressStep step={1} isComplete={isPaidComplete} isActive={props.order.financial_status !== 'paid'}>
        <Text>Thanh toán đơn hàng</Text>
      </OrderProgressStep>
      <OrderProgressStep step={2} isComplete={isConfirmComplete} isActive={isPaidComplete && !isConfirmComplete}>
        <Text>Đợi nhà cung cấp xác nhận đơn</Text>
      </OrderProgressStep>
      <OrderProgressStep step={3} isActive={isPaidComplete && isConfirmComplete} isComplete={!!billOfLading?.bill_of_lading}>
        <Text>Gửi phiếu in vận đơn cho nhà cung cấp</Text>

        {billOfLading?.bill_of_lading ? (
          <>
            <Text style={{marginTop: theme.spacing.m, marginBottom: theme.spacing.s, fontSize: theme.typography.base, color: theme.colors.textLight}}>Đã gửi phiếu in vận đơn</Text>
            <View style={{flexDirection: 'row'}}>
              <Hyperlink
                linkDefault={Platform.OS === 'web'}
                onPress={Platform.OS === 'web' ? undefined : url => navigation.navigate('WebView', {url, title: 'Phiếu in vận đơn'})}
                style={{flex: 1}}
                linkStyle={{color: theme.colors.blue, textDecorationLine: 'underline'}}>
                <Text numberOfLines={1}>{billOfLading.bill_of_lading}</Text>
              </Hyperlink>
              <Ionicons name="copy-outline" size={16} color={theme.colors.text} onPress={handleCopyTrackingCode} style={{marginLeft: theme.spacing.xs}} />
              <MaterialCommunityIcons name="square-edit-outline" color={theme.colors.text} size={18} onPress={() => setUpdateTrackingNumberDialogOpen(true)} style={{marginLeft: theme.spacing.s}} />
            </View>
          </>
        ) : (
          <>
            <Text style={{fontSize: theme.typography.sm, marginTop: theme.spacing.xs}}>
              Tải xuống phiếu in vận đơn của {getMarketPlaceLabel(props.order.source_name?.split(':')[1] as any)} và gửi cho nhà cung cấp tại đây
            </Text>
            <BillOfLadingUpload order={props.order} isSubmitTrackingCodeActive={isSubmitTrackingCodeActive} />
            {/*  */}
          </>
        )}
      </OrderProgressStep>

      {billOfLading?.bill_of_lading && (
        <BillOfLadingUpdateDialog
          open={updateTrackingNumberDialogOpen}
          onClose={() => setUpdateTrackingNumberDialogOpen(false)}
          isSubmitTrackingCodeActive={isSubmitTrackingCodeActive}
          order={props.order}
        />
      )}
    </Card>
  );
};

const OrderProgressStep: React.FC<{step: number; isActive?: boolean; children: React.ReactNode; isComplete?: boolean}> = props => {
  return (
    <View style={[styles.container, !props.isActive && {opacity: 0.5}]}>
      <View style={styles.stepNumberContainer}>
        <Text style={[styles.stepText]}>{props.step}</Text>
      </View>

      <View style={styles.contentContainer}>{props.children}</View>

      {props.isComplete && (
        <View style={{marginLeft: theme.spacing.s, flexDirection: 'row', alignItems: 'center'}}>
          <Ionicons name="checkmark-circle" size={18} color={theme.colors.primary} />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.m,
  },
  stepNumberContainer: {
    backgroundColor: theme.colors.white,
    borderWidth: 1,
    borderColor: theme.colors.text,
    width: 20,
    height: 20,
    borderRadius: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepText: {
    color: theme.colors.text,
    fontSize: theme.typography.sm,
  },
  contentContainer: {
    marginLeft: theme.spacing.s,
    flexShrink: 1,
  },
});

export default MarketPlaceOrderProgress;
