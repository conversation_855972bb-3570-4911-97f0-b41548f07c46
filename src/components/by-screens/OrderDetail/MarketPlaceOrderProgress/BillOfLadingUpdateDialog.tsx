import {Dialog} from '@rneui/themed';
import React from 'react';
import BillOfLadingUpload from './BillOfLadingUpload';
import Order from '~utils/types/order';

type BillOfLadingUpdateDialogProps = {
  open: boolean;
  onClose: () => void;
  order: Order;
  isSubmitTrackingCodeActive: boolean;
};

const BillOfLadingUpdateDialog: React.FC<BillOfLadingUpdateDialogProps> = props => {
  return (
    <Dialog isVisible={props.open} onRequestClose={props.onClose} onBackdropPress={props.onClose}>
      <Dialog.Title title="Cập nhật mã vận đơn" />
      <BillOfLadingUpload isSubmitTrackingCodeActive={props.isSubmitTrackingCodeActive} order={props.order} onSubmitComplete={props.onClose} />
      <Dialog.Actions>
        <Dialog.Button title={'Trở về'} onPress={props.onClose} />
      </Dialog.Actions>
    </Dialog>
  );
};

export default BillOfLadingUpdateDialog;
