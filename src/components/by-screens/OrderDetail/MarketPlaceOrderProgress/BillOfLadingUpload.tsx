import {Button, Text} from '@rneui/themed';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {Animated, StyleSheet, View} from 'react-native';
import DocumentPicker, {DocumentPickerResponse} from 'react-native-document-picker';
import {useUpdateOrderFulfillmentLabel} from '~utils/api/order';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Order from '~utils/types/order';

type BillOfLadingUploadProps = {
  isSubmitTrackingCodeActive: boolean;
  order: Order;
  onSubmitComplete?: () => void;
};

const BillOfLadingUpload: React.FC<BillOfLadingUploadProps> = props => {
  const [awbFile, setAwbFile] = useState<DocumentPickerResponse | null>(null);
  const scale = useRef(new Animated.Value(1)).current;
  const mutation = useUpdateOrderFulfillmentLabel();
  const [fulfillmentLabelError, setFulfillmentLabelError] = useState('');

  useEffect(() => {
    if (awbFile) {
      setTimeout(() => {
        animateSubmitButton();
      }, 500);
    }
  }, [awbFile]);

  const animateSubmitButton = useCallback(() => {
    Animated.timing(scale, {
      toValue: 0.9,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      Animated.timing(scale, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }).start();
    });
  }, []);

  const handleSelectAwbFile = useCallback(async () => {
    const res = await DocumentPicker.pickSingle({
      allowMultiSelection: false,
      type: [DocumentPicker.types.images, DocumentPicker.types.pdf],
    });

    if (res) {
      setAwbFile(res);
    }
  }, []);

  const handleRemoveAwbFile = useCallback(() => {
    setAwbFile(null);
    setFulfillmentLabelError('');
  }, []);

  const handleSubmit = useCallback(async () => {
    if (!awbFile) {
      setFulfillmentLabelError('Vui lòng chọn file');
      return;
    }

    mutation
      .mutateAsync({
        label: awbFile,
        orderId: props.order.id,
      })
      .then(() => {
        if (props.onSubmitComplete) {
          props.onSubmitComplete();
        }
      })
      .catch(err => {
        setFulfillmentLabelError(err?.response?.data?.message ?? 'Có lỗi xảy ra');
      });
  }, [awbFile, props.order]);

  return (
    <>
      <View style={{flexDirection: 'row', alignItems: 'center', backgroundColor: theme.colors.gray20, marginTop: theme.spacing.s, padding: theme.spacing.s}}>
        {!awbFile ? (
          <Button
            loading={mutation.isLoading}
            title={'Chọn file'}
            disabled={!props.isSubmitTrackingCodeActive}
            disabledStyle={{
              backgroundColor: theme.colors.gray40,
            }}
            titleStyle={{fontSize: theme.typography.base}}
            size="sm"
            containerStyle={{marginLeft: theme.spacing.m}}
            icon={{
              name: 'attach-sharp',
              type: 'ionicon',
              size: 20,
              color: props.isSubmitTrackingCodeActive ? theme.colors.white : theme.colors.text,
            }}
            buttonStyle={{paddingHorizontal: theme.spacing.s, borderRadius: 8, paddingVertical: theme.spacing.s - 2}}
            onPress={handleSelectAwbFile}
          />
        ) : (
          <View style={{flexDirection: 'row', flex: 1}}>
            <Ionicons name="close-circle-outline" size={20} style={{marginRight: theme.spacing.s}} color={theme.colors.red} onPress={handleRemoveAwbFile} />
            <Text style={{flex: 1}} numberOfLines={1}>
              {awbFile.name}
            </Text>
          </View>
        )}
        <Animated.View
          // heartbeat animation
          style={{
            transform: [{scale: scale}],
          }}>
          <Button
            loading={mutation.isLoading}
            disabled={!props.isSubmitTrackingCodeActive || !awbFile}
            disabledStyle={{backgroundColor: theme.colors.gray40}}
            title={'Gửi'}
            titleStyle={{fontSize: theme.typography.base}}
            size="sm"
            containerStyle={{marginLeft: theme.spacing.m}}
            buttonStyle={{paddingHorizontal: theme.spacing.l, borderRadius: 8, paddingVertical: theme.spacing.s - 2}}
            onPress={handleSubmit}
          />
        </Animated.View>
      </View>
      <Text style={styles.errorText}>{fulfillmentLabelError}</Text>
    </>
  );
};

const styles = StyleSheet.create({
  errorText: {
    color: theme.colors.red,
    fontSize: theme.typography.base,
  },
});

export default BillOfLadingUpload;
