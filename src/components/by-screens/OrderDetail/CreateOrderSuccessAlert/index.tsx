import {Text} from '@rneui/themed';
import React from 'react';
import {View} from 'react-native';
import theme from '~utils/config/themes/theme';
// @ts-ignore
import ConfettiSuccessPNG from '~assets/confetti_success.png';
import {Image} from 'react-native';

const CreateOrderSucessAlert = () => {
  return (
    <View style={{paddingHorizontal: 12, backgroundColor: '#e3ffe9', paddingVertical: 12, marginBottom: 12, flexDirection: 'row'}}>
      <View style={{marginRight: theme.spacing.s}}>
        <Image source={ConfettiSuccessPNG} style={{width: 50, height: 50}} />
      </View>
      <View>
        <Text style={{fontWeight: 'bold', color: theme.colors.primary}}>Tạo đơn hàng thành công</Text>
        <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight, marginTop: 4}}>Đơn hàng đã được g<PERSON>i đến nhà cung cấp</Text>
      </View>
    </View>
  );
};

export default CreateOrderSucessAlert;
