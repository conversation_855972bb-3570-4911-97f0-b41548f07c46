import {Button, Text} from '@rneui/themed';
import React, {useMemo} from 'react';
import {Linking, Platform, View} from 'react-native';
import Card from '~components/shared/Card';
import {useNavigation} from '~hooks';
import {useGetOrder} from '~utils/api/order';
import theme from '~utils/config/themes/theme';
import {formatPriceToString} from '~utils/helpers/price';
import dayjs from 'dayjs';
import CountDownTimer from '~components/shared/CountDownTimer';
import {env} from '~utils/config';

type OrderPrePayAlertProps = {
  orderId: number;
};

const OrderPrePayAlert: React.FC<OrderPrePayAlertProps> = props => {
  const {data} = useGetOrder(props.orderId);
  const navigation = useNavigation();

  const handlePay = () => {
    if (data?.payment_checkout_url) {
      if (Platform.OS !== 'web') {
        navigation.push('OrderPayment', {payment_checkout_url: data.payment_checkout_url});
      } else {
        Linking.openURL(`${data.payment_checkout_url}?callback_url=${env.WEBVIEW_URL}/order-payment`);
      }
    }
  };

  const isPayDisabled = useMemo(() => {
    if (data?.dropship_marketplace) {
      return !data.payment_checkout_url;
    }
    return data?.confirm_status !== 'confirmed';
  }, [data]);

  return (
    <Card title={<Text style={{fontSize: theme.typography.lg}}>Đơn hàng thanh toán trước</Text>}>
      <View style={{paddingHorizontal: theme.spacing.s}}>
        <View style={{flexDirection: 'row', marginTop: 4}}>
          <Text style={{fontSize: theme.typography.base, width: 110, marginRight: 8}}>Thanh toán qua:</Text>
          <Text style={{fontSize: theme.typography.base}}>{data?.payment_label}</Text>
        </View>
        {Boolean(data?.paid_expired_at) && (
          <View style={{flexDirection: 'row', marginTop: 4, justifyContent: 'flex-start'}}>
            <Text style={{fontSize: theme.typography.base, width: 110, marginRight: 8}}>Ngày hết hạn: </Text>
            {Boolean(data?.paid_expired_at) && (
              <CountDownTimer
                initialTime={dayjs(data?.paid_expired_at ?? 0)
                  .toDate()
                  .getTime()}
              />
            )}
            <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight}}> (còn {dayjs(data?.paid_expired_at).diff(new Date(), 'hour')} giờ)</Text>
          </View>
        )}
        <View style={{marginTop: theme.spacing.m}}>
          {data?.confirm_status === 'pending' && data.dropship_marketplace === false && (
            <View>
              <Text style={{fontSize: theme.typography.base, color: theme.colors.pending}}>Bạn chưa thể thanh toán do người bán chưa xác nhận đơn</Text>
            </View>
          )}

          <Button onPress={handlePay} color={'primary'} containerStyle={{}} size="sm" disabled={isPayDisabled} buttonStyle={{borderRadius: 12}}>
            Thanh toán ngay ({formatPriceToString(parseInt(data?.total_price ?? '0', 10))})
          </Button>
        </View>
      </View>
    </Card>
  );
};

export default OrderPrePayAlert;
