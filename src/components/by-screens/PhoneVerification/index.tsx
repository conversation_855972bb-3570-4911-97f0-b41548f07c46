import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Button, Input, LinearProgress, Text} from '@rneui/themed';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {Keyboard, Platform, StyleSheet, View} from 'react-native';
import {useAuthActions, useKeyboard} from '~hooks';
import theme from '~utils/config/themes/theme';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import auth, {FirebaseAuthTypes} from '@react-native-firebase/auth';
import OTPTextView from 'react-native-otp-textinput';
import Resend from '~screens/OTPVerification/Resend';
import {verifyOTP} from '~utils/api/auth';

const PhoneVerification: React.FC<NativeStackScreenProps<RootStackParamList, 'PhoneVerification'>> = ({route, navigation}) => {
  const inputRef = useRef(null);
  const [phone, setPhone] = useState(route.params?.phone);
  const [errorMsg, setErrorMsg] = useState('');
  const [isSubmitting, setSubmitting] = useState(false);
  const {keyboardHeight} = useKeyboard();
  const [progress, setProgress] = useState(0.7);

  // If null, no SMS has been sent
  const [confirm, setConfirm] = useState<FirebaseAuthTypes.ConfirmationResult | null>(null);
  const [otpInput, setOtpInput] = useState('');
  const {login} = useAuthActions();

  useEffect(() => {
    const subscriber = auth().onAuthStateChanged(onAuthStateChanged);
    return subscriber; // unsubscribe on unmount
  }, []);

  useEffect(() => {
    if (otpInput.length === 6) {
      // complete input
      setTimeout(() => {
        submitOTP();
      }, 100);
    }
  }, [otpInput]);

  // Handle user state changes
  const onAuthStateChanged = useCallback(
    async (firebaseUser: FirebaseAuthTypes.User | null) => {
      if (firebaseUser) {
        doVerifyOTPAndCreateUser(firebaseUser);
        // setUser(user);
      }
    },
    [route.params.oauth_key],
  );

  const onPhoneChange = (text: string) => {
    setPhone(text);
    setErrorMsg('');
  };

  const doVerifyOTPAndCreateUser = useCallback(
    async (firebaseUser: FirebaseAuthTypes.User) => {
      setSubmitting(true);
      const res = await verifyOTP({
        firebase_id_token: await firebaseUser.getIdToken(),
        phone: firebaseUser.phoneNumber as any,
        oauth_key: route.params.oauth_key,
      }).catch(err => {
        setErrorMsg(err?.response?.data?.message ?? 'Có lỗi xảy ra');
      });
      setSubmitting(false);
      if (res?.token) {
        await login({access_token: res.token.access_token, refresh_token: res.token.refresh_token});
        navigation.pop();
        Keyboard.dismiss();
      }
    },
    [route.params.oauth_key],
  );

  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      setErrorMsg('');
      if (phone) {
        const confirmation = await auth().signInWithPhoneNumber(phone.replace(/^0/, '+84'));
        setConfirm(confirmation);
        setProgress(1);
      }
    } catch (error: any) {
      if (error?.message && error?.message?.includes('auth/invalid-phone-number')) {
        setErrorMsg('Số điện thoại không hợp lệ');
      } else {
        setErrorMsg(error?.response?.data?.message ?? 'Có lỗi xảy ra');
      }
    }

    setSubmitting(false);
  };

  const submitOTP = async () => {
    setSubmitting(true);
    confirm?.confirm(otpInput).catch(err => {
      setSubmitting(false);
      if (err?.message && err?.message?.includes('auth/invalid-verification-code')) {
        setErrorMsg('Mã OTP không chính xác');
      } else {
        setErrorMsg(err?.message ?? 'Có lỗi xảy ra');
      }
    });
  };

  return (
    <View style={{flex: 1, backgroundColor: theme.colors.white, paddingHorizontal: theme.spacing.m, paddingBottom: Platform.OS === 'ios' ? keyboardHeight : 0, justifyContent: 'space-between'}}>
      <View style={{alignItems: 'center', marginBottom: theme.spacing.m}}>
        <LinearProgress
          style={{marginVertical: 10, width: '90%', borderRadius: 100}}
          value={progress}
          variant="determinate"
          color={theme.colors.primary}
          animation={{
            duration: 500,
          }}
        />
      </View>
      {!confirm ? (
        <>
          <View>
            <Text style={{fontSize: theme.typography.lg4, fontWeight: '600'}}>Số điện thoại của bạn là gì?</Text>
            <Text style={{fontSize: theme.typography.base, marginVertical: theme.spacing.m, marginBottom: theme.spacing.l * 2}}>Nhập số điện thoại của bạn và ấn "Tiếp tục"</Text>
          </View>
          <Input
            keyboardType="numeric"
            autoFocus
            containerStyle={{marginHorizontal: 0, paddingHorizontal: 0, marginBottom: theme.spacing.m}}
            placeholder="Số điện thoại"
            returnKeyType="done"
            value={phone}
            onChangeText={onPhoneChange}
            inputStyle={{height: 50, fontSize: theme.typography.lg2}}
            errorMessage={errorMsg}
            onSubmitEditing={handleSubmit}
          />

          <Button size="sm" buttonStyle={{borderRadius: 30}} loading={isSubmitting} disabled={!phone} onPress={handleSubmit} containerStyle={[{marginTop: 'auto', marginBottom: theme.spacing.s}]}>
            Tiếp tục
          </Button>
        </>
      ) : (
        <>
          <Text style={{fontSize: theme.typography.lg4, fontWeight: '600'}}>Xác nhận số điện thoại</Text>
          <Text style={{fontSize: theme.typography.base, marginVertical: theme.spacing.m, marginBottom: theme.spacing.l * 2}}>
            Kiểm tra tin nhắn SMS của bạn. Chúng tôi đã gửi mã OTP đến số điện thoại {phone}
          </Text>
          <OTPTextView
            // @ts-ignore
            textContentType="oneTimeCode"
            autoComplete="sms-otp"
            tintColor={theme.colors.primary}
            textInputStyle={{marginRight: 0}}
            ref={inputRef}
            containerStyle={styles.textInputContainer}
            handleTextChange={setOtpInput}
            inputCount={6}
            keyboardType="numeric"
            autoFocus
            onSubmitEditing={submitOTP}
          />

          <Text style={{color: theme.colors.red}}>{errorMsg}</Text>

          <Button size="sm" buttonStyle={{borderRadius: 30}} disabled={otpInput.length !== 6} loading={isSubmitting} containerStyle={{marginTop: 'auto', marginBottom: theme.spacing.m}}>
            Hoàn tất
          </Button>
          <Resend onResend={handleSubmit} />
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  textInputContainer: {
    marginBottom: theme.spacing.l,
  },
});

export default PhoneVerification;
