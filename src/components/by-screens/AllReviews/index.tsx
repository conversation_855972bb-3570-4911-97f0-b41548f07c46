import {AirbnbRating, Text} from '@rneui/themed';
import React, {useState} from 'react';
import {ActivityIndicator, FlatList, ListRenderItemInfo, Platform, StyleSheet, View} from 'react-native';
import ReviewItem from '../ProductDetail/Reviews/ReviewItem';
import theme from '~utils/config/themes/theme';
import {Review, useGetReviewsInfinite} from '~utils/api/review';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {RootStackParamList} from '~utils/navigation/NavigationProvider';
import {useQueryClient} from '@tanstack/react-query';
import {getStaleTime} from '~utils/helpers/reactQuery';
// @ts-ignore
import AirBnBStar from '~assets/airbnbstar.png';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

const AllReviews: React.FC<NativeStackScreenProps<RootStackParamList, 'AllReviews'>> = ({route, navigation}) => {
  const insets = useSafeAreaInsets();
  const {data, hasNextPage, fetchNextPage, isFetchingNextPage, isLoading} = useGetReviewsInfinite({
    product_id: route.params.productId,
    limit: 10,
  });
  const [showReachedEndIndicator, setReachedEndIndicator] = useState(false);

  const dataFlat = data?.pages.flatMap(page => page.reviews) ?? [];

  const handleEndReached = () => {
    if (!hasNextPage && dataFlat.length > 10) {
      setReachedEndIndicator(true);
    }
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  const renderItem = ({item, index}: ListRenderItemInfo<Review>) => {
    return (
      <ReviewItem
        bottomDivider={index !== (dataFlat.length ?? 1) - 1}
        key={item._id}
        user={item.user}
        createdAt={item.created_at}
        label={item.label}
        // @ts-ignore
        rateLineItem={{
          ...item,
          quality_rate: item.rating,
        }}
      />
    );
  };

  return (
    <View
      style={{
        flex: 1,
        paddingBottom: insets.bottom,
      }}>
      <FlatList
        contentContainerStyle={{
          paddingHorizontal: theme.spacing.m,
          backgroundColor: theme.colors.white,
        }}
        ListHeaderComponent={
          <>
            <View style={{borderBottomWidth: 1, borderBottomColor: theme.colors.gray20}}>
              <View style={[theme.globalStyles.flexRow, {marginVertical: theme.spacing.m, alignItems: 'flex-end'}]}>
                <AirbnbRating count={1} defaultRating={data?.pages[0]?.rating_avg ?? 0} starImage={Platform.OS === 'web' ? AirBnBStar : undefined} size={20} showRating={false} />

                <Text style={styles.ratingTextBold}> {data?.pages[0].rating_avg}</Text>
                <Text style={styles.ratingTextNormal}>/5</Text>
                <Text style={{fontSize: theme.typography.base, marginLeft: theme.spacing.s}}>{data?.pages[0].rating_count} đánh giá</Text>
              </View>
              {/* <ReviewsFilter /> */}
            </View>
          </>
        }
        onEndReached={handleEndReached}
        data={dataFlat}
        renderItem={renderItem}
        ListFooterComponent={
          <>
            {!showReachedEndIndicator && (isLoading || isFetchingNextPage) && (
              <View>
                <ActivityIndicator />
              </View>
            )}
            {showReachedEndIndicator && <Text style={styles.endReachedIndicator}>Bạn đã xem hết!</Text>}
          </>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  ratingTextBold: {
    fontSize: theme.typography.lg,
    fontWeight: 'bold',
  },
  ratingTextNormal: {
    fontSize: theme.typography.lg,
    color: theme.colors.textLight,
  },
  endReachedIndicator: {
    textAlign: 'center',
    fontSize: theme.typography.base,
    marginTop: theme.spacing.s,
    color: theme.colors.textLight,
  },
});

export default AllReviews;
