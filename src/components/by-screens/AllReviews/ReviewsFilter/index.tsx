import {Button, Text} from '@rneui/themed';
import React from 'react';
import {StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';

const ReviewsFilter: React.FC<any> = props => {
  return (
    <View style={[theme.globalStyles.flexRow]}>
      <Button>OK</Button>
      <Button>OK</Button>
      <Button>OK</Button>
      <Button>OK</Button>
    </View>
  );
};

const styles = StyleSheet.create({});

export default ReviewsFilter;
