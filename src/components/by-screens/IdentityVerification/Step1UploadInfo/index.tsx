import {SCREEN_WIDTH} from '@gorhom/bottom-sheet';
import {Button, Text} from '@rneui/themed';
import React, {useCallback, useMemo} from 'react';
import {ScrollView, StyleSheet, View} from 'react-native';
import {responsiveWidth} from '~components/shared/web/WebContainer';
import theme from '~utils/config/themes/theme';
import imageCompression from 'browser-image-compression';
import {idCardUpload} from '~utils/api/auth';
import {useToast} from 'react-native-toast-notifications';

type Step1UploadInfoProps = {
  onDone: (step1Data: any) => void;
};

const Step1UploadInfo: React.FC<Step1UploadInfoProps> = props => {
  const [step, setStep] = React.useState(1);
  const [selectedIdFront, setSelectedIdFront] = React.useState(null);
  const [selectedIdBack, setSelectedIdBack] = React.useState(null);
  const [idFrontError, setIdFrontError] = React.useState('');
  const [idBackError, setIdBackError] = React.useState('');
  const [isLoading, setIsLoading] = React.useState(false);
  const toast = useToast();

  const handleSelectIdFront = useCallback(async (e: any) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedIdFront(file);
    }
  }, []);

  const handleSelectIdBack = useCallback(async (e: any) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedIdBack(file);
    }
  }, []);

  const SCREENWIDTH = useMemo(() => {
    return Math.min(responsiveWidth.sm, SCREEN_WIDTH);
  }, []);

  const handleSubmit = async () => {
    setIdBackError('');
    setIdFrontError('');

    if (!selectedIdFront) {
      setIdFrontError('Vui lòng chọn ảnh mặt trước');
      return;
    }
    if (!selectedIdBack) {
      setIdBackError('Vui lòng chọn ảnh mặt sau');
      return;
    }

    setIsLoading(true);
    toast.show('Vui lòng đợi', {
      duration: 3000,
      placement: 'center',
    });

    const compressOptions = {
      useWebWorker: true,
      maxSizeMB: 1,
      maxWidthOrHeight: 1920,
    };
    const compressedIdFront = await imageCompression(selectedIdFront, compressOptions);

    const compressedIdBack = await imageCompression(selectedIdBack, compressOptions);

    const promises = [
      {file: compressedIdFront, type: 'front'},
      {file: compressedIdBack, type: 'back'},
    ].map(async item => {
      return await idCardUpload(item.file, item.type as 'front' | 'back');
    });

    try {
      const results = await Promise.all(promises);
      setIsLoading(false);

      if (results.length === 2 && results[0]?.attachment_id && results[1]?.attachment_id) {
        props.onDone(results);
      } else {
        throw new Error('Có lỗi xảy ra');
      }
    } catch (error: any) {
      toast.show(error?.response?.data?.message ?? 'Có lỗi xảy ra', {
        duration: 3000,
        placement: 'center',
      });
      setIsLoading(false);
    }
    toast.hideAll();
  };

  return (
    <View style={{flex: 1}}>
      <ScrollView style={styles.container}>
        <View style={{padding: theme.spacing.m}}>
          <Text style={{marginBottom: theme.spacing.l, color: theme.colors.textLight}}>Bước 1/2</Text>
          <Text style={{fontSize: theme.typography.md, fontWeight: '500'}}>Ảnh CCCD/CMND</Text>

          <View style={{flexDirection: 'row', justifyContent: 'space-around', alignItems: 'flex-start', marginTop: theme.spacing.m}}>
            <View style={{alignItems: 'center', flex: 1}}>
              <Text style={styles.identityText}>Mặt trước</Text>

              {!selectedIdFront ? (
                <>
                  <View style={{width: 100, height: 50, backgroundColor: theme.colors.gray30, borderRadius: 4}} />
                </>
              ) : (
                <>
                  <img
                    src={URL.createObjectURL(selectedIdFront)}
                    style={{
                      width: SCREENWIDTH * 0.3,
                      height: SCREENWIDTH * 0.3 * 0.5,
                      objectFit: 'cover',
                    }}
                  />
                </>
              )}
              <label
                htmlFor="id-select-front"
                style={{
                  marginTop: theme.spacing.m,
                  backgroundColor: theme.colors.primary,
                  padding: theme.spacing.m,
                  paddingTop: theme.spacing.s,
                  paddingBottom: theme.spacing.s,
                  borderRadius: 100,
                  color: theme.colors.white,
                  cursor: 'pointer',
                }}>
                Thêm hình
              </label>
              {idFrontError ? <Text style={styles.formError}>{idFrontError}</Text> : null}
              <input type="file" accept="image/*" capture="environment" id="id-select-front" style={{opacity: 0, position: 'absolute'}} onChange={handleSelectIdFront} />
            </View>
            <View style={{width: 1, height: 30, borderRightWidth: 1, borderRightColor: theme.colors.gray30, alignSelf: 'center'}} />
            <View style={{alignItems: 'center', flex: 1}}>
              <Text style={styles.identityText}>Mặt sau</Text>

              {!selectedIdBack ? (
                <>
                  <View style={{width: 100, height: 50, backgroundColor: theme.colors.gray30, borderRadius: 4}} />
                </>
              ) : (
                <>
                  <img
                    src={URL.createObjectURL(selectedIdBack)}
                    style={{
                      width: SCREENWIDTH * 0.3,
                      height: SCREENWIDTH * 0.3 * 0.5,
                      objectFit: 'cover',
                    }}
                  />
                </>
              )}
              <label
                htmlFor="id-select-back"
                style={{
                  marginTop: theme.spacing.m,
                  backgroundColor: theme.colors.primary,
                  padding: theme.spacing.m,
                  paddingTop: theme.spacing.s,
                  paddingBottom: theme.spacing.s,
                  borderRadius: 100,
                  color: theme.colors.white,
                  cursor: 'pointer',
                }}>
                Thêm hình
              </label>
              <input type="file" accept="image/*" id="id-select-back" style={{opacity: 0, position: 'absolute'}} onChange={handleSelectIdBack} capture="environment" />
              {idBackError ? <Text style={styles.formError}>{idBackError}</Text> : null}
            </View>
          </View>

          <Text style={{marginTop: theme.spacing.l}}>LƯU Ý:</Text>
          <Text>- Chụp giấy tờ bản gốc, còn hạn sử dụng.</Text>
          <Text>- Hình chụp nên rõ ràng, không bị mất góc</Text>
        </View>
      </ScrollView>
      <Button size="sm" onPress={handleSubmit} loading={isLoading} disabled={isLoading}>
        Tiếp tục
      </Button>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {flex: 1, backgroundColor: theme.colors.white},

  identityText: {
    fontSize: theme.typography.md,
    marginBottom: theme.spacing.m,
  },
  formError: {
    color: theme.colors.red,
    fontSize: theme.typography.base,
  },
});

export default Step1UploadInfo;
