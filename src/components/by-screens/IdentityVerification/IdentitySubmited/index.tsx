import {Button, Text} from '@rneui/themed';
import React from 'react';
import {StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {sendWebViewEvent} from '~utils/helpers/webview';
import inInTTSDropshipWebview from '~utils/helpers/isInTTSDropshipWebview';
import {useNavigation} from '~hooks';

const IdentitySubmited = (props: {step2Data: any}) => {
  const navigation = useNavigation();
  const handleGoBack = () => {
    if (inInTTSDropshipWebview()) {
      sendWebViewEvent('Goback', {});
    } else {
      navigation.goBack();
    }
  };

  const verifyStatus = props.step2Data?.status;
  return (
    <View style={{flex: 1, backgroundColor: theme.colors.white, alignItems: 'center', paddingTop: theme.spacing.l * 3, paddingHorizontal: theme.spacing.l * 2}}>
      {['verified', 'under_review'].includes(verifyStatus) ? (
        <>
          <View style={{alignSelf: 'center', alignItems: 'center', paddingHorizontal: theme.spacing.l, marginTop: theme.spacing.l}}>
            <Ionicons
              name={verifyStatus === 'under_review' ? 'time' : 'checkmark-circle'}
              size={60}
              color={verifyStatus === 'under_review' ? theme.colors.pending : theme.colors.primary}
              style={{marginBottom: theme.spacing.m}}
            />
            <Text
              style={{
                fontSize: theme.typography.lg,
                fontWeight: '600',
                marginBottom: theme.spacing.l,
              }}>
              Tài khoản của bạn {verifyStatus === 'under_review' ? 'đang' : 'đã'} được định danh
            </Text>

            {verifyStatus === 'under_review' ? (
              <Text style={{marginTop: theme.spacing.s, textAlign: 'center', fontSize: theme.typography.md}}>Thông tin định danh của bạn sẽ được xét duyệt trong tối đa 7 ngày làm việc.</Text>
            ) : null}
          </View>
        </>
      ) : null}

      <Button
        containerStyle={{marginTop: theme.spacing.l * 2}}
        size="sm"
        color={theme.colors.secondary}
        buttonStyle={{borderRadius: 100}}
        titleStyle={{color: theme.colors.text}}
        onPress={handleGoBack}
        icon={{
          type: 'ionicon',
          name: 'chevron-back',
          size: 20,
          color: theme.colors.text,
        }}>
        Trở về
      </Button>
    </View>
  );
};
const styles = StyleSheet.create({
  secondaryText: {
    fontSize: theme.typography.md,
    color: theme.colors.textLight,
    alignSelf: 'flex-start',
  },
});

export default IdentitySubmited;
