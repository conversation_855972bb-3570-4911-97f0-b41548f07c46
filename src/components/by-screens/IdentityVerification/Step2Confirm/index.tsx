import {Button, Input, Text} from '@rneui/themed';
import React, {ChangeEvent, useCallback, useState} from 'react';
import {ScrollView, StyleSheet, View} from 'react-native';
import {useToast} from 'react-native-toast-notifications';
import Select from '~components/shared/Form/Select';
import {verifyIdCardSave} from '~utils/api/auth';
import theme from '~utils/config/themes/theme';
import {IdCardUploadResponse} from '~utils/types/user';

type IdentityData = {
  address: string;
  name: string;
  id_num: string;
  id_type: 'cmnd' | 'cccd';
  issued_date: string;
};

type Step2ConfirmProps = {
  step1Data: IdCardUploadResponse[];
  onDone: (step2Data: any) => void;
};

const Step2Confirm: React.FC<Step2ConfirmProps> = props => {
  const toast = useToast();
  const [errors, setErrors] = useState<{[key: string]: any}>({});
  const [identityData, setIdentityData] = useState<Partial<IdentityData>>({
    id_type: 'cccd',
    address: props.step1Data?.[0]?.info?.address || props.step1Data?.[1]?.info?.address || '',
    id_num: props.step1Data?.[0]?.info?.id_num || props.step1Data?.[1]?.info?.id_num || '',
    issued_date: props.step1Data[1]?.info?.issued_date || props.step1Data[0]?.info?.issued_date || '',
    name: props.step1Data?.[0]?.info?.name || props.step1Data?.[1]?.info?.name || '',
  });

  const [isLoading, setLoading] = useState(false);

  const handleIdTypeChange = useCallback(
    (newType: string) => {
      setIdentityData(prev => ({...prev, id_type: newType as any}));
      if (errors.id_type) {
        setErrors(prev => ({...prev, id_type: ''}));
      }
    },
    [errors],
  );

  const handleNameChange = useCallback(
    (newName: string) => {
      setIdentityData(prev => ({...prev, name: newName as any}));
      if (errors.name) {
        setErrors(prev => ({...prev, name: ''}));
      }
    },
    [errors],
  );

  const handleChangeIdNumber = useCallback(
    (newNumber: string) => {
      setIdentityData(prev => ({...prev, id_num: newNumber as any}));
      if (errors.id_num) {
        setErrors(prev => ({...prev, id_num: ''}));
      }
    },
    [errors],
  );

  const handleAddressChange = useCallback(
    (newAddress: string) => {
      setIdentityData(prev => ({...prev, address: newAddress}));
      if (errors.address) {
        setErrors(prev => ({...prev, address: ''}));
      }
    },
    [errors],
  );

  const handleIssuedDateChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      setIdentityData(prev => ({...prev, issued_date: e.target.value}));
      if (errors.issued_date) {
        setErrors(prev => ({...prev, issued_date: ''}));
      }
    },
    [errors],
  );

  const handleSubmit = async () => {
    setErrors({});

    let errors: {[key: string]: any} = {};
    // validate
    Object.keys(identityData).forEach(key => {
      if (!identityData[key as keyof IdentityData]) {
        errors[key] = 'Vui lòng nhập thông tin';
      }
    });

    if (Object.keys(errors).length > 0) {
      setErrors(prev => ({...prev, ...errors}));
      return;
    }

    setLoading(true);

    try {
      const res = await verifyIdCardSave({
        front_attachment_id: props.step1Data[0].attachment_id,
        back_attachment_id: props.step1Data[1].attachment_id,
        info: {
          address: identityData.address!,
          issued_date: identityData.issued_date!,
          id_num: identityData.id_num!,
          id_type: identityData.id_type!,
          name: identityData.name!,
        },
      });

      if (res.message === 'Thành công') {
        props.onDone(res.id_card);
      } else {
        throw new Error(res.message);
      }
    } catch (error: any) {
      console.log(error.response);
      toast.show(error?.message || error?.response?.data?.message || 'Có lỗi xảy ra', {
        type: 'danger',
        duration: 3000,
        placement: 'center',
      });
    }

    setLoading(false);
  };

  return (
    <View style={{flex: 1}}>
      <ScrollView style={styles.container}>
        <View style={{padding: theme.spacing.m}}>
          <Text style={{marginBottom: theme.spacing.l, color: theme.colors.textLight}}>Bước 2/2</Text>
          <Text style={{marginBottom: theme.spacing.s, fontSize: theme.typography.lg}}>Xác nhận thông tin</Text>
          <Text style={{marginBottom: theme.spacing.l, fontSize: theme.typography.md, color: theme.colors.textLight}}>Nhập một số thông tin bên dưới để hoàn tất định danh tài khoản.</Text>
          <Select
            label={'Loại giấy tờ'}
            // options={['Căn cước công dân', 'Chứng minh nhân dân']}
            webOptions={[
              {
                label: 'Căn cước công dân',
                value: 'cccd',
              },
            ]}
            containerStyle={styles.formContainer}
            value={identityData.id_type}
            onChange={handleIdTypeChange as any}
          />
          <Input
            label="Họ và tên"
            containerStyle={styles.formContainer}
            inputContainerStyle={styles.inputContainer}
            inputStyle={styles.input}
            labelStyle={styles.label}
            value={identityData.name}
            onChangeText={handleNameChange}
            errorMessage={errors.name}
          />
          <Input
            label="Số căn cước công dân"
            containerStyle={styles.formContainer}
            inputContainerStyle={styles.inputContainer}
            inputStyle={styles.input}
            labelStyle={styles.label}
            keyboardType="numeric"
            value={identityData.id_num}
            onChangeText={handleChangeIdNumber}
            errorMessage={errors.id_num}
          />
          <Input
            label="Địa chỉ thường trú (trên CCCD/CMND)"
            containerStyle={styles.formContainer}
            inputContainerStyle={styles.inputContainer}
            inputStyle={styles.input}
            labelStyle={styles.label}
            value={identityData.address}
            onChangeText={handleAddressChange}
            errorMessage={errors.address}
          />

          <View style={{paddingHorizontal: 12}}>
            <Text style={styles.label}>Ngày cấp</Text>
            <input
              type="date"
              style={{...styles.inputContainer, height: 42, borderColor: theme.colors.gray30, paddingLeft: 12, paddingRight: 12}}
              value={identityData.issued_date}
              placeholder="Nhập ngày cấp"
              onChange={handleIssuedDateChange}
            />
            {errors.issued_date ? <Text style={styles.formError}>{errors.issued_date}</Text> : null}
          </View>
        </View>
      </ScrollView>
      <Button size="sm" onPress={handleSubmit} loading={isLoading} disabled={isLoading}>
        Tiếp tục
      </Button>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {flex: 1, backgroundColor: theme.colors.white},

  identityText: {
    fontSize: theme.typography.md,
    marginBottom: theme.spacing.m,
  },
  formError: {
    color: theme.colors.red,
    fontSize: theme.typography.sm,
  },
  inputContainer: {
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,.3)',
    borderRadius: 8,
    marginTop: 8,
  },
  formContainer: {
    marginBottom: 0,
    marginHorizontal: 0,
  },
  input: {
    padding: 12,
    fontSize: theme.typography.md,
  },
  label: {
    fontWeight: '500',
    fontSize: 16,
    color: theme.colors.textLight,
  },
});

export default Step2Confirm;
