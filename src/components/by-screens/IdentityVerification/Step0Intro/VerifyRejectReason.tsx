import {Text} from '@rneui/themed';
import React from 'react';
import {StyleSheet, View} from 'react-native';
import {useGetMyIdCard} from '~utils/api/auth';
import theme from '~utils/config/themes/theme';

const VerifyRejectedAlert = () => {
  const {data} = useGetMyIdCard();

  return (
    <View style={{padding: 16, backgroundColor: '#fff3cd'}}>
      <Text style={styles.rejectedText}>Thông tin định danh của bạn bị từ chối vì lý do</Text>
      <Text style={styles.reasonText}>{data?.reject_reason}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  rejectedText: {
    fontWeight: 'bold',
    color: theme.colors.red,
    fontSize: theme.typography.base,
  },
  reasonText: {
    color: theme.colors.text,
    fontSize: theme.typography.base,
  },
});

export default VerifyRejectedAlert;
