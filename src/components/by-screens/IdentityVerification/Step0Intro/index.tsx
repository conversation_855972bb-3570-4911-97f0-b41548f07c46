import {Button, Text} from '@rneui/themed';
import React, {useEffect} from 'react';
import {ActivityIndicator, StyleSheet, View} from 'react-native';
import IdentityVerificationBenefits from '~components/shared/IdentityVerificationBenefits';
import {useGetCurrentUser} from '~utils/api/auth';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import VerifyRejectedAlert from './VerifyRejectReason';
import {useToast} from 'react-native-toast-notifications';
import useRefreshOnFocus from '~hooks/useRefreshOnFocus';

type Step0IntroProps = {
  onDone: () => void;
};

const Step0Intro: React.FC<Step0IntroProps> = props => {
  const {data: currentUser, error, isError, refetch} = useGetCurrentUser();
  const toast = useToast();

  useRefreshOnFocus(refetch);

  useEffect(() => {
    if (isError) {
      toast.show((error as any)?.response?.data?.message || 'Đã xảy ra lỗi, vui lòng thử lại sau', {type: 'danger'});
    }
  }, [isError]);

  if (!currentUser) {
    return <ActivityIndicator />;
  }

  return (
    <View style={{backgroundColor: theme.colors.white, flex: 1}}>
      {['verified', 'under_review'].includes(currentUser?.idcard_verify!) ? (
        <>
          <View style={{alignSelf: 'center', alignItems: 'center', paddingHorizontal: theme.spacing.l, marginTop: theme.spacing.l}}>
            <Ionicons
              name={currentUser.idcard_verify === 'under_review' ? 'time' : 'checkmark-circle'}
              size={60}
              color={currentUser.idcard_verify === 'under_review' ? theme.colors.pending : theme.colors.primary}
              style={{marginBottom: theme.spacing.m}}
            />
            <Text
              style={{
                fontSize: theme.typography.lg,
                fontWeight: '600',
                marginBottom: theme.spacing.l,
              }}>
              Tài khoản của bạn {currentUser.idcard_verify === 'under_review' ? 'đang' : 'đã'} được định danh
            </Text>
            {currentUser.idcard_verify === 'under_review' && (
              <>
                <Text style={styles.secondaryText}>
                  Thông tin định danh của bạn sẽ được xét duyệt trong tối đa 7 ngày làm việc. Trường hợp bạn muốn dùng loại giấy tờ tùy thân khác, thay đổi{' '}
                  <Text style={[styles.secondaryText, {textDecorationLine: 'underline', color: theme.colors.blue}]} onPress={props.onDone}>
                    tại đây
                  </Text>
                </Text>
                <Text style={[styles.secondaryText, {marginTop: theme.spacing.m}]}>Lưu ý: Quá trình định danh sẽ được thao tác lại từ đầu</Text>
              </>
            )}
          </View>
        </>
      ) : null}
      {['rejected', 'not_verified'].includes(currentUser?.idcard_verify!) ? (
        <>
          {/* @ts-ignore */}
          {currentUser.idcard_verify === 'rejected' && <VerifyRejectedAlert />}

          <IdentityVerificationBenefits />
          <View style={{alignSelf: 'center', alignItems: 'center'}}>
            <Button size="sm" buttonStyle={{borderRadius: 100}} onPress={props.onDone} containerStyle={{marginTop: theme.spacing.l}}>
              Bắt đầu định danh
            </Button>
            <Text
              style={{
                fontSize: theme.typography.base,
                color: theme.colors.textLight,
                marginTop: theme.spacing.xs,
              }}>
              chỉ khoảng 2 phút
            </Text>
          </View>

          <View style={{paddingHorizontal: theme.spacing.m, marginTop: theme.spacing.l * 3, alignSelf: 'center', marginBottom: theme.spacing.l}}>
            <Text style={{fontSize: theme.typography.md, fontWeight: '600', marginBottom: theme.spacing.xs, textAlign: 'center'}}>Tại sao bạn phải định danh tài khoản?</Text>
            <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight}}>
              Theo Luật an ninh mạng, Văn bản hợp nhất số 14/VBHN-BCT hợp nhất nghị định về thương mại điện tử, Nghị định 91/2022/NĐ-CP sửa đổi, bổ sung Nghị định 126/2020/NĐ-CP quy định chi tiết một
              số điều của Luật Quản lý thuế, Luật bảo vệ quyền lợi người tiêu dùng năm 2023, Người kinh doanh có nghĩa vụ và trách nhiệm nộp đầy đủ thông tin kinh doanh và cá nhân nhằm phục vụ cho
              việc quản lý thuế, quản lý hoạt động thương mại điện tử, đảm bảo an toàn đối với giao dịch trên môi trường mạng cho Người tiêu dùng.
            </Text>
          </View>
        </>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  secondaryText: {
    fontSize: theme.typography.md,
    color: theme.colors.textLight,
    alignSelf: 'flex-start',
  },
});

export default Step0Intro;
