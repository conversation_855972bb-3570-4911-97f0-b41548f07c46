import {Button, Text} from '@rneui/themed';
import React, {useCallback, useMemo, useState} from 'react';
import {Pressable, ScrollView, StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import LinkifyIt from 'linkify-it';
import {useGetAvailableTransactionTypes, useGetWalletTransactions, WalletTransactionType} from '~utils/api/wallet';
import TransactionItem from '../TransactionItem';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '~hooks';

const linkifyObject = LinkifyIt();

linkifyObject.add('#', {
  validate: /\w+/,
});

const PaymentHistory = () => {
  const navigation = useNavigation();
  const [selectedTabKey, setSelectedTabKey] = useState<WalletTransactionType | 'all'>('all');
  const {data: availableTypes} = useGetAvailableTransactionTypes();
  const {data: walletTransactions, isLoading} = useGetWalletTransactions({limit: 5, types: selectedTabKey === 'all' ? null : selectedTabKey});

  const tabs = useMemo(() => {
    return ['all', ...Object.keys(availableTypes ?? {})];
  }, [availableTypes]);

  const handleTabChange = useCallback((tabKey: string) => {
    setSelectedTabKey(tabKey as any);
  }, []);

  const handleViewMore = useCallback(() => {
    navigation.navigate('Transactions');
  }, []);

  const dataFlat = useMemo(() => {
    return walletTransactions?.pages.flatMap(page => page?.transactions ?? []) ?? [];
  }, [walletTransactions]);

  return (
    <View style={[styles.container, {marginTop: 20}]}>
      <View style={{flexDirection: 'row', alignItems: 'center', marginBottom: theme.spacing.m}}>
        <Text>Lịch sử tiền có thể rút</Text>
        <Pressable onPress={handleViewMore} style={{marginLeft: 'auto', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingVertical: 2, paddingHorizontal: 2}}>
          <Text style={{fontWeight: '600', fontSize: theme.typography.base}}>Xem tất cả</Text>
          <Ionicons name="chevron-forward" size={18} color={theme.colors.text} />
        </Pressable>
      </View>

      <ScrollView
        style={styles.tabContainer}
        horizontal
        snapToAlignment="center"
        decelerationRate={0}
        snapToInterval={100} //your element width
        showsHorizontalScrollIndicator={false}>
        {tabs.map(key => {
          const isSelected = key === selectedTabKey;
          return (
            <Button
              key={key}
              type="clear"
              size="sm"
              containerStyle={{marginRight: theme.spacing.s, marginTop: theme.spacing.m}}
              buttonStyle={[
                {
                  borderColor: theme.colors.gray40,
                  // borderRadius: 6,
                  paddingVertical: 6,
                  paddingHorizontal: 16,
                  backgroundColor: isSelected ? '#fcfffd' : theme.colors.white,
                },
                isSelected && {
                  borderColor: theme.colors.primary,
                  borderBottomWidth: 1,
                },
              ]}
              titleStyle={[
                {
                  fontSize: theme.typography.base,
                  color: isSelected ? theme.colors.primary : theme.colors.text,
                  fontWeight: '400',
                },
                isSelected && {
                  fontWeight: 'bold',
                },
              ]}
              onPress={() => handleTabChange(key)}>
              {availableTypes?.[key] ?? 'Tất cả'}
            </Button>
          );
        })}
      </ScrollView>

      {dataFlat.map(item => (
        <TransactionItem transaction={item} key={item.id} />
      ))}

      {!isLoading && dataFlat.length === 0 && <Text style={{textAlign: 'center', marginVertical: 20, fontSize: theme.typography.base, color: theme.colors.textLight}}>Chưa có giao dịch nào</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    borderRadius: 12,
    padding: 12,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: theme.spacing.l,
  },
  historyItem: {
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray20,
  },
});
export default PaymentHistory;
