import React, {useCallback, useMemo} from 'react';
import {Pressable, StyleSheet, View} from 'react-native';
import {useNavigation} from '~hooks';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useGetMyBankAccounts} from '~utils/api/wallet';
import {Text} from '@rneui/themed';

const BankAccounts = () => {
  const {data, isLoading} = useGetMyBankAccounts();
  const navigation = useNavigation();

  const isEmptyAccounts = useMemo(() => {
    return !data?.length && !isLoading;
  }, [isLoading, data]);

  const handleBankAccountClick = useCallback(() => {
    if (isEmptyAccounts) {
      navigation.navigate('AddBankAccount');
    } else {
      navigation.navigate('BankAccountList');
    }
  }, [isEmptyAccounts]);

  return (
    <Pressable onPress={handleBankAccountClick} style={[styles.item, {marginTop: 20, alignItems: 'center', flexDirection: 'row'}]} accessible={false}>
      <View>
        <Text>Tài khoản Ngân hàng</Text>
        {!isLoading && !data?.length && <Text style={{fontSize: theme.typography.base, marginTop: theme.spacing.s, color: theme.colors.textLight}}>Bạn chưa thêm tài khoản ngân hàng</Text>}
        {!isLoading && data && data.length > 0 && <Text style={{fontSize: theme.typography.base, marginTop: theme.spacing.s, color: theme.colors.textLight}}>{data.length} tài khoản</Text>}
      </View>
      <View style={{marginLeft: 'auto'}}>
        <Ionicons name="chevron-forward" size={20} color={theme.colors.text} />
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  item: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 12,
  },
});

export default BankAccounts;
