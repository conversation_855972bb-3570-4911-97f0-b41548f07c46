import {Text} from '@rneui/themed';
import React, {useCallback, useMemo} from 'react';
import {Pressable, StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import LinkifyIt from 'linkify-it';
import {useGetEscrowTransactions} from '~utils/api/wallet';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '~hooks';
import EscrowTransactionItem from '../EscrowTransactionItem';

const linkifyObject = LinkifyIt();

linkifyObject.add('#', {
  validate: /\w+/,
});

const EscrowPaymentHistory = () => {
  const navigation = useNavigation();
  const {data: walletTransactions, isLoading} = useGetEscrowTransactions({
    type: 'commission',
    limit: 5,
    status: ['order_created', 'payment_received'],
  });

  const dataFlat = useMemo(() => {
    return walletTransactions?.pages.flatMap(page => page.transactions) ?? [];
  }, [walletTransactions]);

  const handleViewMore = useCallback(() => {
    navigation.navigate('EscrowTransactions');
  }, []);

  return (
    <View style={[styles.container, {marginTop: 20}]}>
      <View style={{flexDirection: 'row', alignItems: 'center', marginBottom: theme.spacing.m}}>
        <Text>Lịch sử tiền chờ đối soát</Text>
        <Pressable onPress={handleViewMore} style={{marginLeft: 'auto', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingVertical: 2, paddingHorizontal: 2}}>
          <Text style={{fontWeight: '600', fontSize: theme.typography.base}}>Xem tất cả</Text>
          <Ionicons name="chevron-forward" size={18} color={theme.colors.text} />
        </Pressable>
      </View>

      {dataFlat.map(item => (
        <EscrowTransactionItem transaction={item} key={item.id} />
      ))}

      {!isLoading && !dataFlat.length && <Text style={{textAlign: 'center', marginVertical: 20, fontSize: theme.typography.base, color: theme.colors.textLight}}>Chưa có giao dịch nào</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    borderRadius: 12,
    padding: 12,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: theme.spacing.l,
  },
  historyItem: {
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray20,
  },
});
export default EscrowPaymentHistory;
