import {Text} from '@rneui/themed';
import dayjs from 'dayjs';
import LinkifyIt from 'linkify-it';
import React, {useCallback, useMemo} from 'react';
import {StyleSheet, View} from 'react-native';
import Hyperlink from 'react-native-hyperlink';
import {useNavigation} from '~hooks';
import theme from '~utils/config/themes/theme';
import {formatPriceToString} from '~utils/helpers/price';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {WalletTransaction} from '~utils/types/finance';

type TransactionItemProps = {
  transaction: WalletTransaction;
};

const linkifyObject = LinkifyIt();

linkifyObject.add('#', {
  validate: /\w+/,
});

const TransactionItem: React.FC<TransactionItemProps> = props => {
  const navigation = useNavigation();
  const handleLinkPress = useCallback(async (url: string) => {
    if (url.match(/#\w+/)) {
      navigation.push('OrderDetail', {orderId: props.transaction.order_id});
    }
  }, []);

  const isWalletIncreased = useMemo(() => {
    return props.transaction.new_balance > props.transaction.old_balance;
  }, [props.transaction]);

  return (
    <View style={styles.historyItem}>
      <View style={{flex: 1, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center'}}>
        <Text style={{fontSize: theme.typography.base}}>{dayjs(props.transaction.created_at).format('DD/MM/YYYY HH:mm')}</Text>
        <Text
          style={{
            color: isWalletIncreased ? theme.colors.primary : theme.colors.text,
            fontSize: theme.typography.base,
          }}>
          {isWalletIncreased ? '+' : ''}
          {formatPriceToString(props.transaction.amount)}
        </Text>
      </View>
      <View style={{marginTop: theme.spacing.s}}>
        <Hyperlink
          onPress={handleLinkPress}
          linkify={linkifyObject}
          linkStyle={{
            color: theme.colors.text,
            textDecorationLine: 'underline',
          }}>
          <Text style={{color: theme.colors.textLight, fontSize: theme.typography.base}}>{props.transaction.description}</Text>
        </Hyperlink>
      </View>
      {props.transaction.status === 'pending' && (
        <View style={{flexDirection: 'row', alignItems: 'center', marginTop: theme.spacing.s}}>
          <Ionicons name="time-outline" color={theme.colors.pending} size={14} />
          <Text style={{fontSize: theme.typography.base, color: theme.colors.pending, marginLeft: 2}}>Đang xử lý</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  historyItem: {
    paddingVertical: theme.spacing.s,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray20,
  },
});

export default TransactionItem;
