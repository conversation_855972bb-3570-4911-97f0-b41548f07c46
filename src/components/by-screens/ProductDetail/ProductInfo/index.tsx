import {Button} from '@rneui/base';
import React, {useCallback, useEffect, useState} from 'react';
import {Platform, StyleSheet, Text, TextInput, TouchableOpacity, View} from 'react-native';
import {useToast} from 'react-native-toast-notifications';
import {isAddedWishlist, useAddToWishlistMutation, useRemoveFromWishlistMutation} from '~utils/api/product';
import theme from '~utils/config/themes/theme';
import {formatPriceToString, kFormatter} from '~utils/helpers/price';
import Product from '~utils/types/product';
import {FirebaseAnalyticsTypes} from '@react-native-firebase/analytics';
import {useAuthActions, useNavigation} from '~hooks';
import googleAnalytics from '~utils/helpers/analytics';
import isInZaloMiniApp from '~utils/helpers/isInZaloMiniApp';
import Ionicons from 'react-native-vector-icons/Ionicons';

type ProductInfoProps = {
  product: Product;
};

const ProductInfo: React.FC<ProductInfoProps> = props => {
  const [isZaloMiniApp] = useState(isInZaloMiniApp());
  const [isAddedToWishlist, setAddedToWishlist] = useState(false);
  const addToWishlistMutation = useAddToWishlistMutation();
  const removeFromWishlistMutation = useRemoveFromWishlistMutation();
  const toast = useToast();
  const navigation = useNavigation();

  const {isLoggedIn, openLoginRequiredModal} = useAuthActions();

  useEffect(() => {
    if (isLoggedIn) {
      isAddedWishlist(props.product.id).then(res => {
        setAddedToWishlist(res.added);
      });
    }
  }, [props.product.id, isLoggedIn]);

  const handleToggleWishlist = useCallback(() => {
    setAddedToWishlist(!isAddedToWishlist);
    if (isAddedToWishlist) {
      toast.show('Đã xóa khỏi danh sách yêu thích', {placement: 'center', duration: 1000});
      removeFromWishlistMutation.mutate({productId: props.product.id});
    } else {
      toast.show('Đã lưu', {placement: 'center', duration: 1000});
      addToWishlistMutation.mutate({productId: props.product.id});

      // analytics
      const addToWishlistParams: FirebaseAnalyticsTypes.AddToWishlistEventParameters = {
        items: [
          {
            item_name: props.product.title,
            item_id: props.product.id,
            item_category: props.product.categories?.lv1?.title,
            item_category2: props.product.categories?.lv2?.title,
          },
        ],
      };
      googleAnalytics.logAddToWishlist(addToWishlistParams);
    }
  }, [isAddedToWishlist, props.product]);

  const handleDownloadImage = useCallback(() => {
    navigation.navigate('DownloadProductMedia', {productId: props.product.id_number});
  }, []);

  return (
    <View style={styles.container}>
      {props.product.available === 0 && (
        <View style={{backgroundColor: theme.colors.secondary, alignSelf: 'flex-start', paddingHorizontal: 4, borderRadius: 4, paddingVertical: 1}}>
          <Text style={{color: theme.colors.text}}>Tạm hết hàng</Text>
        </View>
      )}
      <View style={{flexDirection: 'row', alignItems: 'flex-start', marginBottom: 12}}>
        {Platform.OS === 'web' && !isZaloMiniApp && !isLoggedIn ? (
          <TouchableOpacity style={{paddingHorizontal: theme.spacing.s, backgroundColor: '#eeffed'}} onPress={() => openLoginRequiredModal('register')}>
            <Text style={styles.loginToSeePriceText}>Đăng nhập để xem giá</Text>
          </TouchableOpacity>
        ) : (
          <Text style={styles.priceText}>{formatPriceToString(props.product.dropship_price)}</Text>
        )}
        <View style={{marginLeft: 'auto', alignSelf: 'flex-start', alignItems: 'center', flexDirection: 'row'}}>
          {Platform.OS !== 'web' && (
            <Button
              onPress={handleDownloadImage}
              icon={{
                name: 'download-outline',
                type: 'ionicon',
                size: 16,
                color: theme.colors.text,
              }}
              type="outline"
              buttonStyle={{borderRadius: 100, paddingVertical: 4, paddingLeft: 2, borderColor: theme.colors.textLight, borderWidth: 1}}
              title={'Tải ảnh'}
              titleStyle={{
                fontSize: theme.typography.sm,
                color: theme.colors.text,
              }}
            />
          )}
          <Button
            onPress={handleToggleWishlist}
            accessibilityLabel="Lưu sản phẩm"
            icon={{
              name: isAddedToWishlist ? 'bookmark' : 'bookmark-outline',
              type: 'ionicon',
              size: 24,
              color: isAddedToWishlist ? 'orange' : theme.colors.textLight,
            }}
            type="clear"
            buttonStyle={{paddingHorizontal: 4}}
          />
        </View>
      </View>
      {Platform.OS === 'ios' ? (
        <TextInput editable={false} value={props.product.title} multiline style={styles.title} scrollEnabled={false} />
      ) : (
        <Text selectable style={styles.title}>
          {props.product.title}
        </Text>
      )}

      <View style={[theme.globalStyles.flexRow, {marginTop: theme.spacing.s}]}>
        {props.product.rating_avg > 0 && (
          <View style={[theme.globalStyles.flexRow]}>
            <Ionicons name="star" size={14} color={'#ffc400'} />
            <Text style={styles.ratingAvgText} accessibilityLabel={`${props.product.rating_avg?.toFixed(1)} sao`}>
              {props.product.rating_avg?.toFixed(1)}
            </Text>
          </View>
        )}
        {props.product.sales > 0 && (
          <>
            {props.product.rating_avg > 0 && <View style={styles.totalSaleSeparator} />}
            <Text style={styles.totalSaleText}>Đã bán {kFormatter(props.product.sales)}</Text>
          </>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    padding: 12,
    paddingVertical: 8,
  },
  title: {
    fontSize: 16,
    // textTransform: 'uppercase',
    fontWeight: '500',
    color: theme.colors.text,
  },
  priceText: {
    paddingTop: 8,
    fontSize: 28,
    fontWeight: '400',
    color: '#008060',
  },
  loginToSeePriceText: {
    fontSize: theme.typography.base,
    color: theme.colors.primary,
  },
  totalSaleText: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
  },
  totalSaleSeparator: {
    width: 1,
    height: 8,
    backgroundColor: theme.colors.gray40,
    marginHorizontal: theme.spacing.s,
  },
  ratingAvgText: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
    marginLeft: 2,
  },
});

export default ProductInfo;
