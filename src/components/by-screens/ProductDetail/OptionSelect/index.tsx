import {Button} from '@rneui/base';
import produce from 'immer';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {Image, Platform, Pressable, ScrollView, StyleSheet, View} from 'react-native';
import {handleImageDomain} from '~utils/helpers/image';
import {formatPriceToString} from '~utils/helpers/price';
import Product from '~utils/types/product';
import QuantityInput from './QuantityInput';
import PriceInput from './PriceInput';
import Variant from '~utils/types/variant';
import {hasVariants} from '~utils/helpers/product';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useAddToCartMutation} from '~utils/api/cart';
import {useGetCurrentUser} from '~utils/api/auth';
import {useAuthActions, useNavigation} from '~hooks';
import theme from '~utils/config/themes/theme';
import {useToast} from 'react-native-toast-notifications';
import {useDisableAvoidSoftInput} from '~hooks/useAvoidSoftInput';
import * as Sentry from '@sentry/react-native';
import {FirebaseAnalyticsTypes} from '@react-native-firebase/analytics';
import {Text} from '@rneui/themed';
import ImageViewerModal from '~components/shared/ImageViewerModal';
import googleAnalytics from '~utils/helpers/analytics';

type OptionSelectProps = {
  product: Product;
  onClose: () => void;
};

const OptionSelect: React.FC<OptionSelectProps> = props => {
  useDisableAvoidSoftInput();
  const isMultiVariants = useMemo(() => {
    return hasVariants(props.product);
  }, [props.product.id]);
  const {openLoginRequiredModal} = useAuthActions();
  const [selectedVariant, setSelectedVariant] = useState<Variant | null>(isMultiVariants ? null : props.product.variants[0]);
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const [quantity, setQuantity] = useState(1);
  const [sellingPrice, setSellingPrice] = useState(props.product.market_price);
  const insets = useSafeAreaInsets();
  const addToCartMutation = useAddToCartMutation();
  const {data: user} = useGetCurrentUser();
  const navigation = useNavigation();
  const [errors, setErrors] = useState<string[]>([]);
  const toast = useToast();
  const [selectedVariantImageIndex, setSelectedVariantImageIndex] = useState(0);
  const [imageViewerOpen, setImageViewerOpen] = useState(false);

  useEffect(() => {
    let variantTitle = selectedOptions.join(' / ');

    const variant = props.product.variants.find(v => v.title === variantTitle);
    if (variant) {
      setSelectedVariant(variant);
    }
  }, [selectedOptions]);

  const handleOptionNameClick = useCallback((optionIndex: number, optionName: string) => {
    setSelectedOptions(prev =>
      produce(prev, draft => {
        draft[optionIndex] = optionName;
      }),
    );
  }, []);

  const handleAddToCart = useCallback(async () => {
    if (!user || !selectedVariant) {
      props.onClose();
      openLoginRequiredModal();

      return;
    }

    let validateErrs: string[] = [];

    setErrors([]);

    if (sellingPrice < selectedVariant.dropship_price) {
      validateErrs.push(`Giá bán phải cao hơn giá nhà cung cấp (${formatPriceToString(selectedVariant.dropship_price)})`);
    }

    if (validateErrs.length > 0) {
      setErrors(validateErrs);
      return;
    }

    await addToCartMutation
      .mutateAsync({
        user_id: user.id,
        items: [
          {
            dropship_selling_price: sellingPrice,
            quantity,
            variant_id: selectedVariant.id,
            note: '',
          },
        ],
      })
      .catch(err => {
        toast.show(err?.message, {
          type: 'danger',
          dangerColor: theme.colors.red,
        });

        Sentry.captureMessage('Them vao gio that bai');

        return;
      });

    props.onClose();

    // analytics
    const analyticsCartItems = [
      {
        quantity,
        price: sellingPrice,
        item_name: selectedVariant.product_title,
        item_id: selectedVariant.product_id,
        item_category: selectedVariant?.product_type?.split(' > ')[0],
        item_category2: selectedVariant?.product_type?.split(' > ')[1],
        item_variant: selectedVariant?.title,
      },
    ];
    const addToCartParams: FirebaseAnalyticsTypes.AddToCartEventParameters = {
      currency: 'VND',
      value: sellingPrice * quantity,
      items: analyticsCartItems,
    };
    const viewCartParams: FirebaseAnalyticsTypes.ViewCartEventParameters = {
      currency: 'VND',
      value: sellingPrice * quantity,
      items: analyticsCartItems,
    };

    googleAnalytics.logAddToCart(addToCartParams);
    googleAnalytics.logViewCart(viewCartParams);

    navigation.navigate('Cart', {
      preSelectedVariantIds: [selectedVariant.id],
      productId: selectedVariant.product_id,
    });
  }, [selectedVariant, quantity, sellingPrice, user]);

  const getThumbnail = useMemo(() => {
    if (selectedVariant && selectedVariant.image_id) {
      const image = props.product.images.find(img => img.id === selectedVariant.image_id);
      if (image) {
        return handleImageDomain(image.src);
      }
    }

    return handleImageDomain(props.product.image?.src);
  }, [props.product, selectedVariant]);

  const handleImagePress = useCallback(() => {
    if (selectedVariant && selectedVariant.image_id) {
      const imageIndex = props.product.images.findIndex(img => img.id === selectedVariant.image_id);

      if (imageIndex !== -1) {
        setSelectedVariantImageIndex(imageIndex);
      }
    }

    setImageViewerOpen(true);
  }, [props.product, selectedVariant]);

  const imageViewerImages = useMemo(() => {
    return props.product.images.map(image => ({
      url: handleImageDomain(image.src),
      props: {
        type: 'image',
        productId: image.product_id,
      },
    }));
  }, [props.product.images]);

  return (
    <View style={styles.container}>
      <View style={{paddingHorizontal: 8, paddingBottom: 12, flexDirection: 'row', alignItems: 'flex-start'}}>
        <Pressable onPress={handleImagePress}>
          <Image
            style={{width: 100, height: 100, marginTop: -30, borderWidth: 1, borderColor: theme.colors.gray40, marginRight: 20, borderRadius: 12}}
            source={{
              uri: getThumbnail,
            }}
          />
        </Pressable>
        {!selectedVariant && (
          <View style={{marginTop: 8, flex: 1}}>
            <Text style={{fontSize: 16, marginBottom: 4}} numberOfLines={3}>
              {props.product.title}
            </Text>
            <Text style={{fontSize: theme.typography.base}}>
              <Text style={{color: 'rgba(0,0,0,.5)'}}>Giá nhà cung cấp:</Text> {formatPriceToString(props.product.dropship_price)}
            </Text>
          </View>
        )}
        {selectedVariant && (
          <View style={{marginTop: 8, flex: 1}}>
            <Text style={{fontSize: theme.typography.lg, marginBottom: 4}} numberOfLines={3}>
              {selectedVariant.product_title}
            </Text>
            <Text style={{marginBottom: 4}}>{selectedVariant.title === 'Default Title' ? 'Mặc định' : selectedVariant.title}</Text>
            <Text style={{fontSize: theme.typography.base}}>
              <Text style={{color: 'rgba(0,0,0,.5)'}}>Giá nhà cung cấp:</Text> {formatPriceToString(selectedVariant.dropship_price)}
            </Text>
          </View>
        )}
      </View>

      {isMultiVariants && (
        <ScrollView style={{paddingTop: 20}}>
          {props.product.options.map((option, index) => {
            return (
              <View style={{paddingHorizontal: 20, marginBottom: 20}} key={option.id}>
                <Text>{option.display_name}</Text>
                <View style={{flexDirection: 'row', marginTop: 8, flexWrap: 'wrap'}}>
                  {option.values.map(optionName => (
                    <Pressable onPress={() => handleOptionNameClick(index, optionName)} key={optionName}>
                      <View
                        style={{
                          marginBottom: 8,
                          marginRight: 8,
                          padding: 8,
                          paddingHorizontal: 20,
                          backgroundColor: 'rgba(0,0,0,0)',
                          borderRadius: 4,
                          borderWidth: selectedOptions[index] === optionName ? 2 : 1,
                          borderColor: selectedOptions[index] === optionName ? '#008060' : theme.colors.gray40,
                        }}>
                        <Text style={{fontSize: theme.typography.base}}>{optionName}</Text>
                      </View>
                    </Pressable>
                  ))}
                </View>
              </View>
            );
          })}
        </ScrollView>
      )}

      <View
        style={{
          backgroundColor: '#fff1dc',
          paddingHorizontal: 20,
          paddingVertical: 20,
          paddingBottom: insets.bottom + 20,
        }}>
        {/* @ts-ignore */}
        <QuantityInput value={quantity} onChange={setQuantity} disabled={!selectedVariant} />
        {/* @ts-ignore */}
        <PriceInput value={sellingPrice} onChange={setSellingPrice} disabled={!selectedVariant} />

        {errors.length > 0 && (
          <View>
            {errors.map(errorText => (
              <Text style={{color: theme.colors.red}} key={errorText}>
                {errorText}
              </Text>
            ))}
          </View>
        )}

        <View style={{justifyContent: 'flex-end', flexDirection: 'row'}}>
          <View style={{marginRight: 20}}>
            <Text>Lợi nhuận</Text>
          </View>
          <View style={{width: 130}}>
            <Text style={{}}>{formatPriceToString(sellingPrice * quantity - (selectedVariant?.dropship_price ?? props.product.dropship_price) * quantity)}</Text>
          </View>
        </View>

        <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 24}}>
          <Button
            disabled={!selectedVariant}
            color={'rgb(0, 128, 96)'}
            containerStyle={{flex: 1, marginRight: 8}}
            buttonStyle={{
              borderRadius: 100,
              borderWidth: 1,
              borderColor: !selectedVariant ? 'transparent' : 'rgb(0, 128, 96)',
            }}
            loading={addToCartMutation.isLoading}
            onPress={handleAddToCart}
            title={
              <View style={{alignItems: 'center'}}>
                <Text style={{color: '#fff', fontSize: 20}}>Thêm vào giỏ &raquo;</Text>
              </View>
            }
          />
          <Button title={'Hủy'} type="clear" titleStyle={{color: '#333'}} onPress={props.onClose} />
        </View>
      </View>

      {Platform.OS !== 'web' && <ImageViewerModal visible={imageViewerOpen} onClose={() => setImageViewerOpen(false)} images={imageViewerImages} selectedIndex={selectedVariantImageIndex} />}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
  },
});

export default OptionSelect;
