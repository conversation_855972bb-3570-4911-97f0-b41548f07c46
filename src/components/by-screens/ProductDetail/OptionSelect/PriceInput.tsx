import {Input} from '@rneui/base';
import {Text} from '@rneui/themed';
import React, {useCallback} from 'react';
import {View} from 'react-native';

type PriceInputProps = {
  value: number;
  onChange: (value: number | string) => void;
  disabled: boolean;
};

const PriceInput: React.FC<PriceInputProps> = props => {
  const handleChange = useCallback((text: string) => {
    props.onChange(parseInt(text.replace(/\./g, ''), 10) || '');
  }, []);

  return (
    <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end', marginTop: 8}}>
      <Text style={{marginBottom: 16, marginRight: 20}}>Giá bán của bạn (đ)</Text>
      <View style={{width: 126}}>
        <Input
          containerStyle={{paddingHorizontal: 0}}
          inputStyle={{width: 126, flex: 1, paddingHorizontal: 12, paddingVertical: 12}}
          keyboardType="numeric"
          value={props.value?.toLocaleString('vi-VN')}
          onChangeText={handleChange}
          disabled={props.disabled}
          inputContainerStyle={{
            borderWidth: 1,
            backgroundColor: '#fff',
            borderColor: 'rgba(0,0,0,.1)',
            borderRadius: 12,
          }}
        />
      </View>
    </View>
  );
};

export default PriceInput;
