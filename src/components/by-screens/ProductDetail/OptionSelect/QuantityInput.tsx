import {Input} from '@rneui/base';
import React, {useCallback} from 'react';
import {Text, View} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';

type QuantityInputProps = {
  value: number;
  onChange: (newValue: number | string) => void;
  disabled: boolean;
};

const QuantityInput: React.FC<QuantityInputProps> = props => {
  const handleChange = useCallback((text: string) => {
    props.onChange(parseInt(text, 10) || '');
  }, []);

  const handleAddQuantity = useCallback(() => {
    // @ts-ignore
    props.onChange(parseInt(props.value + 1, 10));
  }, [props.value]);

  const handleReduceQuantity = useCallback(() => {
    // @ts-ignore
    props.onChange(props.value ? parseInt(props.value - 1, 10) : props.value);
  }, [props.value]);

  return (
    <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end'}}>
      <Text style={{marginBottom: 16, marginRight: 20}}>Số lượng</Text>
      <View style={{width: 130}}>
        <Input
          leftIcon={
            <Ionicons
              onPress={handleReduceQuantity}
              name="remove"
              color={'rgba(0,0,0,.7)'}
              size={30}
              style={{
                paddingRight: 4,
                borderRightWidth: 1,
                borderColor: 'rgba(0,0,0,.1)',
              }}
            />
          }
          rightIcon={
            <Ionicons
              onPress={handleAddQuantity}
              name="add"
              color={'rgba(0,0,0,.7)'}
              size={30}
              style={{
                paddingLeft: 4,
                borderLeftWidth: 1,
                borderColor: 'rgba(0,0,0,.1)',
              }}
            />
          }
          renderErrorMessage={false}
          containerStyle={{paddingHorizontal: 0}}
          inputStyle={{width: 45, fontSize: 16, minHeight: 32, height: 32, paddingVertical: 0, textAlign: 'center'}}
          inputContainerStyle={{
            borderWidth: 1,
            borderRadius: 12,
            overflow: 'hidden',
            borderColor: 'rgba(0,0,0,.1)',
            backgroundColor: '#fff',
          }}
          keyboardType="numeric"
          value={String(props.value)}
          onChangeText={handleChange}
          disabled={props.disabled}
        />
      </View>
    </View>
  );
};

export default QuantityInput;
