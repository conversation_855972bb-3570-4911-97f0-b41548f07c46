import React from 'react';
import Card from '~components/shared/Card';
import Product from '~utils/types/product';
import ProductSpecifications from './ProductSpecifications';

type ProductDescriptionProps = {
  product: Product;
};

const ProductDescription: React.FC<ProductDescriptionProps> = props => {
  return (
    <Card title="Thông tin sản phẩm">
      <ProductSpecifications specifications={props.product.specifications} categories={props.product.categories} />
      <div dangerouslySetInnerHTML={{__html: props.product.body_html}} className="product-body-html" />
    </Card>
  );
};

export default ProductDescription;
