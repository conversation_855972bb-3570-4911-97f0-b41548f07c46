import {Button} from '@rneui/base';
import React, {useCallback, useState} from 'react';
import {View} from 'react-native';
import WebView, {WebViewMessageEvent} from 'react-native-webview';
import Card from '~components/shared/Card';
import theme from '~utils/config/themes/theme';
import ee, {EventNames} from '~utils/events';
import Product from '~utils/types/product';
import ProductSpecifications from './ProductSpecifications';

type ProductDescriptionProps = {
  product: Product;
};

const DEFAULT_CONTENT_HEIGHT = 200;

const ProductDescription: React.FC<ProductDescriptionProps> = props => {
  const [contentHeight, setContentHeight] = useState(DEFAULT_CONTENT_HEIGHT);
  const [webviewContentHeight, setWebviewContentHeight] = useState(0);
  const [isExpanded, setExpanded] = useState(false);

  const webViewScript = `
  setTimeout(function() { 
    window.ReactNativeWebView.postMessage(JSON.stringify({type:"GET_SCROLL_HEIGHT", data:document.documentElement.scrollHeight})); 
  }, 1000);
  var imgs = document.getElementsByTagName("img");
  var newImages = [...imgs].map(img => img.src);
  var getParentAnchor = function (element) {
    while (element !== null) {
      if (element.tagName && element.tagName.toUpperCase() === "IMG") {
        return element;
      }
      element = element.parentNode;
    }
    return null;
  };
  document.querySelector("body").addEventListener('click', function(e) {
    var img = getParentAnchor(e.target);
    var newIndex = newImages.indexOf(img.src);
    
    if(img !== null && newIndex !== -1) {
      window.ReactNativeWebView.postMessage(JSON.stringify({type:"IMG_VIEW", data:{newImages,newIndex}}));
    }
  }, false);

  true; // note: this is required, or you'll sometimes get silent failures
`;

  const handleExpand = useCallback(() => {
    setContentHeight(webviewContentHeight);
    setExpanded(true);
  }, [webviewContentHeight]);

  const handleCollapse = useCallback(() => {
    setContentHeight(DEFAULT_CONTENT_HEIGHT);
    setExpanded(false);
  }, []);

  const handleMesssage = useCallback((event: WebViewMessageEvent) => {
    try {
      const parsedMessage = JSON.parse(event.nativeEvent.data);
      switch (parsedMessage.type) {
        case 'GET_SCROLL_HEIGHT':
          setWebviewContentHeight(parsedMessage.data);
          break;

        case 'IMG_VIEW':
          ee.emit(EventNames.OpenImageViewer, {
            newImages: parsedMessage.data?.newImages.map((url: any) => ({
              url: url,
              props: {
                type: 'image',
                productId: props.product.id,
              },
            })),
            newIndex: parsedMessage.data?.newIndex,
          });
          break;

        default:
          break;
      }
    } catch (error) {}
  }, []);

  return (
    <>
      <Card title="Thông tin sản phẩm">
        <ProductSpecifications specifications={props.product.specifications} categories={props.product.categories} />
        <View style={{height: contentHeight}} renderToHardwareTextureAndroid>
          <WebView
            scrollEnabled={false}
            onMessage={handleMesssage}
            injectedJavaScript={webViewScript}
            source={{
              html: `<html><head><meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>body{font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;font-size:16px;color:${theme.colors.textLight}}img{max-width:100%}</style>
            </head><body>${props.product.body_html}</body></html>`,
            }}
          />
        </View>
        {webviewContentHeight > DEFAULT_CONTENT_HEIGHT && (
          <Button
            onPress={isExpanded ? handleCollapse : handleExpand}
            title={isExpanded ? 'Thu gọn' : 'Xem thêm'}
            iconRight
            type="clear"
            containerStyle={{borderTopColor: 'rgba(0,0,0,.07)', borderTopWidth: 1, marginTop: 8}}
            titleStyle={{color: '#008060', fontSize: 16}}
            icon={
              isExpanded
                ? {
                    type: 'ionicon',
                    name: 'chevron-up',
                    size: 16,
                    color: '#008060',
                  }
                : {
                    type: 'ionicon',
                    name: 'chevron-down',
                    size: 16,
                    color: '#008060',
                  }
            }
          />
        )}
      </Card>
    </>
  );
};

export default ProductDescription;
