import {Link} from '@react-navigation/native';
import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import {ProductDetailCategories, ProductSpecification} from '~utils/types/product';
import Ionicons from 'react-native-vector-icons/Ionicons';

type ProductSpecificationsProps = {
  specifications: ProductSpecification[];
  categories?: ProductDetailCategories;
};

const ProductSpecifications: React.FC<ProductSpecificationsProps> = props => {
  return (
    <View style={styles.container}>
      {props.categories?.lv1 && props.categories?.lv2 && (
        <View style={styles.specItem}>
          <Text style={styles.specItem_name}><PERSON>h m<PERSON>c</Text>

          <View style={[styles.specItem_value, theme.globalStyles.flexRow, {flexWrap: 'wrap'}]}>
            <Link screen="CategoryDetail" params={{categoryId: props.categories?.lv1.id, slug: props.categories?.lv1.slug}} style={styles.link}>
              {props.categories?.lv1.title}
            </Link>
            <Ionicons name="chevron-forward" size={14} color={theme.colors.textLight} style={{marginHorizontal: 2}} />
            <Link screen="CategoryDetail" params={{categoryId: props.categories?.lv2.id, slug: props.categories?.lv2.slug}} style={styles.link}>
              {props.categories?.lv2.title}
            </Link>
          </View>
        </View>
      )}
      {props.specifications.map((spec, index) => (
        <View style={styles.specItem} key={index}>
          <Text style={styles.specItem_name}>{spec.name}</Text>
          <Text style={styles.specItem_value}>{spec.value}</Text>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,.05)',
    marginBottom: 12,
  },
  specItem: {
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  specItem_name: {
    fontSize: theme.typography.base,
    width: 120,
    marginRight: 20,
    color: theme.colors.textLight,
  },

  specItem_value: {
    fontSize: theme.typography.base,
    flex: 1,
    color: theme.colors.text,
  },
  link: {
    textDecorationLine: 'underline',
    textDecorationColor: theme.colors.text,
    color: theme.colors.text,
  },
});

export default ProductSpecifications;
