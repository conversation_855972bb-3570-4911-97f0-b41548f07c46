import {Button, Text} from '@rneui/themed';
import React, {useCallback} from 'react';
import {Image, View} from 'react-native';
// @ts-ignore
import NotFoundIllustrator from '~assets/notfound.png';
import {useNavigation} from '~hooks';
import theme from '~utils/config/themes/theme';

type ProductNotFoundProps = {
  error?: any;
};

const ProductNotFound: React.FC<ProductNotFoundProps> = () => {
  const navigation = useNavigation();

  const handleBack = useCallback(() => {
    if (navigation.canGoBack()) {
      navigation.goBack();
    } else {
      navigation.navigate('MainTabs', {screen: 'Home'});
    }
  }, []);

  return (
    <View style={{backgroundColor: 'white', flex: 1, paddingHorizontal: theme.spacing.l}}>
      <View style={{alignItems: 'center', marginTop: 50}}>
        <Image source={NotFoundIllustrator} style={{width: 300, height: 300}} />

        <Text style={{fontSize: theme.typography.lg2, fontWeight: 'bold'}}>Không tìm thấy sản phẩm</Text>

        <Text style={{fontSize: theme.typography.md, marginTop: theme.spacing.m, marginBottom: theme.spacing.xl}}>
          Sản phẩm không tồn tại hoặc đã bị gỡ bỏ. Vui lòng tìm sản phẩm khác tương tự trên TTS Dropship.
        </Text>

        <Button onPress={handleBack} size="sm" icon={{type: 'ionicon', name: 'arrow-back', color: theme.colors.white}} buttonStyle={{borderRadius: 30, paddingHorizontal: 20}}>
          Quay lại
        </Button>
      </View>
    </View>
  );
};

export default ProductNotFound;
