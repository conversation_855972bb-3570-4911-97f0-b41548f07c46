import {Text} from '@rneui/themed';
import React, {useCallback, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {useGetShopPriceRules} from '~utils/api/voucher';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {Dialog} from '@rneui/base';
import PriceRuleItem from './PriceRuleItem';

type ShopDiscountApplicationsProps = {
  shopId: string;
};

const ShopDiscountApplications: React.FC<ShopDiscountApplicationsProps> = props => {
  const [helpDialogShow, setHelpDialogShow] = useState(false);

  const {data} = useGetShopPriceRules({
    shop_id: props.shopId,
    order: 'asc',
    sort_by: 'value',
  });

  const handleOpenHelpDialog = useCallback(() => {
    setHelpDialogShow(true);
  }, []);

  const handleCloseHelpDialog = useCallback(() => {
    setHelpDialogShow(false);
  }, []);

  return (
    <>
      <View style={styles.container}>
        {Boolean(data?.length) && (
          <>
            <View style={[theme.globalStyles.flexRow, {marginBottom: theme.spacing.s, paddingTop: 12}]}>
              <Text style={[{fontSize: theme.typography.base, marginRight: theme.spacing.xs, color: '#d0011b'}]}>🎁 Chương trình khuyến mãi</Text>

              <Ionicons name="help-circle-outline" size={20} onPress={handleOpenHelpDialog} color={'#d0011b'} style={{paddingHorizontal: 8, paddingVertical: 4}} />
            </View>
            <View style={{paddingHorizontal: 12, marginBottom: 12}}>
              {data?.map(promotion => {
                return <PriceRuleItem promotion={promotion} key={promotion.id} />;
              })}
            </View>
          </>
        )}

        {/*  */}
      </View>

      <Dialog isVisible={helpDialogShow} onBackdropPress={handleCloseHelpDialog} overlayStyle={{backgroundColor: 'white'}}>
        <Text style={{marginBottom: theme.spacing.m}}>- Các khuyến mãi sẽ được tự động áp dụng khi bạn tạo đơn.</Text>
        <Text style={{marginBottom: theme.spacing.m}}>- Tận dụng các chương trình khuyến mãi giúp bạn dễ bán được hàng hơn.</Text>
        <Dialog.Actions>
          <Dialog.Button title={'Đã hiểu'} titleStyle={{color: theme.colors.blue}} onPress={handleCloseHelpDialog} />
        </Dialog.Actions>
      </Dialog>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: theme.spacing.s,
    backgroundColor: theme.colors.white,
    // paddingVertical: 12,
    paddingHorizontal: 12,
  },
});

export default ShopDiscountApplications;
