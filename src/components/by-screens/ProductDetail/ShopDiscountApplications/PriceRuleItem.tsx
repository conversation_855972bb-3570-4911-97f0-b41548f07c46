import {Text} from '@rneui/themed';
import React, {useMemo} from 'react';
import {Linking, Platform, Pressable, View} from 'react-native';
import {ShopPriceRule} from '~utils/api/voucher';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import dayjs from 'dayjs';
import {useNavigation} from '~hooks';

type PriceRuleItemProps = {
  promotion: ShopPriceRule;
};

const PriceRuleItem: React.FC<PriceRuleItemProps> = props => {
  const expiredHours = useMemo(() => {
    try {
      return dayjs(props.promotion.ends_at).diff(dayjs(), 'hours');
    } catch (error) {
      return 99999;
    }
  }, [props.promotion.ends_at]);

  const navigation = useNavigation();

  const handlePress = () => {
    if (!props.promotion.link) {
      return;
    }

    if (Platform.OS === 'web') {
      return Linking.openURL(props.promotion.link);
    } else {
      navigation.navigate('WebView', {url: props.promotion.link});
    }
  };

  return (
    <View key={props.promotion.id}>
      <Pressable onPress={handlePress}>
        <Text style={[{marginBottom: theme.spacing.s}]}>
          {expiredHours <= 72 && (
            <Text
              style={{
                fontSize: theme.typography.base,
                color: theme.colors.pending,
              }}>
              <Ionicons name="time-outline" size={14} color={theme.colors.pending} />
              &nbsp;Còn {expiredHours} giờ &nbsp;
            </Text>
          )}
          <Text
            style={[
              {
                fontSize: theme.typography.base,
                color: theme.colors.text,
                marginRight: theme.spacing.xs,
              },
              !!props.promotion.link && {
                textDecorationLine: 'underline',
                textDecorationColor: theme.colors.textLightest,
              },
            ]}>
            {props.promotion.title}
          </Text>
          {!!props.promotion.link && (
            <Text>
              <Ionicons name="document-attach-outline" size={14} color={theme.colors.textLight} />
            </Text>
          )}
        </Text>
      </Pressable>
    </View>
  );
};

export default PriceRuleItem;
