import {Button, Input, Text} from '@rneui/themed';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {Dimensions, FlatList, Keyboard, Platform, StyleSheet, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useDisableAvoidSoftInput} from '~hooks/useAvoidSoftInput';
import theme from '~utils/config/themes/theme';
import {useGenerateProductPostMutation, useGetChatMessages, useGetPromptTemplates} from '~utils/api/chatgpt';
import {SCREEN_HEIGHT} from '@gorhom/bottom-sheet';
import MessageItem from './ChatGPT/MessageItem';
import PromptTemplates from './ChatGPT/PromptTemplates';
import {useGetCurrentUser} from '~utils/api/auth';
import ChatGPTTypping from './ChatGPT/ChatGPTTyping';
// @ts-ignore
import ChatGPTQRCode from '~assets/chatgpt-qr.png';
import {QuickImage} from '~components/shared/QuickImage';

const WINDOW_HEIGHT = Dimensions.get('window').height;

type ChatGPTProductPostProps = {
  onClose: (isNewPostCreated?: boolean) => void;
  productId: string;
  onContentCopy: (content: string) => void;
};

const ChatGPTProductPost: React.FC<ChatGPTProductPostProps> = props => {
  useDisableAvoidSoftInput();
  const insets = useSafeAreaInsets();
  const [postContent, setPostContent] = useState('');
  const {data, isLoading, isError, error, fetchNextPage, isFetchingNextPage, hasNextPage} = useGetChatMessages({
    limit: 5,
    product_id: props.productId,
    type: 'product_post',
  });
  const mutation = useGenerateProductPostMutation();
  const {data: promptTemplates} = useGetPromptTemplates({type: 'product_post'});
  const {data: currentUser} = useGetCurrentUser();

  useEffect(() => {
    if (!isLoading && ((data && data.pages?.[0]?.data.length === 0) || (isError === true && (error as any)?.response?.data?.message === 'Invalid Conversation ID'))) {
      mutation.mutateAsync({product_id: props.productId, conversation_id: null, new_conversation: false, prompt: null, type: 'product_post'});
    }
  }, [isLoading]);

  const dataFlat = useMemo(() => {
    return data?.pages.flatMap(page => page.data) ?? [];
  }, [data]);

  const handleTemplatePromptPress = useCallback(
    (prompt: string) => {
      mutation.mutateAsync({product_id: props.productId, conversation_id: null, new_conversation: false, prompt, type: 'product_post'});
    },
    [props.productId],
  );

  const handlePrompt = useCallback(() => {
    Keyboard.dismiss();
    mutation.mutateAsync({product_id: props.productId, conversation_id: null, new_conversation: false, prompt: postContent, type: 'product_post'});

    setPostContent('');
  }, [postContent, props.productId]);

  const handleEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage]);

  const handleStartNewConversation = useCallback(() => {
    mutation.mutateAsync({product_id: props.productId, conversation_id: null, new_conversation: true, prompt: null, type: 'product_post'});
  }, [props.productId]);

  return (
    <View style={{backgroundColor: 'white', paddingTop: insets.top, height: WINDOW_HEIGHT}}>
      <View style={styles.header}>
        <Button
          type="clear"
          icon={{type: 'ionicon', name: 'chevron-back', size: 30, color: theme.colors.textLight}}
          titleStyle={styles.backTitle}
          buttonStyle={{paddingHorizontal: 4}}
          iconContainerStyle={{width: 50, height: 30}}
          onPress={() => props.onClose()}
          containerStyle={{position: 'absolute', left: 0, top: 0, bottom: 0, justifyContent: 'center', alignItems: 'center'}}
        />

        <Text style={{fontSize: theme.typography.md, color: theme.colors.text}}>Tạo bài viết bằng AI</Text>

        <QuickImage source={ChatGPTQRCode} style={{width: 60, height: 60, position: 'absolute', right: 10, opacity: 1}} />
      </View>

      <FlatList
        ListFooterComponent={
          // create ChatGPT generated content warning
          <View style={{padding: 12}}>
            <Text style={{color: theme.colors.textLight, fontSize: theme.typography.base}}>
              Nội dung này được tạo tự động bởi AI. Vui lòng đọc kĩ và chỉnh sửa nếu cần thiết. Tránh việc thông tin sản phẩm sai với thực tế.
            </Text>
          </View>
        }
        ListEmptyComponent={
          <>
            {/* Chat gpt is getting your post indicator */}
            {mutation.isLoading && (
              <View
                style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: SCREEN_HEIGHT * 0.7,
                  transform: [{scaleY: -1}],
                }}>
                <Text style={{color: theme.colors.textLight, fontSize: theme.typography.base}}>AI đang tạo nội dung cho bạn...</Text>
              </View>
            )}
          </>
        }
        ListHeaderComponent={
          // ChatGPT typing component
          <>{mutation.isLoading && <ChatGPTTypping />}</>
        }
        renderItem={({item}) => <MessageItem message={item} userAvatarUrl={currentUser?.avatar} onContentCopy={props.onContentCopy} />}
        style={{flex: 1, backgroundColor: theme.colors.white}}
        data={dataFlat}
        inverted
        onEndReached={handleEndReached}
        keyExtractor={item => item._id}
      />

      {/* flex row with input message and send button */}
      {dataFlat.length > 0 && !mutation.isLoading && (
        <PromptTemplates onStartNewConversation={handleStartNewConversation} lastMessage={dataFlat[0]} promptTemplates={promptTemplates ?? []} onPress={handleTemplatePromptPress} />
      )}

      <View style={{flexDirection: 'row', alignItems: 'center', paddingHorizontal: 12, paddingVertical: 8, paddingBottom: insets.bottom}}>
        <Input
          value={postContent}
          onChangeText={setPostContent}
          multiline
          inputContainerStyle={{padding: 0, paddingBottom: 0, margin: 0}}
          inputStyle={{flex: 1, fontSize: theme.typography.base}}
          containerStyle={{flex: 1}}
          placeholder="Nhập yêu cầu tại đây..."
          renderErrorMessage={false}
          onSubmitEditing={handlePrompt}
        />
        <Button
          disabled={postContent.length === 0 || mutation.isLoading}
          size="sm"
          type="clear"
          icon={{type: 'ionicon', name: 'send', size: 30, color: theme.colors.primary}}
          onPress={handlePrompt}
          disabledStyle={{
            opacity: 0.2,
          }}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    ...theme.globalStyles.flexRow,
    position: 'relative',
    paddingVertical: theme.spacing.m + 2,
    marginTop: Platform.OS === 'android' ? 20 : 0,
    justifyContent: 'center',
    flexShrink: 0,
    alignItems: 'flex-end',
    // backgroundColor: 'red',
    // marginTop: 20,
  },
  backTitle: {
    fontSize: theme.typography.md,
    color: theme.colors.textLight,
  },
});

export default ChatGPTProductPost;
