import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {ActivityIndicator, Image, Pressable, ScrollView, StyleSheet, View} from 'react-native';
import {trackProductPostCopy, useDeleteProductPost, useGetProductPost} from '~utils/api/product';
import {CheckBox, ScreenHeight} from '@rneui/base';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Product, {ProductImage, ProductPost as ProductPostType, ProductVideo} from '~utils/types/product';
import SocialShareShortcut from './SocialShareShortcut';
import theme from '~utils/config/themes/theme';
import {Button, Text, Dialog} from '@rneui/themed';
import WebView from 'react-native-webview';
import ProductPostEdit from './ProductPostEdit';
import {useAuthActions} from '~hooks';
import {getLastSelectedMediaItems, saveLastSelectedMediaItems} from '~utils/helpers/product';
import debounce from 'lodash/fp/debounce';
import ChatGPTProductPost from './ChatGPTProductPost';
import Clipboard from '@react-native-clipboard/clipboard';
import Toast from 'react-native-toast-notifications';

export type ProductPostProps = {
  product: Product;
  productId: string;
  onClose: () => void;
};

const ProductPost: React.FC<ProductPostProps> = props => {
  const insets = useSafeAreaInsets();
  const toastRef = useRef<Toast>();

  const {data, isLoading} = useGetProductPost(props.productId);
  const [selectedPostIndex, setSelectedPostIndex] = useState(0);

  const mediaItems = useMemo(() => {
    return [...props.product.videos.map(video => ({type: 'video', item: video})), ...props.product.images.map(image => ({type: 'image', item: image}))];
  }, [props.product.images, props.product.videos]);

  const [selectedImages, setSelectedImages] = useState(mediaItems);
  const [selectedMediaItems, setSelectedMediaItems] = useState(mediaItems.slice(0, 10));
  const [isDeleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const deletePostMutation = useDeleteProductPost();
  const [selectedPost, setSelectedPost] = useState<ProductPostType | null | undefined>();
  const {openLoginRequiredModal, isLoggedIn} = useAuthActions();
  const [mode, setMode] = useState<'preview' | 'edit' | 'chatgpt'>('preview');
  const [chatGPTClipboardContent, setChatGPTClipboardContent] = useState('');

  const debounceSaveLastSelectedMediaItems = useRef(
    debounce(2000)((productId: string, mediaItems: {type: string; item: ProductImage | ProductVideo}[]) => {
      saveLastSelectedMediaItems(productId, mediaItems);
    }),
  ).current;

  // optimize UX: save and restore last selected images
  useEffect(() => {
    (async () => {
      const lastSelectedMediaItems = await getLastSelectedMediaItems(props.productId);
      if (lastSelectedMediaItems.length) {
        setSelectedMediaItems(lastSelectedMediaItems as any);
      }
    })();
  }, []);

  const checkedMediaItemsArr = useMemo(() => {
    const selectedMediaItemsIds = selectedMediaItems.map(img => img.item.id);
    return mediaItems.map(img => selectedMediaItemsIds.includes(img.item.id));
  }, [selectedMediaItems, mediaItems]);

  const handleToggleCheckbox = useCallback(
    (mediaItem: {type: string; item: ProductImage | ProductVideo}) => {
      const selectedMediaItemsArr = selectedMediaItems.map(img => img.item.id);
      let newSelectedMediaItems = [];
      if (selectedMediaItemsArr.includes(mediaItem.item.id)) {
        newSelectedMediaItems = selectedMediaItems.filter(img => img.item.id !== mediaItem.item.id);
      } else {
        newSelectedMediaItems = [...selectedMediaItems, mediaItem];
      }
      setSelectedMediaItems(newSelectedMediaItems as any);
      debounceSaveLastSelectedMediaItems(props.productId, newSelectedMediaItems);
    },
    [selectedMediaItems, props.productId],
  );

  const handleEditProductPost = useCallback(
    (isCreateNewPost?: boolean) => {
      if (!isLoggedIn) {
        props.onClose();

        setTimeout(() => {
          openLoginRequiredModal();
        }, 400);

        return;
      }
      if (isCreateNewPost) {
        setSelectedPost(null);
      } else {
        setSelectedPost(data?.[selectedPostIndex]);
      }
      setMode('edit');
    },
    [data, selectedPostIndex, isLoggedIn],
  );

  const handleCloseEditProductPost = useCallback((isNewPostCreated?: boolean) => {
    if (isNewPostCreated) {
      setSelectedPostIndex(0);
    }
    setMode('preview');
  }, []);

  const handleDeletePostPress = useCallback(() => {
    setDeleteDialogOpen(true);
  }, []);

  const handleDeletePostConfirm = useCallback(async () => {
    const post = data?.[selectedPostIndex];
    if (post) {
      await deletePostMutation.mutateAsync({
        postId: post.id,
        productId: props.productId,
      });
    }

    handleCloseDeletePostDialog();
  }, [selectedPostIndex, data, props.productId]);

  const handleCloseDeletePostDialog = useCallback(() => {
    setDeleteDialogOpen(false);
  }, []);

  const privatePostCount = useMemo(() => {
    return (
      data?.reduce((a, c) => {
        return c.privacy === 'private' ? a + 1 : a;
      }, 0) ?? 0
    );
  }, [data]);

  const handleChatGPTPress = useCallback(() => {
    setMode('chatgpt');
  }, []);

  const handleChatGPTContentCopy = useCallback(
    (content: string) => {
      setChatGPTClipboardContent(content);
      Clipboard.setString(content);
      trackProductPostCopy('chat', props.productId, content);
    },
    [props.productId],
  );

  const handleCopyContentPress = useCallback(() => {
    if (data?.[selectedPostIndex]?.id) {
      try {
        trackProductPostCopy(data?.[selectedPostIndex]?.id);
      } catch (error) {}
      Clipboard.setString(data?.[selectedPostIndex]?.content);
      setChatGPTClipboardContent('');
      if (toastRef.current) {
        (toastRef.current as Toast).show('Đã sao chép!', {
          duration: 1000,
        });
      }
    }
  }, [data, selectedPostIndex]);

  return (
    <>
      {mode === 'preview' && (
        <View style={[styles.container, {paddingBottom: insets.bottom + 20, position: 'relative'}]}>
          {isLoading && <ActivityIndicator />}

          <View style={[theme.globalStyles.flexRow, {alignItems: 'flex-start', paddingRight: 12}]}>
            {
              <View
                style={{
                  flexDirection: 'row',
                  flex: 1,
                  flexWrap: 'wrap',
                  paddingBottom: 4,
                  marginBottom: theme.spacing.s,
                  paddingHorizontal: 12,
                  paddingVertical: 8,
                }}>
                {(data ?? []).map((post, index) => {
                  const isSelected = index === selectedPostIndex;
                  const postName = post.privacy === 'public' ? `Mẫu ${index + 1 - privatePostCount}` : 'Mẫu của tôi';
                  return (
                    <Pressable onPress={() => setSelectedPostIndex(index)} key={post.id}>
                      <View
                        style={{
                          backgroundColor: isSelected ? theme.colors.primary : theme.colors.gray10,
                          paddingVertical: 4,
                          paddingHorizontal: 20,
                          borderRadius: 30,
                          marginRight: 8,
                          marginBottom: 8,
                        }}>
                        <Text style={{color: isSelected ? theme.colors.white : theme.colors.text, fontSize: theme.typography.base}}>{postName}</Text>
                      </View>
                    </Pressable>
                  );
                })}
                <Button
                  icon={{type: 'ionicon', name: 'add-circle', size: 18, color: theme.colors.textLight}}
                  size="sm"
                  buttonStyle={{backgroundColor: theme.colors.gray10, borderRadius: 100, paddingVertical: 4, paddingHorizontal: 4}}
                  onPress={() => handleEditProductPost(true)}
                />
              </View>
            }

            <Ionicons name="close-circle" color={theme.colors.textLight} size={45} onPress={props.onClose} style={{zIndex: 1}} />
          </View>
          <View style={{flex: 1}}>
            {!isLoading && data?.[selectedPostIndex]?.content && (
              <View style={{flex: 1, position: 'relative'}} onStartShouldSetResponder={() => true}>
                <View style={{position: 'absolute', right: 10, top: 4, zIndex: 1, flexDirection: 'row'}}>
                  <Button
                    size="sm"
                    icon={{
                      type: 'material-community',
                      name: 'circle-edit-outline',
                      size: 16,
                      color: theme.colors.textLight,
                    }}
                    onPress={() => handleEditProductPost()}
                    buttonStyle={{
                      backgroundColor: theme.colors.white,
                      borderRadius: 12,
                      paddingHorizontal: 4,
                      paddingVertical: 3,
                    }}
                    titleStyle={{
                      fontSize: theme.typography.sm,
                      color: theme.colors.textLight,
                    }}>
                    Chỉnh sửa
                  </Button>
                  {data?.[selectedPostIndex]?.privacy === 'private' && (
                    <Button
                      size="sm"
                      icon={{
                        type: 'material-community',
                        name: 'delete-outline',
                        size: 16,
                        color: theme.colors.red,
                      }}
                      containerStyle={{marginLeft: 4}}
                      onPress={handleDeletePostPress}
                      buttonStyle={{
                        backgroundColor: theme.colors.white,
                        borderRadius: 8,
                        paddingHorizontal: 4,
                        paddingVertical: 4,
                      }}
                      titleStyle={{
                        fontSize: theme.typography.sm,
                        color: theme.colors.text,
                      }}
                    />
                  )}
                </View>
                <WebView
                  onStartShouldSetResponder={() => true}
                  style={{flex: 1, backgroundColor: theme.colors.gray10}}
                  source={{
                    html: `<html><head><meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>body{font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;font-size:14px;background-color:${
          theme.colors.gray10
        };white-space:pre-wrap;color:${theme.colors.text};padding:0px 16px;}img{max-width:100%}</style>
        </head><body>${data?.[selectedPostIndex]?.content as string}</body></html>`,
                  }}
                />
              </View>
            )}
            {!isLoading && !data?.length && (
              <View style={{marginTop: 40, flex: 1}} onStartShouldSetResponder={() => true}>
                {/* <WebView
                  onStartShouldSetResponder={() => true}
                  style={{flex: 1}}
                  source={{
                    html: `<html><head><meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>body{font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;font-size:14px;white-space:pre-wrap;color:${theme.colors.text};padding:0px 16px;}img{max-width:100%}</style>
        </head><body>${props.product.body_html}</body></html>`,
                  }}
                /> */}

                <View style={{alignItems: 'center'}}>
                  <Text>Chưa có mẫu bài đăng nào</Text>
                  <Button
                    size="sm"
                    buttonStyle={{borderRadius: 8}}
                    icon={{type: 'ionicon', name: 'add', color: theme.colors.white, size: 20}}
                    containerStyle={{marginTop: theme.spacing.m}}
                    titleStyle={{fontSize: theme.typography.base}}
                    onPress={() => handleEditProductPost()}>
                    Tạo mẫu bài đăng của bạn
                  </Button>

                  <Button
                    type="outline"
                    size="sm"
                    containerStyle={{marginTop: theme.spacing.xl}}
                    buttonStyle={{borderRadius: 8, borderColor: theme.colors.red}}
                    titleStyle={{fontSize: theme.typography.sm, color: theme.colors.red}}
                    icon={{
                      type: 'material-community',
                      name: 'robot-outline',
                      color: theme.colors.red,
                      size: 20,
                    }}
                    onPress={handleChatGPTPress}>
                    Tạo bài đăng tự động bằng AI
                  </Button>
                </View>
              </View>
            )}

            <View
              style={{
                shadowColor: '#000000',
                shadowOffset: {
                  width: -1,
                  height: -10,
                },
                shadowOpacity: 0.1,
                shadowRadius: 10,
                elevation: 1,
                // paddingTop: 20,
                backgroundColor: '#fff',
                marginBottom: -3,
                paddingLeft: 20,
              }}>
              <ScrollView
                horizontal
                style={{
                  flexGrow: 0,
                  marginBottom: theme.spacing.s,
                  marginTop: 'auto',
                  paddingTop: theme.spacing.s,
                }}
                showsHorizontalScrollIndicator={false}>
                {mediaItems.map((mediaItem, index) => (
                  <Pressable key={mediaItem.item.id} onPress={() => handleToggleCheckbox(mediaItem)} style={{position: 'relative', marginRight: 8}}>
                    <Image
                      style={{width: 100, height: 100, borderRadius: 8, borderWidth: 1, borderColor: 'rgba(0,0,0,.07)'}}
                      source={{uri: mediaItem.type === 'image' ? (mediaItem.item as ProductImage).src : (mediaItem.item as ProductVideo).thumb_url}}
                    />
                    {mediaItem.type === 'video' && (
                      <View style={{position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, zIndex: 1, backgroundColor: 'rgba(0,0,0,.5)', justifyContent: 'center', alignItems: 'center'}}>
                        <Ionicons name="play-circle-outline" color={theme.colors.white} size={20} />
                      </View>
                    )}
                    <View style={{position: 'absolute', top: -3, right: -10, zIndex: 2}}>
                      <CheckBox
                        checked={checkedMediaItemsArr[index]}
                        size={28}
                        onPress={() => handleToggleCheckbox(mediaItem)}
                        center
                        iconType="ionicon"
                        uncheckedIcon="square-outline"
                        checkedIcon="checkbox"
                        checkedColor="#008060"
                        uncheckedColor="#008060"
                        // containerStyle={{backgroundColor: 'transparent'}}
                        containerStyle={{margin: 0, padding: 0, borderRadius: 5}}
                        wrapperStyle={{padding: 0, paddingVertical: 0, paddingHorizontal: 0, marginLeft: 1}}
                      />
                    </View>
                  </Pressable>
                ))}
              </ScrollView>
            </View>

            <View style={{flexDirection: 'row', paddingVertical: 20, borderTopWidth: 1, borderTopColor: 'rgba(0,0,0,.05)', paddingHorizontal: 20}}>
              <SocialShareShortcut
                postContent={data?.[selectedPostIndex]?.content as string}
                bodyHtml={props.product.body_html}
                productId={props.productId}
                selectedMediaItems={selectedMediaItems}
                chatGPTContent={chatGPTClipboardContent}
                onCopyContent={handleCopyContentPress}
                postId={data?.[selectedPostIndex]?.id}
              />
            </View>
          </View>
        </View>
      )}
      {mode === 'edit' && <ProductPostEdit productId={props.productId} onClose={handleCloseEditProductPost} post={selectedPost} onSwitchToChatGPT={handleChatGPTPress} />}

      {mode === 'chatgpt' && <ChatGPTProductPost productId={props.productId} onClose={handleCloseEditProductPost} onContentCopy={handleChatGPTContentCopy} />}

      <Dialog isVisible={isDeleteDialogOpen} onRequestClose={handleCloseDeletePostDialog} onBackdropPress={handleCloseDeletePostDialog}>
        <Dialog.Title title="Xóa mẫu bài đăng?" titleStyle={{color: theme.colors.text}} />
        <Dialog.Actions>
          <Dialog.Button title={'Xóa'} titleStyle={{color: theme.colors.red}} onPress={handleDeletePostConfirm} loading={deletePostMutation.isLoading} />
          <Dialog.Button title={'Trở về'} titleStyle={{color: theme.colors.textLight}} onPress={handleCloseDeletePostDialog} />
        </Dialog.Actions>
      </Dialog>

      <Toast ref={toastRef as any} />
    </>
  );
};

const styles = StyleSheet.create({
  container: {backgroundColor: '#fff', height: (ScreenHeight * 80) / 100, paddingVertical: 20, paddingTop: 0, borderTopLeftRadius: 20, borderTopRightRadius: 20},
});

export default ProductPost;
