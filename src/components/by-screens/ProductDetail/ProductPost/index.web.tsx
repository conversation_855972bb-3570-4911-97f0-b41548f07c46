import React, {useCallback, useMemo, useRef, useState} from 'react';
import {ActivityIndicator, Image, Linking, Pressable, ScrollView, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {trackProductPostCopy, useGetProductPost} from '~utils/api/product';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Toast from 'react-native-toast-notifications';
import Clipboard from '@react-native-clipboard/clipboard';
import {ProductPostProps} from '.';
import theme from '~utils/config/themes/theme';
import {Button, CheckBox} from '@rneui/themed';
import isInZaloMiniApp from '~utils/helpers/isInZaloMiniApp';
import {ProductImage} from '~utils/types/product';
import {getDownloadableURL, handleImageDomain} from '~utils/helpers/image';
// @ts-ignore
import FacebookIcon from '~assets/facebook.png';
// @ts-ignore
import ZaloIcon from '~assets/zalo.png';
import {useIsMobile} from '~hooks';
import axios from '~utils/api/axios';
import ImageWithDownload from './ImageWithDownload';
import api from '~utils/helpers/zmp-sdk';

const ProductPost: React.FC<ProductPostProps> = props => {
  const toastRef = useRef<Toast>();
  const insets = useSafeAreaInsets();
  const {data, isLoading} = useGetProductPost(props.productId);
  const [selectedPostIndex, setSelectedPostIndex] = useState(0);
  const [isZaloMiniApp] = useState(isInZaloMiniApp());
  const [selectedImages, setSelectedImages] = useState(props.product.images.slice(0, 10));
  const [isShareLoading, setShareLoading] = useState(false);
  const isMobile = useIsMobile();

  const handleCopyCaption = useCallback(() => {
    if (data?.[selectedPostIndex]) {
      trackProductPostCopy(data?.[selectedPostIndex]?.id);
      Clipboard.setString(data?.[selectedPostIndex]?.content ?? '');
    }
    (toastRef.current as Toast).show('Đã sao chép!', {
      duration: 1000,
    });
  }, [data, selectedPostIndex]);

  const handleDownloadApp = useCallback(() => {
    Linking.openURL('https://ttsdropship.page.link/uEsh');
  }, []);

  const checkedImageArr = useMemo(() => {
    const selectedImageIds = selectedImages.map(img => img.id);
    return props.product.images.map(img => selectedImageIds.includes(img.id));
  }, [selectedImages, props.product.images]);

  const handleToggleCheckbox = useCallback(
    (image: ProductImage) => {
      const selectedImagesArr = selectedImages.map(img => img.id);
      let newSelectedImages = [];
      if (selectedImagesArr.includes(image.id)) {
        newSelectedImages = selectedImages.filter(img => img.id !== image.id);
        // setSelectedImages(prev => prev.filter(img => img.id !== image.id));
      } else {
        newSelectedImages = [...selectedImages, image];
        // setSelectedImages(prev => [...prev, image]);
      }

      setSelectedImages(newSelectedImages);
    },
    [selectedImages, props.productId],
  );

  const handleZaloPostFeed = async () => {
    setShareLoading(true);
    await api
      ?.openPostFeed({
        type: 'image',
        data: {
          imageUrl: handleImageDomain(selectedImages[0].src),
        },
      })
      .catch(() => {});
    setShareLoading(false);
  };
  const handleZaloShareSheet = async () => {
    setShareLoading(true);
    await api
      ?.openShareSheet({
        type: 'image',
        data: {
          imageUrls: selectedImages.map(img => handleImageDomain(img.src)),
        },
      })
      .catch(() => {});
    setShareLoading(false);
  };

  const handleSocialShare = useCallback(async () => {
    try {
      setShareLoading(true);

      const promises = selectedImages.map(async img => {
        try {
          const res = await axios.get(getDownloadableURL(handleImageDomain(img.src.replace('rs:fill:600', 'rs:fill:1000')), img.product_id, ''), {
            responseType: 'blob',
          });
          return res.data;
        } catch (error) {}
      });
      const blobs = (await Promise.all(promises)).filter(Boolean);
      const filesArray = blobs.map((blob, index) => new File([blob as Blob], `${props.productId}${index}.jpeg`), {
        type: 'image/jpeg',
        lastModified: new Date().getTime(),
      });
      const shareData = {
        files: filesArray,
      };
      if (navigator && 'share' in navigator && navigator.canShare(shareData)) {
        await navigator.share(shareData);
      }
    } catch (error) {
      console.log(error);
    }

    setShareLoading(false);
  }, [selectedImages]);

  return (
    <>
      <View style={[styles.container, {paddingBottom: insets.bottom + 20, position: 'relative'}]}>
        {isLoading && <ActivityIndicator />}
        <Pressable style={{right: 10, zIndex: 1, top: 0, position: 'absolute'}} onPress={props.onClose}>
          <Ionicons name="close-circle" color={theme.colors.textLight} size={45} />
        </Pressable>
        {/* <View style={{height: 20}} /> */}

        <View style={{flexDirection: 'row', flexWrap: 'wrap', paddingBottom: 10, marginBottom: theme.spacing.s}}>
          {data?.map((post, index) => (
            <Pressable onPress={() => setSelectedPostIndex(index)}>
              <View
                style={[
                  {backgroundColor: theme.colors.white, paddingVertical: 8, paddingHorizontal: 20, marginRight: 8, marginBottom: 8},
                  index === selectedPostIndex && {borderBottomWidth: 1, borderBottomColor: theme.colors.primary},
                ]}>
                <Text style={{color: index === selectedPostIndex ? theme.colors.primary : theme.colors.text}}>{`Mẫu ${index + 1}`}</Text>
              </View>
            </Pressable>
          ))}
        </View>
        {!isLoading && data?.[selectedPostIndex]?.content && (
          <div
            dangerouslySetInnerHTML={{__html: data?.[selectedPostIndex]?.content as string}}
            style={{flex: 1, flexShrink: 1, overflowY: 'auto', whiteSpace: 'pre-wrap', backgroundColor: theme.colors.gray10, padding: theme.spacing.m}}
          />
        )}
        {!isLoading && !data?.length && (
          <View style={{marginVertical: 40}}>
            <Text style={{textAlign: 'center'}}>Chưa có bài đăng mẫu nào</Text>
          </View>
        )}
        {!isZaloMiniApp && (
          <View style={[{backgroundColor: theme.colors.secondary, padding: theme.spacing.m, paddingVertical: theme.spacing.s}, theme.globalStyles.flexRow]}>
            <Text style={{flex: 1}}>Tải App để đăng bán dễ dàng hơn</Text>
            <Button color={'primary'} size="sm" buttonStyle={{marginLeft: 'auto', borderRadius: 12, paddingHorizontal: 12}} onPress={handleDownloadApp}>
              Tải App
            </Button>
          </View>
        )}
        <ScrollView
          horizontal
          style={{
            flexGrow: 0,
            marginBottom: theme.spacing.s,
            marginTop: 'auto',
            paddingTop: theme.spacing.s,
            paddingLeft: theme.spacing.m,
          }}
          showsHorizontalScrollIndicator={false}>
          {props.product.images.map((image, index) =>
            !isMobile ? (
              <ImageWithDownload image={image} key={image.id} fileName={`${props.product.title}-${index}.jpg`} />
            ) : (
              <Pressable key={image.id} onPress={() => handleToggleCheckbox(image)} style={{position: 'relative', marginRight: 8}}>
                <Image style={{width: 80, height: 80, borderRadius: 8, borderWidth: 1, borderColor: 'rgba(0,0,0,.07)'}} source={{uri: handleImageDomain(image.src)}} />
                <View style={{position: 'absolute', top: -3, right: -10, zIndex: 1}}>
                  <CheckBox
                    checked={checkedImageArr[index]}
                    size={28}
                    onPress={() => handleToggleCheckbox(image)}
                    center
                    iconType="ionicon"
                    uncheckedIcon="square-outline"
                    checkedIcon="checkbox"
                    checkedColor="#008060"
                    uncheckedColor="#008060"
                    // containerStyle={{backgroundColor: 'transparent'}}
                    containerStyle={{margin: 0, padding: 0, borderRadius: 5}}
                    wrapperStyle={{padding: 0, paddingVertical: 0, paddingHorizontal: 0, marginLeft: 1}}
                  />
                </View>
              </Pressable>
            ),
          )}
        </ScrollView>
        <View style={{flexDirection: 'row', paddingTop: 20, paddingHorizontal: theme.spacing.m, borderTopWidth: 1, borderTopColor: 'rgba(0,0,0,.05)'}}>
          {(data?.length as any) > 0 && (
            <TouchableOpacity onPress={handleCopyCaption} style={styles.postAction}>
              <Ionicons name="copy-outline" size={40} color={theme.colors.text} />
              <Text style={styles.socialText}>Sao chép</Text>
            </TouchableOpacity>
          )}
          {isMobile && !isZaloMiniApp && (
            <TouchableOpacity
              disabled={isShareLoading}
              onPress={() => handleSocialShare()}
              style={{alignItems: 'center', paddingHorizontal: 12, paddingVertical: 7, backgroundColor: theme.colors.gray10, borderRadius: 12}}>
              {!isShareLoading ? (
                <>
                  <View style={{flexDirection: 'row', marginTop: 8}}>
                    <Image source={FacebookIcon} style={{width: 26, height: 26, borderRadius: 100}} />
                    <Image source={ZaloIcon} style={{width: 26, height: 26, borderRadius: 100, marginLeft: 4}} />
                    <View style={{width: 26, height: 26, borderRadius: 100, marginLeft: 4, backgroundColor: '#999999', alignItems: 'center', justifyContent: 'center'}}>
                      <Ionicons name="ellipsis-horizontal" size={14} color="#fff" />
                    </View>
                  </View>
                  <Text style={[styles.socialText, {marginTop: 'auto'}]}>Đăng bán</Text>
                </>
              ) : (
                <>
                  <View style={{flex: 1, justifyContent: 'center'}}>
                    <ActivityIndicator size={'small'} style={{marginTop: theme.spacing.m}} />
                    <Text style={{marginTop: 'auto', fontSize: theme.typography.sm, color: theme.colors.textLight}}>Vui lòng đợi</Text>
                  </View>
                </>
              )}
            </TouchableOpacity>
          )}

          {isZaloMiniApp && (
            <>
              <TouchableOpacity style={styles.postAction} onPress={handleZaloPostFeed}>
                <Image source={ZaloIcon} style={{width: 30, height: 30, borderRadius: 100, marginLeft: 4}} />
                <Text style={styles.socialText}>Đăng lên nhật ký</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.postAction} onPress={handleZaloShareSheet}>
                <Image source={ZaloIcon} style={{width: 30, height: 30, borderRadius: 100, marginLeft: 4}} />
                <Text style={styles.socialText}>Gửi vào nhóm/bạn bè</Text>
              </TouchableOpacity>
            </>
          )}
        </View>
      </View>
      <Toast ref={toastRef as any} />
    </>
  );
};

const styles = StyleSheet.create({
  container: {backgroundColor: '#fff', height: 600, paddingVertical: 20, paddingTop: 8},
  socialText: {
    fontSize: 12,
    marginTop: 4,
  },
  postAction: {
    justifyContent: 'center',
    marginRight: 12,
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 7,
    backgroundColor: theme.colors.gray10,
    borderRadius: 12,
  },
});

export default ProductPost;
