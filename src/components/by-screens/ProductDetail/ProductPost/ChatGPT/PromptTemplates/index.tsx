import {Text} from '@rneui/themed';
import React, {useMemo} from 'react';
import {ScrollView, StyleSheet, TouchableWithoutFeedback, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import {ChatGPTMessage} from '~utils/types/chatgpt';

type PromptTemplatesProps = {
  promptTemplates: string[];
  onPress: (prompt: string) => void;
  lastMessage?: ChatGPTMessage;
  onStartNewConversation?: () => void;
};

const PromptTemplates: React.FC<PromptTemplatesProps> = props => {
  const isReachedLimitMessage = useMemo(() => {
    return props.lastMessage?.content.includes('Xin lỗi bạn, đoạn hội thoại này đã có quá nhiều nội dung');
  }, [props.lastMessage]);

  return (
    <View>
      <ScrollView style={styles.container} horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={{alignItems: 'flex-start'}}>
        {isReachedLimitMessage && (
          <TouchableWithoutFeedback onPress={props.onStartNewConversation}>
            <View style={styles.prompt}>
              <Text style={styles.text}>Bắt đầu hội thoại mới!</Text>
            </View>
          </TouchableWithoutFeedback>
        )}
        {!isReachedLimitMessage &&
          props.promptTemplates.map((template, index) => {
            return (
              <TouchableWithoutFeedback key={index} onPress={() => props.onPress(template)}>
                <View style={styles.prompt}>
                  <Text style={styles.text}>{template}</Text>
                </View>
              </TouchableWithoutFeedback>
            );
          })}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingTop: theme.spacing.s,
  },
  prompt: {
    // paddingVertical: 2,
    // paddingHorizontal: 10,
    padding: theme.spacing.s,
    marginLeft: theme.spacing.m,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: theme.colors.gray30,
    maxWidth: 150,
  },
  text: {
    color: theme.colors.textLight,
    fontSize: theme.typography.base,
  },
});
export default PromptTemplates;
