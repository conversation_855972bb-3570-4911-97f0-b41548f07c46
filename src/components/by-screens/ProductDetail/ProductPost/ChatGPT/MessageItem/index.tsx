// MessageItem component

import React, {useCallback, useState} from 'react';
import {View, Text, StyleSheet, TouchableWithoutFeedback} from 'react-native';
import {QuickImage} from '~components/shared/QuickImage';
import {ChatGPTMessage} from '~utils/types/chatgpt';
// @ts-ignore
import <PERSON><PERSON><PERSON> from '~assets/robot.png';
import theme from '~utils/config/themes/theme';
import {Button} from '@rneui/themed';
import ReadMoreText from '~components/shared/ReadMoreText';

type MessageItemProps = {
  message: ChatGPTMessage;
  userAvatarUrl?: string;
  onContentCopy?: (content: string) => void;
};

const MessageItem: React.FC<MessageItemProps> = props => {
  const [isCopySuccess, setIsCopySuccess] = useState(false);

  const handleCopy = useCallback(() => {
    if (props.onContentCopy) {
      props.onContentCopy(props.message.content);
    }

    setIsCopySuccess(true);
  }, [props.message.content, props.onContentCopy]);

  const renderViewMore = useCallback((onPress: () => void) => {
    return (
      <Text style={{color: theme.colors.primary, fontSize: theme.typography.sm}} onPress={onPress}>
        Xem thêm
      </Text>
    );
  }, []);

  const renderViewLess = useCallback((onPress: () => void) => {
    return (
      <Text style={{color: theme.colors.primary, fontSize: theme.typography.sm}} onPress={onPress}>
        Thu gọn
      </Text>
    );
  }, []);

  if (props.message?.role === 'system') {
    return null;
  }

  return (
    <TouchableWithoutFeedback>
      <View style={[styles.container, props.message.role === 'user' && styles.selfMessage, props.message.role === 'assistant' && styles.assistantMessage]}>
        <QuickImage source={props.message.role === 'assistant' ? RobotLogo : {uri: props.userAvatarUrl}} style={styles.avatar} />
        <View style={{flex: 1}}>
          <ReadMoreText numberOfLines={props.message.role === 'user' ? 5 : 40} text={props.message.content} textStyle={styles.text} renderViewLess={renderViewLess} renderViewMore={renderViewMore} />

          {props.message.role === 'assistant' && (
            <Button
              containerStyle={{alignSelf: 'flex-end', marginTop: theme.spacing.s}}
              buttonStyle={[styles.copyButton, isCopySuccess && {backgroundColor: theme.colors.gray10}]}
              titleStyle={[{color: theme.colors.textLight, fontSize: theme.typography.sm}]}
              size="sm"
              icon={{
                name: 'copy-outline',
                color: theme.colors.textLight,
                size: 12,
                type: 'ionicon',
              }}
              onPress={handleCopy}>
              {isCopySuccess ? 'Đã sao chép' : 'Sao chép'}
            </Button>
          )}
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    // marginBottom: theme.spacing.m,
    padding: theme.spacing.s,
    paddingVertical: theme.spacing.m,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray30,
  },
  selfMessage: {
    backgroundColor: theme.colors.white,
  },
  assistantMessage: {
    backgroundColor: theme.colors.gray10,
  },
  avatar: {
    width: 30,
    height: 30,
    borderRadius: 100,
    marginRight: theme.spacing.m,
  },
  text: {
    color: theme.colors.textLight,
    flex: 1,
    fontSize: theme.typography.base,
  },

  copyButton: {
    backgroundColor: theme.colors.white,
    padding: theme.spacing.s,
    paddingVertical: theme.spacing.s - 2,
    borderRadius: 100,
  },

  newConversationButton: {
    backgroundColor: theme.colors.white,
    padding: theme.spacing.s,
    paddingVertical: theme.spacing.s - 2,
    borderRadius: 100,
    borderColor: theme.colors.textLight,
  },
});

export default MessageItem;
