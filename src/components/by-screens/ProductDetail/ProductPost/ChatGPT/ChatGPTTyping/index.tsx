import React, {useEffect, useRef} from 'react';

import {Animated, StyleSheet} from 'react-native';
import theme from '~utils/config/themes/theme';
import {QuickImage} from '~components/shared/QuickImage';
// @ts-ignore
import RobotLoading from '~assets/robot-loading.png';
import {DotTypingAnimation} from 'react-native-dot-typing';

const ChatGPTTypping = () => {
  // slide from left entrance animation
  const postionX = useRef(new Animated.Value(-100)).current;

  useEffect(() => {
    slideIn();
  }, []);

  const slideIn = () => {
    Animated.timing(postionX, {
      toValue: 0,
      duration: 250,
      useNativeDriver: true,
    }).start();
  };

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{translateX: postionX}],
        },
      ]}>
      <QuickImage
        source={RobotLoading}
        style={{
          width: 30,
          height: 30,
          borderRadius: 100,
          marginRight: theme.spacing.m,
        }}
      />
      <DotTypingAnimation dotRadius={3} dotAmplitude={1} dotMargin={8} dotX={18} dotY={0} dotColor={theme.colors.gray30} />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    // marginBottom: theme.spacing.m,
    padding: theme.spacing.m,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray20,
    alignItems: 'center',
    overflow: 'hidden',
  },
});

export default ChatGPTTypping;
