import React from 'react';
import {Image, Pressable} from 'react-native';
import {handleImageDomain} from '~utils/helpers/image';
import {ProductImage} from '~utils/types/product';
import './img-with-download.css';
import Ionicons from 'react-native-vector-icons/Ionicons';
import theme from '~utils/config/themes/theme';
import instance from '~utils/api/axios';
import {getDownloadableURL} from '~utils/helpers/image';

type ImageWithDownloadProps = {
  image: ProductImage;
  fileName: string;
};

const ImageWithDownload: React.FC<ImageWithDownloadProps> = props => {
  const handlePress = async () => {
    const response = await instance.get(getDownloadableURL(props.image.src.replace('rs:fill:600', 'rs:fill:1000'), props.image.product_id, ''), {
      responseType: 'blob',
    });

    // create file link in browser's memory
    const href = URL.createObjectURL(response.data);

    // create "a" HTML element with href to file & click
    const link = document.createElement('a');
    link.href = href;
    link.setAttribute('download', props.fileName); //or any other extension
    document.body.appendChild(link);
    link.click();

    // clean up "a" element & remove ObjectURL
    document.body.removeChild(link);
    URL.revokeObjectURL(href);
  };
  return (
    <Pressable onPress={handlePress}>
      <div className="img-with-download">
        <Image style={{width: 80, height: 80, borderRadius: 8, borderWidth: 1, borderColor: 'rgba(0,0,0,.07)'}} source={{uri: handleImageDomain(props.image.src)}} />

        <div className="img-with-download__overlay">
          <Ionicons name="download-outline" size={30} color={theme.colors.white} />
        </div>
      </div>
    </Pressable>
  );
};

export default ImageWithDownload;
