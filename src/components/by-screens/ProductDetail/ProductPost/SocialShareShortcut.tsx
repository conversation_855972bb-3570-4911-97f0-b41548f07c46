import React, {useCallback, useEffect, useRef, useState} from 'react';
import {ActivityIndicator, Image, StyleSheet, TouchableOpacity, View} from 'react-native';

// @ts-ignore
import FacebookIcon from '~assets/facebook.png';
// @ts-ignore
import ZaloIcon from '~assets/zalo.png';

import Ionicons from 'react-native-vector-icons/Ionicons';
import {ProductImage, ProductVideo} from '~utils/types/product';
import RNFetchBlob from 'rn-fetch-blob';
import Share from 'react-native-share';
import {getDownloadableURL, handleImageDomain} from '~utils/helpers/image';
import * as Progress from 'react-native-progress';
import Toast from 'react-native-toast-notifications';
import downloadImage, {downloadVideo} from '~utils/helpers/downloadImage';
import {FirebaseAnalyticsTypes} from '@react-native-firebase/analytics';
import googleAnalytics from '~utils/helpers/analytics';
import {Text} from '@rneui/themed';
import theme from '~utils/config/themes/theme';
import * as Sentry from '@sentry/react-native';
import {checkAndRequestSavePhotoPermission} from '~utils/helpers/downloadImagePermissions';

type SocialShareShortcutProps = {
  productId: string | number;
  postContent: string;
  selectedMediaItems: {type: string; item: ProductImage | ProductVideo}[];
  postId?: string;
  bodyHtml?: string;
  onCopyContent: () => void;
  chatGPTContent?: string;
};

type MediaBlob = {
  uri: string;
  type: string;
  fileName: string;
};

type MediaFileItem = {
  type: 'image' | 'video' | string;
  url: string;
};

const getMimeType = (fileName: string): string => {
  const extension = fileName.split('.').pop()?.toLowerCase();
  switch (extension) {
    // Images
    case 'png':
      return 'image/png';
    case 'gif':
      return 'image/gif';
    case 'webp':
      return 'image/webp';
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    // Videos
    case 'mp4':
      return 'video/mp4';
    case 'mov':
      return 'video/quicktime';
    case 'webm':
      return 'video/webm';
    default:
      return 'application/octet-stream';
  }
};

const getMediaItemBlobs = async (mediaItems: MediaFileItem[]): Promise<MediaBlob[]> => {
  try {
    const blobs = await Promise.all(
      mediaItems.map(async (item, index) => {
        const extension = item.type === 'video' ? 'mp4' : item.url.split('.').pop()?.toLowerCase() ?? 'jpg';

        const fileName = `temp_media_${Date.now()}_${index}.${extension}`;
        const filePath = `${RNFetchBlob.fs.dirs.CacheDir}/${fileName}`;

        if (item.url.startsWith('http')) {
          await RNFetchBlob.config({
            fileCache: true,
            path: filePath,
          }).fetch('GET', item.url);
        } else if (item.url.startsWith('file://')) {
          const sourcePath = item.url.replace('file://', '');
          await RNFetchBlob.fs.cp(sourcePath, filePath);
        }

        return {
          uri: `file://${filePath}`,
          type: getMimeType(fileName),
          fileName: fileName,
        };
      }),
    );

    return blobs;
  } catch (error) {
    console.error('Error creating media blobs:', error);
    return [];
  }
};

const SocialShareShortcut: React.FC<SocialShareShortcutProps> = props => {
  const toastRef = useRef<Toast>();
  const [isDownloadingMediaItems, setDownloadingMediaItems] = useState(false);
  const [dowloadingMediaItemsProgress, setDownloadingMediaItemsProgress] = useState(0);
  const [isShareLoading, setShareLoading] = useState(false);

  const handleCopyCaption = useCallback(() => {
    props.onCopyContent();
  }, [props.onCopyContent]);

  const handleSocialShare = useCallback(
    async (socialName?: string) => {
      if (!props.chatGPTContent) {
        handleCopyCaption();
      }
      // analytics

      const shareParams: FirebaseAnalyticsTypes.ShareEventParameters = {
        content_type: 'product',
        item_id: props.postId ? `${props.postId}/${props.productId}` : `${props.productId}`,
        method: socialName || 'any',
      };

      googleAnalytics.logShare(shareParams);

      try {
        setShareLoading(true);

        const shareMessage = props.chatGPTContent || props.postContent;

        switch (socialName) {
          default:
            try {
              const itemToShare: MediaFileItem[] = props.selectedMediaItems.map(item => ({
                url: item.type === 'image' ? (item.item as ProductImage).src : (item.item as ProductVideo).url,
                type: item.type,
              }));

              const mediaBlobs = await getMediaItemBlobs(itemToShare);

              await Share.open({
                message: shareMessage,
                type: '*/*',
                ...(mediaBlobs.length > 1 ? {urls: mediaBlobs.map(blob => blob.uri)} : {url: mediaBlobs[0]?.uri}),
              });

              // Clean up temporary files
              await Promise.all(mediaBlobs.map(blob => RNFetchBlob.fs.unlink(blob.uri.replace('file://', ''))));
            } catch (error) {
              console.error('Error sharing:', error);
            }
            break;
        }
      } catch (error) {
        Sentry.captureException(error);
      }

      setShareLoading(false);
    },
    [props.postContent, props.onCopyContent, props.chatGPTContent, props.selectedMediaItems, props.productId, props.postId],
  );

  const handleDownloadMediaItems = useCallback(async () => {
    googleAnalytics.logEvent('save_product_images', {
      product_id: props.productId,
    });
    setDownloadingMediaItems(true);
    const canContinue = await checkAndRequestSavePhotoPermission();

    if (!canContinue) {
      setDownloadingMediaItems(false);
      return;
    }

    let done = 0;

    for (let item of props.selectedMediaItems) {
      try {
        if (item.type === 'image') {
          await downloadImage(getDownloadableURL(handleImageDomain((item.item as ProductImage).src.replace('rs:fill:600', 'rs:fill:1000')), String(props.productId), ''));
        } else if (item.type === 'video') {
          await downloadVideo((item.item as ProductVideo).url);
        }
      } catch (error) {
        Sentry.captureMessage('Mo hop thoai dang ban that bai');
      }
      done++;
      setDownloadingMediaItemsProgress(done / props.selectedMediaItems.length);
      if (done === props.selectedMediaItems.length) {
        setDownloadingMediaItems(false);
        if (toastRef.current) {
          (toastRef.current as Toast).show('Đã lưu về máy', {
            duration: 1000,
          });
        }
      }
    }
  }, [props.selectedMediaItems, props.productId]);

  return (
    <View style={{width: '100%'}}>
      <View style={styles.container}>
        {
          <>
            <TouchableOpacity
              onPress={handleCopyCaption}
              style={{
                justifyContent: 'center',
                marginRight: 12,
                alignItems: 'center',
                paddingHorizontal: 12,
                paddingVertical: 7,
                backgroundColor: theme.colors.gray10,
                borderRadius: 12,
              }}>
              <Ionicons name="copy-outline" size={40} color={theme.colors.text} />
              <Text style={styles.socialText}>Sao chép</Text>
            </TouchableOpacity>
            {/* )} */}
            <TouchableOpacity
              disabled={isDownloadingMediaItems}
              onPress={handleDownloadMediaItems}
              style={{
                justifyContent: 'center',
                marginRight: 12,
                alignItems: 'center',
                paddingHorizontal: 12,
                paddingVertical: 7,
                backgroundColor: isDownloadingMediaItems ? 'rgba(0,0,0,.1)' : theme.colors.gray10,
                borderRadius: 12,
              }}>
              {isDownloadingMediaItems ? (
                <Progress.Circle progress={dowloadingMediaItemsProgress} size={50} showsText color="#008060" borderWidth={4} textStyle={{fontSize: 16, fontWeight: 'bold'}} />
              ) : (
                <>
                  <Ionicons name="download-outline" size={40} color={theme.colors.text} />
                  <Text style={styles.socialText}>Lưu ảnh</Text>
                </>
              )}
            </TouchableOpacity>
            <TouchableOpacity
              disabled={isShareLoading}
              onPress={() => handleSocialShare()}
              style={{alignItems: 'center', paddingHorizontal: 12, paddingVertical: 7, backgroundColor: theme.colors.gray10, borderRadius: 12}}>
              {!isShareLoading ? (
                <>
                  <View style={{flexDirection: 'row', marginTop: 8}}>
                    <Image source={FacebookIcon} style={{width: 26, height: 26, borderRadius: 100}} />
                    <Image source={ZaloIcon} style={{width: 26, height: 26, borderRadius: 100, marginLeft: 4}} />
                    <View style={{width: 26, height: 26, borderRadius: 100, marginLeft: 4, backgroundColor: '#999999', alignItems: 'center', justifyContent: 'center'}}>
                      <Ionicons name="ellipsis-horizontal" size={14} color="#fff" />
                    </View>
                  </View>
                  <Text style={[styles.socialText, {marginTop: 'auto'}]}>Đăng bán</Text>
                </>
              ) : (
                <>
                  <View style={{flex: 1, justifyContent: 'center'}}>
                    <ActivityIndicator size={'small'} style={{marginTop: theme.spacing.m}} />
                    <Text style={{marginTop: 'auto', fontSize: theme.typography.sm, color: theme.colors.textLight}}>Vui lòng đợi</Text>
                  </View>
                </>
              )}
            </TouchableOpacity>
          </>
        }
      </View>

      <Toast ref={toastRef as any} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingBottom: theme.spacing.s,
  },
  socialLogo: {
    width: 40,
    height: 40,
    borderRadius: 12,
  },
  socialItem: {
    alignItems: 'center',
    marginRight: 20,
    justifyContent: 'center',
  },
  socialText: {
    fontSize: 12,
    marginTop: 4,
  },
});

export default SocialShareShortcut;
