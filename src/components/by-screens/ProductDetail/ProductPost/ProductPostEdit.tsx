import {Button, Input, Text} from '@rneui/themed';
import React, {useCallback, useState} from 'react';
import {Dimensions, Pressable, StyleSheet, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useKeyboard} from '~hooks';
import {useDisableAvoidSoftInput} from '~hooks/useAvoidSoftInput';
import {ProductPost} from '~utils/types/product';
import theme from '~utils/config/themes/theme';
import {useCreateProductPost, useUpdateProductPost} from '~utils/api/product';

type ProductPostEditProps = {
  post?: ProductPost | null;
  onClose: (isNewPostCreated?: boolean) => void;
  productId: string;
  onSwitchToChatGPT: () => void;
};

const WINDOW_HEIGHT = Dimensions.get('window').height;

const ProductPostEdit: React.FC<ProductPostEditProps> = props => {
  useDisableAvoidSoftInput();
  const insets = useSafeAreaInsets();
  const {keyboardHeight} = useKeyboard();
  const [postContent, setPostContent] = useState(props.post?.content);
  const mutation = useCreateProductPost();
  const updateMutation = useUpdateProductPost();

  const handleSubmit = useCallback(async () => {
    if (props.post?.privacy === 'private') {
      await updateMutation.mutateAsync({
        productId: props.productId,
        postId: props.post.id,
        post: {
          title: props.post.title,
          privacy: props.post.privacy,
          content: postContent ?? '',
        },
      });
      props.onClose();
    } else {
      await mutation.mutateAsync({
        post: {
          content: postContent ?? '',
          privacy: 'private',
          title: 'Mẫu của tôi',
        },
        productId: props.productId,
      });
      props.onClose(true);
    }
  }, [props.productId, postContent, props.post]);

  return (
    <View style={{backgroundColor: 'white', height: WINDOW_HEIGHT - (keyboardHeight || 350), paddingTop: insets.top}}>
      <View style={styles.header}>
        <Button type="clear" icon={{type: 'ionicon', name: 'chevron-back', size: 20, color: theme.colors.textLight}} titleStyle={styles.backTitle} onPress={() => props.onClose()}>
          Quay lại
        </Button>

        <Pressable onPress={props.onSwitchToChatGPT}>
          <Text style={{marginRight: 20, fontSize: theme.typography.base, color: theme.colors.blue}}>Sử dụng Chat GPT</Text>
        </Pressable>
      </View>
      <Input
        value={postContent}
        onChangeText={setPostContent}
        multiline
        inputContainerStyle={{flex: 1, alignItems: 'flex-start'}}
        inputStyle={{flex: 1, fontSize: theme.typography.base}}
        containerStyle={{flex: 1}}
        autoFocus
      />

      <Button
        size="sm"
        containerStyle={{paddingHorizontal: 12}}
        buttonStyle={{borderRadius: 8}}
        onPress={handleSubmit}
        loading={mutation.isLoading || updateMutation.isLoading}
        disabled={mutation.isLoading || updateMutation.isLoading}>
        Lưu lại
      </Button>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    ...theme.globalStyles.flexRow,
    paddingTop: 20,
    justifyContent: 'space-between',
  },
  backTitle: {
    fontSize: theme.typography.md,
    color: theme.colors.textLight,
  },
});

export default ProductPostEdit;
