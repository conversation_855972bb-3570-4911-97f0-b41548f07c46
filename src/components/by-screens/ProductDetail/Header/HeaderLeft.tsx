import {useNavigation} from '@react-navigation/native';
import React from 'react';
import {Platform, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {Pressable} from 'react-native-gesture-handler';

type HeaderLeftProps = {
  reverse: boolean;
};

const HeaderLeft: React.FC<HeaderLeftProps> = props => {
  const navigation = useNavigation();
  return (
    <Pressable style={[{paddingRight: 20, paddingVertical: 4, marginLeft: -10, paddingLeft: 10}, Platform.OS === 'web' && {paddingLeft: 20}]} onPress={navigation.goBack} accessibilityLabel="Trở về">
      <View
        style={[
          {width: 35, height: 35, backgroundColor: 'rgba(0,0,0,.4)', borderRadius: 100, justifyContent: 'center', alignItems: 'center', paddingLeft: 4},
          props.reverse && {backgroundColor: 'transparent'},
        ]}>
        <Ionicons name="arrow-back" color={props.reverse ? theme.colors.textLight : theme.colors.white} size={30} />
      </View>
    </Pressable>
  );
};

export default HeaderLeft;
