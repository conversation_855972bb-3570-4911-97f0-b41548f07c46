import React, {useCallback} from 'react';
import {Platform, Share, StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useAuthActions, useNavigation} from '~hooks';
import {Menu, MenuOption, MenuOptions, MenuTrigger, renderers} from 'react-native-popup-menu';
import Product from '~utils/types/product';
import {Pressable} from 'react-native-gesture-handler';

const {Popover} = renderers;

type HeaderRightProps = {
  reverse: boolean;
  product?: Product;
};

const HeaderRight: React.FC<HeaderRightProps> = props => {
  const navigation = useNavigation();
  const {isLoggedIn} = useAuthActions();

  const handleHomePress = useCallback(() => {
    navigation.navigate('MainTabs', {screen: 'Home'});
  }, []);

  const handleCartPress = useCallback(() => {
    return navigation.navigate('Cart');
  }, []);

  const handleShare = () => {
    if (props.product) {
      const link = `https://dropship.thitruongsi.com/product/${props.product?.id_number}`;
      Share.share({
        message: Platform.OS === 'android' ? link : props.product?.title,
        url: link,
        title: `${props.product?.title} `,
      });
    }
  };

  const iconColor = props.reverse ? theme.colors.textLight : theme.colors.white;

  return (
    <View style={[styles.container, Platform.OS === 'web' && {marginRight: 20}]}>
      {isLoggedIn && (
        <Pressable style={[styles.iconContainer, {marginRight: theme.spacing.m}, props.reverse && {backgroundColor: 'transparent'}]} onPress={handleCartPress}>
          <Ionicons name="cart-outline" color={iconColor} size={28} />
        </Pressable>
      )}
      <Menu renderer={Popover} rendererProps={{placement: 'bottom', anchorStyle: {backgroundColor: '#f5f5f5'}}}>
        <MenuTrigger customStyles={{TriggerTouchableComponent: Pressable}}>
          <View style={[styles.iconContainer, props.reverse && {backgroundColor: 'transparent'}]} accessible accessibilityLabel="Mở menu">
            <Ionicons name="ellipsis-vertical" size={20} color={iconColor} style={{padding: 4}} />
          </View>
        </MenuTrigger>
        <MenuOptions
          customStyles={{
            optionText: {color: '#333'},
            optionsContainer: {
              backgroundColor: '#f5f5f5',
              borderRadius: 8,
            },
            optionWrapper: {
              padding: 12,
              borderBottomColor: 'rgba(0,0,0,.1)',
              borderBottomWidth: 1,
            },
          }}>
          <MenuOption text="Về trang chủ" onSelect={handleHomePress} />
          <MenuOption text="Chia sẻ" onSelect={handleShare} />
        </MenuOptions>
      </Menu>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...theme.globalStyles.flexRow,
  },
  iconContainer: {
    width: 35,
    height: 35,
    backgroundColor: 'rgba(0,0,0,.4)',
    borderRadius: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default HeaderRight;
