import {Text} from '@rneui/base';

import React, {useCallback, useMemo} from 'react';
import {Platform, Pressable, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import ReviewItem from './ReviewItem';
import {AirbnbRating, Button} from '@rneui/themed';
import {useNavigation} from '~hooks';
import {useGetReviews} from '~utils/api/review';
import Product from '~utils/types/product';
// @ts-ignore
import AirBnBStar from '~assets/airbnbstar.png';

type ReviewsProps = {
  product: Product;
};

const Reviews: React.FC<ReviewsProps> = props => {
  const navigation = useNavigation();
  const {data, isLoading} = useGetReviews({product_id: props.product.id, limit: 2});

  const isEmptyReviews = useMemo(() => {
    return !data?.rating_count;
  }, [data?.rating_count]);

  const handleGoToAllReviews = useCallback(() => {
    if (!isEmptyReviews) {
      navigation.navigate('AllReviews', {
        productId: props.product.id,
      });
    }
  }, [props.product?.id, isEmptyReviews]);

  return (
    <View style={{backgroundColor: theme.colors.white, marginTop: theme.spacing.s}}>
      <Pressable onPress={handleGoToAllReviews} style={[{paddingHorizontal: theme.spacing.m, paddingTop: theme.spacing.s, alignItems: 'flex-start', flexDirection: 'row'}]}>
        <View>
          <Text style={{fontSize: theme.typography.lg, fontWeight: '500'}}>Đánh giá sản phẩm</Text>
          {!isEmptyReviews ? (
            <View style={{flexDirection: 'row', marginTop: theme.spacing.s, alignItems: 'center'}}>
              <AirbnbRating count={1} defaultRating={data?.rating_avg} starImage={Platform.OS === 'web' ? AirBnBStar : undefined} size={16} showRating={false} />
              <View style={{flexDirection: 'row', alignItems: 'center', marginLeft: theme.spacing.s}}>
                <Text style={{fontSize: theme.typography.md, fontWeight: '500'}}>{data?.rating_avg}</Text>
                <Text style={{fontSize: theme.typography.sm, color: theme.colors.textLight}}> /5</Text>
                <Text style={{fontSize: theme.typography.sm, color: theme.colors.textLight}}> ({data?.rating_count} đánh giá)</Text>
              </View>
            </View>
          ) : null}
        </View>

        {!isEmptyReviews && (
          <Button
            onPress={handleGoToAllReviews}
            size="sm"
            type="clear"
            titleStyle={{fontSize: theme.typography.base, color: theme.colors.text}}
            iconRight
            icon={{
              type: 'ionicon',
              name: 'chevron-forward',
              size: 12,
              color: theme.colors.text,
            }}
            buttonStyle={{paddingTop: 0}}
            containerStyle={{marginLeft: 'auto'}}>
            Xem tất cả
          </Button>
        )}
      </Pressable>

      <View style={{paddingHorizontal: theme.spacing.m, paddingBottom: theme.spacing.m}}>
        {!isLoading && isEmptyReviews ? (
          <View style={{marginTop: theme.spacing.l, alignItems: 'center'}}>
            <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight}}>Chưa có đánh giá nào</Text>
          </View>
        ) : null}
        {data?.reviews.map((review, index) => (
          <ReviewItem
            bottomDivider={index !== data.reviews.length - 1}
            key={review._id}
            user={review.user}
            createdAt={review.created_at}
            rateLineItem={{
              ...review,
              quality_rate: review.rating,
            }}
            label={review.label}
          />
        ))}
      </View>
    </View>
  );
};

export default Reviews;
