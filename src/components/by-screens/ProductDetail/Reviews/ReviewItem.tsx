import {Text} from '@rneui/themed';
import React, {useCallback, useMemo} from 'react';
import {Image, Pressable, StyleSheet, TouchableOpacity, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import {handleImageDomain} from '~utils/helpers/image';
import {QuickImage} from '~components/shared/QuickImage';
import {RateLineItem} from '~utils/api/review';
import ee, {EventNames} from '~utils/events';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {formatDate} from '~utils/helpers/common';
import {ScreenHeight, ScreenWidth} from '@rneui/base';
import IoniconsRating from '~components/shared/IoniconsRating';

type RateUser = {
  name: string;
  avatar: string;
};

type ReviewItemProps = {
  rateLineItem: RateLineItem;
  anonymous?: boolean;
  createdAt?: string;
  user?: RateUser;
  bottomDivider?: boolean;
  label?: string;

  showProduct?: boolean;
  onProductPress?: (productId: number) => void;
};

const ReviewItem: React.FC<ReviewItemProps> = props => {
  const handleAttachmentPress = useCallback((src: string, type: 'image' | 'video') => {
    ee.emit(EventNames.OpenImageViewer, {
      newImages: [
        {
          url: handleImageDomain(src),
          width: type === 'video' ? ScreenWidth : undefined,
          height: type === 'video' ? ScreenHeight : undefined,
          props: {
            type: type,
          },
        },
      ],
      newIndex: 0,
    });
  }, []);

  const isDefaultVariant = useMemo(() => {
    return ['default', 'default title'].includes(props.rateLineItem.variant_title?.toLowerCase() ?? '');
  }, [props.rateLineItem.variant_title]);

  return (
    <View style={[styles.container, props.bottomDivider && styles.bottomDivider]}>
      {props.user ? (
        <View style={{flexDirection: 'row'}}>
          <Image
            source={{
              uri: props.user?.avatar,
            }}
            style={styles.userAvatar}
          />

          <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight}}>{props.user?.name}</Text>
        </View>
      ) : null}
      <View style={{alignSelf: 'flex-start', marginTop: theme.spacing.xs, alignItems: 'flex-start'}}>
        {!isDefaultVariant ? (
          <Text style={{fontSize: theme.typography.sm, color: theme.colors.textLight}}>
            {['default', 'default title'].includes(props.rateLineItem.variant_title?.toLowerCase() ?? '') ? '' : props.rateLineItem.variant_title}
          </Text>
        ) : null}
        <View style={[theme.globalStyles.flexRow]}>
          <IoniconsRating value={props.rateLineItem.quality_rate} size={13} />
          <Text style={{fontSize: theme.typography.sm, marginLeft: theme.spacing.s, color: theme.colors.textLight}}>{formatDate(props.createdAt)}</Text>
          {props.label ? (
            <Text style={{flex: 1, fontSize: theme.typography.sm, marginLeft: theme.spacing.s, color: theme.colors.textLight}} numberOfLines={1}>
              {props.label}
            </Text>
          ) : null}
        </View>
        {props.rateLineItem.comment ? <Text style={styles.commentText}>{props.rateLineItem.comment}</Text> : null}
        <View style={styles.attachmentContainers}>
          {props.rateLineItem.videos.map(attachment => (
            <TouchableOpacity onPress={() => handleAttachmentPress(attachment.url, 'video')} style={styles.attachmentContainer} key={attachment._id}>
              <QuickImage
                style={styles.attachment}
                source={{
                  uri: handleImageDomain(attachment.video_thumb_url!),
                }}
              />

              <View style={styles.durationContainer}>
                <Ionicons name="videocam" size={14} color={theme.colors.white} />
                {attachment.duration ? (
                  <Text style={styles.duration}>
                    {Math.floor((attachment.duration % 3600) / 60)
                      .toString()
                      .padStart(2, '0')}
                    :{Math.floor((attachment.duration % 3600) % 60)}
                  </Text>
                ) : null}
              </View>
            </TouchableOpacity>
          ))}
          {props.rateLineItem.photos.map(attachment => (
            <TouchableOpacity onPress={() => handleAttachmentPress(attachment.url, 'image')} style={styles.attachmentContainer} key={attachment._id}>
              <QuickImage
                style={styles.attachment}
                source={{
                  uri: handleImageDomain(attachment.url),
                }}
              />
            </TouchableOpacity>
          ))}
          {props.rateLineItem?.replies?.length > 0 ? (
            <View style={styles.supplierReplyContainer}>
              <Text style={styles.supplierReplyTitle}>Phản hồi nhà cung cấp</Text>
              {props.rateLineItem.replies.map(reply => (
                <Text key={reply._id} style={styles.supplierReplyContent}>
                  {reply.content}
                </Text>
              ))}
            </View>
          ) : null}
        </View>
      </View>
      {props.showProduct && (
        <Pressable style={styles.productContainer} onPress={() => props.onProductPress?.(props.rateLineItem.product_id_number!)}>
          <Image
            style={{width: 40, height: 40, marginRight: theme.spacing.s}}
            source={{
              uri: handleImageDomain(props.rateLineItem.product_img),
            }}
          />
          <Text style={{flex: 1, fontSize: theme.typography.base}} numberOfLines={1}>
            {props.rateLineItem.product_title}
          </Text>
        </Pressable>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  bottomDivider: {
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray20,
  },
  container: {
    marginTop: theme.spacing.m,
    paddingBottom: theme.spacing.m,
  },
  attachment: {
    width: 60,
    height: 60,
    borderWidth: 1,
    borderColor: theme.colors.gray20,
    borderRadius: 4,
  },
  userAvatar: {
    width: 20,
    height: 20,
    borderRadius: 100,
    marginRight: theme.spacing.s,
  },
  attachmentContainers: {
    marginTop: theme.spacing.xs,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  attachmentContainer: {
    marginRight: theme.spacing.s,
  },
  durationContainer: {
    paddingHorizontal: theme.spacing.xs,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    position: 'absolute',
    bottom: 0,
    right: 1,
    left: 1,
    flexDirection: 'row',
  },
  duration: {
    fontSize: theme.typography.xs,
    color: theme.colors.white,
    marginLeft: theme.spacing.xs,
  },
  commentText: {
    marginVertical: theme.spacing.s,
    fontSize: theme.typography.base,
  },
  productContainer: {
    marginTop: theme.spacing.s,
    backgroundColor: theme.colors.gray10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  supplierReplyTitle: {
    fontSize: theme.typography.base,
    color: theme.colors.primary,
  },
  supplierReplyContainer: {
    backgroundColor: theme.colors.gray10,
    padding: theme.spacing.s,
    flex: 1,
  },
  supplierReplyContent: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
  },
});

export default ReviewItem;
