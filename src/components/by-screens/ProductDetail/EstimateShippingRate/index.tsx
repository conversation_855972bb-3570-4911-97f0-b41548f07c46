import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {View, Modal, StyleSheet, Pressable, ActivityIndicator} from 'react-native';
import LocationTabView from '~components/shared/LocationTabView';
import Address from '~utils/types/address';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {Button} from '@rneui/base';
import {getShippingRate} from '~utils/api/order';
import Product from '~utils/types/product';
import {useUpdateEffect} from '~hooks';
import {ShippingRate} from '~utils/types/order';
import ShippingRateItem from '~components/by-screens/OrderConfirm/ShippingRate';
import {ASYNC_STORAGE_KEYS} from '~utils/config';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {Text} from '@rneui/themed';
import theme from '~utils/config/themes/theme';
import AsyncStorage from '~utils/helpers/storage';

type EstimateShippingRateProps = {
  product: Product;
};

const EstimateShippingRate: React.FC<EstimateShippingRateProps> = props => {
  const [address, setAddress] = useState<Partial<Address>>({});
  const [modalVisible, setModalVisible] = useState(false);
  const [shippingRateLoading, setShippingRateLoading] = useState(false);
  const [shippingRates, setShippingRates] = useState<ShippingRate[]>([]);
  const insets = useSafeAreaInsets();

  useUpdateEffect(() => {
    if (!modalVisible) {
      const isAddressComplete = address.city && address.province && address.ward;
      if (isAddressComplete) {
        handleGetShippingRate();
      }
    }
  }, [modalVisible]);

  const addressArr = useMemo(() => {
    return [address.ward, address.province, address.city].filter(Boolean);
  }, [address]);

  const handleGetShippingRate = useCallback(async () => {
    if (!addressArr.length) {
      handleSelectAddress();
      return;
    }
    setShippingRateLoading(true);
    try {
      const res = await getShippingRate({
        destination: {
          city: address.city,
          province: address.province,
          ward: address.ward,
          address1: '1',
        },
        items: [{quantity: 1, product_id: props.product.id, variant_id: props.product.variants[0].id, dropship: true}],
        origin: {
          shop_id: props.product.shop_id,
        },
      });
      if (res) {
        setShippingRates(res.data.rates);
      }
    } catch (error) {}

    setShippingRateLoading(false);
  }, [addressArr, props.product, address]);

  const handleSelectDone = useCallback((add: Partial<Address>) => {
    setAddress(add);
    setModalVisible(false);
    // AsyncStorage.setItem(ASYNC_STORAGE_KEYS.SHIPPING_ADDRESS, JSON.stringify(add));
  }, []);

  const handleSelectAddress = useCallback(() => {
    setModalVisible(true);
  }, []);

  return (
    <>
      <View style={styles.container}>
        {/* <View style={{flexDirection: 'row', alignItems: 'flex-start', marginBottom: theme.spacing.s}}>
          <Text style={[styles.text, {marginRight: 40}]}>Vận chuyển đến</Text>
          <Pressable onPress={handleSelectAddress} style={{flexDirection: 'row', flex: 1}}>
            {addressArr.length > 0 ? (
              <Text numberOfLines={2} style={[styles.text, {flex: 1, textAlign: 'right'}]}>
                {addressArr.join(', ')}
              </Text>
            ) : (
              <Text style={[styles.text, {color: 'rgba(0,0,0,.5)', fontSize: 16, flex: 1, textAlign: 'right', alignSelf: 'center'}]}>Chọn địa chỉ</Text>
            )}
            <Ionicons name="chevron-forward" size={20} color="rgba(0,0,0,.5)" />
          </Pressable>
        </View> */}

        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <Text style={[styles.text, {marginRight: 40}]}>Phí vận chuyển</Text>
          <View style={{flexDirection: 'row', flex: 1, justifyContent: 'flex-end'}}>
            {/* {!shippingRateLoading && !shippingRates.length && ( */}
            <Button
              onPress={handleSelectAddress}
              type="clear"
              title={'Xem phí vận chuyển'}
              titleStyle={{fontSize: theme.typography.base}}
              icon={{type: 'ionicon', name: 'flash', size: 16, color: 'rgb(32, 137, 220)'}}
            />
            {/* )} */}
            {shippingRateLoading && <ActivityIndicator color={'#008060'} />}
          </View>
        </View>
        {!shippingRateLoading && shippingRates.length > 0 && (
          <View>
            {shippingRates.map(rate => (
              <ShippingRateItem shippingRate={rate} arrowHidden key={rate.service_code} />
            ))}
            <View style={{marginTop: theme.spacing.m}}>
              <Text style={{fontSize: theme.typography.sm, color: theme.colors.textLight}}>* Giá các gói vận chuyển chưa áp dụng khuyến mãi (nếu có)</Text>
              <Text style={{fontSize: theme.typography.sm, color: theme.colors.textLight}}>* Phí vận chuyển do người mua trả</Text>
            </View>
          </View>
        )}
      </View>

      <Modal visible={modalVisible} onRequestClose={() => setModalVisible(false)}>
        <View style={{flex: 1, paddingTop: insets.top, paddingBottom: insets.bottom}}>
          <View style={{paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: 'rgba(0,0,0,.08)', position: 'relative'}}>
            <View style={{position: 'absolute', left: 12, top: 4, zIndex: 100}}>
              <Ionicons name="close-circle" size={38} color="rgba(0,0,0,.5)" onPress={() => setModalVisible(false)} />
            </View>
            <Text style={{textAlign: 'center', fontSize: 18, fontWeight: '500', color: '#008060'}}>Chọn địa chỉ</Text>
            <View />
          </View>
          <LocationTabView onSelectDone={handleSelectDone} />
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    // marginTop: 12,
    // paddingVertical: 12,
    paddingHorizontal: 12,
  },
  text: {
    fontSize: 14,
  },
  bold: {
    fontWeight: '600',
  },
});

export default EstimateShippingRate;
