import React, {useMemo} from 'react';
import ContentLoader, {Rect} from 'react-content-loader/native';
import {ScreenHeight, ScreenWidth} from '@rneui/base';
import {responsiveWidth} from '~components/shared/web/WebContainer';

const ProductDetailSkeleton: React.FC<any> = props => {
  const screenWidth = useMemo(() => Math.min(ScreenWidth, responsiveWidth.sm), []);
  return (
    <ContentLoader speed={2} width={screenWidth} height={ScreenHeight} viewBox={`0 0 ${screenWidth} ${ScreenHeight}`} backgroundColor="#ebebf0" foregroundColor="#DDDDE3" {...props}>
      <Rect x="0" y="0" rx="0" ry="0" width={screenWidth} height={screenWidth} />
      <Rect x="12" y={ScreenWidth + 20} rx="0" ry="0" width="235" height="30" />
      <Rect x="12" y={ScreenWidth + 20 + 40} rx="0" ry="0" width={screenWidth - 24} height="40" />
      <Rect x="0" y={ScreenWidth + 20 + 40 + 80} rx="0" ry="0" width={screenWidth} height="120" />
    </ContentLoader>
  );
};

export default ProductDetailSkeleton;
