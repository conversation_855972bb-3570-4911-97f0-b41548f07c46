import {Icon, Slider, Text} from '@rneui/themed';
import React, {useState} from 'react';
import {Platform, StyleSheet, TouchableOpacity, View} from 'react-native';
import Card from '~components/shared/Card';
import {useAuthActions} from '~hooks';
import theme from '~utils/config/themes/theme';
import isInZaloMiniApp from '~utils/helpers/isInZaloMiniApp';
import {formatPriceToString} from '~utils/helpers/price';
import Product from '~utils/types/product';

type PriceInfoProps = {
  product: Product;
};

const PriceInfo: React.FC<PriceInfoProps> = props => {
  const [sellingPrice, setSellingPrice] = useState<any>(props.product.market_price);
  const [isZaloMiniApp] = useState(isInZaloMiniApp());
  const {openLoginRequiredModal, isLoggedIn} = useAuthActions();

  return (
    <Card title="Thông tin giá">
      {Platform.OS === 'web' && !isZaloMiniApp && !isLoggedIn ? (
        <TouchableOpacity style={{paddingHorizontal: theme.spacing.s, backgroundColor: '#eeffed', alignSelf: 'center'}} onPress={() => openLoginRequiredModal('register')}>
          <Text style={{color: theme.colors.primary, fontSize: theme.typography.base}}>Đăng nhập để xem giá</Text>
        </TouchableOpacity>
      ) : (
        <>
          <View style={{flexDirection: 'row', alignItems: 'flex-end', justifyContent: 'space-between', paddingVertical: 12, borderColor: 'rgba(0,0,0,.1)', borderBottomWidth: 1}}>
            <Text style={styles.text}>Giá nhà cung cấp</Text>
            <Text style={styles.text}>{formatPriceToString(props.product.dropship_price)}</Text>
          </View>
          <View style={{flexDirection: 'row', alignItems: 'flex-end', justifyContent: 'space-between', paddingVertical: 12, borderColor: 'rgba(0,0,0,.1)', borderBottomWidth: 1}}>
            <Text style={styles.text}>Giá bán thị trường</Text>
            <Text style={styles.text}>{formatPriceToString(props.product.market_price)}</Text>
          </View>
          {props.product.dropship_selling_price_min > 0 && (
            <View style={{flexDirection: 'row', alignItems: 'flex-end', justifyContent: 'space-between', paddingVertical: 12, borderColor: 'rgba(0,0,0,.1)', borderBottomWidth: 1}}>
              <Text style={styles.text}>Giá bán tối thiểu</Text>
              <Text style={styles.text}>{formatPriceToString(props.product.dropship_selling_price_min)}</Text>
            </View>
          )}
          <View style={{flexDirection: 'row', alignItems: 'flex-end', justifyContent: 'space-between', paddingTop: 12, paddingBottom: 0}}>
            <Text style={styles.text}>Giá bán của bạn</Text>
            <Text style={styles.priceText}>{formatPriceToString(sellingPrice)}</Text>
          </View>
          <View style={{paddingHorizontal: theme.spacing.m}}>
            <Slider
              value={sellingPrice}
              onValueChange={setSellingPrice}
              minimumValue={props.product.dropship_selling_price_min || props.product.dropship_price}
              maximumValue={props.product.market_price}
              step={1000}
              minimumTrackTintColor="#008060"
              maximumTrackTintColor="rgb(125,200,182)"
              trackStyle={{height: 5, backgroundColor: 'transparent'}}
              thumbStyle={{height: 20, width: 20, backgroundColor: 'transparent'}}
              thumbProps={{
                children: (
                  <Icon
                    type="ionicon"
                    name="ellipse"
                    size={20}
                    color={props.product.dropship_selling_price_min === props.product.market_price ? 'transparent' : theme.colors.primary}
                    containerStyle={{bottom: 0, right: 0}}
                  />
                ),
              }}
            />
          </View>
          <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end'}}>
            <Text>Lợi nhuận</Text>
            <Text style={{fontSize: 20, marginLeft: 10, color: '#008060'}}>{formatPriceToString(sellingPrice - props.product.dropship_price)}</Text>
          </View>
        </>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  text: {
    fontSize: 16,
  },
  priceText: {
    fontSize: 20,
  },
});

export default PriceInfo;
