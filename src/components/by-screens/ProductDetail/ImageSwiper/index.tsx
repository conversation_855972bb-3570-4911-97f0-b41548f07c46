import React, {useCallback, useMemo, useRef, useState} from 'react';
import {Text, Dimensions, StyleSheet, View, ListRenderItemInfo, Image, Pressable, Platform, ScrollView, GestureResponderEvent} from 'react-native';
import {SwiperFlatList} from 'react-native-swiper-flatlist';
import {handleImageDomain, getResizedImageUrl} from '~utils/helpers/image';
import Product, {ProductImage, ProductVideo} from '~utils/types/product';
import ee, {EventNames} from '~utils/events';
import {ScreenWidth} from '@rneui/base';
import {responsiveWidth} from '~components/shared/web/WebContainer';
import theme from '~utils/config/themes/theme';
import VideoItem from './VideoItem';
import Ionicons from 'react-native-vector-icons/Ionicons';

export type ImageSwiperProps = {
  product: Product;
};

type ImageSwiperItem = {
  type: 'image' | 'video';
  item: ProductImage | ProductVideo;
};

const ImageSwiper: React.FC<ImageSwiperProps> = props => {
  const [imageIndex, setImageIndex] = useState(0);
  const swiperRef = useRef<SwiperFlatList>(null);
  const scrollViewRef = useRef<ScrollView>(null);

  const mediaItems = useMemo(() => {
    return [...props.product.videos.map(video => ({type: 'video', item: video})), ...props.product.images.map(image => ({type: 'image', item: image}))];
  }, [props.product]);

  const renderItem = useCallback(
    (item: ListRenderItemInfo<ImageSwiperItem>) => {
      if (item.item.type === 'video') {
        return <VideoItem video={item.item.item as ProductVideo} isCurrent={imageIndex === item.index} />;
      }

      return (
        <Pressable style={styles.child} onPress={() => handleAttachmentsClick(item.item.item as ProductImage, item.index)}>
          <View style={styles.imageSpacer} />
          <Image
            style={styles.image}
            source={{
              uri: handleImageDomain((item.item.item as ProductImage).src),
            }}
          />
        </Pressable>
      );
    },
    [imageIndex],
  );

  const handleAttachmentsClick = useCallback(
    (_item: ProductImage | ProductVideo, index: number) => {
      ee.emit(EventNames.OpenImageViewer, {
        newImages: mediaItems.map(item => ({
          url: item.type === 'image' ? (item.item as ProductImage).src : (item.item as ProductVideo).url,
          props: {
            type: item.type,
            productId: (item.item as ProductImage).product_id,
          },
        })),
        newIndex: index,
      });
    },
    [mediaItems],
  );

  const imageWidth = useMemo(() => {
    return Math.min(ScreenWidth, responsiveWidth.sm);
  }, []);

  const getItemLayout = useCallback(
    (__data: any, itemIndex: number) => {
      return {
        length: imageWidth,
        offset: imageWidth * itemIndex,
        index: itemIndex,
      };
    },
    [imageWidth],
  );

  const handleIndexChange = useCallback(({index}: any) => {
    setImageIndex(index);
  }, []);

  const handlePaginationPress = useCallback(
    (event: GestureResponderEvent, index: number) => {
      swiperRef.current?.scrollToIndex({index});

      if (event.nativeEvent.pageX >= ScreenWidth - 80 * 2) {
        scrollViewRef.current?.scrollTo({x: (index - 3) * 80, animated: true});
      }
      if (event.nativeEvent.pageX <= 80 * 2) {
        //  scroll left
        scrollViewRef.current?.scrollTo({x: (index - 1) * 80, animated: true});
      }
    },
    [swiperRef, imageIndex],
  );

  const hasVideo = useMemo(() => {
    return props.product.videos.length > 0;
  }, [props.product]);

  return (
    <>
      <View style={styles.container}>
        <SwiperFlatList ref={swiperRef} getItemLayout={getItemLayout} onChangeIndex={handleIndexChange} data={mediaItems} renderItem={renderItem} />
        <View style={styles.countContainer}>
          <Text style={{color: '#fff'}}>
            {imageIndex + 1} / {mediaItems.length}
          </Text>
        </View>
        {hasVideo && imageIndex > props.product.videos.length - 1 && (
          <Pressable
            onPress={() => {
              swiperRef.current?.scrollToIndex({index: 0});
            }}
            style={styles.scrollToVideo}>
            <Ionicons name="play" size={30} color="white" />
          </Pressable>
        )}
      </View>

      {/* pagination as image */}
      {props.product.images.length > 1 && (
        <View style={{height: 82}}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.paginationContainer} ref={scrollViewRef}>
            {props.product.images.map((image, index) => (
              <Pressable onPress={e => handlePaginationPress(e, index + props.product.videos.length)} key={image.id}>
                <Image
                  style={[styles.paginationImageItem, {borderColor: index + props.product.videos.length === imageIndex ? theme.colors.black : 'white'}]}
                  source={{
                    uri: handleImageDomain(getResizedImageUrl(image.src, 320, 320)),
                  }}
                />
              </Pressable>
            ))}
          </ScrollView>
        </View>
      )}
    </>
  );
};

const {width} = Dimensions.get('window');
const styles = StyleSheet.create({
  container: {flex: 1, backgroundColor: 'white', position: 'relative'},
  child: {width: Platform.OS === 'web' ? Math.min(width, responsiveWidth.sm) : width, justifyContent: 'center', position: 'relative'},
  text: {fontSize: width * 0.5, textAlign: 'center'},
  imageSpacer: {paddingTop: '100%'},
  image: {
    ...StyleSheet.absoluteFillObject,
  },
  paginationContainer: {
    backgroundColor: theme.colors.white,
  },
  countContainer: {
    position: 'absolute',
    right: 8,
    bottom: 8,
    paddingHorizontal: 12,
    paddingVertical: 4,
    backgroundColor: 'rgba(0,0,0,.8)',
    borderRadius: 30,
  },

  paginationImageItem: {
    width: 80,
    height: 80,
    borderWidth: 1.5,
  },

  scrollToVideo: {
    position: 'absolute',
    left: 0,
    bottom: '45%',
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: 'rgba(0,0,0,.5)',
    borderTopRightRadius: 100,
    borderBottomRightRadius: 100,
  },
});

export default ImageSwiper;
