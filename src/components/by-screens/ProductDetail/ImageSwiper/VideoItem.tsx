import React, {useEffect, useRef, useState} from 'react';
import {Dimensions, Platform, Pressable, StyleSheet, TouchableOpacity, View} from 'react-native';
import {ProductVideo} from '~utils/types/product';
import Video, {OnPlaybackStateChangedData, VideoRef} from 'react-native-video';
import {responsiveWidth} from '~components/shared/web/WebContainer';
import Ionicons from 'react-native-vector-icons/Ionicons';
import theme from '~utils/config/themes/theme';

type VideoItemProps = {
  video: ProductVideo;
  isCurrent: boolean;
};

const {width: windowWidth} = Dimensions.get('window');

const VideoItem: React.FC<VideoItemProps> = props => {
  const [isPlaying, setIsPlaying] = useState(false);
  const videoRef = useRef<VideoRef>(null);
  const [ended, setEnded] = useState(false);

  useEffect(() => {
    if (!props.isCurrent && isPlaying) {
      setIsPlaying(false);
    }
  }, [props.isCurrent]);

  const togglePlay = () => {
    if (ended) {
      setEnded(false);
      videoRef.current?.seek(0);
    }
    setIsPlaying(!isPlaying);
  };

  const handleVideoEnd = () => {
    setIsPlaying(false);
    setEnded(true);
  };

  const toogleFullscreen = () => {
    if (!isPlaying) {
      togglePlay();
    }
    videoRef.current?.presentFullscreenPlayer();
  };

  const handlePlaybackStateChanged = (state: OnPlaybackStateChangedData) => {
    if (state.isPlaying !== isPlaying) {
      setIsPlaying(state.isPlaying);
    }
  };

  return (
    <View style={styles.container}>
      <View style={{paddingTop: '100%'}} />

      <View style={styles.videoContainer}>
        <Video
          ref={videoRef}
          source={{uri: props.video.url}}
          style={styles.video}
          fullscreen={false}
          paused={!isPlaying}
          onEnd={handleVideoEnd}
          resizeMode="contain"
          onPlaybackStateChanged={handlePlaybackStateChanged}
        />
      </View>

      <Pressable style={styles.playButtonContainer} onPress={togglePlay}>
        {!isPlaying && (
          <TouchableOpacity onPress={togglePlay} style={styles.playButton}>
            <Ionicons name="play" size={40} color="white" style={{marginLeft: 5}} />
          </TouchableOpacity>
        )}
      </Pressable>

      <View style={styles.actionContainer}>
        <Pressable style={styles.actionButton} onPress={toogleFullscreen}>
          <Ionicons name={'expand-outline'} size={20} color="white" />
        </Pressable>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: Platform.OS === 'web' ? Math.min(windowWidth, responsiveWidth.sm) : windowWidth,
    justifyContent: 'center',
    position: 'relative',
  },
  videoContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#252525',
  },
  video: {
    width: '100%',
    height: '100%',
  },
  playButtonContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 100,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    width: 70,
    height: 70,
    borderWidth: 2,
    borderColor: theme.colors.white,
  },
  actionContainer: {
    position: 'absolute',
    bottom: 8,
    right: 80,
  },
  actionButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 100,
    justifyContent: 'center',
    alignItems: 'center',
    width: 30,
    height: 30,
  },
});

export default VideoItem;
