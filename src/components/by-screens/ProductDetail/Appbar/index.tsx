import {Button} from '@rneui/themed';
import React, {useCallback, useEffect, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {useNavigation} from '~hooks';
import Product from '~utils/types/product';
import ProductPost from '../ProductPost';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import googleAnalytics from '~utils/helpers/analytics';
import ee, {EventNames} from '~utils/events';
import Modal from 'react-native-modal';
import {env} from '~utils/config';
import {TouchableOpacity} from 'react-native-gesture-handler';

type AppbarProps = {
  product: Product;
};

const Appbar: React.FC<AppbarProps> = props => {
  const [productPostModalVisible, setProductPostModalVisible] = useState(false);
  const navigation = useNavigation();

  useEffect(() => {
    ee.on(EventNames.OpenAddToCart, handleCreateOrderPress);
    return () => {
      ee.off(EventNames.OpenAddToCart, handleCreateOrderPress);
    };
  }, []);

  const handleCreateOrderPress = useCallback(() => {
    // if (Platform.OS === 'web') {
    //   setModalVisible(true);
    //   return;
    // }

    if (props.product.customize) {
      navigation.push('WebView', {includeAuth: true, url: `${env.API_BASE_URL}/v1/pod-studio/products/${props.product.id}`, title: 'Tạo đơn in theo yêu cầu'});
      return;
    }

    navigation.navigate('AddToCart', {productId: props.product.id_number});
  }, [props.product]);

  const handleShareProduct = useCallback(() => {
    googleAnalytics.logEvent('product_post_open', {
      product_id: props.product.id,
    });
    setProductPostModalVisible(true);
  }, [props.product]);

  const handleCloseProductPostModal = useCallback(() => {
    setProductPostModalVisible(false);
  }, []);

  return (
    <>
      <View style={styles.container}>
        <Button
          TouchableComponent={TouchableOpacity}
          containerStyle={{flex: 1, marginRight: 4}}
          titleStyle={{color: '#fff', fontWeight: '600'}}
          buttonStyle={{
            // flex: 1,
            borderRadius: 4,
            paddingVertical: 12,
            backgroundColor: 'rgb(32, 137, 220)',
          }}
          onPress={handleShareProduct}>
          <MaterialCommunityIcons name="share" color={'#fff'} size={22} style={{alignSelf: 'flex-start', marginRight: 8, marginBottom: 0}} />
          Đăng bán
        </Button>
        <Button
          title="Tạo đơn"
          TouchableComponent={TouchableOpacity}
          containerStyle={{flex: 1}}
          titleStyle={{fontWeight: '600'}}
          buttonStyle={{
            borderRadius: 4,
            paddingVertical: 12,
          }}
          onPress={handleCreateOrderPress}
        />
      </View>

      <Modal
        isVisible={productPostModalVisible}
        swipeDirection={['right']}
        style={{marginHorizontal: 0, marginBottom: 0, justifyContent: 'flex-end', marginTop: 0}}
        onBackdropPress={handleCloseProductPostModal}
        onBackButtonPress={handleCloseProductPostModal}
        onSwipeComplete={handleCloseProductPostModal}
        propagateSwipe
        avoidKeyboard>
        <ProductPost
          product={props.product}
          productId={props.product.id}
          onClose={() => {
            setProductPostModalVisible(false);
          }}
        />
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    // backgroundColor: '#fff',
    paddingHorizontal: 12,
    paddingVertical: 4,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
});

export default Appbar;
