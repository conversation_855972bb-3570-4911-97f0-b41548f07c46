import {Text} from '@rneui/themed';
import React, {useCallback, useMemo} from 'react';
import {Pressable, StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import Product from '~utils/types/product';
import uniqBy from 'lodash/fp/uniqBy';
import {handleImageDomain, getResizedImageUrl} from '~utils/helpers/image';
import {QuickImage} from '~components/shared/QuickImage';
import ee, {EventNames} from '~utils/events';

type ProductVariantsProps = {
  product: Product;
};

const ProductVariants: React.FC<ProductVariantsProps> = props => {
  const renderTotalOptions = useMemo(() => {
    return props.product.options.map(option => `${option.values.length} ${option.display_name}`).join(', ');
  }, [props.product]);

  const getThumbnail = useCallback(
    (imageId: string | null) => {
      if (imageId) {
        const image = props.product.images.find(img => img.id === imageId);
        if (image) {
          return handleImageDomain(getResizedImageUrl(image.src, 100, 100));
        }
      }

      return null;
    },
    [props.product],
  );

  const renderUniqueVariant = useMemo(() => {
    const uniqueOptions = uniqBy('image_id', props.product.variants);
    const isOneOption = props.product.options.length === 1;
    return uniqueOptions.map(variant => {
      const thumb = getThumbnail(variant.image_id);
      if (thumb) {
        return <QuickImage key={variant.image_id} source={{uri: thumb}} style={[styles.imageThumb, isOneOption && variant.inventory_quantity === 0 && {opacity: 0.5}]} />;
      }

      return null;
    });
  }, [props.product]);

  const renderOutOfStockVariant = useMemo(() => {
    return props.product.variants.map(v => {
      if (v.inventory_quantity > 0) {
        return null;
      }

      return <Text style={[styles.text, {color: theme.colors.red}]}>{v.title} đang hết hàng</Text>;
    });
  }, [props.product]);

  const handlePress = useCallback(() => {
    ee.emit(EventNames.OpenAddToCart);
  }, []);

  if (props.product.variants.length < 2) {
    return null;
  }

  return (
    <Pressable style={styles.container} onPress={handlePress}>
      <Text style={styles.text}>
        Nhiều lựa chọn <Text style={[styles.totalOptions, styles.text]}>({renderTotalOptions})</Text>
      </Text>
      <View style={[theme.globalStyles.flexRow, {flexWrap: 'wrap', marginTop: theme.spacing.s}]}>{renderUniqueVariant}</View>

      <View>{renderOutOfStockVariant}</View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: theme.spacing.m,
    paddingHorizontal: 8,
    backgroundColor: theme.colors.white,
    paddingVertical: 8,
  },
  totalOptions: {
    color: theme.colors.textLight,
  },
  text: {
    fontSize: theme.typography.base,
  },
  imageThumb: {width: 40, height: 40, marginRight: theme.spacing.s, borderRadius: 4, marginTop: theme.spacing.s},
});

export default ProductVariants;
