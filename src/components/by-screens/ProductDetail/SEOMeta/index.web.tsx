import React, {useMemo} from 'react';
import {SEOMetaProps} from '.';
import {Helmet} from 'react-helmet';
import {formatPriceToString} from '~utils/helpers/price';
import {handleImageDomain} from '~utils/helpers/image';

const SEOMeta: React.FC<SEOMetaProps> = props => {
  const ogDescription = useMemo(() => {
    return props.product?.title ? `Bán ${props.product?.title} lợi nhuận lên tới ${formatPriceToString(props.product?.dropship_profit)}` : '';
  }, [props.product?.title]);

  const seoTitle = useMemo(() => {
    return props.product?.title ? `${props.product.title}` : '';
  }, [props.product?.title]);

  const ogImage = useMemo(() => {
    return props.product?.image?.src ? handleImageDomain(props.product.image.src) : '';
  }, [props.product?.image]);

  return (
    // @ts-ignore
    <Helmet>
      <meta charSet="UTF-8" />
      {ogDescription ? <meta name="description" content={ogDescription} /> : null}
      {ogDescription ? <meta property="og:description" content={ogDescription} /> : null}
      {seoTitle ? <meta property="og:title" content={seoTitle + ' - TTS Dropship'} /> : null}
      <meta property="og:site_name" content="TTS Dropship" />
      <meta property="og:locale" content="vi-VN" />
      <meta property="og:type" content="website" />
      {ogImage ? <meta property="og:image" content={ogImage} /> : null}
      {/* preload og image */}
      <link rel="preload" as="image" href={handleImageDomain(props.product.image.src)} />
    </Helmet>
  );
};

export default SEOMeta;
