import {Text} from '@rneui/themed';
import React, {useCallback} from 'react';
import {Pressable, ScrollView, View} from 'react-native';
import ProductCardMini from '~components/shared/ProductCardMini';
import {useNavigation} from '~hooks';
import {useSearchProducts} from '~utils/api/product';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';

type SupplierProductsProps = {
  shopId: string;
  excludeProductId: string;
};

const SupplierProducts: React.FC<SupplierProductsProps> = props => {
  const {data} = useSearchProducts({limit: 5, filter_shop_id: props.shopId, exclude_product_ids: props.excludeProductId});
  const navigation = useNavigation();

  const handleNavigationSupplier = useCallback(() => {
    navigation.navigate('SupplierDetail', {shopId: props.shopId});
  }, [props.shopId]);

  if (!data?.total) {
    return null;
  }

  return (
    <View style={{backgroundColor: theme.colors.white, marginTop: theme.spacing.s}}>
      <Pressable onPress={handleNavigationSupplier} style={[{paddingHorizontal: theme.spacing.m, paddingTop: theme.spacing.s, paddingVertical: theme.spacing.m}, theme.globalStyles.flexRow]}>
        <Text style={{fontSize: theme.typography.lg, fontWeight: '500'}}>Cùng nhà cung cấp</Text>
        <Ionicons name="arrow-forward" size={22} color={theme.colors.text} style={{marginLeft: 'auto', paddingRight: 20}} />
      </Pressable>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={{paddingLeft: theme.spacing.m - 5}}>
        {data?.products.map(product => (
          <ProductCardMini product={product} key={product.id} />
        ))}
      </ScrollView>
    </View>
  );
};

export default SupplierProducts;
