import {Text} from '@rneui/themed';
import React, {useCallback, useMemo} from 'react';
import {Image, Pressable, StyleSheet, TouchableOpacity, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import {handleImageDomain} from '~utils/helpers/image';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {ShopV1} from '~utils/types/shop';
import {useNavigation} from '~hooks';
import FollowButton from '~components/shared/FollowButton';
import {kFormatter} from '~utils/helpers/price';
import LinearGradient from 'react-native-linear-gradient';
import {useHeaderHeight} from '@react-navigation/elements';

type ShopInfoProps = {
  shop: ShopV1;
  rounded?: boolean;
  paddingInsetTop?: boolean;
};

const ShopInfo: React.FC<ShopInfoProps> = props => {
  const navigation = useNavigation();

  const handleNavigation = useCallback(() => {
    navigation.navigate('SupplierDetail', {shopId: props.shop.slug});
  }, []);

  const handleNavigateSupplierReviews = () => {
    navigation.navigate('SupplierReviews', {shopId: props.shop.id});
  };

  const contentColorScheme = useMemo(() => {
    return props.shop.styles?.title?.color || theme.colors.text;
  }, [props.shop.styles]);

  return (
    <ShopInfoContainer {...props}>
      <Pressable onPress={handleNavigation}>
        <View style={styles.container}>
          <Image source={{uri: handleImageDomain(props.shop.avatar)}} style={styles.shopAvatar} />
          <View style={styles.shopInfoRight}>
            <View style={{flexDirection: 'row'}}>
              <Text style={[styles.shopName, {color: contentColorScheme}]}>{props.shop.name}</Text>
              <FollowButton tintColor={props.shop.styles?.title?.color} shopId={props.shop.id} styles={{marginLeft: 'auto'}} />
            </View>
            <Text style={[styles.textSmall, {marginTop: 4, color: props.shop.styles?.title?.color || theme.colors.textLight}]}>
              <Ionicons name="location-outline" size={12} />
              &nbsp;{props.shop.province.name}
            </Text>
          </View>

          <View style={{marginLeft: 'auto', marginRight: 8}}>{/* <Ionicons name="chevron-forward" size={20} color={theme.colors.textLight} /> */}</View>
        </View>
        <View style={{marginTop: theme.spacing.m, justifyContent: 'center', alignItems: 'flex-end', flexDirection: 'row'}}>
          <TouchableOpacity style={styles.statisticContainer} onPress={handleNavigateSupplierReviews}>
            {props.shop.rating_avg > 0 ? (
              <View style={styles.ratingAvg}>
                <Text style={[styles.ratingAvgText, {color: contentColorScheme}]}>{props.shop.rating_avg}</Text>
                <Ionicons name="star" size={12} color={'#ffc400'} />
              </View>
            ) : (
              <Text style={[styles.statisticValue, props.shop.rating_avg ? {} : {color: theme.colors.textLightest}]}>{props.shop.rating_avg || 'N/A'}</Text>
            )}
            <Text style={[styles.statisticName, {color: props.shop.styles?.title?.color || theme.colors.textLight}]}>
              {props.shop.rating_count ? `${kFormatter(props.shop.rating_count)} ` : ''}đánh giá
            </Text>
          </TouchableOpacity>
          <Text style={styles.separator}>|</Text>

          <View style={styles.statisticContainer}>
            <Text style={[styles.statisticValue, {color: contentColorScheme}]}>{props.shop.total_dropship_products ?? '?'}</Text>
            <Text style={[styles.statisticName, {color: props.shop.styles?.title?.color || theme.colors.textLight}]}>sản phẩm</Text>
          </View>
          <Text style={styles.separator}>|</Text>

          <View style={styles.statisticContainer}>
            <Text style={[styles.statisticValue, {color: contentColorScheme}]}>{props.shop.total_following}</Text>
            <Text style={[styles.statisticName, {color: props.shop.styles?.title?.color || theme.colors.textLight}]}>người theo dõi</Text>
          </View>
        </View>
      </Pressable>
    </ShopInfoContainer>
  );
};

const ShopInfoContainer: React.FC<React.PropsWithChildren<ShopInfoProps>> = props => {
  const height = useHeaderHeight();

  if (props.shop.styles?.bg?.gradientColors && props.shop.styles?.bg?.gradientColors.length > 0) {
    return (
      <LinearGradient start={{x: 0, y: 0.2}} colors={props.shop.styles.bg.gradientColors} style={[styles.linearGradient, props.paddingInsetTop && {paddingTop: height}]}>
        {props.children}
      </LinearGradient>
    );
  }

  return <View style={[styles.cardContainer, props.rounded && {borderRadius: 12}, props.paddingInsetTop && {paddingTop: height}]}>{props.children}</View>;
};

const styles = StyleSheet.create({
  linearGradient: {
    paddingHorizontal: theme.spacing.m,
    paddingVertical: 12,
    backgroundColor: '#fff',
  },
  cardContainer: {
    paddingHorizontal: theme.spacing.m,
    paddingVertical: 12,
    backgroundColor: '#fff',
  },

  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  shopAvatar: {
    width: 50,
    height: 50,
    borderRadius: 100,
    borderColor: theme.colors.gray20,
    borderWidth: 1,
  },
  shopInfoRight: {
    marginLeft: theme.spacing.s,
    flex: 1,
  },

  shopName: {
    fontSize: theme.typography.md,
    fontWeight: 'bold',
    color: theme.colors.text,
    flex: 1,
  },
  textSmall: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
  },

  statisticContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  statisticName: {
    color: theme.colors.textLight,
    fontSize: theme.typography.base,
    marginTop: theme.spacing.xs,
  },
  statisticValue: {
    color: theme.colors.text,
    fontWeight: 'bold',
  },
  ratingAvg: {
    borderRadius: 100,
    borderWidth: 1,
    borderColor: theme.colors.textLightest,
    paddingHorizontal: theme.spacing.m,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 2,
  },
  ratingAvgText: {
    color: theme.colors.primary,
    fontSize: theme.typography.base,
    marginRight: theme.spacing.xs,
  },
  separator: {fontSize: theme.typography.sm, color: theme.colors.textLight, marginHorizontal: theme.spacing.m, alignSelf: 'center'},
});

export default ShopInfo;
