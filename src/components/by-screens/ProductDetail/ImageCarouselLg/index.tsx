import React, {useEffect, useState} from 'react';
import {View} from 'react-native';

import Slider from 'react-slick';
import Product, {ProductImage} from '~utils/types/product';
import './image-carousel-lg.css';
import ImageWithDownload from '~components/shared/ImgWithDownloadWeb';

type ImageCarouselLgProps = {
  product: Product;
  images: ProductImage[];
};

const ImageCarouselLg: React.FC<ImageCarouselLgProps> = props => {
  const [nav1, setNav1] = useState<Slider>();
  const [nav2, setNav2] = useState<Slider>();
  const [slider1, setSlider1] = useState<Slider>();
  const [slider2, setSlider2] = useState<Slider>();
  const [selectedIndex, setSelectedIndex] = useState(0);

  useEffect(() => {
    setNav1(slider1);
    setNav2(slider2);
  });

  const settingsMain = {
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: false,
    // fade: true,
    asNavFor: '.slider-nav',
  };

  const settingsThumbs = {
    slidesToShow: Math.min(4, props.images.length),
    slidesToScroll: 1,
    asNavFor: '.slider-for',
    // dots: true,
    centerMode: true,
    swipeToSlide: true,
    focusOnSelect: true,
    centerPadding: '10px',
  };

  return (
    <View>
      <div className="slider-wrapper">
        <Slider
          {...settingsMain}
          asNavFor={nav2}
          ref={slider => setSlider1(slider as any)}
          beforeChange={(_, nextSlide) => {
            setSelectedIndex(nextSlide);
          }}>
          {props.images.map(img => (
            <div className="slick-slide-wrapper" key={img.id}>
              <img className="slick-slide-image" src={img.src} />
            </div>
          ))}
        </Slider>
        <div className="thumbnail-slider-wrap">
          <Slider {...settingsThumbs} asNavFor={nav1} ref={slider => setSlider2(slider as any)}>
            {props.images.map((img, index) => (
              <div className={`slick-slide-nav-wrapper ${index === selectedIndex ? 'selected' : ''}`} key={img.id}>
                <ImageWithDownload image={img} fileName={`${props.product.title}-${index}.jpg`}>
                  <img className="slick-slide-nav-image" src={img.src} />
                </ImageWithDownload>
              </div>
            ))}
          </Slider>
        </div>
      </div>
    </View>
  );
};

export default ImageCarouselLg;
