.slider-wrapper {
    width: 400px;
    margin: auto;
    padding: 0px 20px;
    background-color: #fff;
  }
  .slick-slide-wrapper {
    text-align: center;
    position: relative;
    height: 400px !important;
    display: flex !important;
    justify-content: center;
    align-items: center;
  }

  .slick-slide:focus {
    outline: none;
  }
  .slick-slide-title {
    text-transform: capitalize;
  }
  .slick-slide-image {
   
   height: auto;
   max-width: 100%;
   max-height: 400px;
   /* object-fit: cover; */
    border-radius: 8px;
   
  }

  .slick-slide-label {
    color: #fff;
    padding: 10px;
    position: absolute;
    left: 0px;
    font-size: 1.5em;
    bottom: 0px;
    width: 100%;
  }
  .slick-prev:before,
  .slick-next:before {
    color: black !important;
  }

  .thumbnail-slider-wrap {
    margin-top: 8px;
    height: 85px;
  }
  .thumbnail-slider-wrap .slick-track .slick-slide {
    text-align: center;
  }
  .thumbnail-slider-wrap .slick-track .slick-slide img {
    height: 80px;
    width: 100%;
    height:auto;max-width: 80px;
    max-height: 80px;
    /* object-fit: cover; */
     border-radius: 4px;
    border:1px solid rgba(0,0,0,.1);

     /* margin-right: 15px; */
  }
  .slick-slide-nav-wrapper {
    text-align: center;
    position: relative;
    height: 84px !important;
    display: flex !important;
    align-items: center;

  }

  .slick-slide-nav-wrapper.selected img {
    border: 2px solid #008060 !important
  }

  