import React, {useCallback, useMemo} from 'react';
import {Platform, Pressable, StyleSheet, View} from 'react-native';
import Customer from '~utils/types/customer';
import {CheckBox} from '@rneui/base';
import {Text} from '@rneui/themed';
import theme from '~utils/config/themes/theme';
import {Menu, MenuOption, MenuOptions, MenuTrigger, renderers} from 'react-native-popup-menu';
import Ionicons from 'react-native-vector-icons/Ionicons';
import ee, {EventNames} from '~utils/events';
import Address from '~utils/types/address';
import {patternFormatter} from 'react-number-format';

const {Popover} = renderers;

type CustomerItemProps = {
  customer: Customer;
  selected: boolean;
  onSelect: (customer: Customer) => void;
  selectedAddress?: Address;
};

const CustomerItem: React.FC<CustomerItemProps> = props => {
  const handleCustomerSelect = useCallback(() => {
    props.onSelect(props.customer);
  }, [props.customer, props.selected]);

  const handleOpenAddressList = useCallback(() => {
    ee.emit(EventNames.OpenAddressList, props.customer);
  }, [props.customer]);

  const address = useMemo(() => {
    return props.selected && props.selectedAddress ? props.selectedAddress : props.customer.default_address;
  }, [props.customer, props.selectedAddress, props.selected]);

  const formatedPhone = useMemo(() => {
    return patternFormatter(address.phone, {
      format: '#### ### ###',
      allowEmptyFormatting: true,
    });
  }, [address.phone]);

  return (
    <Pressable onPress={handleCustomerSelect} style={[styles.container, theme.globalStyles.rounded12]}>
      <View>
        <CheckBox
          size={28}
          checked={props.selected}
          onPress={handleCustomerSelect}
          iconType="ionicon"
          uncheckedIcon="radio-button-off-outline"
          checkedIcon="checkmark-circle"
          checkedColor="#008060"
          containerStyle={{margin: 0, padding: 0, backgroundColor: 'transparent'}}
          style={{marginHorizontal: 0}}
          accessibilityLabel="Ấn để chọn khách hàng này"
        />
      </View>
      <View style={{flexShrink: 1}}>
        <Text style={styles.customerName}>{[props.customer.first_name, props.customer.last_name].filter(Boolean).join(' ')}</Text>
        <Text style={styles.text}>{formatedPhone}</Text>
        <Text style={styles.text}>{[address.address1, address.ward, address.province, address.city].join(', ')}</Text>
      </View>

      {Platform.OS !== 'web' && (
        <Menu renderer={Popover} rendererProps={{placement: 'bottom', anchorStyle: {backgroundColor: '#f5f5f5'}}}>
          <MenuTrigger>
            <Ionicons name="ellipsis-horizontal" size={20} color="#616161" style={{paddingHorizontal: 20, paddingVertical: 10}} />
          </MenuTrigger>
          <MenuOptions
            customStyles={{
              optionText: {color: '#333'},
              optionsContainer: {
                backgroundColor: '#f5f5f5',
                borderRadius: 8,
              },
              optionWrapper: {
                padding: 12,
                borderBottomColor: 'rgba(0,0,0,.1)',
                borderBottomWidth: 1,
              },
            }}>
            <MenuOption text="Danh sách địa chỉ" onSelect={handleOpenAddressList} />
          </MenuOptions>
        </Menu>
      )}
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 8,
    paddingHorizontal: 8,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,.06)',
    paddingBottom: 12,
    ...theme.globalStyles.flexRow,
    marginTop: theme.spacing.s,
  },
  customerName: {
    fontSize: theme.typography.md,
  },
  text: {
    marginTop: 4,
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
  },
});

export default CustomerItem;
