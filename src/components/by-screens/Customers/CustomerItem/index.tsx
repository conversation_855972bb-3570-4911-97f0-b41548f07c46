import React, {useCallback} from 'react';
import {Pressable, StyleSheet, View} from 'react-native';
import Customer from '~utils/types/customer';
import {CheckBox} from '@rneui/base';
import {Text} from '@rneui/themed';
import theme from '~utils/config/themes/theme';

type CustomerItemProps = {
  customer: Customer;
  selected: boolean;
  onSelect: (customer: Customer) => void;
  selectable: boolean;
};

const CustomerItem: React.FC<CustomerItemProps> = props => {
  const handleCustomerSelect = useCallback(() => {
    props.onSelect(props.customer);
  }, [props.customer]);

  return (
    <Pressable onPress={handleCustomerSelect} style={styles.container}>
      {props.selectable && (
        <View>
          <CheckBox
            size={28}
            checked={props.selected}
            onPress={handleCustomerSelect}
            iconType="ionicon"
            uncheckedIcon="radio-button-off-outline"
            checkedIcon="checkmark-circle"
            checkedColor="#008060"
            containerStyle={{margin: 0, padding: 0, backgroundColor: 'transparent'}}
            style={{marginHorizontal: 0}}
            wrapperStyle={{marginLeft: -10, padding: 0}}
          />
        </View>
      )}
      <View style={{flexShrink: 1}}>
        <Text style={styles.customerName}>{[props.customer.first_name, props.customer.last_name].filter(Boolean).join(' ')}</Text>
        <Text style={styles.text}>{props.customer.default_address?.phone}</Text>
        <Text style={styles.text}>
          {[props.customer.default_address?.address1, props.customer.default_address?.ward, props.customer.default_address?.province, props.customer.default_address?.city].join(', ')}
        </Text>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 8,
    paddingHorizontal: 8,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,.06)',
    paddingBottom: 12,
    ...theme.globalStyles.flexRow,
    ...theme.globalStyles.rounded12,
  },
  customerName: {
    fontSize: theme.typography.md,
  },
  text: {
    marginTop: 4,
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
  },
});

export default CustomerItem;
