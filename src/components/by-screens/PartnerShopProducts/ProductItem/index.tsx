import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Dialog} from '@rneui/themed';
import React, {useState} from 'react';
import {View, Text, StyleSheet, Pressable} from 'react-native';
import {QuickImage} from '~components/shared/QuickImage';
import theme from '~utils/config/themes/theme';
import {formatPriceToString} from '~utils/helpers/price';
import {MarketplaceProduct, PartnerTiktokProductStatus, PartnerTiktokProductStatusColor} from '~utils/types/partner';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Clipboard from '@react-native-clipboard/clipboard';
import {useToast} from 'react-native-toast-notifications';
import {Linking} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {ASYNC_STORAGE_KEYS} from '~utils/config';
import {addSyncProductJob} from '~utils/api/partner';
import inInTTSDropshipWebview from '~utils/helpers/isInTTSDropshipWebview';
import {sendWebViewEvent} from '~utils/helpers/webview';
import {WebViewMessageTypes} from '~utils/types/common';

const ProductItem: React.FC<{product: MarketplaceProduct; partnerShopId: string}> = ({product, partnerShopId}) => {
  const partnerProductItemId = product.synced_product?.partner_product?.item_id;
  const toast = useToast();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [isNotShowDialogAgain, setIsNotShowDialogAgain] = useState(false);
  const [isSyncingToTiktokShop, setIsSyncingToTiktokShop] = useState(false);
  const [syncedProduct, setSyncedProduct] = useState(product.synced_product);

  const handleCopyPartnerProductItemId = () => {
    if (partnerProductItemId) {
      Clipboard.setString(partnerProductItemId);
      toast.show('Đã sao chép ID Tiktok Shop', {placement: 'center', duration: 1000});
    }
  };

  const handleViewProduct = () => {
    Linking.openURL(`https://shop-vn.tiktok.com/view/product/${product.synced_product?.partner_product?.item_id}`);
  };

  const handlePressSync = async () => {
    const isIgnored = await AsyncStorage.getItem(ASYNC_STORAGE_KEYS.PARTNER_PRODUCT_SYNC_DIALOG_IGNORED);
    if (isIgnored) {
      handleSyncToTiktokShop();
      return;
    }

    setDialogOpen(true);
  };

  const handleSyncToTiktokShop = async () => {
    setIsSyncingToTiktokShop(true);
    try {
      const res = await addSyncProductJob(partnerShopId, product.id);
      setSyncedProduct(res.synced_product);
      //
    } catch (error: any) {
      toast.show(error?.response?.data?.message ?? 'Có lỗi xảy ra', {placement: 'center', duration: 1000, type: 'danger'});
    }
    if (isNotShowDialogAgain) {
      AsyncStorage.setItem(ASYNC_STORAGE_KEYS.PARTNER_PRODUCT_SYNC_DIALOG_IGNORED, 'true');
    }
    setDialogOpen(false);
    setIsSyncingToTiktokShop(false);
  };

  const handleGoToProductDetail = () => {
    if (inInTTSDropshipWebview()) {
      sendWebViewEvent(WebViewMessageTypes.Push, ['ProductDetail', {productId: product.id}]);
    } else {
      // console.log('ok');
    }
  };

  return (
    <View style={styles.container}>
      <View style={{marginBottom: theme.spacing.m}}>
        {partnerProductItemId && (
          <View style={[theme.globalStyles.flexRow]}>
            <Text style={styles.partnerIdText}>ID Tiktok Shop: {partnerProductItemId}</Text>
            <Ionicons name="copy-outline" size={theme.typography.base} color={theme.colors.textLight} style={{paddingHorizontal: theme.spacing.xs}} onPress={handleCopyPartnerProductItemId} />
          </View>
        )}
      </View>
      <View style={[theme.globalStyles.flexRow, {alignItems: 'flex-start'}]}>
        <Pressable onPress={handleGoToProductDetail}>
          <QuickImage
            source={{
              uri: product.image_thumb,
            }}
            style={styles.image}
          />
        </Pressable>
        <View style={[theme.globalStyles.flex1]}>
          {syncedProduct?.partner_product?.status && (
            <View style={{flexDirection: 'row', alignItems: 'center', marginBottom: theme.spacing.xs}}>
              <QuickImage
                source={{
                  uri: 'https://files.thitruongsi.com/assets/social-icons/tiktokshop-small-notext.png',
                }}
                style={{width: 13, height: 15, marginRight: theme.spacing.xs}}
              />
              <View
                style={{
                  backgroundColor: PartnerTiktokProductStatusColor[syncedProduct?.partner_product?.status as keyof typeof PartnerTiktokProductStatusColor].background,
                  paddingHorizontal: theme.spacing.xs,
                  borderRadius: 4,
                }}>
                <Text style={{color: PartnerTiktokProductStatusColor[syncedProduct?.partner_product?.status as keyof typeof PartnerTiktokProductStatusColor].text, fontSize: theme.typography.sm}}>
                  {PartnerTiktokProductStatus[syncedProduct?.partner_product?.status as keyof typeof PartnerTiktokProductStatus]}
                </Text>
              </View>
            </View>
          )}
          <Text onPress={handleGoToProductDetail}>{product.title}</Text>

          <Text style={styles.priceText}>Giá vốn: {formatPriceToString(product.dropship_price, true)}</Text>
        </View>
      </View>

      {product.synced_product?.status === 'failed' && (
        <View style={{marginTop: theme.spacing.s}}>
          <Text style={{fontSize: theme.typography.sm, color: theme.colors.red}}>{product.synced_product?.message}</Text>
        </View>
      )}

      <View style={styles.buttonContainer}>
        {(!syncedProduct || syncedProduct?.status === 'failed') && (
          <Button
            size="sm"
            titleStyle={{fontSize: theme.typography.sm}}
            buttonStyle={{paddingVertical: 4, backgroundColor: '#000', borderRadius: 4}}
            loading={isSyncingToTiktokShop}
            loadingProps={{size: 13}}
            loadingStyle={{minWidth: 130}}
            onPress={handlePressSync}>
            Đăng lên TiktokShop
          </Button>
        )}

        {syncedProduct?.status === 'pending' && <Text style={{fontSize: theme.typography.base, color: theme.colors.pending}}>Chờ đồng bộ</Text>}
        {syncedProduct?.status === 'completed' && (
          <>
            <Button
              size="sm"
              onPress={handleViewProduct}
              titleStyle={{fontSize: theme.typography.sm, color: theme.colors.black}}
              buttonStyle={{
                borderWidth: 1,
                borderColor: theme.colors.black,
                paddingVertical: 4,
                backgroundColor: theme.colors.white,
                borderRadius: 4,
              }}>
              Xem sản phẩm
            </Button>
          </>
        )}

        <Dialog isVisible={dialogOpen}>
          <Dialog.Title
            title="Đăng lên TiktokShop?"
            titleStyle={{
              color: theme.colors.text,
            }}
          />

          <View>
            <Text style={{fontSize: theme.typography.base}}>
              Khi ấn ĐĂNG, sản phẩm sẽ chuyển sang trạng thái Chờ đồng bộ, và sẽ xuất hiện trên gian hàng TiktokShop của bạn trong khoảng 1 đến 5 phút.
            </Text>

            <CheckBox
              style={{marginHorizontal: 0, paddingHorizontal: 0}}
              containerStyle={{marginHorizontal: 0, paddingHorizontal: 0}}
              wrapperStyle={{marginHorizontal: 0, paddingHorizontal: 0}}
              textStyle={{fontWeight: 'normal'}}
              iconType="ionicon"
              uncheckedIcon="square-outline"
              size={20}
              checkedIcon="checkbox-outline"
              title={'Không hiển thị lại'}
              checked={isNotShowDialogAgain}
              onPress={() => setIsNotShowDialogAgain(!isNotShowDialogAgain)}
            />
          </View>

          <Dialog.Actions>
            <Dialog.Button title={'ĐĂNG'} titleStyle={{color: theme.colors.primary}} onPress={handleSyncToTiktokShop} />
            <Dialog.Button title={'HỦY'} titleStyle={{color: theme.colors.textLight}} onPress={() => setDialogOpen(false)} />
          </Dialog.Actions>
        </Dialog>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 10,
    backgroundColor: theme.colors.white,
    marginBottom: theme.spacing.s,
  },
  image: {
    width: 100,
    height: 100,
    borderRadius: 4,
    flexShrink: 0,
    marginRight: theme.spacing.s,
  },
  partnerIdText: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
  },
  priceText: {
    marginTop: theme.spacing.xs,
    fontSize: theme.typography.base,
    // fontWeight: '500',
  },
  buttonContainer: {
    marginTop: theme.spacing.m,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
});

export default ProductItem;
