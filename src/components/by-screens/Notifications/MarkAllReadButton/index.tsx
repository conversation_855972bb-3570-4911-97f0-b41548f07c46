import {Dialog} from '@rneui/themed';
import React, {useCallback, useState} from 'react';
import {View} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useMarkNotificationRead} from '~utils/api/notifications';
import theme from '~utils/config/themes/theme';

const MarkAllReadButton = () => {
  const mutation = useMarkNotificationRead();
  const [dialogOpen, setDialogOpen] = useState(false);

  const handleMarkAllRead = useCallback(() => {
    mutation.mutate({
      ids: [],
    });
    toggleDialogOpen();
  }, []);

  const toggleDialogOpen = useCallback(() => {
    setDialogOpen(prev => !prev);
  }, []);

  return (
    <View>
      <Ionicons name="checkmark-done-sharp" size={24} color={theme.colors.text} onPress={toggleDialogOpen} />

      <Dialog isVisible={dialogOpen} onBackdropPress={toggleDialogOpen}>
        <Dialog.Title title="<PERSON><PERSON>h dấu tất cả là đã đọc?" />
        <Dialog.Actions>
          <Dialog.Button loading={mutation.isLoading} title="CÓ" onPress={handleMarkAllRead} titleStyle={{fontWeight: 'bold'}} />
          <Dialog.Button title="KHÔNG" onPress={toggleDialogOpen} titleStyle={{color: theme.colors.textLight, fontWeight: 'bold'}} />
        </Dialog.Actions>
      </Dialog>
    </View>
  );
};

export default MarkAllReadButton;
