import {Text} from '@rneui/themed';
import {FlashList, ListRenderItemInfo} from '@shopify/flash-list';
import React, {useCallback, useMemo, useState} from 'react';
import {ActivityIndicator, StyleSheet} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import useRefreshOnFocus from '~hooks/useRefreshOnFocus';
import {useGetNotifications} from '~utils/api/notifications';
import theme from '~utils/config/themes/theme';
import Notification from '~utils/types/notification';
import NotificationItem from './NotificationItem';

type NotificationListProps = {
  type: string;
};

const NotificationList: React.FC<NotificationListProps> = props => {
  const [showReachedEndIndicator, setReachedEndIndicator] = useState(false);
  const {data, fetchNextPage, isFetchingNextPage, hasNextPage, isLoading, refetch} = useGetNotifications({
    platform: 'dropship',
    type: props.type,
  });

  useRefreshOnFocus(refetch);

  const dataFlat = useMemo(() => {
    return data?.pages.flatMap(page => page.notifications) ?? [];
  }, [data]);

  const renderItem = useCallback(
    (item: ListRenderItemInfo<Notification>) => {
      return <NotificationItem item={item.item} />;
    },
    [dataFlat],
  );

  const handleReachedEnd = useCallback(() => {
    if (!hasNextPage && dataFlat.length > 5) {
      setReachedEndIndicator(true);
    }
    if (!isFetchingNextPage) {
      fetchNextPage();
    }
  }, [isFetchingNextPage, dataFlat]);

  return (
    <SafeAreaView style={theme.globalStyles.flex1} edges={['left', 'right', 'bottom']}>
      <FlashList
        onEndReached={handleReachedEnd}
        showsVerticalScrollIndicator={false}
        data={dataFlat}
        keyExtractor={item => item.id}
        estimatedItemSize={50}
        renderItem={renderItem}
        ListEmptyComponent={
          <>
            {!isLoading && <Text style={styles.listEmptyIndicator}>Chưa có thông báo nào</Text>}
            {isLoading && !dataFlat.length && <ActivityIndicator size={'small'} />}
          </>
        }
        ListFooterComponent={<>{showReachedEndIndicator && <Text style={styles.endReachedIndicator}>Bạn đã xem hết!</Text>}</>}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  endReachedIndicator: {
    textAlign: 'center',
    fontSize: theme.typography.base,
    marginTop: theme.spacing.s,
    color: theme.colors.textLight,
  },
  listEmptyIndicator: {
    textAlign: 'center',
    color: theme.colors.textLight,
    marginTop: theme.spacing.l,
    fontSize: theme.typography.base,
  },
});

export default NotificationList;
