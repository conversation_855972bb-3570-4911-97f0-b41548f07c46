import {Text} from '@rneui/themed';
import React from 'react';
import {Image, Linking, Platform, Pressable, StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import Notification from '~utils/types/notification';
import dayjs from 'dayjs';
import {useMarkNotificationRead} from '~utils/api/notifications';
import {parseURLToDeepLink} from '~utils/helpers/common';

type NotificationItemProps = {
  item: Notification;
};

const NotificationItem: React.FC<NotificationItemProps> = props => {
  const mutation = useMarkNotificationRead();

  const handlePress = async () => {
    const url = Platform.OS === 'web' ? props.item.mobile_url : parseURLToDeepLink(props.item.url);
    if (await Linking.canOpenURL(url)) {
      Linking.openURL(url);
    }

    mutation.mutate({ids: [props.item.id]});
  };

  return (
    <Pressable
      onPress={handlePress}
      style={[
        styles.container,
        {
          alignItems: 'flex-start',
          backgroundColor: props.item.read_at ? theme.colors.white : '#e6f2fe',
        },
      ]}>
      {Boolean(props.item.image) && (
        <Image
          source={{
            uri: props.item.image,
          }}
          style={[{width: 60, height: 60, marginTop: theme.spacing.s}, theme.globalStyles.mr2, theme.globalStyles.roundedFull]}
        />
      )}
      <View style={[theme.globalStyles.flex1, {marginLeft: theme.spacing.s}]}>
        <Text style={styles.title}>{props.item.title}</Text>
        <Text style={styles.content}>{props.item.content}</Text>
        <Text style={styles.time}>{dayjs(props.item.created_at).fromNow()}</Text>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 12,
    ...theme.globalStyles.paddingH12,
    ...theme.globalStyles.flexRow,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray10,
    backgroundColor: theme.colors.white,
  },
  title: {
    fontSize: theme.typography.base,
    fontWeight: '500',
  },
  content: {
    fontSize: theme.typography.sm,
    color: theme.colors.textLight,
  },
  time: {
    marginTop: theme.spacing.s,
    fontSize: theme.typography.sm,
    color: theme.colors.textLight,
  },
});

export default NotificationItem;
