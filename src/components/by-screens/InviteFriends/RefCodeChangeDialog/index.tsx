import {Text, Dialog, Input} from '@rneui/themed';
import React, {useCallback, useState} from 'react';
import {ActivityIndicator, Pressable, StyleSheet, View} from 'react-native';
import {checkRefCodeAvailable, linkRefCode, useGetCurrentUser} from '~utils/api/auth';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useDebounce, useUpdateEffect} from '~hooks';
import {useQueryClient} from '@tanstack/react-query';
import {useToast} from 'react-native-toast-notifications';

type RefCodeChangeDialogProps = {
  open: boolean;
  onClose: () => void;
};

const RefCodeChangeDialog: React.FC<RefCodeChangeDialogProps> = props => {
  const {data} = useGetCurrentUser();
  const [isCheckingRefCode, setCheckingRefCode] = useState(false);
  const [refCode, setRefCode] = useState<string>(data?.ref_code ?? '');
  const debouncedRefCode = useDebounce(refCode, 400);
  const [checkRefCodeResult, setCheckRefCodeResult] = useState<{available: boolean; suggest: string[]} | null>(null);
  const [refCodeError, setRefCodeError] = useState('');
  const [isSubmiting, setSubmiting] = useState(false);
  const queryClient = useQueryClient();
  const toast = useToast();

  useUpdateEffect(() => {
    setRefCode(data?.ref_code as any);
  }, [data?.ref_code]);

  useUpdateEffect(() => {
    if (debouncedRefCode) {
      setCheckingRefCode(true);
      setCheckRefCodeResult(null);
      setRefCodeError('');
      checkRefCodeAvailable(debouncedRefCode)
        .then(setCheckRefCodeResult)
        .catch(err => {
          setRefCodeError(err.response?.data?.errors?.code ?? '');
        })
        .finally(() => {
          setCheckingRefCode(false);
        });
    }
  }, [debouncedRefCode]);

  const handleSubmit = useCallback(async () => {
    setSubmiting(true);
    try {
      await linkRefCode(refCode);
      queryClient.invalidateQueries(['user']);
    } catch (error: any) {
      toast.show(error?.response?.data?.message ?? 'Có lỗi xảy ra');
    }
    setSubmiting(false);
    setCheckRefCodeResult(null);
    props.onClose();
  }, [refCode]);

  return (
    <Dialog isVisible={props.open} onRequestClose={props.onClose}>
      <Dialog.Title title="Thay đổi mã giới thiệu" titleStyle={{color: theme.colors.text}} />

      <View style={{marginVertical: theme.spacing.m}}>
        <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight, marginBottom: theme.spacing.m}}>
          Link giới thiệu: https://dropship.thitruongsi.com/invite?ref_code=
          <Text
            style={{
              fontSize: theme.typography.base,
              color: theme.colors.textLight,
              fontWeight: 'bold',
            }}>
            {refCode}
          </Text>
        </Text>
        <View style={{position: 'relative'}}>
          <Input value={refCode} onChangeText={setRefCode} renderErrorMessage={!!refCodeError} errorMessage={refCodeError} containerStyle={{paddingHorizontal: 0, marginBottom: theme.spacing.s}} />
          {
            <Text style={[styles.textSm, {color: checkRefCodeResult?.available === false ? theme.colors.red : theme.colors.primary}]}>
              {checkRefCodeResult?.available === true ? 'Tên bạn chọn có thể sử dụng' : checkRefCodeResult?.available === false ? 'Tên bạn chọn không thể dùng' : ''}
            </Text>
          }
          <View style={{position: 'absolute', top: 10, right: 20}}>
            {!isCheckingRefCode && checkRefCodeResult?.available === true && <Ionicons name="checkmark-circle" size={20} color={theme.colors.primary} />}
            {isCheckingRefCode && <ActivityIndicator />}
          </View>

          {checkRefCodeResult?.available === false && (
            <>
              <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight, marginTop: theme.spacing.s}}>Gợi ý:</Text>
              <View style={[theme.globalStyles.flexRow, {flexWrap: 'wrap'}]}>
                {checkRefCodeResult?.suggest.map(text => (
                  <Pressable
                    onPress={() => setRefCode(text)}
                    key={text}
                    style={{
                      marginBottom: theme.spacing.xs,
                      marginRight: theme.spacing.s,
                    }}>
                    <Text style={{fontSize: theme.typography.base}}>{text}</Text>
                  </Pressable>
                ))}
              </View>
            </>
          )}
        </View>
        {/*  */}

        {/*  */}
      </View>

      <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight}}>Các mã giới thiệu cũ vẫn sẽ còn hiệu lực.</Text>
      <Dialog.Actions>
        <Dialog.Button disabled={isCheckingRefCode} loading={isSubmiting} onPress={handleSubmit}>
          Thay đổi
        </Dialog.Button>
        <Dialog.Button titleStyle={{color: theme.colors.textLight}} onPress={props.onClose}>
          Trở về
        </Dialog.Button>
      </Dialog.Actions>
    </Dialog>
  );
};

const styles = StyleSheet.create({
  textSm: {
    fontSize: theme.typography.base,
  },
});

export default RefCodeChangeDialog;
