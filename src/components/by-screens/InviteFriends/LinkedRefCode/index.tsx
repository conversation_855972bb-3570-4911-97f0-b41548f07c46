import {Input, Text, Dialog} from '@rneui/themed';
import {debounce} from 'lodash/fp';
import React, {useCallback, useMemo, useRef, useState} from 'react';
import {ActivityIndicator, Image, Pressable, StyleSheet, View} from 'react-native';
import {useGetCurrentUser, useUpdateUserInfoMutation} from '~utils/api/auth';
import theme from '~utils/config/themes/theme';
import {getRefCodeDetail} from '~utils/api/auth';
import {ReferralCode} from '~utils/types/user';
import {handleImageDomain} from '~utils/helpers/image';
import {formatPhoneString} from '~utils/helpers/common';
import dayjs from 'dayjs';

const LinkedRefCode = () => {
  const {data} = useGetCurrentUser();
  const [refCodeLinkDialogOpen, setRefCodeLinkDialogOpen] = useState(false);
  const [refCodeDetail, setRefCodeDetail] = useState<ReferralCode | null>(null);
  const [refCodeDetailLoading, setRefCodeDetailLoading] = useState(false);
  const [refCodeInputValue, setRefCodeInputValue] = useState('');
  const mutation = useUpdateUserInfoMutation();

  const debounceGetRefCodeDetail = useRef(
    debounce(1000)(async refCode => {
      try {
        const detail = await getRefCodeDetail(refCode);
        setRefCodeDetail(detail);
      } catch (error: any) {
        if (error?.response?.status === 404) {
        }
      }
      setRefCodeDetailLoading(false);
    }),
  ).current;

  const refCode = useMemo(() => {
    const referralSourceArr = data?.referral_source?.split(':') ?? [];
    if (referralSourceArr[1] === 'mgm') {
      return referralSourceArr[2];
    }

    return null;
  }, [data]);

  const canUpdateReferralSource = useMemo(() => {
    if (data?.created_at) {
      const diffDays = dayjs().diff(data.created_at, 'days');
      if (diffDays <= 7) {
        return true;
      }
    }

    return false;
  }, [data]);

  const handleOpenDialog = useCallback(() => {
    setRefCodeLinkDialogOpen(true);
  }, []);

  const handleCloseDialog = useCallback(() => {
    setRefCodeLinkDialogOpen(false);
  }, []);

  const handleChangeRefCode = useCallback((text: string) => {
    setRefCodeInputValue(text);
    setRefCodeDetailLoading(true);
    setRefCodeDetail(null);
    debounceGetRefCodeDetail(text);
  }, []);

  const handleLinkRefCode = useCallback(async () => {
    const [platform] = data?.referral_source?.split(':') ?? [];
    const newReferralSource = [platform || 'none', 'mgm', refCodeInputValue].join(':');
    await mutation
      .mutateAsync({
        referral_source: newReferralSource,
      })
      .catch(err => {});

    handleCloseDialog();
  }, [refCodeInputValue, data]);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Người giới thiệu của bạn: </Text>
      {refCode ? (
        <Text style={styles.refCode}>{refCode}</Text>
      ) : (
        <Pressable onPress={handleOpenDialog} style={{flex: 1}}>
          <View style={{flex: 1, paddingVertical: 8, borderRadius: 10, borderWidth: 1, borderColor: theme.colors.gray30, paddingHorizontal: 8}}>
            <Text style={{color: theme.colors.textLightest}}>Nhập mã giới thiệu</Text>
          </View>
        </Pressable>
      )}

      <Dialog isVisible={refCodeLinkDialogOpen} onBackdropPress={handleCloseDialog}>
        <Dialog.Title title="Nhập mã giới thiệu" titleStyle={{color: theme.colors.text}} />
        <View style={{marginTop: theme.spacing.l}}>
          {canUpdateReferralSource ? (
            <View>
              <Input
                placeholder="Nhập mã giới thiệu"
                onChangeText={handleChangeRefCode}
                value={refCodeInputValue}
                rightIcon={refCodeDetailLoading && <ActivityIndicator />}
                renderErrorMessage={false}
              />
              {refCodeDetail ? (
                <View style={{backgroundColor: theme.colors.gray10, padding: 10, flexDirection: 'row', alignItems: 'center'}}>
                  <Image source={{uri: handleImageDomain(refCodeDetail.user.avatar)}} style={{width: 30, height: 30, borderRadius: 30, marginRight: theme.spacing.s}} />
                  <View>
                    <Text>{refCodeDetail.user.full_name}</Text>
                    <Text style={{color: theme.colors.textLight}}>{formatPhoneString(refCodeDetail.user.phone_number)}</Text>
                  </View>
                </View>
              ) : null}

              <Text style={{textAlign: 'center', marginVertical: theme.spacing.m}}>{!refCodeDetailLoading && !refCodeDetail && refCodeInputValue ? 'Không tìm thấy người giới thiệu' : ''}</Text>
            </View>
          ) : (
            <Text>Tài khoản này không thể thêm người giới thiệu vì đã quá 7 ngày từ khi tạo tài khoản.</Text>
          )}
        </View>
        <Dialog.Actions>
          {canUpdateReferralSource && (
            <Dialog.Button disabled={!refCodeDetail} onPress={handleLinkRefCode} loading={mutation.isLoading}>
              Đồng ý
            </Dialog.Button>
          )}
          <Dialog.Button titleStyle={{color: theme.colors.textLight}} onPress={handleCloseDialog}>
            Trở về
          </Dialog.Button>
        </Dialog.Actions>
      </Dialog>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    padding: 12,
    paddingVertical: 8,
    borderRadius: 12,
    marginTop: theme.spacing.m,
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: theme.typography.base,
  },
  refCode: {
    color: theme.colors.textLight,
    fontSize: theme.typography.base,
  },
});

export default LinkedRefCode;
