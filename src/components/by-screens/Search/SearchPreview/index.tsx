import {Text} from '@rneui/themed';
import React, {useCallback} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {QuickImage} from '~components/shared/QuickImage';
import {useDebounce, useNavigation} from '~hooks';
import {useGetSearchSuggest} from '~utils/api/product';
import {useSearchSuppliers} from '~utils/api/shop';
import theme from '~utils/config/themes/theme';
import {handleImageDomain} from '~utils/helpers/image';
import {SearchShop} from '~utils/types/shop';

type SearchPreviewProps = {
  searchValue: string;
};

const SearchPreview: React.FC<SearchPreviewProps> = props => {
  const searchValueDebounce = useDebounce(props.searchValue, 350);
  const navigation = useNavigation();
  const {data} = useSearchSuppliers({
    limit: 3,
    keyword: searchValueDebounce,
  });
  const {data: suggestKeywords} = useGetSearchSuggest({
    match: 'broad',
    q: searchValueDebounce,
    limit: 6,
  });

  const handlePress = useCallback((shopId: string) => {
    navigation.navigate('SupplierDetail', {
      shopId: shopId,
    });
  }, []);

  const handleKeywordPress = useCallback((keyword: string) => {
    navigation.navigate('SearchResult', {
      keyword: keyword,
    });
  }, []);

  return (
    <View style={styles.container}>
      <View style={{flexDirection: 'row', flexWrap: 'wrap'}}>
        {suggestKeywords?.map(suggestKeyword => (
          <TouchableOpacity style={styles.item} key={suggestKeyword.keyword} onPress={() => handleKeywordPress(suggestKeyword.keyword)}>
            <Text style={styles.suggestKeywordText}>{suggestKeyword.keyword}</Text>
          </TouchableOpacity>
        ))}
      </View>
      {!data?.shops ? null : (
        <>
          <Text style={{fontSize: theme.typography.md, color: theme.colors.textLight, marginBottom: theme.spacing.s, marginTop: theme.spacing.m}}>Nhà cung cấp</Text>
          {data?.shops.map((shop, index) => (
            <PreviewSupplier shop={shop} key={shop.id} borderBottom={index !== data.shops.length - 1} onPress={handlePress} />
          ))}
        </>
      )}
    </View>
  );
};

const PreviewSupplier: React.FC<{shop: SearchShop; borderBottom?: boolean; onPress: (shopId: string) => void}> = props => {
  return (
    <TouchableOpacity
      style={[theme.globalStyles.flexRow, {paddingVertical: theme.spacing.s}, props.borderBottom && {borderBottomWidth: 1, borderBottomColor: theme.colors.gray20}]}
      onPress={() => props.onPress(props.shop.slug)}>
      <QuickImage
        source={{
          uri: handleImageDomain(props.shop.logo),
        }}
        style={{width: 34, height: 34, borderRadius: 100, marginRight: theme.spacing.s}}
      />
      <Text>{props.shop.name}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: theme.spacing.m,
    marginTop: theme.spacing.m,
  },
  item: {
    borderRadius: 4,
    backgroundColor: theme.colors.gray10,
    padding: 8,
    marginBottom: theme.spacing.s,
    marginRight: theme.spacing.s,
  },
  suggestKeywordText: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
  },
});

export default SearchPreview;
