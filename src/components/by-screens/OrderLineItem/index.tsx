import React, {useCallback, useState} from 'react';
import {Image, Pressable, StyleSheet, View} from 'react-native';
import {handleImageDomain} from '~utils/helpers/image';
import {OrderLineItem as OrderLineItemType} from '~utils/types/order';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {formatPriceToString} from '~utils/helpers/price';
import {Text} from '@rneui/themed';
import theme from '~utils/config/themes/theme';
import {useNavigation} from '~hooks';

type OrderLineItemProps = {
  lineItem: OrderLineItemType;
};

const OrderLineItem: React.FC<OrderLineItemProps> = props => {
  const [isPriceDetailExpand, setPriceDetailExpand] = useState(false);
  const navigation = useNavigation();

  const togglePriceDetailExpand = useCallback(() => {
    setPriceDetailExpand(!isPriceDetailExpand);
  }, [isPriceDetailExpand]);

  const handlePress = useCallback(() => {
    navigation.navigate('ProductDetail', {productId: props.lineItem.product_id});
  }, []);

  return (
    <>
      <View key={props.lineItem.id} style={{flexDirection: 'row'}}>
        <Pressable onPress={handlePress}>
          <Image
            style={{width: 70, height: 70, borderWidth: 1, borderColor: 'rgba(0,0,0,.05)', borderRadius: 12}}
            source={{
              uri: handleImageDomain(props.lineItem.image_src),
            }}
          />
        </Pressable>
        <View style={{marginLeft: 12, flex: 1, paddingRight: 20}}>
          <Pressable onPress={handlePress}>
            <Text style={[styles.text1]} numberOfLines={2}>
              {props.lineItem.title}
            </Text>
          </Pressable>

          <Text style={[styles.text2, {marginTop: 8}, styles.textGray]}>{props.lineItem.variant_title === 'Default Title' ? '' : props.lineItem.variant_title}</Text>

          <View style={{flexDirection: 'row', marginTop: 'auto', alignItems: 'center'}}>
            <Text style={[styles.text2]}>SL: {props.lineItem.quantity}</Text>

            <Pressable onPress={togglePriceDetailExpand} style={{marginLeft: 'auto', marginRight: 4, flexDirection: 'row', alignItems: 'center'}}>
              <Text style={[styles.text1]}>{formatPriceToString(parseInt(props.lineItem.price, 10))}</Text>
              <Ionicons name={isPriceDetailExpand ? 'chevron-up-outline' : 'chevron-down-outline'} size={20} color="rgba(0,0,0,.6)" />
            </Pressable>
          </View>
        </View>
      </View>
      {isPriceDetailExpand && (
        <View style={[styles.priceDetail]}>
          <View style={[styles.flexRow, {justifyContent: 'space-between'}]}>
            <Text style={[styles.text2, styles.textGray]}>Giá nhà cung cấp</Text>
            <Text style={[styles.text2, styles.textGray]}>{formatPriceToString(parseInt(props.lineItem.dropship_price, 10))}</Text>
          </View>
          <View style={[styles.flexRow, {justifyContent: 'space-between', marginTop: 4}]}>
            <Text style={[styles.text2, styles.textGray]}>Giá bán của bạn</Text>
            <Text style={[styles.text2]}>{formatPriceToString(parseInt(props.lineItem.price, 10))}</Text>
          </View>
          <View style={[styles.flexRow, {justifyContent: 'space-between', marginTop: 4}]}>
            <Text style={[styles.text2, styles.textGray]}>Lợi nhuận</Text>
            <Text style={[styles.text2, {color: '#008060'}]}>{formatPriceToString((parseInt(props.lineItem.price, 10) - parseInt(props.lineItem.dropship_price, 10)) * props.lineItem.quantity)}</Text>
          </View>
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  text1: {
    fontSize: theme.typography.md,
    fontWeight: '400',
  },
  text2: {
    fontSize: theme.typography.base,
  },

  textGray: {
    color: theme.colors.textLight,
    marginBottom: 4,
  },
  priceDetail: {
    backgroundColor: 'rgba(255, 241, 220,.5)',
    paddingHorizontal: 12,
    paddingVertical: 12,
    marginTop: 8,
    borderRadius: 12,
  },
  flexRow: {
    flexDirection: 'row',
  },
});

export default OrderLineItem;
