import {Button, Switch, Text} from '@rneui/themed';
import React, {useCallback} from 'react';
import {ActivityIndicator, Modal, Platform, StyleSheet, View} from 'react-native';
import {useToast} from 'react-native-toast-notifications';
import {ValidationError} from 'yup';
import {useNavigation} from '~hooks';
import useRateStore, {RateOrderLineItem} from '~hooks/store/useRateStore';
import {addRatings, uploadAttachment} from '~utils/api/review';
import theme from '~utils/config/themes/theme';
import reviewValidate from '~utils/validate/review';

const RateSubmit = () => {
  const navigation = useNavigation();
  const rateStore = useRateStore();
  const toggleRateAsAnons = (value: boolean) => {
    rateStore.updateRateStore('anonymous', value);
  };
  const [isLoading, setLoading] = React.useState(false);
  const toast = useToast();

  const handleSubmit = async () => {
    let isValid = true;
    // validate
    const tobeValidate = {
      line_items: Object.keys(rateStore.line_items).map(lineItemId => ({
        quality_rate: rateStore.line_items[lineItemId].quality_rate,
        comment: rateStore.line_items[lineItemId].comment,
      })),
      delivery_service: rateStore.delivery_service,
      seller_service: rateStore.seller_service,
    };
    await reviewValidate.validate(tobeValidate).catch((err: ValidationError) => {
      isValid = false;
      err.errors.forEach(errString => {
        toast.show(errString, {type: 'danger', duration: 3000, placement: 'center'});
      });
    });

    if (!isValid) {
      return;
    }
    // upload attachments
    setLoading(true);

    try {
      const promises = Object.keys(rateStore.line_items).map(async lineItemId => {
        try {
          let photos: string[] = [];
          let videos: string[] = [];
          const uploadAttachments = rateStore.line_items[lineItemId]._attachments?.map(async att => {
            const res = await uploadAttachment(att, Platform.OS === 'ios');
            if (res) {
              if (res.media_type === 'image') {
                photos.push(res.attachment_id);
              }
              if (res.media_type === 'video') {
                videos.push(res.attachment_id);
              }
            }
          });
          await Promise.all(uploadAttachments ?? []);
          rateStore.updateRateLineItem(lineItemId, 'photos', photos);
          rateStore.updateRateLineItem(lineItemId, 'videos', videos);
          const lineItem: RateOrderLineItem = {
            variant_id: rateStore.line_items[lineItemId].variant_id,
            comment: rateStore.line_items[lineItemId].comment,
            quality_rate: rateStore.line_items[lineItemId].quality_rate,
            photos: photos,
            videos: videos,
          };
          return lineItem;
        } catch (error) {
          return rateStore.line_items[lineItemId];
        }
      });

      const lineItems = await Promise.all(promises);

      await addRatings({
        action_type: 'order',
        action_id: rateStore.action_id,
        anonymous: rateStore.anonymous,
        line_items: lineItems,
        delivery_service: rateStore.delivery_service,
        seller_service: rateStore.seller_service,
        comment: rateStore.comment,
      });

      toast.show('Đã gửi đánh giá', {placement: 'center'});
      setTimeout(() => {
        navigation.goBack();
      }, 300);
    } catch (error: any) {
      toast.show(error?.response?.data?.message ?? 'Có lỗi xảy ra', {placement: 'center', type: 'danger'});
    }
    setLoading(false);
  };

  return (
    <View style={styles.container}>
      <View style={styles.anonsContainer}>
        <Switch
          trackColor={{
            false: theme.colors.gray30,
            true: theme.colors.primary,
          }}
          thumbColor={rateStore.anonymous ? theme.colors.primary : theme.colors.gray50}
          value={rateStore.anonymous}
          onValueChange={toggleRateAsAnons}
        />
        <Text style={{marginLeft: theme.spacing.s}}>Đánh giá ẩn danh</Text>
      </View>
      <Button
        size="sm"
        onPress={handleSubmit}
        buttonStyle={{
          borderRadius: 4,
        }}
        titleStyle={{
          fontSize: theme.typography.base,
          fontWeight: '600',
        }}>
        Gửi đánh giá
      </Button>

      <Modal transparent visible={isLoading}>
        <View style={styles.loadingModalContent}>
          <ActivityIndicator size={'large'} color={theme.colors.white} />
          <Text style={{fontSize: theme.typography.base, marginTop: theme.spacing.m, color: theme.colors.white}}>Đang gửi đánh giá</Text>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    paddingHorizontal: theme.spacing.m,
    paddingVertical: theme.spacing.xs,
  },
  anonsContainer: {
    paddingVertical: theme.spacing.xs,
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingModalContent: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default RateSubmit;
