import React, {useCallback, useEffect, useState} from 'react';
import {View} from 'react-native';
import Modal from 'react-native-modal';
import ProductPost from '~components/by-screens/ProductDetail/ProductPost';
import ee, {EventNames} from '~utils/events';
import Product from '~utils/types/product';

const ProductPostModal = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [product, setProduct] = useState<Product | undefined>();

  useEffect(() => {
    ee.on(EventNames.InspirationPageOpenProductPost, handleOpenProductPostEvent);

    return () => {
      ee.off(EventNames.InspirationPageOpenProductPost, handleOpenProductPostEvent);
    };
  }, []);

  const handleClose = useCallback(() => {
    setModalVisible(false);
  }, []);

  const handleOpenProductPostEvent = (receivedProduct: Product) => {
    setProduct(undefined);
    setProduct(receivedProduct);
    setModalVisible(true);
  };

  return (
    <Modal
      isVisible={modalVisible}
      swipeDirection={['right']}
      style={{marginHorizontal: 0, marginBottom: 0, justifyContent: 'flex-end', marginTop: 0}}
      onBackdropPress={handleClose}
      onBackButtonPress={handleClose}
      onSwipeComplete={handleClose}
      propagateSwipe
      avoidKeyboard>
      {product ? (
        <ProductPost
          product={product}
          productId={product.id}
          onClose={() => {
            handleClose();
          }}
        />
      ) : (
        <View />
      )}
    </Modal>
  );
};

export default ProductPostModal;
