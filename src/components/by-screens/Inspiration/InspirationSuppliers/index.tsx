import {Text} from '@rneui/themed';
import React, {useCallback, useRef} from 'react';
import {ActivityIndicator, FlatList, ListRenderItemInfo, RefreshControl, View} from 'react-native';
import {useGetSearchSuppliersInfinite} from '~utils/api/shop';
import SupplierItem from './SupplierItem';
import {SearchShop} from '~utils/types/shop';
import theme from '~utils/config/themes/theme';
import {useScrollToTop} from '@react-navigation/native';

const InspirationSuppliers = () => {
  const flatlistRef = useRef<FlatList>(null);
  const {data, fetchNextPage, hasNextPage, isFetchingNextPage, refetch} = useGetSearchSuppliersInfinite({
    // @ts-ignore
    ref: 'inspiration',
  });
  const [isRefreshing, setRefreshing] = React.useState(false);

  const dataFlat = data?.pages?.flatMap(page => page.shops) || [];

  useScrollToTop(flatlistRef);

  const renderItem = (item: ListRenderItemInfo<SearchShop>) => {
    return <SupplierItem item={item.item} />;
  };

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  }, [refetch]);

  const handleReachedEnd = () => {
    if (!isFetchingNextPage && hasNextPage) {
      fetchNextPage();
    }
  };

  return (
    <FlatList
      ref={flatlistRef}
      style={{backgroundColor: 'black'}}
      data={dataFlat}
      renderItem={renderItem}
      onEndReached={handleReachedEnd}
      showsVerticalScrollIndicator={false}
      ListHeaderComponent={
        <>
          <View style={{height: 100}} />
        </>
      }
      ListFooterComponent={
        isFetchingNextPage ? (
          <View style={{flexDirection: 'row', justifyContent: 'center', marginBottom: 40}}>
            <ActivityIndicator color={theme.colors.white} />
            <Text style={{marginLeft: 8, color: theme.colors.white, fontSize: theme.typography.sm}}>Đang tải thêm kết quả</Text>
          </View>
        ) : null
      }
      refreshControl={<RefreshControl tintColor={theme.colors.white} onRefresh={handleRefresh} refreshing={isRefreshing} />}
    />
  );
};

export default InspirationSuppliers;
