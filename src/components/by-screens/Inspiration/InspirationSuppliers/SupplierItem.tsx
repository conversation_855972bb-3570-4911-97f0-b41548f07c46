import {Text} from '@rneui/themed';
import React from 'react';
import {Pressable, StyleSheet, TouchableOpacity, View} from 'react-native';
import {QuickImage} from '~components/shared/QuickImage';
import theme from '~utils/config/themes/theme';
import {handleImageDomain} from '~utils/helpers/image';
import {SearchShop} from '~utils/types/shop';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {kFormatter} from '~utils/helpers/price';
import {useNavigation} from '~hooks';

type SupplierItemProps = {
  item: SearchShop;
};

const SupplierItem: React.FC<SupplierItemProps> = props => {
  const navigation = useNavigation();
  const handlePress = () => {
    navigation.push('SupplierDetail', {shopId: props.item.slug});
  };

  const handleNavigateSupplierReviews = () => {
    navigation.push('SupplierReviews', {shopId: props.item.id});
  };

  return (
    <Pressable style={styles.container} onPress={handlePress}>
      <View style={styles.infoContainer}>
        <QuickImage
          source={{
            uri: handleImageDomain(props.item.logo),
          }}
          style={styles.shopAvatar}
        />

        <View style={{flex: 1}}>
          <Text style={styles.shopName}>{props.item.name}</Text>
          <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 2}}>
            <Ionicons name="location-outline" size={14} color={theme.colors.textLight} />
            <Text style={styles.city}>{props.item.city}</Text>
          </View>
        </View>
      </View>

      <View style={styles.statistics}>
        <TouchableOpacity style={styles.statisticContainer} onPress={handleNavigateSupplierReviews}>
          {props.item.rating_avg > 0 ? (
            <View style={styles.ratingAvg}>
              <Text style={styles.ratingAvgText}>{props.item.rating_avg}</Text>
              <Ionicons name="star" size={12} color={'#ffc400'} />
            </View>
          ) : (
            <Text style={[styles.statisticValue, props.item.rating_avg ? {} : {color: theme.colors.textLightest}]}>{props.item.rating_avg || 'N/A'}</Text>
          )}
          <Text style={[styles.statisticName]}>{props.item.rating_count ? `${kFormatter(props.item.rating_count)} ` : ''}đánh giá</Text>
        </TouchableOpacity>
        <Text style={styles.separator}>|</Text>

        <View style={styles.statisticContainer}>
          <Text style={[styles.statisticValue]}>{props.item.total_products?.toLocaleString('vi-VN') ?? '?'}</Text>
          <Text style={[styles.statisticName]}>sản phẩm</Text>
        </View>
        <Text style={styles.separator}>|</Text>

        <View style={styles.statisticContainer}>
          <Text style={[styles.statisticValue]}>{props.item.total_follows?.toLocaleString('vi-VN')}</Text>
          <Text style={[styles.statisticName]}>người theo dõi</Text>
        </View>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderRadius: 12,
    marginBottom: 12,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  statistics: {
    marginTop: theme.spacing.m,
    justifyContent: 'center',
    alignItems: 'flex-end',
    flexDirection: 'row',
  },
  shopAvatar: {
    width: 60,
    height: 60,
    borderRadius: 100,
    marginRight: 12,
    flexShrink: 0,
    borderWidth: 1,
    borderColor: theme.colors.gray20,
  },
  shopName: {
    fontSize: theme.typography.md,
    fontWeight: '500',
  },
  city: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
    marginLeft: theme.spacing.xs,
  },
  statisticContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  statisticName: {
    color: theme.colors.textLight,
    fontSize: theme.typography.base,
    marginTop: theme.spacing.xs,
  },
  statisticValue: {
    color: theme.colors.text,
    fontWeight: 'bold',
  },
  ratingAvg: {
    borderRadius: 100,
    borderWidth: 1,
    borderColor: theme.colors.textLightest,
    paddingHorizontal: theme.spacing.m,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 2,
  },
  ratingAvgText: {
    color: theme.colors.primary,
    fontSize: theme.typography.base,
    marginRight: theme.spacing.xs,
  },
  separator: {fontSize: theme.typography.sm, color: theme.colors.textLight, marginHorizontal: theme.spacing.m, alignSelf: 'center'},
});

export default SupplierItem;
