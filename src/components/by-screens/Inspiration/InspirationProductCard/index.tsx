import {Text} from '@rneui/themed';
import React, {useCallback} from 'react';
import {Dimensions, ListRenderItemInfo, Platform, Pressable, StyleSheet, View, Image} from 'react-native';
import SwiperFlatList from 'react-native-swiper-flatlist';
import {QuickImage} from '~components/shared/QuickImage';
import {responsiveWidth} from '~components/shared/web/WebContainer';
import theme from '~utils/config/themes/theme';
import {handleImageDomain} from '~utils/helpers/image';
import {SearchProduct} from '~utils/types/product';
import PriceLine from './PriceLine';
import {formatPriceToString, kFormatter} from '~utils/helpers/price';
import ActionBar from './ActionBar';
import ViewRelatedProducts from './ViewRelatedProducts';
import {useNavigation} from '~hooks';
import Ionicons from 'react-native-vector-icons/Ionicons';

type InspirationProductCardProps = {
  item: SearchProduct;
};

const {width} = Dimensions.get('window');

const imageWidth = Math.min(width, responsiveWidth.sm);

const InspirationProductCard: React.FC<InspirationProductCardProps> = props => {
  const navigation = useNavigation();
  const images = props.item.images?.length ? props.item.images : [props.item.image_thumb];

  const getItemLayout = useCallback(
    (__data: any, itemIndex: number) => {
      return {
        length: imageWidth,
        offset: imageWidth * itemIndex,
        index: itemIndex,
      };
    },
    [imageWidth],
  );

  const handleGoToProductDetail = () => {
    navigation.push('ProductDetail', {
      productId: props.item.product_id,
    });
  };

  const renderItem = (item: ListRenderItemInfo<string>) => {
    return (
      <Pressable style={styles.child} onPress={handleGoToProductDetail}>
        <View style={styles.imageSpacer} />
        <Image
          style={styles.image}
          source={{
            uri: handleImageDomain(item.item),
          }}
        />
      </Pressable>
    );
  };

  return (
    <View style={{alignSelf: 'center', flex: 1, marginBottom: theme.spacing.m, borderRadius: 12, backgroundColor: theme.colors.white, marginHorizontal: 0}}>
      <View style={styles.container}>
        <View style={{position: 'relative'}}>
          <SwiperFlatList
            getItemLayout={getItemLayout}
            data={images}
            renderItem={renderItem}
            initialNumToRender={5}
            showPagination={images.length > 1}
            paginationStyle={{
              marginBottom: -6,
            }}
            paginationStyleItem={{
              width: (imageWidth - 100) / images.length,
              height: 3,
              borderRadius: 30,
              marginHorizontal: theme.spacing.xs,
            }}
            paginationDefaultColor={'rgba(255,255,255,.4)'}
          />
          <ViewRelatedProducts categoryLv2={props.item.category_level2} />
        </View>
        <Pressable style={{padding: theme.spacing.s}} onPress={handleGoToProductDetail}>
          {props.item.badges.length > 0 && (
            <View style={styles.badgesContainer}>
              {props.item.badges.map(badge => (
                <View
                  key={badge.id}
                  style={[
                    theme.globalStyles.flexRow,
                    {
                      backgroundColor: badge.text_bg_color,
                      borderRadius: 2,
                      borderWidth: 1,
                      padding: 2,
                      borderColor: badge.border_color || 'transparent',
                    },
                  ]}>
                  {Boolean(badge.icon) && <QuickImage source={{uri: badge.icon}} style={{width: badge.icon_width, height: badge.icon_height, marginRight: theme.spacing.xs}} />}
                  <Text style={{color: badge.text_color, fontSize: theme.typography.xs}}>{badge.text}</Text>
                </View>
              ))}
            </View>
          )}
          <Text>{props.item.title}</Text>

          <View style={[theme.globalStyles.flexRow, {marginTop: theme.spacing.s, paddingHorizontal: theme.spacing.xs}]}>
            {props.item.rating_avg > 0 && (
              <View style={[theme.globalStyles.flexRow]}>
                <Ionicons name="star" size={12} color={'#ffc400'} />
                <Text style={styles.ratingAvgText} accessibilityLabel={`${props.item.rating_avg?.toFixed(1)} sao`}>
                  {props.item.rating_avg?.toFixed(1)}
                </Text>
              </View>
            )}
            {props.item.total_sales > 0 && (
              <>
                {props.item.rating_avg > 0 && <View style={styles.totalSaleSeparator} />}
                <Text style={styles.totalSaleText}>Đã bán {kFormatter(props.item.total_sales)}</Text>
              </>
            )}
          </View>

          <View style={{marginVertical: theme.spacing.m}}>
            <PriceLine label="Giá nhà cung cấp" price={formatPriceToString(props.item.dropship_price)} />
            <PriceLine label="Giá bán thị trường" price={formatPriceToString(props.item.market_price)} />
            <PriceLine label="Lợi nhuận" price={formatPriceToString(props.item.dropship_profit)} highlight />
          </View>
        </Pressable>
        <View style={{padding: theme.spacing.s, paddingTop: 0}}>
          <ActionBar productId={props.item.product_id} shopId={props.item.shop_id} />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    maxWidth: imageWidth,
  },
  child: {
    width: Platform.OS === 'web' ? Math.min(imageWidth, responsiveWidth.sm) : imageWidth,
    justifyContent: 'center',
    position: 'relative',
  },
  imageSpacer: {paddingTop: '100%'},
  image: {
    ...StyleSheet.absoluteFillObject,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },

  badgesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: theme.spacing.s,
  },
  ratingAvgText: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
    marginLeft: 2,
  },
  totalSaleText: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
  },
  totalSaleSeparator: {
    width: 1,
    height: 8,
    backgroundColor: theme.colors.gray40,
    marginHorizontal: theme.spacing.xs,
  },
});

export default InspirationProductCard;
