import {Button} from '@rneui/themed';
import {useQueryClient} from '@tanstack/react-query';
import React, {useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {getProductDetail} from '~utils/api/product';
import theme from '~utils/config/themes/theme';
import ee, {EventNames} from '~utils/events';
import {useToast} from 'react-native-toast-notifications';
import {useNavigation} from '~hooks';

type ActionBarProps = {
  productId: number;
  shopId: string;
};

const ActionBar: React.FC<ActionBarProps> = props => {
  const [shareBtnLoading, setShareBtnLoading] = useState(false);
  const queryClient = useQueryClient();
  const toast = useToast();
  const navigation = useNavigation();

  const handleShareProduct = async () => {
    try {
      setShareBtnLoading(true);
      const data = await queryClient.fetchQuery(['product', props.productId], () => getProductDetail(props.productId));
      setShareBtnLoading(false);

      if (data) {
        ee.emit(EventNames.InspirationPageOpenProductPost, data);
      }
    } catch (error) {
      setShareBtnLoading(false);
      toast.show(error as string, {
        placement: 'center',
        type: 'danger',
      });
    }
  };

  const handleCreateOrder = () => {
    navigation.navigate('SupplierDetail', {shopId: props.shopId});
  };

  return (
    <View style={styles.container}>
      <Button
        size="sm"
        containerStyle={{marginRight: theme.spacing.s, flex: 1}}
        titleStyle={{color: theme.colors.black, fontWeight: '600', fontSize: theme.typography.base}}
        type="outline"
        icon={{
          type: 'ionicon',
          name: 'chevron-forward',
          size: 18,
          color: theme.colors.black,
        }}
        iconRight
        buttonStyle={{
          paddingVertical: 8,
          paddingHorizontal: 18,
          borderRadius: 30,
          borderColor: theme.colors.white,
        }}
        onPress={handleCreateOrder}>
        Đến gian hàng
      </Button>
      <Button
        size="sm"
        containerStyle={{flex: 1}}
        titleStyle={{color: '#fff', fontWeight: '600', fontSize: theme.typography.base}}
        loading={shareBtnLoading}
        icon={{
          type: 'material-community',
          name: 'share',
          size: 18,
          color: theme.colors.white,
        }}
        buttonStyle={{
          paddingVertical: 8,
          paddingHorizontal: 12,
          borderRadius: 30,
          backgroundColor: 'rgb(32, 137, 220)',
        }}
        onPress={handleShareProduct}>
        Đăng bán
      </Button>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.s,
  },
});

export default ActionBar;
