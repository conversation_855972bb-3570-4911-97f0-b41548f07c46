import React from 'react';
import {TouchableOpacity, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '~hooks';

type ViewRelatedProductsProps = {
  categoryLv2: string;
};

const ViewRelatedProducts: React.FC<ViewRelatedProductsProps> = props => {
  const navigation = useNavigation();
  return (
    <View
      style={{
        position: 'absolute',
        bottom: 30,
        left: 0,
        zIndex: 100,
        backgroundColor: theme.colors.white,
        paddingHorizontal: theme.spacing.s,
        borderTopRightRadius: 30,
        borderBottomRightRadius: 30,

        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,

        elevation: 5,
      }}>
      <TouchableOpacity
        style={{position: 'relative'}}
        onPress={() => {
          navigation.navigate('CategoryDetail', {
            categoryId: props.categoryLv2,
            slug: 'danh-muc',
          });
        }}>
        <Ionicons name="scan-outline" size={30} color={theme.colors.textLight} />
        <Ionicons name="search-outline" size={16} color={theme.colors.text} style={{position: 'absolute', top: 8, left: 6}} />
      </TouchableOpacity>
    </View>
  );
};

export default ViewRelatedProducts;
