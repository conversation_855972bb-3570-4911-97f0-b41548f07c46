import {Text} from '@rneui/themed';
import React from 'react';
import {StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';

type PriceLineProps = {
  label: string;
  price: string;
  highlight?: boolean;
};

const PriceLine: React.FC<PriceLineProps> = props => {
  return (
    <View style={styles.container}>
      <Text style={[styles.label, props.highlight && {color: theme.colors.text}]}>{props.label}</Text>
      <Text style={[styles.price, props.highlight && {fontSize: theme.typography.lg, color: theme.colors.primary, fontWeight: '600'}]}>{props.price}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: theme.spacing.xs,
  },
  label: {
    color: theme.colors.textLight,
    width: 150,
    fontSize: theme.typography.base,
  },
  price: {
    color: theme.colors.text,
    fontSize: theme.typography.base,
  },
});

export default PriceLine;
