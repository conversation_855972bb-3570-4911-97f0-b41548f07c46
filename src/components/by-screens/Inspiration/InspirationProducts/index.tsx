import {useScrollToTop} from '@react-navigation/native';
import {Text} from '@rneui/themed';
import {InfiniteData} from '@tanstack/react-query';
import React, {useCallback, useRef, useState} from 'react';
import {ActivityIndicator, FlatList, ListRenderItemInfo, RefreshControl, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import InspirationProductCard from '~components/by-screens/Inspiration/InspirationProductCard';
import ProductPostModal from '~components/by-screens/Inspiration/ProductPostModal';
import SatisfiedFeedback from '~components/by-screens/Inspiration/SatisfiedFeedback';
import {GetSmartCollectionProductsResponse, useGetSmartCollection} from '~utils/api/product';
import theme from '~utils/config/themes/theme';
import {SearchProduct} from '~utils/types/product';

type DynamicFlatlistCSFeedback = {
  type: 'satisfied_feedback';
  item: any;
};

type DynamicFlatlistProduct = {
  type: 'inspiration_product_card';
  item: SearchProduct;
};

type InspirationFlatlistItem = DynamicFlatlistProduct | DynamicFlatlistCSFeedback;

const InspirationProducts = () => {
  const {data, fetchNextPage, isFetchingNextPage, hasNextPage, refetch} = useGetSmartCollection('kham-pha', {});
  const insets = useSafeAreaInsets();
  const flatlistRef = useRef<FlatList>(null);
  const [isRefreshing, setRefreshing] = useState(false);

  useScrollToTop(flatlistRef);

  const withDynamicFlatlist = (flatListData: InfiniteData<GetSmartCollectionProductsResponse> | undefined) => {
    const dynamicFlatlist: InspirationFlatlistItem[] = [];
    for (const page of flatListData?.pages ?? []) {
      for (const product of page.products) {
        dynamicFlatlist.push({type: 'product' as any, item: product});
      }
    }

    if (dynamicFlatlist.length > 14) {
      dynamicFlatlist.splice(14, 0, {
        type: 'satisfied_feedback',
        item: {
          id: 'satisfied_feedback_1',
        },
      });
    }

    return dynamicFlatlist;
  };

  const dataFlat = withDynamicFlatlist(data);

  const renderItem = (item: ListRenderItemInfo<InspirationFlatlistItem>) => {
    if (item.item.type === 'satisfied_feedback') {
      return <SatisfiedFeedback />;
    }
    return <InspirationProductCard item={item.item.item} />;
  };

  const handleReachedEnd = () => {
    if (!isFetchingNextPage && hasNextPage) {
      fetchNextPage();
    }
  };

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  }, [refetch]);

  return (
    <>
      <FlatList
        ref={flatlistRef}
        style={{backgroundColor: 'black'}}
        data={dataFlat}
        renderItem={renderItem}
        onEndReached={handleReachedEnd}
        maxToRenderPerBatch={3}
        initialNumToRender={5}
        windowSize={5}
        ListHeaderComponent={
          <>
            <View style={{height: 100}} />
          </>
        }
        showsVerticalScrollIndicator={false}
        ListFooterComponent={
          isFetchingNextPage ? (
            <View style={{flexDirection: 'row', justifyContent: 'center', marginBottom: 40}}>
              <ActivityIndicator color={theme.colors.white} />
              <Text style={{marginLeft: 8, color: theme.colors.white, fontSize: theme.typography.sm}}>Đang tải thêm kết quả</Text>
            </View>
          ) : null
        }
        refreshControl={<RefreshControl tintColor={theme.colors.white} onRefresh={handleRefresh} refreshing={isRefreshing} />}
      />
      <ProductPostModal />
    </>
  );
};

export default InspirationProducts;
