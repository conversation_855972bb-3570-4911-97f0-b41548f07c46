import AsyncStorage from '@react-native-async-storage/async-storage';
import {AirbnbRating, Button, Text} from '@rneui/themed';
import React, {useCallback, useEffect, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {useNavigation} from '~hooks';
import {createFeedback} from '~utils/api/analytics';
import {ASYNC_STORAGE_KEYS} from '~utils/config';
import theme from '~utils/config/themes/theme';

const SatisfiedFeedback = () => {
  const [value, setValue] = useState(0);
  const [showRate, setShowRate] = useState(false);
  const [isSuccess, setSuccess] = useState(false);
  const navigation = useNavigation();

  useEffect(() => {
    checkShowRateApp();
  }, []);

  const checkShowRateApp = useCallback(async () => {
    try {
      // eslint-disable-next-line no-extra-boolean-cast
      setShowRate(!Boolean(await AsyncStorage.getItem(ASYNC_STORAGE_KEYS.INSPIRATION_RATED)));
    } catch (error) {}
  }, []);

  const handleSubmitRating = useCallback((star: number) => {
    setValue(star);
    AsyncStorage.setItem(ASYNC_STORAGE_KEYS.INSPIRATION_RATED, String(star));
    createFeedback({
      features: 'dropship-inspiration',
      response: star,
      feedback: '',
    });
    setShowRate(prev => !prev);
    setSuccess(true);
  }, []);

  if (!showRate && !isSuccess) {
    return null;
  }

  return (
    <>
      <View style={styles.container}>
        {showRate && (
          <>
            <Text>Đánh giá trải nghiệm này!</Text>
            <AirbnbRating
              count={5}
              reviews={['Rất tệ', 'Tệ', 'Bình thường', 'Tốt', 'Rất tốt']}
              defaultRating={value}
              onFinishRating={handleSubmitRating}
              size={30}
              reviewSize={theme.typography.lg1}
              showRating={false}
            />
          </>
        )}
        {isSuccess && (
          <>
            <Text style={{marginTop: theme.spacing.m}}>Cảm ơn bạn đã đánh giá!</Text>

            <Button
              type="clear"
              icon={{
                type: 'ionicon',
                name: 'chatbox-outline',
                size: 18,
              }}
              size="sm"
              titleStyle={{fontSize: theme.typography.base, fontWeight: '600', color: theme.colors.black}}
              buttonStyle={{backgroundColor: theme.colors.gray10}}
              onPress={() => {
                navigation.navigate('Feedback', {
                  initialStar: value,
                });
              }}>
              Gửi thêm góp ý
            </Button>
          </>
        )}
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    paddingVertical: theme.spacing.xs,
    marginHorizontal: theme.spacing.m,
    marginBottom: theme.spacing.m,
    borderRadius: 12,
    alignItems: 'center',
    minHeight: 60,
  },
});

export default SatisfiedFeedback;
