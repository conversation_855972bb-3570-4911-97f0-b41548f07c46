import {Image, Text} from '@rneui/themed';
import React from 'react';
import {Pressable, StyleSheet, View} from 'react-native';
import {useSearchSuppliers} from '~utils/api/shop';
import theme from '~utils/config/themes/theme';
import {handleImageDomain} from '~utils/helpers/image';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '~hooks';
import {SearchShop} from '~utils/types/shop';

type RelatedSupplierProps = {
  keyword?: string;
};

const RelatedSupplier: React.FC<RelatedSupplierProps> = props => {
  const navigation = useNavigation();
  const {data} = useSearchSuppliers({
    limit: 1,
    keyword: props.keyword,
  });

  const handleNavigation = (shop: SearchShop) => {
    navigation.navigate('SupplierDetail', {shopId: shop.slug});
  };

  if (!data?.shops.length || !props.keyword) {
    return null;
  }

  return (
    <View style={{padding: theme.spacing.s, backgroundColor: theme.colors.white}}>
      <Text style={{color: theme.colors.textLight, fontSize: theme.typography.sm, marginBottom: theme.spacing.s}}>Nhà cung cấp</Text>
      {data.shops.map(shop => (
        <Pressable onPress={() => handleNavigation(shop)}>
          <View style={styles.container}>
            <Image source={{uri: handleImageDomain(shop.logo)}} style={styles.shopAvatar} />
            <View style={styles.shopInfoRight}>
              <View style={{flexDirection: 'row'}}>
                <Text style={styles.shopName}>{shop.name}</Text>
              </View>
            </View>
            <View style={{marginLeft: 'auto', marginRight: 8}}>{<Ionicons name="chevron-forward" size={20} color={theme.colors.textLight} />}</View>
          </View>
        </Pressable>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.white,
    borderRadius: theme.spacing.s,
    padding: theme.spacing.s,
    shadowColor: theme.colors.gray50,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
  shopAvatar: {
    width: 40,
    height: 40,
    borderRadius: 100,
    borderColor: theme.colors.gray20,
    borderWidth: 1,
  },
  shopInfoRight: {
    marginLeft: theme.spacing.s,
    flex: 1,
  },

  shopName: {
    fontSize: theme.typography.md,
    fontWeight: '500',
    color: theme.colors.text,
    flex: 1,
  },
  textSmall: {
    fontSize: theme.typography.base,
    color: theme.colors.textLight,
  },
});

export default RelatedSupplier;
