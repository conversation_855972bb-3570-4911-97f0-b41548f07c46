import {Text} from '@rneui/themed';
import {FlashList, ListRenderItemInfo} from '@shopify/flash-list';
import React, {useCallback, useMemo, useState} from 'react';
import {ActivityIndicator, RefreshControl, StyleSheet, View} from 'react-native';
import TransactionItem from '~components/by-screens/AccountAndPayment/TransactionItem';
import {useGetWalletTransactions, WalletTransactionType} from '~utils/api/wallet';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {WalletTransaction} from '~utils/types/finance';
import {SafeAreaView} from 'react-native-safe-area-context';
import useRefreshOnFocus from '~hooks/useRefreshOnFocus';
import EndReachedIndicator from '~components/shared/EndReachedIndicator';

type TransactionListProps = {
  type: string;
};

const TransactionList: React.FC<TransactionListProps> = props => {
  const [isRefreshing, setRefreshing] = useState(false);
  const {
    data: walletTransactions,
    isLoading,
    refetch,
    isFetchingNextPage,
    fetchNextPage,
    hasNextPage,
  } = useGetWalletTransactions({limit: 20, page: 1, types: props.type === 'all' ? null : (props.type as WalletTransactionType)});
  const [showEndReached, setShowEndReached] = useState(false);

  useRefreshOnFocus(refetch);

  const dataFlat = useMemo(() => {
    return walletTransactions?.pages.flatMap(page => page?.transactions ?? []) ?? [];
  }, [walletTransactions]);

  const renderItem = useCallback(
    (item: ListRenderItemInfo<WalletTransaction>) => {
      return (
        <View style={styles.item}>
          <TransactionItem transaction={item.item} />
        </View>
      );
    },
    [dataFlat],
  );

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  }, [refetch]);

  const handleEndReached = useCallback(() => {
    if (!hasNextPage) {
      setShowEndReached(true);
      return;
    }
    if (!isFetchingNextPage) {
      fetchNextPage();
    }
  }, [fetchNextPage, isFetchingNextPage, hasNextPage]);

  return (
    <SafeAreaView
      edges={['bottom', 'left', 'right']}
      style={{
        flex: 1,
        backgroundColor: theme.colors.white,
      }}>
      <FlashList
        onEndReached={handleEndReached}
        refreshControl={<RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />}
        renderItem={renderItem}
        data={dataFlat}
        estimatedItemSize={102}
        keyExtractor={item => String(item.id)}
        ListEmptyComponent={
          isLoading ? (
            <ActivityIndicator />
          ) : (
            <View style={{marginVertical: theme.spacing.xl, alignItems: 'center'}}>
              <Ionicons name="document-text-outline" size={60} color={theme.colors.textLightest} />
              <Text style={{textAlign: 'center', marginTop: theme.spacing.s, color: theme.colors.textLightest}}>Chưa có giao dịch nào</Text>
            </View>
          )
        }
        ListFooterComponent={<>{showEndReached && dataFlat.length >= 10 && <EndReachedIndicator />}</>}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  item: {
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
});

export default TransactionList;
