import {NativeStackHeaderProps} from '@react-navigation/native-stack';
import React, {useCallback, useMemo} from 'react';
import {Linking, Platform, Share, StyleSheet, ToastAndroid, View} from 'react-native';
import {Menu, MenuOption, MenuOptions, MenuTrigger, renderers} from 'react-native-popup-menu';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Clipboard from '@react-native-clipboard/clipboard';
import {Text} from '@rneui/themed';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import theme from '~utils/config/themes/theme';
const {Popover} = renderers;

const WebviewScreenHeader: React.FC<NativeStackHeaderProps> = props => {
  const insets = useSafeAreaInsets();
  const handleClose = useCallback(() => {
    props.navigation.goBack();
  }, []);

  const uri = useMemo(() => {
    return (props.route.params as any)?.url ?? '';
  }, [props.route]);

  const handleViewOnBrowser = useCallback(async () => {
    const canOpenUrl = await Linking.canOpenURL(uri);
    if (canOpenUrl) {
      Linking.openURL(uri);
    }
  }, [uri]);

  const handleCopyLink = useCallback(() => {
    Clipboard.setString(uri);
    if (Platform.OS === 'android') {
      ToastAndroid.showWithGravityAndOffset('Đã sao chép', ToastAndroid.SHORT, ToastAndroid.BOTTOM, 0, 50);
    }
  }, [uri]);

  const handleShare = useCallback(() => {
    Share.share({
      message: 'Chia sẻ link này',
      url: uri,
    });
  }, [uri]);

  return (
    <View style={[{paddingTop: insets.top, backgroundColor: theme.colors.white}]}>
      <View style={styles.container}>
        <View style={styles.closeBtnContainer}>
          <Ionicons name="close-outline" size={30} onPress={handleClose} color="#616161" />
        </View>
        <Text style={{fontSize: 16}} numberOfLines={1}>
          {(props.route.params as any)?.title ?? props.options.title}
        </Text>
        <Menu style={{position: 'absolute', right: 8}} renderer={Popover} rendererProps={{placement: 'bottom', anchorStyle: {backgroundColor: '#f5f5f5'}}}>
          <MenuTrigger>
            <Ionicons name="ellipsis-vertical" size={20} color="#616161" style={{padding: 4}} />
          </MenuTrigger>
          <MenuOptions
            customStyles={{
              optionText: {color: '#333'},
              optionsContainer: {
                backgroundColor: '#f5f5f5',
                borderRadius: 8,
              },
              optionWrapper: {
                padding: 12,
                borderBottomColor: 'rgba(0,0,0,.1)',
                borderBottomWidth: 1,
              },
            }}>
            <MenuOption text="Xem trên trình duyệt" onSelect={handleViewOnBrowser} />
            <MenuOption text="Sao chép link" onSelect={handleCopyLink} />
            <MenuOption text="Chia sẻ" onSelect={handleShare} />
          </MenuOptions>
        </Menu>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 50,
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 45,
    position: 'relative',
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray10,
  },
  closeBtnContainer: {
    position: 'absolute',
    left: 8,
  },
});

export default WebviewScreenHeader;
