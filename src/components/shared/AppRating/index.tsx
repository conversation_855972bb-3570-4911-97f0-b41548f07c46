import {AirbnbRating, Text} from '@rneui/themed';
import React, {useCallback, useState} from 'react';
import {Platform, View} from 'react-native';
import theme from '~utils/config/themes/theme';
// @ts-ignore
import AirBnBStar from '~assets/airbnbstar.png';

type AppRatingProps = {
  initialStar?: number;
  onChange: (star: number) => void;
  horizontal?: boolean;
  title?: string;
  showRating?: boolean;
};

const AppRating: React.FC<AppRatingProps> = props => {
  const [value, setValue] = useState(props.initialStar ?? 0);

  const handleChange = useCallback((star: number) => {
    setValue(star);
    if (props.onChange) {
      props.onChange(star);
    }
  }, []);

  return (
    <View
      style={{
        marginVertical: 20,
        ...(props.horizontal ? {flexDirection: 'row', alignItems: 'center'} : {}),
      }}>
      <Text style={{fontSize: theme.typography.base, textAlign: 'center', ...(props.horizontal ? {width: '40%', marginRight: 'auto', textAlign: 'left'} : {})}}>
        {props.title || 'Trải nghiệm của bạn thế nào?'}
      </Text>
      <AirbnbRating
        count={5}
        reviews={['Rất tệ', 'Tệ', 'Bình thường', 'Tốt', 'Rất tốt']}
        defaultRating={value}
        starImage={Platform.OS === 'web' ? AirBnBStar : undefined}
        onFinishRating={handleChange}
        size={30}
        reviewColor={getSelectedColor(value)}
        selectedColor={getSelectedColor(value)}
        reviewSize={theme.typography.lg1}
        showRating={props.showRating}
      />
    </View>
  );
};

const getSelectedColor = (value: number) => {
  switch (true) {
    case value < 3:
      return theme.colors.red;

    case value > 3:
      return theme.colors.primary;

    default:
      return undefined;
  }
};

export default AppRating;
