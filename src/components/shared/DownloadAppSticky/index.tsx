import {Button, Text} from '@rneui/themed';
import React, {useCallback, useEffect, useState} from 'react';
import {Image, Linking, Pressable, StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {sendWebViewEvent} from '~utils/helpers/webview';
// @ts-ignore
import LogoDropship70 from '~assets/logo-dropship-icon-square-x70.png';
import {useExtendContainerWidth} from '../web/WebContainer';

const DownloadAppSticky = () => {
  const [isPressed, setPressed] = useState(false);

  // useEffect(() => {
  //   if (!isPressed) {
  //     const appRoot = document.getElementById('app-root');
  //     if (appRoot) {
  //       appRoot.style.paddingTop = '70px';
  //     }
  //   }
  // }, [isPressed]);

  const handleGoToDownloadApp = useCallback(() => {
    if ((window as any).ReactNativeWebView) {
      sendWebViewEvent('OpenURL', 'https://ttsdropship.page.link/uEsh');
    } else {
      Linking.openURL('https://ttsdropship.page.link/uEsh');
    }
    setPressed(true);
  }, []);

  if (__DEV__) {
    return null;
  }

  return (
    // <View style={styles.container}>
    //   <View
    //     style={{
    //       backgroundColor: theme.colors.white,
    //       paddingHorizontal: 8,
    //       paddingVertical: 4,
    //       flexDirection: 'row',
    //       alignItems: 'center',
    //       width: '100%',
    //       maxWidth: 992,
    //       alignSelf: 'center',
    //       borderBottomWidth: 1,
    //       borderBottomColor: theme.colors.gray10,
    //     }}>
    //     <View>
    //       <Image source={LogoDropship70} style={{width: 50, height: 50, borderRadius: 8}} />
    //     </View>

    //     <View style={{marginLeft: theme.spacing.s, flex: 1}}>
    //       <Text style={{fontSize: theme.typography.md, fontWeight: 'bold'}}>Tải app TTS Dropship</Text>
    //       <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight}}>Bán hàng cộng tác viên dễ dàng hơn</Text>
    //     </View>

    //     <Button
    //       size="sm"
    //       buttonStyle={{borderRadius: 8}}
    //       containerStyle={{marginLeft: 'auto'}}
    //       icon={{type: 'ionicon', name: 'arrow-down-circle-outline', size: 24, color: theme.colors.white}}
    //       onPress={handleGoToDownloadApp}>
    //       Tải app
    //     </Button>
    //   </View>
    // </View>
    <Pressable style={styles.oldContainer} onPress={handleGoToDownloadApp}>
      <Ionicons name="download-outline" size={20} color={theme.colors.white} />
      <Text style={{color: theme.colors.white, fontSize: theme.typography.base, marginLeft: 4}}>Mở APP</Text>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    // @ts-ignore
    position: 'fixed',
    top: 0,
    right: 0,
    left: 0,
  },
  oldContainer: {
    position: 'absolute',
    top: 200,
    right: 0,
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    flexDirection: 'row',
    alignItems: 'center',
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
  },
});

export default DownloadAppSticky;
