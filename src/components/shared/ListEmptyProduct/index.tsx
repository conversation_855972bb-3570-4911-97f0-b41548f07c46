import {Text} from '@rneui/themed';
import React from 'react';
import {Image, View} from 'react-native';
// @ts-ignore
import EmptyIllustrator from '~assets/empty-illustrator.png';
import theme from '~utils/config/themes/theme';

type ListEmptyProductProps = {
  content?: string;
};

const ListEmptyProduct: React.FC<ListEmptyProductProps> = props => {
  return (
    <View style={{alignSelf: 'center', marginTop: 20, alignItems: 'center', backgroundColor: theme.colors.white}}>
      <Image source={EmptyIllustrator} style={{width: 250, height: 250}} />
      <Text style={{fontSize: theme.typography.lg2, fontWeight: '500', marginBottom: 8, marginTop: 20, color: theme.colors.text}}>Danh sách trống</Text>
      <Text style={{fontSize: 16, textAlign: 'center', color: theme.colors.textLight, paddingHorizontal: theme.spacing.xl}}>{props.content}</Text>
    </View>
  );
};

export default ListEmptyProduct;
