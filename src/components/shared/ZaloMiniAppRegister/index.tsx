import {Button, Dialog, Image, Text} from '@rneui/themed';
import React, {useEffect, useState} from 'react';
import {ActivityIndicator, StyleSheet, TouchableOpacity, View} from 'react-native';
import theme from '~utils/config/themes/theme';
// @ts-ignore
import HappyRich from '~assets/happy-rich.png';
import api from '~utils/helpers/zmp-sdk';
import {GetUserInfoReturns} from 'zmp-sdk';
import {verifyOTPZalo, zmpExchangeToken} from '~utils/api/auth';
import {useAuthActions, useNavigation} from '~hooks';
import Ionicons from 'react-native-vector-icons/Ionicons';

const ZaloMiniAppRegister = () => {
  const [currentZaloUser, setCurrentZaloUser] = useState<GetUserInfoReturns['userInfo']>();
  const [isAuthLoading, setAuthLoading] = useState(false);
  const {login} = useAuthActions();
  const navigation = useNavigation();
  const [isShowGetPhoneConfirm, setShowGetPhoneConfirm] = useState(false);
  const [isGetPhoneGranted, setGetPhoneGranted] = useState(false);

  useEffect(() => {
    api?.getUserInfo({
      success(res) {
        setCurrentZaloUser(res.userInfo);
      },
    });
  }, []);

  const handlePressZaloLogin = async (phoneToken?: string) => {
    if (!isGetPhoneGranted && !isShowGetPhoneConfirm) {
      setShowGetPhoneConfirm(true);
      return;
    }

    setAuthLoading(true);
    const token = await api?.getAccessToken({});
    if (token) {
      const res = await zmpExchangeToken(token);

      if (res) {
        if (res.token) {
          // user already created, login
          await login({
            access_token: res.token.access_token,
            refresh_token: res.token.refresh_token,
          });
          setAuthLoading(false);
          navigation.goBack();
        } else {
          // user not exist or verification required, navigate to OTP
          if (phoneToken) {
            const result = await verifyOTPZalo({
              access_token: token,
              oauth_key: res.oauth_user.key,
              code: phoneToken,
            });

            if (result.token) {
              await login({
                access_token: result.token.access_token,
                refresh_token: result.token.refresh_token,
              });
              setAuthLoading(false);
              navigation.goBack();
            }
          }
        }
      }
    } else {
      setTimeout(() => {
        setAuthLoading(false);
      }, 3000);
    }
  };

  const onAllowPress = async () => {
    setGetPhoneGranted(true);
    setShowGetPhoneConfirm(false);

    api?.getPhoneNumber({
      async success(res) {
        let {token, number} = res;
        if (number) {
        } else if (token) {
          handlePressZaloLogin(token);
        }
      },
    });
  };

  const onRejectPress = () => {
    setGetPhoneGranted(false);
    setShowGetPhoneConfirm(false);
  };

  return (
    <View style={styles.container}>
      <Image source={HappyRich} style={{width: 250, height: 167, marginBottom: theme.spacing.m}} />
      <View style={{paddingHorizontal: theme.spacing.l}}>
        <Text style={{color: '#333', fontSize: theme.typography.lg2, textAlign: 'center'}}>Đăng nhập ngay, bắt đầu kiếm tiền với TTS Dropship</Text>
      </View>

      {isAuthLoading ? (
        <View style={{height: 210, justifyContent: 'center'}}>
          <ActivityIndicator />
        </View>
      ) : (
        <>
          <TouchableOpacity
            style={{
              backgroundColor: '#2196f3',
              flexDirection: 'row',
              alignItems: 'center',
              borderRadius: 30,
              width: 250,
              height: 50,
              marginTop: theme.spacing.l * 4,
              justifyContent: 'center',
            }}
            onPress={() => handlePressZaloLogin()}>
            <Image
              source={{
                uri: currentZaloUser?.avatar,
              }}
              style={{
                width: 35,
                height: 35,
                marginRight: theme.spacing.s,
                borderRadius: 30,
              }}
            />
            <Text style={{fontSize: theme.typography.md, color: theme.colors.white, fontWeight: '500'}}>Đăng nhập bằng Zalo</Text>
          </TouchableOpacity>
          {/* 
          <TouchableOpacity
            style={{
              backgroundColor: '#fff',
              flexDirection: 'row',
              alignItems: 'center',
              borderRadius: 30,
              width: 250,
              height: 46,
              marginTop: theme.spacing.m,
              justifyContent: 'center',
              borderWidth: 2,
              borderColor: theme.colors.black,
            }}>
            <Text style={{fontSize: theme.typography.md, color: theme.colors.black, fontWeight: '500'}}>Đăng nhập bằng số điện thoại</Text>
          </TouchableOpacity> */}
        </>
      )}

      <Dialog isVisible={isShowGetPhoneConfirm}>
        <View>
          <View
            style={{
              alignSelf: 'center',
              backgroundColor: theme.colors.primary,
              width: 80,
              height: 80,
              justifyContent: 'center',
              alignItems: 'center',
              borderWidth: 10,
              borderRadius: 100,
              borderColor: theme.colors.primaryLight,
              marginBottom: theme.spacing.m,
            }}>
            <Ionicons name="call" color={theme.colors.white} size={30} />
          </View>
          <Text style={{fontSize: theme.typography.lg1, fontWeight: '500', textAlign: 'center'}}>TTS Dropship cần số điện thoại của bạn</Text>
          <Text style={{textAlign: 'center', marginTop: theme.spacing.m}}>Dùng để định danh tài khoản và hoàn tất việc đăng nhập</Text>

          <Button containerStyle={{marginTop: theme.spacing.l * 3, borderRadius: 30}} size="sm" onPress={onAllowPress}>
            Cho phép
          </Button>
          <Button containerStyle={{marginTop: theme.spacing.m, borderRadius: 30}} size="sm" type="clear" titleStyle={{color: theme.colors.text, fontSize: theme.typography.md}} onPress={onRejectPress}>
            Không đăng nhập
          </Button>
        </View>
      </Dialog>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  actionContainer: {
    marginTop: 100,
  },
  authLoading: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 200,
  },
  loginReason: {
    fontSize: 16,
    marginTop: 8,
  },
});

export default ZaloMiniAppRegister;
