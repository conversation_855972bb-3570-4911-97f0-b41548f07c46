import {Input, InputProps} from '@rneui/base';
import React from 'react';
import {Pressable, StyleSheet, Text} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import theme from '~utils/config/themes/theme';

type SelectProps = {
  options?: string[];
  webOptions?: {label: string; value: string}[];
  onPress?: () => void;

  label?: string;
  value?: string;
  onChange?: (newValue: string) => void;
} & InputProps;

const Select: React.FC<SelectProps> = props => {
  return (
    <Input
      {...(props as any)}
      // eslint-disable-next-line react/no-unstable-nested-components
      InputComponent={() => (
        <Pressable onPress={props.onPress} style={{paddingHorizontal: 20, flex: 1, paddingVertical: 12, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center'}}>
          <Text style={[{fontSize: theme.typography.base, color: theme.colors.textLight}, !!props.value && {color: theme.colors.text}]}>{props.value || `Chọn ${props.label}`}</Text>
          <Ionicons name="chevron-down" size={20} color={theme.colors.textLight} />
        </Pressable>
      )}
      containerStyle={styles.formContainer}
      inputContainerStyle={styles.inputContainer}
      inputStyle={styles.input}
      labelStyle={styles.label}
      renderErrorMessage
    />
  );
};

const styles = StyleSheet.create({
  inputContainer: {
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,.2)',
    borderRadius: 8,
    marginTop: theme.spacing.s,
  },
  formContainer: {
    marginBottom: 0,
  },
  input: {
    padding: 12,
    fontSize: theme.typography.md,
  },
  label: {
    fontWeight: '500',
    fontSize: theme.typography.md,
    color: theme.colors.text,
  },
});

export default Select;
