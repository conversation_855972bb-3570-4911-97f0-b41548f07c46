import {InputProps} from '@rneui/base';
import React, {useCallback} from 'react';
import {StyleSheet, Text} from 'react-native';
import theme from '~utils/config/themes/theme';

type SelectProps = {
  options?: string[];
  webOptions?: {label: string; value: string}[];
  onPress?: () => void;
  label?: string;
  value?: string;
  onChange?: (newValue: string) => void;
} & InputProps;

const Select: React.FC<SelectProps> = props => {
  const handleChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    e.persist();
    if (props.onChange) {
      props.onChange(e.target.value);
    }
  }, []);

  return (
    <div style={{padding: '0px 10px', marginBottom: 20}}>
      <div style={{marginBottom: 8}}>
        <Text style={styles.label}>{props.label}</Text>
      </div>
      <select
        value={props.value}
        onChange={handleChange}
        style={{
          padding: 12,
          width: '100%',
          fontSize: theme.typography.md,
          borderColor: 'rgba(0,0,0,.3)',
          borderRadius: 8,
        }}>
        <option value="">{props.label}</option>
        {(props.options || []).map(option => (
          <option key={option} value={option}>
            {option}
          </option>
        ))}

        {(props.webOptions || []).map(({label, value}) => (
          <option key={value} value={value}>
            {label}
          </option>
        ))}
      </select>
      {Boolean(props.errorMessage) && (
        <div
          style={{
            margin: 5,
            fontSize: 12,
            color: ' rgb(255, 25, 12)',
          }}>
          {props.errorMessage}
        </div>
      )}
    </div>
  );
};

const styles = StyleSheet.create({
  inputContainer: {
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,.3)',
    borderRadius: 8,
    marginTop: 8,
  },
  formContainer: {
    marginBottom: 0,
  },
  input: {
    padding: 12,
    fontSize: theme.typography.md,
  },
  label: {
    color: 'rgb(134, 147, 158)',
    fontWeight: '500',
    fontSize: theme.typography.md,

    marginBottom: 8,
  },
});

export default Select;
