import {Input, Text} from '@rneui/themed';
import React, {useCallback, useState} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import AddressInputWithSearchModal from './AddressInputWithSearchModal';

type AddressInputWithSearchProps = {
  label?: string;
  address?: string;
  onDone: (address: string, additionalData?: {ward: string; city: string; province: string}) => void;
  errorMessage?: string;
};

const AddressInputWithSearch: React.FC<AddressInputWithSearchProps> = props => {
  const [addressSearchModalOpen, setAddressSearchModalOpen] = useState(false);

  const openAddressSearchModal = useCallback(() => {
    setAddressSearchModalOpen(true);
  }, []);

  const closeAddressSearchModal = useCallback(() => {
    setAddressSearchModalOpen(false);
  }, []);

  // const handleSubmitEditing = useCallback(() => {
  //   props.onDone(addressText);
  //   closeAddressSearchModal();
  // }, [addressText, closeAddressSearchModal, props.onDone]);

  return (
    <View>
      <Input
        {...(props as any)}
        InputComponent={() => (
          <TouchableOpacity onPress={openAddressSearchModal} style={{flex: 1, paddingVertical: 12, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center'}}>
            {props.address ? (
              <Text style={{fontSize: theme.typography.md, color: theme.colors.text}}>{props.address}</Text>
            ) : (
              <Text style={{fontSize: theme.typography.md, color: theme.colors.textLight}}>{'Nhập địa chỉ'}</Text>
            )}
          </TouchableOpacity>
        )}
        label={props.label}
        containerStyle={styles.formContainer}
        inputContainerStyle={styles.inputContainer}
        inputStyle={styles.input}
        labelStyle={styles.label}
        errorMessage={props.errorMessage}
      />

      <AddressInputWithSearchModal onDone={props.onDone} visible={addressSearchModalOpen} initialAddress={props.address} onClose={closeAddressSearchModal} />
    </View>
  );
};

const styles = StyleSheet.create({
  inputContainer: {
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,.2)',
    borderRadius: 8,
    marginTop: 8,
  },
  formContainer: {
    marginBottom: 0,
  },
  input: {
    padding: 12,
    fontSize: theme.typography.md,
  },
  label: {
    fontWeight: '500',
    fontSize: theme.typography.md,
    color: theme.colors.text,
  },
});

export default AddressInputWithSearch;
