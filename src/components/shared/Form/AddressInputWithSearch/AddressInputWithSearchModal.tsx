import {Input, Text} from '@rneui/themed';
import React, {useRef, useState} from 'react';
import {StyleSheet, TextInput, TouchableOpacity} from 'react-native';
import {Modal, ScrollView, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useDebounce, useUpdateEffect} from '~hooks';
import {useAddressParse} from '~utils/api/customer';
import theme from '~utils/config/themes/theme';

type AddressInputWithSearchModalProps = {
  visible: boolean;
  onClose: () => void;
  initialAddress?: string;
  onDone: (address: string, additionalData?: {ward: string; city: string; province: string}) => void;
};
const AddressInputWithSearchModal: React.FC<AddressInputWithSearchModalProps> = props => {
  const insets = useSafeAreaInsets();
  const [address, setAddress] = useState<string>(props.initialAddress || '');
  const searchInputRef = useRef<TextInput>(null);
  const addressDebounce = useDebounce(address, 500);
  const {data} = useAddressParse(addressDebounce);

  useUpdateEffect(() => {
    if (props.visible) {
      setTimeout(() => {
        searchInputRef.current?.focus();
        setAddress(props.initialAddress || '');
      }, 300);
    }
  }, [props.visible]);

  const handlePressDefaultAddress = () => {
    props.onDone(address);
    props.onClose();
  };

  const handlePressParsedAddress = () => {
    props.onClose();

    setTimeout(() => {
      props.onDone(address, {
        ward: data?.ward!,
        city: data?.city!,
        province: data?.province!,
      });
    }, 350);
  };

  return (
    <Modal visible={props.visible} onRequestClose={props.onClose} animationType="fade">
      <ScrollView style={{flex: 1, paddingTop: insets.top, paddingBottom: insets.bottom}} keyboardShouldPersistTaps="handled" keyboardDismissMode="interactive">
        <View style={{paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: 'rgba(0,0,0,.08)', position: 'relative'}}>
          <View style={{position: 'absolute', left: 12, top: 4, zIndex: 100}}>
            <Ionicons name="close-circle" size={38} color="rgba(0,0,0,.5)" onPress={props.onClose} />
          </View>
          <Text style={{textAlign: 'center', fontSize: 18, fontWeight: '500'}}>Địa chỉ nhận hàng</Text>
          <View />
        </View>
        {/*  */}
        <Input
          leftIcon={{
            type: 'ionicon',
            name: 'search-outline',
            size: 20,
            color: theme.colors.textLight,
          }}
          value={address}
          onChangeText={setAddress}
          returnKeyType="done"
          // @ts-ignore
          ref={searchInputRef}
          containerStyle={styles.formContainer}
          inputContainerStyle={styles.inputContainer}
          inputStyle={styles.input}
          labelStyle={styles.label}
          placeholder="Nhập hoặc dán địa chỉ vào đây"
          onSubmitEditing={handlePressDefaultAddress}
          renderErrorMessage={false}
          rightIcon={{
            type: 'ionicon',
            name: 'close',
            size: 20,
            color: theme.colors.textLight,
            onPress: () => {
              searchInputRef.current?.clear();
            },
          }}
        />

        <View style={{marginHorizontal: theme.spacing.s, marginTop: theme.spacing.m}}>
          {address ? (
            <>
              <TouchableOpacity style={{flexDirection: 'row', paddingVertical: theme.spacing.s, alignItems: 'flex-start'}} onPress={handlePressDefaultAddress}>
                <Ionicons name="location" color={theme.colors.textLight} size={20} />
                {/* @ts-ignore */}
                <Text style={{flex: 1, marginLeft: theme.spacing.s}}>
                  <Text style={{fontSize: theme.typography.md, color: theme.colors.textLight}}>Dùng </Text>
                  <Text style={{fontSize: theme.typography.md, color: theme.colors.text}}>{`"${address}"`}</Text>
                </Text>
              </TouchableOpacity>
            </>
          ) : null}

          {data?.ward ? (
            <TouchableOpacity style={{flexDirection: 'row', paddingVertical: theme.spacing.s, alignItems: 'center'}} onPress={handlePressParsedAddress}>
              <Ionicons name="location" color={theme.colors.primary} size={20} />
              <View style={{marginLeft: theme.spacing.s, flex: 1}}>
                <Text>{data.address}</Text>
                <Text style={{color: theme.colors.textLight}}>{[data.ward, data.province, data.city].join(', ')}</Text>
              </View>
            </TouchableOpacity>
          ) : null}
        </View>
      </ScrollView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  inputContainer: {
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,.2)',
    borderRadius: 8,
    marginTop: 8,
  },
  formContainer: {
    marginBottom: 0,
  },
  input: {
    padding: 12,
    fontSize: theme.typography.md,
  },
  label: {
    fontWeight: '500',
    fontSize: theme.typography.md,
    color: theme.colors.text,
  },
});

export default AddressInputWithSearchModal;
