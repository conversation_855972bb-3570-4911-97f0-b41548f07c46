import * as React from 'react';
import {View, Text, ViewStyle, TextStyle, LayoutChangeEvent, TouchableOpacity, StyleSheet, Linking} from 'react-native';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';
import HyperLink from 'react-native-hyperlink';
import LinkifyIt from 'linkify-it';
import theme from '~utils/config/themes/theme';

const {useState, useEffect} = React;

const linkifyObject = LinkifyIt();

linkifyObject.add('0', {
  validate: /^(3|5|7|8|9|1[2|6|8|9])+([0-9]{8})\b/,
  normalize: function (match) {
    match.url = 'tel:' + match.url;
  },
});

type ReadMoreProps = {
  text: string;
  numberOfLines: number;
  textVisibility?: boolean;
  containerStyle?: ViewStyle;
  textStyle?: TextStyle;
  renderViewMore?: (onPress: () => void) => React.ReactNode;
  renderViewLess?: (onPress: () => void) => React.ReactNode;
  onChangeTextVisibility?: (v: boolean) => void;
};

const DEFAULT_FONT_SIZE = 14;
const DEFAULT_LINE_HEIGHT = 18;

const ReadMoreText: React.FC<ReadMoreProps> = ({
  text,
  numberOfLines,
  renderViewMore = _renderViewMore,
  renderViewLess = _renderViewLess,
  textVisibility = false,
  containerStyle = {},
  textStyle = {},
  onChangeTextVisibility = () => {},
}: ReadMoreProps) => {
  const [textVisibilityState, setTextVisibility] = useState(false);
  const [footerVisibilityState, setFooterVisibility] = useState(false);
  const [textSelection, setTextSelection] = useState(false);

  useEffect(() => {
    if (textVisibility !== textVisibilityState) {
      setTextVisibility(textVisibility);
    }
  }, [textVisibility]);

  const textStyles = [
    {
      fontSize: DEFAULT_FONT_SIZE,
      lineHeight: DEFAULT_LINE_HEIGHT,
      backgroundColor: textSelection ? '#B4D5FE' : 'transparent',
      color: theme.colors.text,
    },
    textStyle,
  ];

  const fontSize = textStyle.fontSize || DEFAULT_FONT_SIZE;

  const handleMorePress = (): void => {
    setTextVisibility(true);
    onChangeTextVisibility(true);
  };

  const handleLessPress = (): void => {
    setTextVisibility(false);
    onChangeTextVisibility(false);
  };

  const handleTextPress = () => {
    if (footerVisibilityState) {
      if (textVisibilityState) {
        handleLessPress();
      } else {
        handleMorePress();
      }
    }
  };

  const handleTextLongPress = React.useCallback(() => {
    setTextSelection(true);
    ReactNativeHapticFeedback.trigger('notificationSuccess', {enableVibrateFallback: true});
    setTimeout(() => {
      setTextSelection(false);
    }, 200);
  }, []);

  const onLayout = (event: LayoutChangeEvent): void => {
    if (event.nativeEvent.layout.height > fontSize * numberOfLines) {
      setFooterVisibility(true);
    } else {
      setFooterVisibility(false);
    }
  };

  const renderFooter = (): React.ReactNode => {
    if (textVisibilityState) {
      return renderViewLess(handleLessPress);
    }
    return renderViewMore(handleMorePress);
  };

  const lines = textVisibilityState ? undefined : numberOfLines;

  const handleLinkPress = React.useCallback(async (url: string) => {
    let regex = /^https\:\/\/banhang\.thitruongsi\.com/;
    let openUrl = url;
    if (regex.test(url)) {
      openUrl = url.replace(regex, 'tts.com.ttsseller://');
    }

    const canOpen = await Linking.canOpenURL(openUrl);
    if (canOpen) {
      Linking.openURL(openUrl);
    }
  }, []);

  return (
    <View style={containerStyle}>
      <TouchableOpacity onPress={handleTextPress} onLongPress={handleTextLongPress} activeOpacity={1}>
        <HyperLink
          linkify={linkifyObject}
          onPress={handleLinkPress}
          linkStyle={{
            textDecorationLine: 'underline',
          }}>
          <Text style={textStyles} numberOfLines={lines} onLayout={onLayout}>
            {text}
          </Text>
        </HyperLink>
      </TouchableOpacity>
      {footerVisibilityState ? renderFooter() : null}
    </View>
  );
};

const _renderViewLess = (onPress: () => void) => {
  return (
    <View style={styles.viewMoreContainer}>
      <TouchableOpacity onPress={onPress}>
        <Text style={styles.viewMoreText}>Rút gọn</Text>
      </TouchableOpacity>
    </View>
  );
};

const _renderViewMore = (onPress: () => void) => {
  return (
    <View style={styles.viewMoreContainer}>
      <TouchableOpacity onPress={onPress}>
        <Text style={styles.viewMoreText}>Xem thêm</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  viewMoreContainer: {
    marginVertical: 8,
  },
  viewMoreText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
});

export default ReadMoreText;
