import React from 'react';
import {View} from 'react-native';
import {ProductBadge} from '~utils/types/product';
import {QuickImage} from '../QuickImage';

type BadgeFrameProps = {
  badges: ProductBadge[];
};

const BadgeFrame: React.FC<BadgeFrameProps> = props => {
  return (
    <View style={{position: 'absolute', top: 0, left: 0, right: 0, bottom: 0}}>
      {/* 'https://i.ibb.co/qR6ZLxD/shopee-frame.png' */}
      {props.badges.map(badge => {
        return <QuickImage key={badge.id} source={{uri: badge.url}} style={{flex: 1}} />;
      })}
    </View>
  );
};

export default BadgeFrame;
