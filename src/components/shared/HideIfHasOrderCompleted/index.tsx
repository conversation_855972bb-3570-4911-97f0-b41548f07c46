import React, {useEffect, useState} from 'react';
import {useAuthActions} from '~hooks';
import {useGetOrders} from '~utils/api/order';
import {REACT_QUERY_CACHE_TIME} from '~utils/helpers/reactQuery';

const HideIfHasOrderCompleted: React.FC<React.PropsWithChildren> = props => {
  const {isLoggedIn} = useAuthActions();
  const [isHide, setHide] = useState(isLoggedIn);
  const {data} = useGetOrders(
    {
      limit: 1,
      buyer_status: 'delivered',
    },
    {
      enabled: isLoggedIn,
      staleTime: REACT_QUERY_CACHE_TIME.FiveMinutes,
    },
  );

  useEffect(() => {
    if (data?.extensions?.totalItems === 0) {
      setHide(false);
    }

    if (isHide === false && data?.extensions?.totalItems && data?.extensions?.totalItems > 0) {
      setHide(true);
    }
  }, [data, isHide]);

  if (isHide) {
    return null;
  }

  return props.children;
};

export default HideIfHasOrderCompleted;
