import {Text} from '@rneui/themed';
import dayjs from 'dayjs';
import React from 'react';
import {View} from 'react-native';
import theme from '~utils/config/themes/theme';

type CountDownTimerProps = {
  initialTime: number;
};

const CountDownTimer: React.FC<CountDownTimerProps> = props => {
  const [time, setTime] = React.useState(props.initialTime || new Date().getTime());
  const timerRef = React.useRef<number>(time);

  React.useEffect(() => {
    const timerId = setInterval(() => {
      timerRef.current -= 1000;
      if (timerRef.current < 0) {
        clearInterval(timerId);
      } else {
        setTime(timerRef.current);
      }
    }, 1000);
    return () => {
      clearInterval(timerId);
    };
  }, []);

  return (
    <View>
      <Text style={{fontSize: theme.typography.base}}>{dayjs(time).format('DD-MM-YYYY HH:mm:ss')}</Text>
    </View>
  );
};

export default CountDownTimer;
