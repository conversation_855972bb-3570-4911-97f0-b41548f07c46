import {Image, Text} from '@rneui/themed';
import React from 'react';
import {StyleSheet, View} from 'react-native';
// @ts-ignore
import IdentityVerificationImage from '~assets/identity-verification.png';
import Ionicons from 'react-native-vector-icons/Ionicons';
import theme from '~utils/config/themes/theme';

const IdentityVerificationBenefits = () => {
  return (
    <View style={{justifyContent: 'center', alignItems: 'center'}}>
      <Image
        source={IdentityVerificationImage}
        style={{
          width: 120,
          height: 120,
        }}
      />

      <Text style={styles.header}>Định danh tài khoản giúp bạn</Text>
      <View>
        <BenefitItem>
          <Text>Bảo vệ tài khoản khi có sự cố</Text>
        </BenefitItem>
        <BenefitItem>
          <Text>Khai báo thuế đầy đủ theo pháp luật</Text>
        </BenefitItem>
        <BenefitItem>
          <Text>Rút tiền bất kỳ lúc nào chỉ từ 100K</Text>
        </BenefitItem>
      </View>
    </View>
  );
};

const BenefitItem: React.FC<React.PropsWithChildren> = props => {
  return (
    <View style={styles.benefitItemContainer}>
      <Ionicons name="checkmark-circle" size={22} color={'#00a97f'} style={styles.iconStyle} />
      {props.children}
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    fontSize: theme.typography.lg,
    fontWeight: '600',
    marginBottom: theme.spacing.m,
  },
  benefitItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: theme.spacing.s,
  },
  iconStyle: {
    marginRight: theme.spacing.s,
  },
});

export default IdentityVerificationBenefits;
