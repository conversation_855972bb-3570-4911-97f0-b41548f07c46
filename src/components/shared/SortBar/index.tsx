import {useRoute} from '@react-navigation/native';
import {But<PERSON>} from '@rneui/themed';
import React, {useCallback} from 'react';
import {StyleSheet, View} from 'react-native';
import {useNavigation} from '~hooks';
import {SortParams} from '~hooks/useSort';
import theme from '~utils/config/themes/theme';
import {ProductSortQuery} from '~utils/types/product';
import Ionicons from 'react-native-vector-icons/Ionicons';

type SortBarProps = {
  sortState: SortParams;
  updateSort: (newSortState: SortParams) => void;
  disableScoreSort?: boolean;
};

const SortBar: React.FC<SortBarProps> = ({sortState, updateSort, disableScoreSort}) => {
  const navigation = useNavigation();
  const route = useRoute();

  const getSelectedColor = useCallback(
    (sortBy: ProductSortQuery) => {
      return sortState.sort_by === sortBy ? theme.colors.primary : theme.colors.textLight;
    },
    [sortState.sort_by],
  );

  const getBorderBottomWidth = useCallback(
    (sortBy: ProductSortQuery) => {
      return sortState.sort_by === sortBy ? 1 : 0;
    },
    [sortState.sort_by],
  );

  const handleSortPress = useCallback(
    (sortBy: ProductSortQuery, ascending?: boolean) => {
      updateSort({sort_by: sortBy, ascending});
    },
    [route.params, sortState],
  );
  return (
    <View style={{flexDirection: 'row', backgroundColor: '#fff', paddingTop: 8, paddingBottom: 2, borderBottomColor: 'rgba(0,0,0,.1)', borderBottomWidth: 1}}>
      {!disableScoreSort && (
        <Button
          type="clear"
          containerStyle={styles.sortItem}
          title={'Liên quan'}
          titleProps={{
            allowFontScaling: false,
          }}
          buttonStyle={[styles.sortItem_btn, {borderBottomWidth: getBorderBottomWidth('_score')}]}
          titleStyle={[styles.sortItem_text, {color: getSelectedColor('_score')}]}
          onPress={() => handleSortPress('_score')}
        />
      )}
      <Button
        containerStyle={styles.sortItem}
        type="clear"
        title={'Mới nhất'}
        titleProps={{
          allowFontScaling: false,
        }}
        buttonStyle={[styles.sortItem_btn, {borderBottomWidth: getBorderBottomWidth('dropship_published_at')}]}
        titleStyle={[styles.sortItem_text, {color: getSelectedColor('dropship_published_at')}]}
        onPress={() => handleSortPress('dropship_published_at')}
      />
      <Button
        containerStyle={styles.sortItem}
        type="clear"
        title={'Bán chạy'}
        titleProps={{
          allowFontScaling: false,
        }}
        buttonStyle={[styles.sortItem_btn, {borderBottomWidth: getBorderBottomWidth('total_sales')}]}
        titleStyle={[styles.sortItem_text, {color: getSelectedColor('total_sales')}]}
        onPress={() => handleSortPress('total_sales')}
      />
      <Button
        type="clear"
        containerStyle={styles.sortItem}
        title={'Lợi nhuận'}
        titleProps={{
          allowFontScaling: false,
        }}
        buttonStyle={[styles.sortItem_btn, {borderBottomWidth: getBorderBottomWidth('dropship_profit')}]}
        titleStyle={[styles.sortItem_text, {color: getSelectedColor('dropship_profit')}]}
        onPress={() => handleSortPress('dropship_profit', typeof sortState.ascending !== 'undefined' && sortState.sort_by === 'dropship_profit' ? !sortState.ascending : false)}
        iconRight
        icon={
          <View style={{marginLeft: 4}}>
            {typeof sortState.ascending !== 'undefined' && sortState.sort_by === 'dropship_profit' ? (
              <>
                {sortState.ascending === true && <Ionicons name="arrow-up-outline" size={18} color={theme.colors.primary} />}
                {sortState.ascending === false && <Ionicons name="arrow-down-outline" size={18} color={theme.colors.primary} />}
              </>
            ) : (
              <>
                <Ionicons name="caret-up" size={10} style={{padding: 0, marginBottom: -4}} color={theme.colors.textLight} />
                <Ionicons name="caret-down" size={10} style={{padding: 0}} color={theme.colors.textLight} />
              </>
            )}
          </View>
        }
      />
      <Button
        containerStyle={styles.sortItem}
        type="clear"
        title={'Giá'}
        titleProps={{
          allowFontScaling: false,
        }}
        buttonStyle={[styles.sortItem_btn, {borderBottomWidth: getBorderBottomWidth('dropship_price'), borderRightWidth: 0}]}
        titleStyle={[styles.sortItem_text, {color: getSelectedColor('dropship_price')}]}
        iconRight
        icon={
          <View style={{marginLeft: 4}}>
            {typeof sortState.ascending !== 'undefined' && sortState.sort_by === 'dropship_price' ? (
              <>
                {sortState.ascending === true && <Ionicons name="arrow-up-outline" size={18} color={theme.colors.primary} />}
                {sortState.ascending === false && <Ionicons name="arrow-down-outline" size={18} color={theme.colors.primary} />}
              </>
            ) : (
              <>
                <Ionicons name="caret-up" size={10} style={{padding: 0, marginBottom: -4}} color={theme.colors.textLight} />
                <Ionicons name="caret-down" size={10} style={{padding: 0}} color={theme.colors.textLight} />
              </>
            )}
          </View>
        }
        onPress={() => handleSortPress('dropship_price', typeof sortState.ascending !== 'undefined' ? !sortState.ascending : true)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  productItem: {
    flex: 1,
    backgroundColor: '#fff',
    marginHorizontal: 4,
    marginVertical: 8,
    borderRadius: 12,
  },
  sortItem_text: {
    fontSize: 14,
    flexShrink: 0,
    color: theme.colors.gray70,
  },
  sortItem_btn: {
    paddingHorizontal: 0,
    // borderRightWidth: 1,
    // borderRightColor: 'rgba(0,0,0,.05)',
    paddingVertical: 4,
    borderBottomColor: theme.colors.primary,
    borderRadius: 0,
  },
  sortItem: {
    flex: 1,
    borderRadius: 0,
  },
});

export default SortBar;
