import {Text} from '@rneui/themed';
import React from 'react';
import {StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';

const EndReachedIndicator = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.text}>Bạn đã xem hết!</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: theme.spacing.m,
  },
  text: {
    fontSize: theme.typography.base,
    textAlign: 'center',
    color: theme.colors.textLight,
  },
});

export default EndReachedIndicator;
