import React, {useCallback} from 'react';
import {View, Pressable, StyleSheet, Platform} from 'react-native';
import {Text} from '@rneui/themed';
import {WP_REST_API_Post} from '~utils/types/wp-json';
import {decode} from 'html-entities';
import useNavigation from '~hooks/useNavigation';
import Ionicons from 'react-native-vector-icons/Ionicons';
import theme from '~utils/config/themes/theme';

type WordpressPostCardProps = {
  post: WP_REST_API_Post;
};

const WordpressPostCard: React.FC<WordpressPostCardProps> = props => {
  const navigation = useNavigation();

  const goToPost = useCallback(() => {
    if (Platform.OS === 'web') {
      window.open(props.post.link, '_blank');
      return;
    }
    navigation.navigate('WebView', {title: '', url: props.post.link});
  }, []);

  return (
    <Pressable onPress={goToPost} style={styles.container}>
      <View style={{flex: 1, borderRadius: 10}}>
        <Text ellipsizeMode="tail" numberOfLines={2} style={{fontSize: 16, padding: 4}}>
          {decode(props.post.title.rendered)}
        </Text>
      </View>

      <Ionicons name="chevron-forward" color={theme.colors.textLight} />
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginBottom: theme.spacing.s,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 20,
  },
});

export default WordpressPostCard;
