import {useFocusEffect} from '@react-navigation/native';
import React, {useContext, useState, useCallback} from 'react';
import {StyleSheet, View} from 'react-native';

type WebContainerProps = {
  children: React.ReactNode;
};

export const responsiveWidth = {
  sm: 576,
  md: 768,
  lg: 992,
};

const WebContainerContext = React.createContext({
  maxWidth: responsiveWidth.sm,
  setMaxWidth: (_params: any) => {},
});

const WebContainer: React.FC<WebContainerProps> = props => {
  const [maxWidth, setMaxWidth] = useState(responsiveWidth.sm);
  return (
    <WebContainerContext.Provider value={{maxWidth, setMaxWidth}}>
      <View style={[styles.container, {maxWidth: maxWidth}]}>{props.children}</View>
    </WebContainerContext.Provider>
  );
};

export const useWebContainerState = () => {
  return useContext(WebContainerContext);
};

export const useExtendContainerWidth = (maxW: 'md' | 'lg' | 'sm') => {
  const {setMaxWidth} = useWebContainerState();

  useFocusEffect(
    useCallback(() => {
      if (maxW) {
        setTimeout(() => {
          setMaxWidth(responsiveWidth[maxW]);
        }, 100);
      }

      return () => {
        setMaxWidth(responsiveWidth.sm);
      };
    }, []),
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    marginHorizontal: 'auto',
    backgroundColor: '#f2f2f2',
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
  },
});

export default WebContainer;
