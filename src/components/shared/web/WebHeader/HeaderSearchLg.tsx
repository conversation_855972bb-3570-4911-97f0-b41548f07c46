import React, {useCallback, useState} from 'react';
import {View} from 'react-native';
import {Input} from '@rneui/themed';
import theme from '~utils/config/themes/theme';
import {useNavigation} from '~hooks';

const HeaderSearchLg = () => {
  const navigation = useNavigation();
  const [searchText, setSearchText] = useState('');

  const handleSumit = useCallback(() => {
    navigation.navigate('SearchResult', {
      keyword: searchText,
    });
  }, [searchText]);

  return (
    <View>
      <Input placeholder="Tìm sản phẩm" renderErrorMessage={false} inputStyle={{fontSize: theme.typography.md}} value={searchText} onChangeText={setSearchText} onSubmitEditing={handleSumit} />
    </View>
  );
};

export default HeaderSearchLg;
