import {Text} from '@rneui/themed';
import React, {useCallback} from 'react';
import {Image, Linking, Platform, Pressable, StyleSheet, View} from 'react-native';
import theme from '~utils/config/themes/theme';
// @ts-ignore
import ImageGOVSeal from '~assets/online_gov_vn_seal.jpeg';
// @ts-ignore
import VecomLogo from '~assets/logovecom-300x94.png';
import {useNavigation} from '~hooks';
import isInZaloMiniApp from '~utils/helpers/isInZaloMiniApp';

const Footer = () => {
  const navigation = useNavigation();

  const handleLinkPress = useCallback((url: string) => {
    if (Platform.OS === 'web' && !isInZaloMiniApp()) {
      Linking.openURL(url);
    } else {
      navigation.navigate('WebView', {
        url: url,
      });
    }
  }, []);

  return (
    <View style={{marginTop: 100, marginBottom: 100, alignItems: 'center'}}>
      <View style={[theme.globalStyles.flexRow, {marginBottom: theme.spacing.l}]}>
        <Pressable onPress={() => handleLinkPress('http://online.gov.vn/Home/WebDetails/18419')}>
          <Image source={ImageGOVSeal} style={{width: 150, height: 55, marginRight: theme.spacing.m}} />
        </Pressable>
        <Pressable onPress={() => handleLinkPress('https://vecom.vn/cong-ty-co-phan-dich-vu-tts')}>
          <Image source={VecomLogo} style={{width: 160, height: 37}} />
        </Pressable>
      </View>
      <Text style={[styles.footerText, {fontWeight: '600', marginVertical: 8, textAlign: 'center', marginBottom: 20}]}>© 2014 - BẢN QUYỀN CỦA CÔNG TY CỔ PHẦN DỊCH VỤ TTS</Text>
      <Text style={styles.footerText}>Giấy chứng nhận Đăng ký số 0313203801 cấp tại Sở kế hoạch &amp; đầu tư TP Hồ Chí Minh.</Text>
      <Text style={styles.footerText}>Địa chỉ văn phòng: 17/4 Hoàng Hoa Thám, Phường Tân Bình, TP Hồ Chí Minh.</Text>
      <Text style={styles.footerText}>Email: <EMAIL> | HOTLINE: 19006074</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  footerText: {
    fontSize: theme.typography.sm,
    marginBottom: theme.spacing.s,
  },
});

export default Footer;
