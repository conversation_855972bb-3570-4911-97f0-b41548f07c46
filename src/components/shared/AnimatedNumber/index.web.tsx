import {Text} from '@rneui/themed';
import * as React from 'react';
import {TextInputProps} from 'react-native';

interface AnimatedNumberProps extends Omit<TextInputProps, 'editable' | 'value'> {
  formatter?: (value: number) => string;
  steps?: number;
  time?: number;
  value: number;
}

function formatFn(value: number) {
  return value.toString();
}

export default function AnimatedNumber({formatter = formatFn, steps = 15, time = 17, value, ...restProps}: AnimatedNumberProps) {
  return (
    <Text allowFontScaling={false} {...restProps}>
      {formatter(value)}
    </Text>
  );
}
