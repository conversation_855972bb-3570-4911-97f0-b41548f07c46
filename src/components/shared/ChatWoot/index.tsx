import React, {useCallback, useEffect, useState} from 'react';
// @ts-ignore
import {getChatWootIdHash, useGetCurrentUser} from '~utils/api/auth';
import {FAB} from '@rneui/themed';
import theme from '~utils/config/themes/theme';
import ee, {EventNames} from '~utils/events';
import {Linking} from 'react-native';

const ChatWoot = () => {
  const [showWidget, setShowWidget] = useState(false);
  const {data} = useGetCurrentUser();
  const [identifierHash, setIdentifierHash] = useState('');

  useEffect(() => {
    ee.on(EventNames.OpenChatWoot, handleOpenChatWidget);

    return () => {
      ee.off(EventNames.OpenChatWoot, handleOpenChatWidget);
    };
  }, []);

  useEffect(() => {
    getChatWootIdHash().then(res => {
      if (res.data.identifier_hash) {
        setIdentifierHash(res.data.identifier_hash);
      }
    });
  }, [data?.id]);

  const handleOpenChatWidget = useCallback(async () => {
    // setShowWidget(true);
    const supportURL = `https://m.me/623709707685878?ref=dropship:${data?.id ?? 'null'}`;
    const canOpenURL = await Linking.canOpenURL(supportURL);
    if (canOpenURL) {
      Linking.openURL(supportURL);
    }
  }, [data]);

  // const closeChatWidget = useCallback(() => {
  //   setShowWidget(false);
  // }, []);

  return (
    <>
      <FAB
        visible={true}
        icon={{name: 'chatbubble-ellipses-sharp', color: theme.colors.primary, type: 'ionicon', size: 40}}
        color="white"
        placement="right"
        onPress={handleOpenChatWidget}
        // buttonStyle={{padding: 0, margin: 0, justifyContent: 'center', alignItems: 'center', borderWidth: 2, borderRadius: 100, borderColor: 'rgba(0,0,0,.05)'}}
        iconContainerStyle={{padding: 0, marginBottom: 2, justifyContent: 'center', alignItems: 'center'}}
        style={{bottom: 100, right: 10}}
      />

      {/* {Boolean(identifierHash) && (
        <ChatWootWidget
          websiteToken={'Dzgne1aFw2uWGojrjsQqroCb'}
          locale={'vi'}
          baseUrl={'https://livehelp.thitruongsi.com'}
          closeModal={closeChatWidget}
          isModalVisible={showWidget}
          user={{
            identifier: data?.id,
            name: data?.full_name,
            avatar_url: handleImageDomain(data?.avatar as string),
            email: data?.email,
            phone_number: data?.account_phone,
            identifier_hash: identifierHash,
          }}
          customAttributes={{}}
        />
      )} */}
    </>
  );
};

export default ChatWoot;
