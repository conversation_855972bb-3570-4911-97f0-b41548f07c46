import React from 'react';
import {ImageViewerModalProps} from './index';
import Ionicons from 'react-native-vector-icons/Ionicons';

const ImageViewerModal = (props: ImageViewerModalProps) => {
  if (!props.visible) {
    return null;
  }

  const image = props.images[props.selectedIndex];

  return (
    <div
      onClick={props.onClose}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}>
      <div style={{position: 'absolute', top: 10, right: 10, zIndex: 1000}} onClick={props.onClose}>
        <div style={{cursor: 'pointer', backgroundColor: 'rgba(0, 0, 0, 0.5)', width: 30, height: 30, borderRadius: 100, display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
          <Ionicons name="close" size={24} color="white" />
        </div>
      </div>
      <img src={image.url} onClick={e => e.stopPropagation()} alt={image.url} style={{width: '100%', maxWidth: 500}} />
    </div>
  );
};

export default ImageViewerModal;
