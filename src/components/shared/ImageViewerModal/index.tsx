import {ScreenHeight, ScreenWidth} from '@rneui/base';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {View, Animated, ModalProps, Modal, ActivityIndicator, StatusBar, Text, ActionSheetIOS, Platform, Image} from 'react-native';
import ImageViewer from 'react-native-image-zoom-viewer';
import {IImageInfo} from 'react-native-image-zoom-viewer/built/image-viewer.type';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Video from 'react-native-video';
import downloadImage, {downloadVideo} from '~utils/helpers/downloadImage';
import {checkAndRequestSavePhotoPermission} from '~utils/helpers/downloadImagePermissions';
import {getDownloadableURL, handleImageDomain} from '~utils/helpers/image';

export type ImageViewerModalProps = ModalProps & {
  images: IImageInfo[];
  selectedIndex: number;
  onClose: () => void;
};

const ImageViewerModal: React.FC<ImageViewerModalProps> = props => {
  const insets = useSafeAreaInsets();
  const [selectedIndex, setSelectedIndex] = useState(props.selectedIndex);
  const toolbarOpacity = useRef(new Animated.Value(1)).current;
  const swipeScale = useRef(new Animated.Value(1)).current;
  const imageScale = useRef(new Animated.Value(0.7)).current;
  const backgroundColor = useRef(
    swipeScale.interpolate({
      inputRange: [0.5, 1],
      outputRange: ['rgba(0, 0, 0, 0.5)', 'rgba(0, 0, 0, 1)'],
    }),
  ).current;
  const [showToolbar, setShowToolbar] = useState(true);
  const imageViewerRef = useRef(null);

  useEffect(() => {
    if (props.visible) {
      setShowToolbar(true);
      setSelectedIndex(props.selectedIndex);
      toolbarOpacity.setValue(1);
      swipeScale.setValue(1);
      // setTimeout(() => {
      //   toggleShowToolbar();
      // }, 1000);

      Animated.timing(imageScale, {
        duration: 200,
        toValue: 1,
        useNativeDriver: true,
      }).start();
    } else {
      imageScale.setValue(0.7);
    }
  }, [props.visible]);

  const handleClose = useCallback(() => {
    if (props.onClose) {
      props.onClose();
    }
  }, []);

  const toggleShowToolbar = useCallback(() => {
    // setShowToolbar(prev => !prev);
    const newValue = !showToolbar;
    if (newValue) {
      setShowToolbar(true);
      Animated.timing(toolbarOpacity, {
        toValue: 1,
        useNativeDriver: true,
        duration: 200,
      }).start();
    } else {
      Animated.timing(toolbarOpacity, {
        toValue: 0,
        useNativeDriver: true,
        duration: 200,
      }).start(() => {
        setShowToolbar(false);
      });
    }
  }, [showToolbar]);

  const handleSwipeMove = useCallback((position?: {type: string; positionX: number; positionY: number; scale: number; zoomCurrentDistance: number} | undefined) => {
    if (position) {
      swipeScale.setValue(position.scale);
    }
  }, []);

  const header = useCallback(() => {
    return (
      <Animated.View
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          // paddingTop: insets.top,
          flexDirection: 'row',
          // paddingHorizontal: 20,
          paddingBottom: 8,
          zIndex: 1,
          // backgroundColor: 'rgba(0,0,0,.5)',
          opacity: toolbarOpacity,
          display: !showToolbar ? 'none' : 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        <Ionicons name="arrow-back" size={30} color="#fff" onPress={handleClose} style={{paddingHorizontal: 16, paddingVertical: 4}} />
        <Text style={{color: '#fff', fontSize: 18, fontWeight: '500'}}>
          {selectedIndex + 1} / {props.images.length}
        </Text>
        <Ionicons name="ellipsis-horizontal" color="#fff" size={30} style={{paddingHorizontal: 16, paddingVertical: 4}} onPress={() => handleLongPress(props.images[selectedIndex])} />
      </Animated.View>
    );
  }, [showToolbar, props.images, selectedIndex]);

  const handleLongPress = useCallback(
    (image: IImageInfo | undefined) => {
      if (image) {
        if (Platform.OS === 'ios') {
          ActionSheetIOS.showActionSheetWithOptions(
            {
              options: [`Tải ${props.images[props.selectedIndex]?.props?.type === 'video' ? 'video' : 'ảnh'} xuống`, 'Đóng'],
              cancelButtonIndex: 1,
            },
            index => {
              switch (index) {
                case 0:
                  if (props.images[props.selectedIndex]?.props?.type === 'video') {
                    handleDownloadVideo(image.url);
                  } else {
                    handleDownloadImage(getDownloadableURL(handleImageDomain(image.url?.replace('rs:fill:600', 'rs:fill:1000')), image.props?.productId, ''));
                  }
                  break;

                default:
                  break;
              }
            },
          );
        } else {
          // @ts-ignore
          (imageViewerRef.current as ImageViewer).setState({
            isShowMenu: true,
          });
        }
      }
    },
    [props.selectedIndex, props.images],
  );

  const handleDownloadImage = async (imageUrl: string) => {
    const canContinue = await checkAndRequestSavePhotoPermission();
    if (!canContinue) {
      return;
    }

    downloadImage(imageUrl);
  };

  const handleDownloadVideo = async (videoUrl: string) => {
    const canContinue = await checkAndRequestSavePhotoPermission();
    if (!canContinue) {
      return;
    }

    downloadVideo(videoUrl);
  };

  return (
    <>
      {props.visible && <StatusBar backgroundColor={'black'} barStyle="light-content" />}
      <Modal {...props} onRequestClose={handleClose} transparent>
        <Animated.View style={{flex: 1, backgroundColor, paddingTop: insets.top, paddingBottom: insets.bottom, width: '100%', height: '100%'}}>
          <ImageViewer
            ref={imageViewerRef}
            onClick={toggleShowToolbar}
            enableImageZoom
            loadingRender={() => <ActivityIndicator />}
            renderIndicator={() => <View />}
            useNativeDriver
            enableSwipeDown
            onMove={handleSwipeMove}
            renderImage={(imageProps: any) => {
              if (imageProps.type === 'video') {
                return (
                  <View style={{flex: 1, justifyContent: 'center', alignItems: 'center', zIndex: 10}}>
                    <Video
                      // @ts-ignore
                      resizeMode="contain"
                      controls={true}
                      source={imageProps.source}
                      style={{
                        width: ScreenWidth,
                        height: ScreenHeight,
                      }}
                    />
                  </View>
                );
              }
              return (
                <Animated.View style={{transform: [{scale: imageScale}]}}>
                  <Image source={imageProps.source} style={imageProps.style} />
                </Animated.View>
              );
            }}
            swipeDownThreshold={70}
            index={props.selectedIndex}
            imageUrls={props.images}
            // @ts-ignore
            onChange={setSelectedIndex}
            onSwipeDown={handleClose}
            onLongPress={Platform.OS === 'android' ? undefined : handleLongPress}
            renderHeader={header}
            menuContext={{saveToLocal: 'Tải ảnh xuống', cancel: 'Hủy'}}
            saveToLocalByLongPress={Platform.OS === 'android'}
            onSave={url => {
              if (props.images[props.selectedIndex]?.props?.type === 'video') {
                handleDownloadVideo(url);
              } else {
                handleDownloadImage(getDownloadableURL(handleImageDomain(url?.replace('rs:fill:600', 'rs:fill:1000')), props.images[props.selectedIndex]?.props?.productId, ''));
              }
            }}
            backgroundColor="rgba(0,0,0,0)"
          />
        </Animated.View>
      </Modal>
    </>
  );
};

export default ImageViewerModal;
