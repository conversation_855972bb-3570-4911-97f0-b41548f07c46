import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import theme from '~utils/config/themes/theme';

type CardProps = {
  title?: string | React.ReactNode;
  children: React.ReactNode;
  rounded?: boolean;
};

const Card: React.FC<CardProps> = props => {
  return (
    <View style={[styles.container, props.rounded && {borderRadius: 12}]}>
      {Boolean(props.title) && <Text style={styles.title}>{props.title}</Text>}
      {props.children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: theme.spacing.m,
    paddingVertical: 12,
    backgroundColor: '#fff',
    marginTop: theme.spacing.s,
  },
  title: {
    fontWeight: '500',
    fontSize: theme.typography.lg,
    marginBottom: theme.spacing.m,
    color: theme.colors.text,
  },
});

export default Card;
