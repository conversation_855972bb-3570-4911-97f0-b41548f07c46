import {Text} from '@rneui/themed';
import React, {useMemo} from 'react';
import {ScrollView} from 'react-native';
import theme from '~utils/config/themes/theme';
import {LocationItem} from './index';
import Fuse from 'fuse.js';

type CityListProps = {
  items: string[];
  onSelect: (item: string) => void;
  selectedItem?: string;
  searchKeyword?: string;
};

const popularCities = ['TP Hồ Chí Minh', 'TP Đà Nẵng', 'Hà Nội'];

const CityList: React.FC<CityListProps> = props => {
  const fuse = useMemo(() => {
    return new Fuse(props.items, {});
  }, [props.items]);

  const results = useMemo(() => {
    return props.searchKeyword ? fuse.search(props.searchKeyword ?? '').map(item => item.item) : props.items;
  }, [props.items, props.searchKeyword]);

  return (
    <ScrollView style={{flex: 1, backgroundColor: theme.colors.gray10}} keyboardShouldPersistTaps="handled">
      {!props.searchKeyword ? (
        <>
          <Text style={{marginHorizontal: 12, marginVertical: theme.spacing.s, marginTop: theme.spacing.m, fontSize: theme.typography.base}}>Phổ biến</Text>
          {popularCities.map((item, index) => (
            <LocationItem key={`${item}-${index}`} text={item} arrow onSelect={props.onSelect} selected={item === props.selectedItem} />
          ))}
        </>
      ) : null}

      <Text style={{marginHorizontal: 12, marginVertical: theme.spacing.s, marginTop: theme.spacing.l, fontSize: theme.typography.base}}>Tất cả</Text>

      {results.map(item => (
        <LocationItem key={item} text={item} arrow onSelect={props.onSelect} selected={item === props.selectedItem} />
      ))}
    </ScrollView>
  );
};

export default CityList;
