import React, {useCallback, useMemo, useState} from 'react';
import {Pressable, StyleSheet, View} from 'react-native';
import {useGetLocations} from '~utils/api/location';
import Address from '~utils/types/address';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {NavigationState, SceneRendererProps, TabBar, TabView} from 'react-native-tab-view';
import {Input, Text} from '@rneui/themed';
import LocationList from './LocationList';
import CityList from './CityList';
import theme from '~utils/config/themes/theme';
import {useDebounce, useUpdateEffect} from '~hooks';

type LocationTabViewProps = {
  initialAddress?: Partial<Address>;
  onSelectDone?: (address: Partial<Address>) => void;
  initialTab?: number;
  address1?: string;
};
type State = NavigationState<{
  key: string;
  title: string;
}>;

const getInitialRoutes = (initialAddress?: Partial<Address>) => {
  return [
    initialAddress?.city ? {key: 'city', title: initialAddress.city as string} : {key: 'city', title: 'Tỉnh / thành'},
    Boolean(initialAddress?.province) && {key: 'province', title: initialAddress?.province as string},
    Boolean(initialAddress?.ward) && {
      key: 'ward',
      title: initialAddress?.ward as string,
    },
  ].filter(Boolean);
};

const getInitialTab = (initialRoutes: {key: string; title: string}[], initialTab?: number) => {
  const routesLength = initialRoutes.length;
  const tabIndex = initialTab || 0;

  if (tabIndex > routesLength - 1) {
    return 0;
  }

  return tabIndex;
};

const LocationTabView: React.FC<LocationTabViewProps> = props => {
  const [selectedCity, setSelectedCity] = useState(props.initialAddress?.city);
  const [selectedProvince, setSelectedProvince] = useState(props.initialAddress?.province);
  const [selectedWard, setSelectedWard] = useState(props.initialAddress?.ward);
  const [searchString, setSearchString] = useState('');
  const debouncedSearchString = useDebounce(searchString, 200);

  const [routes, setRoutes] = React.useState<{key: string; title: string}[]>(getInitialRoutes(props.initialAddress) as any);
  const [index, setIndex] = React.useState(getInitialTab(routes, props.initialTab));

  useUpdateEffect(() => {
    setSearchString('');

    setTimeout(() => {
      setIndex(routes.length - 1);
    }, 10);
  }, [routes]);

  const {data} = useGetLocations();

  const getCities = useMemo(() => (data || []).reduce<string[]>((acc, cur) => [...acc, ...Object.keys(cur)], []), [data]);

  const getProvinces = useMemo(() => {
    if (!selectedCity || !data) {
      return [];
    }

    for (const city of data) {
      if (Object.keys(city).includes(selectedCity)) {
        return Object.keys(city[selectedCity]);
      }
    }

    return [];
  }, [selectedCity, data]);

  const getWards = useMemo(() => {
    if (!data || !selectedCity || !selectedProvince) {
      return [];
    }
    for (const city of data) {
      if (Object.keys(city).includes(selectedCity)) {
        return city[selectedCity][Object.keys(city[selectedCity]).filter(province => province === selectedProvince)[0]] || [];
      }
    }
    return [];
  }, [data, selectedCity, selectedProvince]);

  const renderTabBar = (tabbarProps: SceneRendererProps & {navigationState: State}) => (
    <TabBar
      {...tabbarProps}
      scrollEnabled
      indicatorStyle={styles.indicator}
      style={styles.tabbar}
      tabStyle={styles.tab}
      renderLabel={scene => (
        <View>
          <Text numberOfLines={1} style={{color: scene.focused ? '#008060' : '#333', paddingHorizontal: 4}}>
            {scene.route.title}
          </Text>
        </View>
      )}
    />
  );

  const renderScene = useCallback(
    ({route}: any) => {
      switch (route.key) {
        case 'city':
          return <CityList items={getCities} onSelect={handleSelectCity} selectedItem={selectedCity} searchKeyword={debouncedSearchString} />;
        case 'province':
          return <LocationList items={getProvinces} onSelect={handleSelectProvince} selectedItem={selectedProvince} searchKeyword={debouncedSearchString} />;
        case 'ward':
          return <LocationList items={getWards} onSelect={handleSelectWard} selectedItem={selectedWard} searchKeyword={debouncedSearchString} />;

        default:
          return null;
      }
    },
    [getCities, getProvinces, getWards, selectedCity, selectedProvince, selectedWard, debouncedSearchString],
  );

  const handleIndexChange = useCallback((newIndex: number) => {
    setIndex(newIndex);
  }, []);

  const handleSelectCity = useCallback(
    (city: string) => {
      setSelectedCity(city);
      setRoutes(prev => [
        {...prev[0], title: city},
        {key: 'province', title: 'Quận / huyện'},
      ]);
    },
    [routes],
  );
  const handleSelectProvince = useCallback(
    (province: string) => {
      setSelectedProvince(province);
      setRoutes(prev => [
        prev[0],
        {...prev[1], title: province},
        {
          key: 'ward',
          title: 'Phường / xã',
        },
      ]);
    },
    [routes],
  );

  const handleSelectWard = useCallback(
    (ward: string) => {
      setSelectedWard(ward);
      props.onSelectDone?.({
        city: selectedCity,
        province: selectedProvince,
        ward: ward,
      });
    },
    [selectedCity, selectedProvince],
  );

  return (
    <View style={{flex: 1, backgroundColor: theme.colors.gray20}}>
      <Input
        containerStyle={[{paddingVertical: theme.spacing.s, marginBottom: theme.spacing.s, backgroundColor: theme.colors.white}, !!props.address1 && {marginBottom: 0}]}
        inputContainerStyle={{height: 40}}
        leftIcon={{
          type: 'ionicon',
          name: 'search-outline',
          size: 16,
        }}
        inputStyle={{
          fontSize: theme.typography.md,
          paddingLeft: theme.spacing.xs,
        }}
        value={searchString}
        onChangeText={setSearchString}
        placeholder="Tìm kiếm"
        renderErrorMessage={false}
        clearButtonMode="always"
      />
      {props.address1 && (
        <View style={styles.address1Container}>
          <Text style={{fontWeight: '500', fontSize: theme.typography.base}}>Địa chỉ: </Text>
          <Text style={{fontSize: theme.typography.base, flex: 1}}>{props.address1}</Text>
        </View>
      )}

      <TabView lazy renderTabBar={renderTabBar} navigationState={{index, routes}} renderScene={renderScene} onIndexChange={handleIndexChange} />
    </View>
  );
};

export const LocationItem: React.FC<{text: string; arrow: boolean; onSelect: (item: string) => void; selected: boolean}> = ({text, arrow, onSelect, selected}) => (
  <Pressable style={styles.locationItem} onPress={() => onSelect(text)}>
    <Text style={[styles.locationItem_text, {color: selected ? '#008060' : '#333'}]}>{text}</Text>
    {arrow && <Ionicons name="chevron-forward" size={20} color={theme.colors.textLightest} />}
  </Pressable>
);

const styles = StyleSheet.create({
  locationItem: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: theme.spacing.m - 4,
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,.05)',
  },
  locationItem_text: {
    fontSize: theme.typography.md,
  },
  tabbar: {
    backgroundColor: '#fff',
    borderBottomWidth: 2,
    borderBottomColor: 'rgba(0,0,0,.05)',
  },
  tab: {
    width: 'auto',
  },
  indicator: {
    backgroundColor: '#008060',
  },
  label: {
    fontSize: 16,
    fontWeight: '400',
    color: '#333',
    textTransform: 'none',
  },
  address1Container: {
    backgroundColor: theme.colors.white,
    flexDirection: 'row',
    paddingHorizontal: theme.spacing.m,
    paddingVertical: theme.spacing.s,
    marginBottom: theme.spacing.s,
  },
});

export default LocationTabView;
