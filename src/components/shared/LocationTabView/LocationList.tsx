import React, {useMemo} from 'react';
import {ScrollView} from 'react-native';
import {LocationItem} from './index';
import Fuse from 'fuse.js';

type LocationListProps = {
  items: string[];
  onSelect: (item: string) => void;
  selectedItem?: string;
  isCity?: boolean;
  searchKeyword?: string;
};

const LocationList: React.FC<LocationListProps> = props => {
  const fuse = useMemo(() => {
    return new Fuse(props.items, {});
  }, [props.items]);

  const results = useMemo(() => {
    return props.searchKeyword ? fuse.search(props.searchKeyword ?? '').map(item => item.item) : props.items;
  }, [props.items, props.searchKeyword]);

  return (
    <ScrollView style={{flex: 1}} keyboardShouldPersistTaps="handled">
      {results.map(item => (
        <LocationItem key={item} text={item} arrow onSelect={props.onSelect} selected={item === props.selectedItem} />
      ))}
    </ScrollView>
  );
};

export default LocationList;
