import {Button} from '@rneui/base';
import React, {useCallback, useEffect, useState} from 'react';
import {ActivityIndicator, Image, StyleSheet, Text, View, Modal} from 'react-native';
import {useAuthActions} from '~hooks';
import ModalScreenHeader from '../ModalScreenHeader';
import ee, {EventNames} from '~utils/events';
import theme from '~utils/config/themes/theme';
// @ts-ignore
import HappyRich from '~assets/happy-rich.png';

const LoginRequiredModal = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const {openSSOAuth, isLoggedIn, isLoggingIn} = useAuthActions();

  useEffect(() => {
    ee.on(EventNames.OpenLoginRequiredModal, open);
    ee.on(EventNames.CloseLoginRequiredModal, close);
    return () => {
      ee.off(EventNames.OpenLoginRequiredModal, open);
      ee.off(EventNames.CloseLoginRequiredModal, close);
    };
  }, []);

  useEffect(() => {
    if (isLoggedIn && modalVisible) {
      close();
    }
  }, [isLoggedIn]);

  const open = useCallback(() => {
    setModalVisible(true);
  }, []);

  const close = useCallback(() => {
    setModalVisible(false);
  }, []);

  const handleAuth = useCallback((authType: 'login' | 'register') => {
    openSSOAuth(authType);
  }, []);

  return (
    <Modal onRequestClose={close} style={{margin: 0}} visible={modalVisible}>
      <ModalScreenHeader title="Đăng nhập" onClose={close} />
      <View style={{flex: 1, backgroundColor: '#fff'}}>
        {isLoggingIn ? (
          <View style={styles.authLoading}>
            <ActivityIndicator size={30} />
            <Text>Đang đăng nhập</Text>
          </View>
        ) : (
          <View style={styles.container}>
            <Image source={HappyRich} style={{width: 250, height: 167, marginBottom: theme.spacing.m}} />
            <View style={{paddingHorizontal: theme.spacing.l}}>
              <Text style={{color: '#333', fontSize: theme.typography.lg2, textAlign: 'center'}}>Đăng nhập ngay, bắt đầu kiếm tiền với TTS Dropship</Text>
            </View>
            <View style={styles.actionContainer}>
              <Button
                buttonStyle={{backgroundColor: theme.colors.primary}}
                containerStyle={{marginTop: 16, borderRadius: 30}}
                title="Đăng nhập"
                titleStyle={{color: '#fff'}}
                onPress={() => handleAuth('login')}
              />
              <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 40}}>
                <Text>Chưa có tài khoản?</Text>
                <Button type="clear" titleStyle={{color: theme.colors.primary, fontWeight: 'bold', fontSize: 16}} title="Đăng ký ngay" onPress={() => handleAuth('register')} />
              </View>
            </View>
          </View>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  actionContainer: {
    marginTop: 100,
  },
  authLoading: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 200,
  },
  loginReason: {
    fontSize: 16,
    marginTop: 8,
  },
});

export default LoginRequiredModal;
