import {Badge} from '@rneui/themed';
import React from 'react';
import {View} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '~hooks';
import {useCountNotificationUnseen} from '~utils/api/notifications';
import theme from '~utils/config/themes/theme';

type NotificationIconProps = {
  color?: string;
};

const NotificationIcon: React.FC<NotificationIconProps> = props => {
  const {data} = useCountNotificationUnseen();
  const navigation = useNavigation();

  return (
    <View>
      <Ionicons
        name="notifications-outline"
        style={{paddingHorizontal: theme.spacing.s}}
        size={28}
        color={props.color ?? theme.colors.textLight}
        onPress={() => navigation.navigate('Notifications')}
        accessibilityLabel="Đến trang thông báo"
      />
      {Boolean(data) && <Badge status="error" containerStyle={{position: 'absolute', top: 1, right: 0}} />}
    </View>
  );
};

export default NotificationIcon;
