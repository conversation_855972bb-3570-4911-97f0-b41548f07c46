import {Text} from '@rneui/themed';
import * as React from 'react';
import {View, StyleSheet} from 'react-native';
import theme from '~utils/config/themes/theme';

export type Step = {
  title?: string | React.ReactNode;
  description?: string | React.ReactNode;
  isActive?: boolean;
};

type VerticalStepperProps = {
  steps: Step[];
  destructive?: boolean;
};

const VerticalStepper: React.FC<VerticalStepperProps> = props => {
  const colorSchemeLight = props.destructive ? '#fe2c5624' : '#0080602b';
  const colorScheme = props.destructive ? theme.colors.red : theme.colors.primary;

  if (!props.steps || props.steps.length === 0) {
    return null;
  }

  return (
    <View style={{flex: 1}}>
      <View style={styles.verticalLine} />
      <View style={styles.verticalWrap}>
        {props.steps.map((step, index) => (
          <View style={styles.itemWrap} key={index}>
            <View style={[styles.pointWrap, {justifyContent: 'center', alignItems: 'center', backgroundColor: index === 0 ? colorSchemeLight : 'transparent'}]}>
              <View style={{width: 10, height: 10, backgroundColor: index === 0 ? colorScheme : theme.colors.gray40, borderRadius: 30}} />
            </View>
            <View style={{marginLeft: 10, flex: 1}}>
              <Text
                style={[
                  {
                    color: index === 0 ? colorScheme : theme.colors.textLight,
                  },
                ]}>
                {step.title}
              </Text>
              {Boolean(step.description) && <View style={[{marginTop: 4}]}>{step.description}</View>}
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  verticalLine: {
    backgroundColor: 'rgba(0,0,0,.1)',
    width: 1,
    height: '95%',
    position: 'absolute',
    marginLeft: 13.5,
    marginTop: 20,
  },
  verticalWrap: {
    justifyContent: 'space-between',
    // height: '100%',
  },
  itemWrap: {
    // width: 200,
    // height: 70,

    marginBottom: 20,
    justifyContent: 'center',
    flexDirection: 'row',
    alignItems: 'flex-start',
  },

  pointWrap: {
    marginTop: 4,
    borderRadius: 30,
    height: 20,
    width: 20,
    marginLeft: 4,
  },
  markerText: {color: 'white'},
  currentMarker: {color: 'green'},
});

export default VerticalStepper;
