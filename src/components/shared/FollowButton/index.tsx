import {Text, ThemeContext} from '@rneui/themed';
import React, {useCallback, useEffect, useState} from 'react';
import {ViewStyle} from 'react-native';
import {Pressable, StyleProp, StyleSheet, View} from 'react-native';
import {useToast} from 'react-native-toast-notifications';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useAuthActions} from '~hooks';
import {useGetIsFollowing, useUpdateFollowShopMutation} from '~utils/api/shop';
import theme from '~utils/config/themes/theme';

type FollowButtonProps = {
  shopId: string;
  styles?: StyleProp<ViewStyle>;
  hideFollowed?: boolean;
  tintColor?: string;
};

const FollowButton: React.FC<FollowButtonProps> = props => {
  const {data} = useGetIsFollowing(props.shopId);
  const [isFollowed, setFollowed] = useState(data);
  const {isLoggedIn, openLoginRequiredModal} = useAuthActions();
  const toast = useToast();

  useEffect(() => {
    setFollowed(data);
  }, [data]);

  const mutation = useUpdateFollowShopMutation();

  const handleFollow = useCallback(() => {
    if (!isLoggedIn) {
      openLoginRequiredModal();
      return;
    }
    setFollowed(true);
    mutation.mutate({
      followState: true,
      shopId: props.shopId,
    });
    toast.show('Đã theo dõi', {
      placement: 'center',
      duration: 2000,
    });
  }, [isFollowed, props.shopId, isLoggedIn]);

  const handleUnFollow = useCallback(() => {
    setFollowed(false);
    mutation.mutate({
      followState: true,
      shopId: props.shopId,
    });
    toast.show('Đã bỏ theo dõi', {
      placement: 'center',
      duration: 2000,
    });
  }, [isFollowed, props.shopId]);

  return (
    <View style={props.styles}>
      {isFollowed ? (
        props.hideFollowed ? null : (
          <Pressable style={styles.followingBtn} onPress={handleUnFollow}>
            <Text style={{fontSize: theme.typography.base, color: theme.colors.textLight}}>Đang theo dõi</Text>
          </Pressable>
        )
      ) : (
        <Pressable style={[styles.followBtn, {borderColor: props.tintColor || theme.colors.primary}]} onPress={handleFollow}>
          <Ionicons name="add-sharp" size={12} color={props.tintColor || theme.colors.primary} />
          <Text style={{fontSize: theme.typography.base, color: props.tintColor || theme.colors.primary}}>Theo dõi</Text>
        </Pressable>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  followingBtn: {
    backgroundColor: theme.colors.gray10,
    paddingHorizontal: 8,
    borderRadius: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  followBtn: {
    borderWidth: 1,
    paddingHorizontal: 8,
    borderRadius: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default FollowButton;
