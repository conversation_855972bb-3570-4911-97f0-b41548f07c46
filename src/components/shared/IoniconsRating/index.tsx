import React from 'react';
import {View} from 'react-native';

import Ionicons from 'react-native-vector-icons/Ionicons';

type IoniconsRatingProps = {
  size: number;
  value: number;
};

const IoniconsRating: React.FC<IoniconsRatingProps> = props => {
  return (
    <View style={{flexDirection: 'row'}}>
      {Array.from({length: 5}).map((_, i) => (
        <Ionicons key={i} name={i < props.value ? 'star' : 'star-outline'} size={props.size} color={'#ffc400'} />
      ))}
    </View>
  );
};

export default IoniconsRating;
