import {Input, InputProps} from '@rneui/themed';
import React from 'react';
import {StyleSheet, View} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import theme from '~utils/config/themes/theme';

type SearchBarProps = {} & InputProps;

const SearchBar: React.FC<SearchBarProps> = props => {
  return (
    <View style={styles.container}>
      <Ionicons name="search" size={20} color={theme.colors.textLight} style={{position: 'absolute', top: 10, left: 24}} />
      <Input
        {...props}
        placeholder="Tìm ngân hàng"
        inputStyle={{fontSize: theme.typography.md}}
        inputContainerStyle={{borderWidth: 1, borderRadius: 30, borderColor: theme.colors.gray40, paddingHorizontal: 40}}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
});

export default SearchBar;
