import {NativeStackHeaderRightProps} from '@react-navigation/native-stack';
import React, {useCallback} from 'react';
import {StyleSheet} from 'react-native';
import {Pressable} from 'react-native-gesture-handler';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '~hooks';
import theme from '~utils/config/themes/theme';

type HeaderRightSearchProps = React.PropsWithChildren<NativeStackHeaderRightProps>;

const HeaderRightSearch: React.FC<HeaderRightSearchProps> = props => {
  const navigation = useNavigation();

  const handleSearchPress = useCallback(() => {
    navigation.navigate('Search');
  }, []);

  return (
    <Pressable style={[styles.iconContainer]} onPress={handleSearchPress}>
      <Ionicons name="search-outline" color={props.tintColor} size={24} />
    </Pressable>
  );
};

const styles = StyleSheet.create({
  iconContainer: {
    paddingHorizontal: theme.spacing.s,
    paddingVertical: theme.spacing.xs,
  },
});

export default HeaderRightSearch;
