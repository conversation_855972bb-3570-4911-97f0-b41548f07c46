import {Button, Text} from '@rneui/themed';
import React, {useCallback, useEffect, useState} from 'react';
import {ActivityIndicator, Linking, Platform, StyleSheet, TouchableOpacity, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {ASYNC_STORAGE_KEYS} from '~utils/config';
import {useAsyncStorage} from '@react-native-async-storage/async-storage';
import {formatPriceToString} from '~utils/helpers/price';
import {useGetIncomeSummary} from '~utils/api/wallet';
import {useAuthActions, useNavigation} from '~hooks';
import useRefreshOnFocus from '~hooks/useRefreshOnFocus';
import isInZaloMiniApp from '~utils/helpers/isInZaloMiniApp';
import AnimatedNumber from '../AnimatedNumber';

type BalanceOverviewProps = {
  primaryAction?: React.ReactNode;
};

const BalanceOverview: React.FC<BalanceOverviewProps> = props => {
  const navigation = useNavigation();
  const [balanceVisible, setBalanceVisible] = useState(true);
  const {getItem, setItem} = useAsyncStorage(ASYNC_STORAGE_KEYS.MONEY_MASK);
  const {data, refetch, isLoading, isError} = useGetIncomeSummary();
  const {isLoggedIn} = useAuthActions();

  useRefreshOnFocus(refetch);

  useEffect(() => {
    getIsMaskedMoney();
  }, []);

  const getIsMaskedMoney = useCallback(async () => {
    const res = await getItem();
    setBalanceVisible(!parseInt(res as any, 10));
  }, []);

  const toggleBalanceVisible = useCallback(() => {
    setBalanceVisible(!balanceVisible);
    setItem(balanceVisible ? '1' : '0');
  }, [balanceVisible]);

  const handleLearnMore = useCallback(() => {
    if (Platform.OS === 'web') {
      Linking.openURL('https://thitruongsi.com/pages/docs/giai-thich-cac-thuat-ngu-cua-app-tts-dropship#1-toc-title');
    } else {
      navigation.navigate('WebView', {url: 'https://thitruongsi.com/pages/docs/giai-thich-cac-thuat-ngu-cua-app-tts-dropship#1-toc-title', title: 'Các thuật ngữ thường gặp'});
    }
  }, []);

  const handleGoToEscrowTransaction = useCallback(() => {
    navigation.navigate('EscrowTransactions');
  }, []);

  const handleGoToWalletTransaction = useCallback(() => {
    navigation.navigate('Transactions');
  }, []);

  const handleGoToTaxTransaction = useCallback(() => {
    navigation.navigate('TaxTransactions');
  }, []);

  const handleRefetch = useCallback(() => {
    refetch();
  }, [refetch]);

  return (
    <>
      <View style={[styles.withdrawable, {borderBottomLeftRadius: 0, borderBottomRightRadius: 0, borderBottomWidth: 1, borderBottomColor: 'rgba(0,0,0,0.07)'}]}>
        {isError && isLoggedIn ? (
          <View style={{flexDirection: 'row', marginBottom: theme.spacing.s}}>
            <Text style={{color: theme.colors.red, fontSize: theme.typography.base}}>Có lỗi xảy ra khi lấy số dư</Text>
            <TouchableOpacity onPress={handleRefetch}>
              <Text style={{color: theme.colors.blue, marginLeft: theme.spacing.s, fontSize: theme.typography.base}}>Thử lại</Text>
            </TouchableOpacity>
          </View>
        ) : null}
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <Text allowFontScaling={false} style={[styles.text1, {marginRight: 8}]}>
            Tiền chờ đối soát
          </Text>
          <Ionicons name={balanceVisible ? 'eye-off-outline' : 'eye-outline'} size={20} color="rgba(0,0,0,.4)" onPress={toggleBalanceVisible} />

          <Ionicons name={'reload'} size={20} color="rgba(0,0,0,.4)" onPress={handleRefetch} style={{marginLeft: 8}} />
          {!isInZaloMiniApp() && (
            <Button
              type="clear"
              containerStyle={{marginLeft: 'auto'}}
              onPress={handleLearnMore}
              buttonStyle={{paddingVertical: 0, paddingHorizontal: 0}}
              titleStyle={{fontSize: 14, color: theme.colors.blue}}
              icon={{
                type: 'ionicon',
                name: 'help-circle-outline',
                size: 18,
                color: theme.colors.blue,
              }}>
              Tìm hiểu thêm
            </Button>
          )}
        </View>
        {isLoggedIn && isLoading && (
          <View style={{alignItems: 'flex-start', marginVertical: 8}}>
            <ActivityIndicator />
          </View>
        )}
        {!isLoading && isLoggedIn && (
          <TouchableOpacity onPress={handleGoToEscrowTransaction}>
            {balanceVisible ? (
              <AnimatedNumber
                value={data?.commission_frozen ?? 0}
                style={[styles.currency, {paddingVertical: 0, marginTop: theme.spacing.s}]}
                formatter={value => formatPriceToString(value, false, 0) + 'đ'}
              />
            ) : (
              <Text allowFontScaling={false} style={[styles.currency, {marginTop: 12}]}>
                *** *** ***
              </Text>
            )}
          </TouchableOpacity>
        )}
        {!isLoggedIn && (
          <Text allowFontScaling={false} style={[styles.currency, {marginTop: 12}]}>
            {balanceVisible ? formatPriceToString(0, false) + 'đ' : '*** *** ***'}
          </Text>
        )}
      </View>
      <TouchableOpacity onPress={handleGoToTaxTransaction} style={[styles.withdrawable, {borderRadius: 0, borderBottomWidth: 1, borderBottomColor: 'rgba(0,0,0,0.07)'}]}>
        <View style={theme.globalStyles.flexRow}>
          <View>
            <Text allowFontScaling={false} style={[styles.text1, {marginRight: 8}]}>
              Thuế TNCN tạm giữ
            </Text>
            {!isLoading && isLoggedIn && (
              <View>
                {balanceVisible ? (
                  <AnimatedNumber
                    value={data?.tax_deduction_pending ?? 0}
                    style={[styles.currency, {paddingVertical: 0, marginTop: theme.spacing.s}]}
                    formatter={value => formatPriceToString(value, false, 0) + 'đ'}
                  />
                ) : (
                  <Text allowFontScaling={false} style={[styles.currency, {marginTop: 12}]}>
                    *** *** ***
                  </Text>
                )}
              </View>
            )}
          </View>
          <View style={{marginLeft: 'auto'}}>
            <Ionicons name="chevron-forward-outline" size={20} color="rgba(0,0,0,.4)" />
          </View>
        </View>
      </TouchableOpacity>
      <View style={[styles.withdrawable, {borderTopLeftRadius: 0, borderTopRightRadius: 0}]}>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <Text allowFontScaling={false} style={[styles.text1, {marginRight: 8}]}>
            Tiền có thể rút
          </Text>
        </View>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          {isLoggedIn && isLoading && (
            <View style={{alignItems: 'flex-start', marginVertical: 8}}>
              <ActivityIndicator />
            </View>
          )}
          {!isLoading && isLoggedIn && (
            <TouchableOpacity onPress={handleGoToWalletTransaction}>
              {balanceVisible ? (
                <AnimatedNumber
                  value={data?.can_withdrawl ?? 0}
                  style={[styles.currency, {paddingVertical: 0, marginTop: theme.spacing.s}]}
                  formatter={value => formatPriceToString(value, false, 0) + 'đ'}
                />
              ) : (
                <Text allowFontScaling={false} style={[styles.currency, {marginTop: 12}]}>
                  *** *** ***
                </Text>
              )}
            </TouchableOpacity>
          )}
          {!isLoggedIn && (
            <Text allowFontScaling={false} style={[styles.currency, {marginTop: 12}]}>
              {balanceVisible ? formatPriceToString(0, false) + 'đ' : '*** *** ***'}
            </Text>
          )}

          {props.primaryAction && props.primaryAction}
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  withdrawable: {
    backgroundColor: '#fff',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 12,
  },
  text1: {
    fontSize: theme.typography.md,
  },
  currency: {
    fontSize: theme.typography.lg1,
    color: theme.colors.text,
  },
});

export default BalanceOverview;
