import React from 'react';
import {Pressable} from 'react-native';
import {ProductImage} from '~utils/types/product';
// import './img-with-download.css';
import Ionicons from 'react-native-vector-icons/Ionicons';
import theme from '~utils/config/themes/theme';
import instance from '~utils/api/axios';
import {getDownloadableURL} from '~utils/helpers/image';
import './with-download.css';
import {Text} from '@rneui/themed';

type ImageWithDownloadProps = {
  image: ProductImage;
  fileName: string;
  children: React.ReactNode;
};

const ImageWithDownload: React.FC<ImageWithDownloadProps> = props => {
  const handlePress = async () => {
    const response = await instance.get(getDownloadableURL(props.image.src.replace('rs:fill:600', 'rs:fill:1000'), props.image.product_id, ''), {
      responseType: 'blob',
    });

    // create file link in browser's memory
    const href = URL.createObjectURL(response.data);

    // create "a" HTML element with href to file & click
    const link = document.createElement('a');
    link.href = href;
    link.setAttribute('download', props.fileName); //or any other extension
    document.body.appendChild(link);
    link.click();

    // clean up "a" element & remove ObjectURL
    document.body.removeChild(link);
    URL.revokeObjectURL(href);
  };
  return (
    <div className="with-download">
      {props.children}

      <Pressable onPress={handlePress}>
        <div className="with-download__overlay">
          <Ionicons name="download-outline" size={20} color={theme.colors.white} />
          <Text style={{fontSize: theme.typography.sm, color: theme.colors.white, marginLeft: 2}}>Tải xuống</Text>
        </div>
      </Pressable>
    </div>
  );
};

export default ImageWithDownload;
