import React from 'react';
import {StyleSheet, Dimensions, Image, View, TouchableOpacity, Pressable} from 'react-native';
import {PanGestureHandler} from 'react-native-gesture-handler';
import Animated, {useAnimatedGestureHandler, useAnimatedStyle, useSharedValue, withSpring} from 'react-native-reanimated';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useGetAdsContent} from '~utils/api/banner';
import theme from '~utils/config/themes/theme';
import {navigationRef} from '~utils/navigation/RootNavigation';

const {width, height} = Dimensions.get('window');
const BUTTON_SIZE = 90;

const FloatingBanner = () => {
  const insets = useSafeAreaInsets();
  const translateX = useSharedValue(width - BUTTON_SIZE - 20);
  const translateY = useSharedValue(height - BUTTON_SIZE - insets.bottom - 160);
  const [isShow, setShow] = React.useState(true);
  const {data} = useGetAdsContent('floating_banner');

  const panGestureEvent = useAnimatedGestureHandler({
    onStart: (_, context) => {
      context.startX = translateX.value;
      context.startY = translateY.value;
    },
    onActive: (event, context) => {
      translateX.value = context.startX + event.translationX;
      translateY.value = context.startY + event.translationY;
    },
    onEnd: event => {
      // Xác định xem button nên ở bên trái hay bên phải
      const shouldBeOnLeft = translateX.value < width / 2;

      // Tính toán vị trí X mới
      const newX = shouldBeOnLeft ? 20 : width - BUTTON_SIZE - 20;

      // Giới hạn vị trí Y trong màn hình
      const newY = Math.max(20, Math.min(translateY.value, height - BUTTON_SIZE - insets.bottom - 20));

      // Áp dụng animation để di chuyển button
      translateX.value = withSpring(newX, {damping: 20});
      translateY.value = withSpring(newY, {damping: 20});
    },
  });

  const rStyle = useAnimatedStyle(() => {
    return {
      transform: [{translateX: translateX.value}, {translateY: translateY.value}],
    };
  });

  const handleClose = () => {
    setShow(false);
  };

  const handlePressBanner = (url: string) => {
    if (navigationRef?.isReady()) {
      navigationRef.navigate('WebView', {url: url, includeAuth: true, redirectNativeScreen: true});
    }
  };

  if (!isShow || !data?.length) {
    return null;
  }

  return (
    <PanGestureHandler onGestureEvent={panGestureEvent}>
      {data?.[0] && (
        <Animated.View style={[styles.button, rStyle]}>
          <View style={{position: 'relative'}}>
            <TouchableOpacity style={{position: 'absolute', right: -8, top: -8, zIndex: 1001, backgroundColor: 'rgba(0,0,0,.6)', borderRadius: 100}} onPress={handleClose}>
              <Ionicons name="close-outline" size={24} color={theme.colors.white} />
            </TouchableOpacity>
            <Pressable onPress={() => handlePressBanner(data[0].link)}>
              <Image
                source={{
                  uri: data[0].banner,
                }}
                style={{width: BUTTON_SIZE, height: BUTTON_SIZE}}
              />
            </Pressable>
          </View>
        </Animated.View>
      )}
    </PanGestureHandler>
  );
};

const styles = StyleSheet.create({
  button: {
    width: BUTTON_SIZE,
    height: BUTTON_SIZE,
    borderRadius: BUTTON_SIZE / 2,
    position: 'absolute',
    zIndex: 1000,
  },
});

export default FloatingBanner;
