import {Text} from '@rneui/themed';
import {useCallback, useMemo} from 'react';
import {Dimensions, Platform, StyleSheet, TouchableOpacity, View} from 'react-native';
import theme from '~utils/config/themes/theme';
import {QuickImage} from '../QuickImage';
import {useAuthActions, useNavigation} from '~hooks';
import {env} from '~utils/config';
// @ts-ignore
import TiktokShopIcon from '~assets/tiktokshop.png';
// @ts-ignore
// import FastBackMiniAppLogo from '~assets/fastback-miniapp-logo.png';
// @ts-ignore
import LuckyCat2 from '~assets/luckycat2.png';

import {responsiveWidth} from '../web/WebContainer';

const CommonTools = () => {
  const navigation = useNavigation();
  const {isLoggedIn, authData, openLoginRequiredModal} = useAuthActions();

  const goToTiktokShopConnect = useCallback(() => {
    if (!isLoggedIn) {
      openLoginRequiredModal();
      return;
    }

    navigation.push('WebView', {
      url: `${env.OAUTH_URI}/connect/tiktok-shop?${isLoggedIn && Platform.OS === 'web' ? `access_token=${authData?.access_token}&refresh_token=${authData?.refresh_token}` : ''}`,
      includeAuth: true,
    });
  }, [isLoggedIn, authData]);

  const goToFastbackMiniApp = useCallback(() => {
    if (!isLoggedIn) {
      openLoginRequiredModal();
      return;
    }
    const miniAppUrl = `${env.FASTBACK_MINIAPP_URL}?t=${authData.access_token}`;
    navigation.push('WebView', {
      url: miniAppUrl,
    });
  }, [isLoggedIn, authData]);

  const goToProductPickerGame = useCallback(() => {
    navigation.push('ProductPickerGame');
  }, [navigation]);

  const menuList = useMemo(() => {
    return [
      {icon: <QuickImage source={TiktokShopIcon} style={{width: 24, height: 27}} />, label: 'Bán trên Tiktok Shop', onPress: goToTiktokShopConnect},
      // {icon: <QuickImage source={FastBackMiniAppLogo} style={{width: 27, height: 27}} />, label: 'Fastback - Mua sắm hoàn tiền', onPress: goToFastbackMiniApp},
      {icon: <QuickImage source={LuckyCat2} style={{width: 27, height: 27}} />, label: 'Hôm nay bán gì?', onPress: goToProductPickerGame},
    ];
  }, [isLoggedIn, authData]);

  return (
    <View style={styles.container}>
      {menuList.map((item, index) => (
        <TouchableOpacity key={index} style={styles.menuItem} onPress={item.onPress}>
          {item.icon}
          <Text style={styles.menuItemLabel}>{item.label}</Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: theme.spacing.m,
    backgroundColor: theme.colors.white,
    borderRadius: 12,
    display: 'flex',
    flexDirection: 'row',
    gap: theme.spacing.s,
  },
  menuItem: {
    gap: theme.spacing.s,
    alignItems: 'center',
    maxWidth: Math.min(Dimensions.get('window').width, responsiveWidth.lg) * 0.3,
    padding: theme.spacing.s,
    paddingVertical: theme.spacing.m,
    borderRadius: 12,
  },
  menuItemLabel: {
    fontSize: theme.typography.sm,
    color: theme.colors.text,
    textAlign: 'center',
  },
});

export default CommonTools;
