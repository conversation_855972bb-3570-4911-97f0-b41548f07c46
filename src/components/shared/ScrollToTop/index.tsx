import React from 'react';
import {StyleSheet, TouchableOpacity} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import theme from '~utils/config/themes/theme';

type ScrollToTopProps = {
  onPress: () => void;
};

const ScrollToTop: React.FC<ScrollToTopProps> = props => {
  return (
    <TouchableOpacity onPress={props.onPress} style={styles.container}>
      <Ionicons name="chevron-up-outline" size={26} color={theme.colors.textLight} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    position: 'absolute',
    bottom: 30,
    right: 12,
    // padding: 5,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 100,
    borderWidth: 1,
    borderColor: theme.colors.gray20,
  },
});

export default ScrollToTop;
