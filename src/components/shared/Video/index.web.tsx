import React from 'react';
import {ReactVideoProps, VideoRef} from 'react-native-video';

export const Video: React.ForwardRefExoticComponent<ReactVideoProps & React.RefAttributes<VideoRef>> = props => {
  return (
    <video>
      <source src="movie.mp4" type="video/mp4" />
      <source src="movie.ogg" type="video/ogg" />
      Your browser does not support the video tag.
    </video>
  );
};

export default Video;
