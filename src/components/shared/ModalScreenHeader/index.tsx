import {Text} from '@rneui/base';
import React from 'react';
import {StyleSheet, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import theme from '~utils/config/themes/theme';

type ModalScreenHeaderProps = {
  title?: string;
  onClose?: () => void;
};

const ModalScreenHeader: React.FC<ModalScreenHeaderProps> = props => {
  const insets = useSafeAreaInsets();

  return (
    <View style={[styles.container, {paddingTop: insets.top + 12}]}>
      <Ionicons name="close-outline" size={30} style={{paddingHorizontal: 8}} onPress={props.onClose} color={theme.colors.text} />
      {props.title && <Text style={styles.title}>{props.title}</Text>}
      <View style={{paddingHorizontal: 20}} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomColor: 'rgba(0,0,0,.05)',
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default ModalScreenHeader;
