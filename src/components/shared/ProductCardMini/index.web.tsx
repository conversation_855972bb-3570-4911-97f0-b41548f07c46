import React, {useCallback, useMemo, useState} from 'react';
import {Platform, Pressable, StyleSheet, View} from 'react-native';
import {handleImageDomain} from '~utils/helpers/image';
import {formatPriceToString, kFormatter} from '~utils/helpers/price';
import {ProductBadge, ProductBadgePlacement, SearchProduct} from '~utils/types/product';
import {Text} from '@rneui/themed';
import theme from '~utils/config/themes/theme';
import {ScreenWidth} from '@rneui/base';
import {QuickImage} from '../QuickImage';
import groupBy from 'lodash/fp/groupBy';
import {responsiveWidth} from '../web/WebContainer';
import {useLinkProps} from '@react-navigation/native';
import {useAuthActions, useNavigation} from '~hooks';
import isInZaloMiniApp from '~utils/helpers/isInZaloMiniApp';
import Ionicons from 'react-native-vector-icons/Ionicons';

type ProductCardMiniProps = {
  product: SearchProduct;
};

const ProductCardMini: React.FC<ProductCardMiniProps> = props => {
  const {onPress, ...linkProps} = useLinkProps({
    href: `/product/${props.product.product_id}`,
    screen: 'ProductDetail',
    params: {productId: props.product.product_id},
  });
  const navigation = useNavigation();
  const [isZaloMiniApp] = useState(isInZaloMiniApp());
  const {isLoggedIn} = useAuthActions();

  const badgesGroupBy = useMemo<{[key in ProductBadgePlacement]: ProductBadge[]}>(() => {
    return groupBy('placement', props.product.badges) as any;
  }, [props.product.product_id]);

  const handlePress = useCallback(
    (e: any) => {
      if (Platform.OS === 'web') {
        return onPress(e);
      }

      return navigation.push('ProductDetail', {productId: props.product.product_id});
    },
    [props.product.product_id, onPress],
  );

  return (
    <Pressable style={styles.container} onPress={handlePress} {...linkProps}>
      <View style={styles.thumbContainer}>
        {/* eslint-disable-next-line react/self-closing-comp */}
        <View style={styles.thumbSpacer}></View>
        <QuickImage
          style={styles.thumb}
          source={{
            uri: handleImageDomain(props.product.image_thumb),
          }}
        />
        {badgesGroupBy.dropship_top_left && (
          <View style={[theme.globalStyles.flexRow, {marginBottom: theme.spacing.xs, position: 'absolute', top: 0, left: 0}]}>
            {badgesGroupBy.dropship_top_left.map(badge => (
              <View
                key={badge.id}
                style={[
                  theme.globalStyles.flexRow,
                  {
                    backgroundColor: badge.text_bg_color,
                    padding: 2,
                    borderRadius: 2,
                    borderWidth: 0,
                    borderColor: badge.border_color || 'transparent',
                    transform: [{scale: 0.8}, {translateX: -10}, {translateY: -4}],
                  },
                ]}>
                {Boolean(badge.icon) && <QuickImage source={{uri: badge.icon}} style={{width: badge.icon_width, height: badge.icon_height, marginRight: 4}} />}
                <Text style={{color: badge.text_color, fontSize: theme.typography.xs}}>{badge.text}</Text>
              </View>
            ))}
          </View>
        )}
        {badgesGroupBy.dropship_top_right && (
          <View style={[theme.globalStyles.flexRow, {position: 'absolute', top: 0, right: 0}]}>
            {badgesGroupBy.dropship_top_right.map(badge => (
              <View
                key={badge.id}
                style={[
                  theme.globalStyles.flexRow,
                  {
                    backgroundColor: badge.text_bg_color,
                    borderRadius: 2,
                    borderWidth: 0,
                    borderColor: badge.border_color || 'transparent',
                    transform: [{scale: 0.8}, {translateX: 4}, {translateY: -4}],
                  },
                ]}>
                {Boolean(badge.icon) && <QuickImage source={{uri: badge.icon}} style={{width: badge.icon_width, height: badge.icon_height}} />}
                <Text style={{color: badge.text_color, fontSize: theme.typography.xs}}>{badge.text}</Text>
              </View>
            ))}
          </View>
        )}
        {badgesGroupBy.dropship_bottom_left && (
          <View style={[theme.globalStyles.flexRow, {position: 'absolute', bottom: 0, left: 0}]}>
            {badgesGroupBy.dropship_bottom_left.map(badge => (
              <View
                key={badge.id}
                style={[theme.globalStyles.flexRow, {backgroundColor: badge.text_bg_color, padding: 2, borderRadius: 2, borderWidth: 0, borderColor: badge.border_color || 'transparent'}]}>
                {Boolean(badge.icon) && <QuickImage source={{uri: badge.icon}} style={{width: badge.icon_width, height: badge.icon_height, marginRight: 4}} />}
                <Text style={{color: badge.text_color, fontSize: theme.typography.xs}}>{badge.text}</Text>
              </View>
            ))}
          </View>
        )}
        {badgesGroupBy.dropship_bottom_right && (
          <View style={[theme.globalStyles.flexRow, {position: 'absolute', bottom: 0, right: 0}]}>
            {badgesGroupBy.dropship_bottom_right.map(badge => (
              <View
                key={badge.id}
                style={[theme.globalStyles.flexRow, {backgroundColor: badge.text_bg_color, padding: 2, borderRadius: 2, borderWidth: 0, borderColor: badge.border_color || 'transparent'}]}>
                {Boolean(badge.icon) && <QuickImage source={{uri: badge.icon}} style={{width: badge.icon_width, height: badge.icon_height, marginRight: 4}} />}
                <Text style={{color: badge.text_color, fontSize: theme.typography.xs}}>{badge.text}</Text>
              </View>
            ))}
          </View>
        )}
      </View>
      {badgesGroupBy.dropship_before_title && (
        <View style={[theme.globalStyles.flexRow, {marginTop: theme.spacing.s, flexWrap: 'wrap'}]}>
          {badgesGroupBy.dropship_before_title.map(badge => (
            <View
              key={badge.id}
              style={[
                {
                  flexDirection: 'row',
                  alignItems: 'stretch',
                  backgroundColor: badge.text_bg_color,
                  padding: 2,
                  borderRadius: 2,
                  borderWidth: 1,
                  borderColor: badge.border_color || 'transparent',
                  marginLeft: theme.spacing.xs,
                },
              ]}>
              {Boolean(badge.icon) && <QuickImage source={{uri: badge.icon}} style={{width: badge.icon_width || 15, height: badge.icon_height || 15, marginRight: 4}} />}
              <Text style={{color: badge.text_color, fontSize: theme.typography.xs}}>{badge.text}</Text>
            </View>
          ))}
        </View>
      )}
      <View style={styles.titleContainer}>
        <Text numberOfLines={2} style={styles.title}>
          {props.product.title}
        </Text>

        {Boolean(props.product.dropship_price) && (
          <>
            {Platform.OS === 'web' && !isZaloMiniApp && !isLoggedIn ? (
              <View style={{backgroundColor: '#fff8e7', alignSelf: 'flex-start', marginTop: 8}}>
                <Text style={{color: theme.colors.text, fontSize: theme.typography.sm}}>Đăng nhập để xem giá</Text>
              </View>
            ) : (
              <View style={[theme.globalStyles.flexRow, {marginTop: 8}]}>
                <Text style={styles.price}>{formatPriceToString(props.product.dropship_price)}</Text>
                {/* {Boolean(props.product.unit_name) && <Text style={styles.unitName}>/ {props.product.unit_name}</Text>} */}
              </View>
            )}
          </>
        )}
        {Boolean(props.product.dropship_profit) && (
          <Text style={styles.profitText}>
            <Text style={styles.profit}>+{formatPriceToString(props.product.dropship_profit)}</Text>
          </Text>
        )}

        <View style={[theme.globalStyles.flexRow, {marginTop: theme.spacing.s}]}>
          {props.product.rating_avg > 0 && (
            <View style={[theme.globalStyles.flexRow]}>
              <Ionicons name="star" size={12} color={'#ffc400'} />
              <Text style={styles.ratingAvgText}>{props.product.rating_avg?.toFixed(1)}</Text>
            </View>
          )}
          {props.product.total_sales > 0 && (
            <>
              {props.product.rating_avg > 0 && <View style={styles.totalSaleSeparator} />}
              <Text style={styles.totalSaleText}>Đã bán {kFormatter(props.product.total_sales)}</Text>
            </>
          )}
        </View>
      </View>

      {badgesGroupBy.dropship_after_title && (
        <View style={[theme.globalStyles.flexRow, {marginBottom: theme.spacing.xs, flexWrap: 'wrap'}]}>
          {badgesGroupBy.dropship_after_title.map(badge => (
            <View
              key={badge.id}
              style={[
                {
                  flexDirection: 'row',
                  alignItems: 'stretch',
                  backgroundColor: badge.text_bg_color,
                  padding: 2,
                  borderRadius: 2,
                  borderWidth: 1,
                  borderColor: badge.border_color || 'transparent',
                  marginLeft: theme.spacing.xs,
                  marginTop: theme.spacing.xs,
                },
              ]}>
              {Boolean(badge.icon) && <QuickImage source={{uri: badge.icon}} style={{width: badge.icon_width || 15, height: badge.icon_height || 15, marginRight: 4}} />}
              <Text style={{color: badge.text_color, fontSize: theme.typography.xs}}>{badge.text}</Text>
            </View>
          ))}
        </View>
      )}
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 4,
    width: Math.min(ScreenWidth, responsiveWidth.sm) / 2.4,
    marginHorizontal: 6,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: theme.colors.gray10,
    display: 'flex',
    flexDirection: 'column',
  },
  titleContainer: {
    padding: 4,
    marginVertical: 4,
  },
  text: {
    fontSize: theme.typography.sm,
    color: theme.colors.textLight,
  },
  title: {
    fontSize: theme.typography.base,
    fontWeight: '400',
  },
  thumbContainer: {
    position: 'relative',
  },
  thumbSpacer: {
    paddingTop: '100%',
  },
  thumb: {
    ...StyleSheet.absoluteFillObject,
    // borderRadius: 4,
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
  },
  price: {
    fontWeight: '500',
    fontSize: theme.typography.md,
  },
  profitText: {
    fontSize: 12,
    marginTop: 4,
    color: theme.colors.textLight,
  },
  profit: {
    fontSize: theme.typography.sm,
    fontWeight: '500',
    color: '#008060',
  },
  unitName: {
    fontSize: theme.typography.sm,
    color: theme.colors.textLight,
    marginLeft: theme.spacing.xs,
  },
  totalSaleText: {
    fontSize: theme.typography.sm,
    color: theme.colors.textLight,
  },
  totalSaleSeparator: {
    width: 1,
    height: 8,
    backgroundColor: theme.colors.gray40,
    marginHorizontal: theme.spacing.xs,
  },
  ratingAvgText: {
    fontSize: theme.typography.sm,
    color: theme.colors.textLight,
    marginLeft: 2,
  },
});

export default ProductCardMini;
