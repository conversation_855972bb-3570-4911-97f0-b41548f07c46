import {Text} from '@rneui/themed';
import React from 'react';
import {Pressable, StyleSheet} from 'react-native';
import theme from '~utils/config/themes/theme';
import {Bank} from '~utils/types/common';

type BankItemProps = {
  isActive?: boolean;
  bank: Bank;
  onPress: (bank: Bank) => void;
};

const BankItem: React.FC<BankItemProps> = props => {
  return (
    <Pressable style={styles.container} onPress={() => props.onPress(props.bank)}>
      <Text style={[styles.text, {color: props.isActive ? theme.colors.primary : theme.colors.text}]}>
        {props.bank.code} - {props.bank.short_name} - {props.bank.name}
      </Text>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderBottomColor: theme.colors.gray20,
    borderBottomWidth: 1,
  },
  text: {
    fontSize: theme.typography.base,
  },
});

export default BankItem;
