import {BottomSheetBackdrop, BottomSheetFlatList, BottomSheetModal} from '@gorhom/bottom-sheet';
import Fuse from 'fuse.js';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {ListRenderItemInfo, View} from 'react-native';
import {useDebounce, useUpdateEffect} from '~hooks';
import {useGetBanks} from '~utils/api/wallet';
import {Bank} from '~utils/types/common';
import SearchBar from '../SearchBar';
import BankItem from './BankItem';
import {useReducedMotion} from 'react-native-reanimated';

type BankSelectModal = {
  selectedId?: number;
  onChange: (bank: Bank) => void;
  open: boolean;
  onClose: () => void;
};

const BankSelectModal: React.FC<BankSelectModal> = props => {
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  const [searchString, setSearchString] = useState('');
  const [debouncedSearchString] = useDebounce(searchString, 300);
  const [searchResult, setSearchResult] = useState<any[]>([]);

  const {data} = useGetBanks();
  const reducedMotion = useReducedMotion();

  useUpdateEffect(() => {
    if (debouncedSearchString) {
      setSearchResult(
        fuse.search(debouncedSearchString).map(result => {
          return result.item;
        }),
      );
    } else {
      setSearchResult(data);
    }
  }, [debouncedSearchString]);

  useEffect(() => {
    if (props.open) {
      bottomSheetRef.current?.present();
    } else {
      bottomSheetRef.current?.close();
    }
  }, [props.open]);

  const fuse = useMemo(() => {
    return new Fuse(data ?? [], {
      // includeScore: true,
      // Search in `author` and in `tags` array
      threshold: 0.0,
      keys: ['code', 'short_name'],
    });
  }, [data]);

  const handleSearch = useCallback(
    (text: string) => {
      setSearchString(text);
    },
    [data],
  );

  const snapPoints = useMemo(() => ['90%'], []);

  const handleBankSelect = useCallback((bank: Bank) => {
    props.onChange(bank);
    setTimeout(() => {
      props.onClose();
    }, 100);
  }, []);

  const renderItem = useCallback(
    (item: ListRenderItemInfo<Bank>) => {
      return <BankItem bank={item.item} onPress={handleBankSelect} isActive={item.item.id === props.selectedId} />;
    },
    [data, searchResult, props.selectedId],
  );

  const renderBackdrop = useCallback((bsProps: any) => <BottomSheetBackdrop {...bsProps} disappearsOnIndex={-1} appearsOnIndex={1} />, []);

  return (
    <BottomSheetModal
      animateOnMount={!reducedMotion}
      ref={bottomSheetRef}
      enableDismissOnClose
      enablePanDownToClose
      snapPoints={snapPoints}
      index={0}
      backdropComponent={renderBackdrop}
      onDismiss={props.onClose}>
      <BottomSheetFlatList
        keyboardShouldPersistTaps="handled"
        ListHeaderComponent={
          <View style={{backgroundColor: '#fff'}}>
            <SearchBar value={searchString} onChangeText={handleSearch} />
          </View>
        }
        stickyHeaderIndices={[0]}
        data={searchString ? searchResult : data}
        renderItem={renderItem}
      />
    </BottomSheetModal>
  );
};

export default BankSelectModal;
