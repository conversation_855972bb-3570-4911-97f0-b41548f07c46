import {Button, Text} from '@rneui/themed';
import React, {useState} from 'react';
import {Platform, View} from 'react-native';
import {Asset, launchCamera, launchImageLibrary} from 'react-native-image-picker';
import Modal from 'react-native-modal';
import theme from '~utils/config/themes/theme';
import {requestCameraPermission} from '~utils/helpers/image';

type SearchByImageOptionDialogProps = {
  open: boolean;
  onClose: () => void;
  onSelectPhotoComplete: (asset: Asset) => void;
};

const SearchByImageOptionDialog: React.FC<SearchByImageOptionDialogProps> = props => {
  const [isTakePhotoLoading, setTakePhotoLoading] = useState<boolean>(false);
  const [isSelectPhotoLoading, setSelectPhotoLoading] = useState<boolean>(false);

  const handleSelectPhoto = async () => {
    try {
      setSelectPhotoLoading(true);
      const result = await launchImageLibrary({
        mediaType: 'photo',
        selectionLimit: 1,
        maxWidth: 2048,
      });
      if (result.assets && result.assets.length) {
        props.onSelectPhotoComplete(result.assets[0]);
      }
    } catch (error) {}

    setSelectPhotoLoading(false);
    props.onClose();
  };

  const handleTakePhoto = async () => {
    try {
      setTakePhotoLoading(true);

      if (Platform.OS === 'android') {
        await requestCameraPermission();
      }
      const result = await launchCamera({
        mediaType: 'photo',
      });

      if (result.assets && result.assets.length) {
        props.onSelectPhotoComplete(result.assets[0]);
      }
    } catch (error) {}

    setTakePhotoLoading(false);
    props.onClose();
  };

  return (
    <Modal isVisible={props.open} onBackButtonPress={props.onClose} onBackdropPress={props.onClose}>
      <View style={{backgroundColor: 'white', borderRadius: 12, paddingVertical: theme.spacing.m, paddingHorizontal: theme.spacing.m, position: 'relative'}}>
        <View>
          <Text style={{marginBottom: theme.spacing.m}}>Chọn hình thức tải ảnh</Text>

          <Button
            icon={{
              type: 'ionicon',
              name: 'close',
              color: theme.colors.text,
              size: 24,
            }}
            type="clear"
            onPress={props.onClose}
            containerStyle={{alignSelf: 'center', position: 'absolute', top: -20, right: -20, zIndex: 100}}
            size="sm"
          />
        </View>
        <View style={{flexDirection: 'row', justifyContent: 'center'}}>
          <Button
            icon={{
              name: 'camera',
              type: 'ionicon',
              size: 20,
              color: theme.colors.white,
            }}
            size="sm"
            containerStyle={{marginRight: theme.spacing.m}}
            onPress={handleTakePhoto}
            loading={isTakePhotoLoading}
            titleStyle={{
              fontSize: theme.typography.base,
            }}>
            Chụp ảnh
          </Button>
          <Button
            onPress={handleSelectPhoto}
            size="sm"
            icon={{
              type: 'ionicon',
              name: 'images',
              size: 20,
              color: theme.colors.white,
            }}
            loading={isSelectPhotoLoading}
            titleStyle={{
              fontSize: theme.typography.base,
            }}>
            Chọn ảnh từ máy
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default SearchByImageOptionDialog;
