import React, {useMemo} from 'react';
import ContentLoader, {Rect} from 'react-content-loader/native';
import {ScreenHeight, ScreenWidth} from '@rneui/base';
import {responsiveWidth} from '../web/WebContainer';

const SearchLayoutSkeleton: React.FC<any> = props => {
  const screenWidth = useMemo(() => Math.min(ScreenWidth, responsiveWidth.sm), []);
  const itemHeight = useMemo(() => ((screenWidth / 2 - 16) * 130) / 100, [screenWidth]);
  return (
    <ContentLoader speed={2} width={screenWidth} height={ScreenHeight} viewBox={`0 0 ${screenWidth} ${ScreenHeight}`} backgroundColor="#ebebf0" foregroundColor="#DDDDE3" {...props}>
      <Rect x="0" y="0" rx="12" ry="12" width={screenWidth / 2 - 16} height={itemHeight} />
      <Rect x={screenWidth / 2} y="0" rx="12" ry="12" width={screenWidth / 2 - 16} height={itemHeight} />
      <Rect x={0} y={itemHeight + 16} rx="12" ry="12" width={screenWidth / 2 - 16} height={itemHeight} />
      <Rect x={screenWidth / 2} y={itemHeight + 16} rx="12" ry="12" width={screenWidth / 2 - 16} height={itemHeight} />
    </ContentLoader>
  );
};

export default SearchLayoutSkeleton;
