import {useFocusEffect} from '@react-navigation/native';
import React, {useCallback, useState} from 'react';
import {IImageInfo} from 'react-native-image-zoom-viewer/built/image-viewer.type';
import ImageViewerModal from '~components/shared/ImageViewerModal';
import {EventNames} from '~utils/events';
import ee from '~utils/events';

const GlobalImageViewerModal = () => {
  const [images, setImages] = useState<IImageInfo[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [modalVisible, setModalVisible] = useState(false);

  useFocusEffect(
    useCallback(() => {
      const handleOpenImageViewer = ({newImages, newIndex}: any) => {
        setImages(newImages);
        setSelectedIndex(newIndex);
        setModalVisible(true);
      };

      ee.on(EventNames.OpenImageViewer, handleOpenImageViewer);
      return () => {
        ee.off(EventNames.OpenImageViewer, handleOpenImageViewer);
      };
    }, []),
  );

  return <ImageViewerModal visible={modalVisible} onClose={() => setModalVisible(false)} images={images} selectedIndex={selectedIndex} />;
};

export default GlobalImageViewerModal;
