import React from 'react';
import {StyleSheet, View} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '~hooks';
import theme from '~utils/config/themes/theme';

const HeaderRight = () => {
  const navigation = useNavigation();

  return (
    <View style={styles.container}>
      <Ionicons name="search-outline" size={28} color={theme.colors.textLight} style={styles.iconItem} onPress={() => navigation.navigate('Search')} />
      <Ionicons name="cart-outline" size={28} color={theme.colors.textLight} style={styles.iconItem} onPress={() => navigation.navigate('Cart')} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
  },
  iconItem: {
    marginRight: 12,
  },
});

export default HeaderRight;
