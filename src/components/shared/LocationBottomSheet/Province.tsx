import React, {useCallback} from 'react';
import {ListRenderItemInfo, Pressable, StyleSheet, Text, View} from 'react-native';
import {BottomSheetFlatList} from '@gorhom/bottom-sheet';

type ProvinceProps = {
  selectedCity?: string;
  selectedProvince?: string;
  onChange: (provinceName: string) => void;
  provinces: string[];
};

const Province: React.FC<ProvinceProps> = props => {
  const renderItem = useCallback(
    (item: ListRenderItemInfo<string>) => {
      return (
        <Pressable style={styles.provinceItem} onPress={() => props.onChange(item.item)}>
          <Text style={[styles.provinceItem__text, {color: props.selectedProvince === item.item ? '#008060' : '#333'}]}>{item.item}</Text>
        </Pressable>
      );
    },
    [props.selectedProvince],
  );

  return (
    <BottomSheetFlatList
      data={props.provinces}
      renderItem={renderItem}
      ListEmptyComponent={
        <View style={styles.listEmptyContainer}>
          <Text style={styles.listEmptyText}><PERSON><PERSON> lòng chọn Tỉnh / thành phố</Text>
        </View>
      }
    />
  );
};

const styles = StyleSheet.create({
  container: {},
  provinceItem: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,.05)',
  },
  provinceItem__text: {
    fontSize: 18,
  },
  listEmptyContainer: {
    marginVertical: 80,
  },
  listEmptyText: {
    textAlign: 'center',
    fontSize: 18,
    color: 'rgba(0,0,0,.7)',
  },
});

export default Province;
