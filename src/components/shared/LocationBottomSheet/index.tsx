import React, {forwardRef, useCallback, useImperativeHandle, useMemo, useRef} from 'react';
import BottomSheet, {BottomSheetBackdrop} from '@gorhom/bottom-sheet';
import City from './City';
import Province from './Province';
import Ward from './Ward';

export type LocationType = 'city' | 'province' | 'ward';

export type LocationBottomSheetRef = {
  open: (snapIndex?: number) => void;
};

type LocationBottomSheetProps = {
  type: LocationType;
  selectedCity?: string;
  selectedProvince?: string;
  selectedWard?: string;
  onChange: (type: 'city' | 'province' | 'ward', name: string) => void;
  cities: string[];
  provinces: string[];
  wards: string[];
};

const LocationBottomSheet = forwardRef<LocationBottomSheetRef, LocationBottomSheetProps>((props, ref) => {
  const bottomSheetRef = useRef<BottomSheet>(null);

  useImperativeHandle(ref, () => ({
    open(snapIndex?) {
      bottomSheetRef.current?.snapToIndex(snapIndex ?? 1);
    },
    close() {
      handleClose();
    },
  }));

  const handleClose = useCallback(() => {
    bottomSheetRef.current?.close();
  }, []);

  const snapPoints = useMemo(() => ['50%', '100%'], []);

  const handleCityChange = useCallback((city: string) => {
    props.onChange('city', city);
    props.onChange('province', '');
    props.onChange('ward', '');

    handleClose();
  }, []);

  const handleProvinceChange = useCallback((province: string) => {
    props.onChange('province', province);
    props.onChange('ward', '');
    handleClose();
  }, []);

  const handleWardChange = useCallback((ward: string) => {
    props.onChange('ward', ward);
    handleClose();
  }, []);

  const renderBackdrop = useCallback((bsProps: any) => <BottomSheetBackdrop {...bsProps} disappearsOnIndex={-1} appearsOnIndex={1} />, []);

  return (
    <BottomSheet backdropComponent={renderBackdrop} ref={bottomSheetRef} index={-1} snapPoints={snapPoints} enablePanDownToClose>
      {props.type === 'city' && <City cities={props.cities} onChange={handleCityChange} selectedCity={props.selectedCity} />}
      {props.type === 'province' && <Province provinces={props.provinces} onChange={handleProvinceChange} selectedCity={props.selectedCity} selectedProvince={props.selectedProvince} />}
      {props.type === 'ward' && <Ward wards={props.wards} onChange={handleWardChange} selectedCity={props.selectedCity} selectedProvince={props.selectedProvince} selectedWard={props.selectedWard} />}
    </BottomSheet>
  );
});

export default LocationBottomSheet;
