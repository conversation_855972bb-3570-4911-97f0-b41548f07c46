import React, {useCallback, useMemo} from 'react';
import {ListRenderItemInfo, Pressable, StyleSheet, Text} from 'react-native';
import {useGetLocations} from '~utils/api/location';
import {BottomSheetFlatList} from '@gorhom/bottom-sheet';

type CityProps = {
  selectedCity?: string;
  onChange: (cityName: string) => void;
  cities: string[];
};

const City: React.FC<CityProps> = props => {
  const {data} = useGetLocations();
  const getCities = useMemo(() => (data || []).reduce<string[]>((acc, cur) => [...acc, ...Object.keys(cur)], []), [data]);

  const renderItem = useCallback(
    (item: ListRenderItemInfo<string>) => {
      return (
        <Pressable style={styles.cityItem} onPress={() => props.onChange(item.item)}>
          <Text style={[styles.cityItem__text, {color: props.selectedCity === item.item ? '#008060' : '#333'}]}>{item.item}</Text>
        </Pressable>
      );
    },
    [props.selectedCity],
  );

  return <BottomSheetFlatList data={getCities} renderItem={renderItem} />;
};

const styles = StyleSheet.create({
  container: {},
  cityItem: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,.05)',
  },
  cityItem__text: {
    fontSize: 18,
  },
});

export default City;
