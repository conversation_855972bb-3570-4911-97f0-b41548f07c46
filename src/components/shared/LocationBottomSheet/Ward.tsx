import React, {useCallback} from 'react';
import {ListRenderItemInfo, Pressable, StyleSheet, Text, View} from 'react-native';
import {BottomSheetFlatList} from '@gorhom/bottom-sheet';

type WardProps = {
  selectedCity?: string;
  selectedProvince?: string;
  selectedWard?: string;
  onChange: (cityName: string) => void;
  wards: string[];
};

const Ward: React.FC<WardProps> = props => {
  const renderItem = useCallback(
    (item: ListRenderItemInfo<string>) => {
      return (
        <Pressable style={styles.wardItem} onPress={() => props.onChange(item.item)}>
          <Text style={[styles.wardItem__text, {color: props.selectedWard === item.item ? '#008060' : '#333'}]}>{item.item}</Text>
        </Pressable>
      );
    },
    [props.selectedWard],
  );

  return (
    <BottomSheetFlatList
      data={props.wards}
      renderItem={renderItem}
      ListEmptyComponent={
        <View style={styles.listEmptyContainer}>
          <Text style={styles.listEmptyText}><PERSON>ui lòng chọn Quận / huyện</Text>
        </View>
      }
    />
  );
};

const styles = StyleSheet.create({
  container: {},
  wardItem: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,.05)',
  },
  wardItem__text: {
    fontSize: 18,
  },
  listEmptyContainer: {
    marginVertical: 80,
  },
  listEmptyText: {
    textAlign: 'center',
    fontSize: 18,
    color: 'rgba(0,0,0,.7)',
  },
});

export default Ward;
