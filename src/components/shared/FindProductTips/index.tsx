import {Text} from '@rneui/themed';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {Animated, Linking, Platform, Pressable, View} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '~hooks';
import {ASYNC_STORAGE_KEYS} from '~utils/config';
import theme from '~utils/config/themes/theme';
import AsyncStorage from '~utils/helpers/storage';

const FindProductTips = () => {
  const navigation = useNavigation();
  //   write item disapear animation
  const opacity = useRef(new Animated.Value(1)).current;
  const [isShow, setShow] = useState(false);

  useEffect(() => {
    AsyncStorage.getItem(ASYNC_STORAGE_KEYS.FIND_PRODUCT_TIP_DISSMISSED).then(value => {
      setShow(!!!value);
    });
  }, []);

  const handleGoToDocs = useCallback(() => {
    if (Platform.OS === 'web') {
      Linking.openURL('https://go.thitruongsi.com/Z9Gow');
    } else {
      navigation.navigate('WebView', {
        url: 'https://go.thitruongsi.com/Z9Gow',
      });
    }
  }, []);

  const handleDismiss = useCallback(() => {
    Animated.timing(opacity, {
      toValue: 0,
      useNativeDriver: true,
      duration: 300,
    }).start(() => {
      setShow(false);
      AsyncStorage.setItem(ASYNC_STORAGE_KEYS.FIND_PRODUCT_TIP_DISSMISSED, '1');
    });
  }, []);

  if (!isShow) {
    return null;
  }

  return (
    <Animated.View
      style={[
        {
          backgroundColor: theme.colors.gray20,
          paddingVertical: 4,
          borderRadius: 8,
          marginBottom: theme.spacing.m,
          flexDirection: 'row',
          alignItems: 'center',
        },
        {
          opacity,
        },
      ]}>
      <Ionicons name="close-outline" size={22} color={theme.colors.text} onPress={handleDismiss} style={{zIndex: 1}} />
      <View style={{width: 1, height: 10, backgroundColor: theme.colors.gray50, alignSelf: 'center', marginRight: theme.spacing.s}} />
      <Pressable style={{flex: 1, flexDirection: 'row', alignItems: 'center'}} onPress={handleGoToDocs}>
        <Text numberOfLines={1} style={{color: theme.colors.text, fontSize: theme.typography.base}}>
          Mẹo tìm lại sản phẩm đã đăng bán cực nhanh
        </Text>
        <Ionicons name="chevron-forward" size={12} color={theme.colors.text} />
      </Pressable>
    </Animated.View>
  );
};

export default FindProductTips;
