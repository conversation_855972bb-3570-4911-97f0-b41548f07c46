/**
 * @format
 */
import {AppRegistry, Platform} from 'react-native';
import App from './App';
import {name as appName} from './app.json';
import 'react-native-gesture-handler';
import {AVAILABLE_CHANNELS} from '~utils/config';
import messaging from '@react-native-firebase/messaging';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import {handleNotificationClick} from '~utils/helpers/common';
import {Text} from '@rneui/themed';

Text.defaultProps = Text.defaultProps || {};
Text.defaultProps.allowFontScaling = false;

// Register background handler
messaging().setBackgroundMessageHandler(async remoteMessage => {
  console.log('Message handled in the background!', remoteMessage);
});

var PushNotification = require('react-native-push-notification');

PushNotification.configure({
  // (optional) Called when Token is generated (iOS and Android)
  onRegister: function (token) {
    // console.log('TOKEN:', token);
  },

  // (required) Called when a remote is received or opened, or local notification is opened
  onNotification: function (notification) {
    if (notification.foreground && !notification.data?.__preventLoop && Platform.OS === 'ios') {
      PushNotification.localNotification({
        foreground: false, // BOOLEAN: If the notification was received in foreground or not
        userInteraction: false, //
        message: notification.message,
        title: notification.title,
        userInfo: {
          __preventLoop: true,
          ...notification.data,
        }, // OBJ
      });
    }
    if (notification.userInteraction) {
      // clicked notification
      handleNotificationClick(notification);
    }

    // (required) Called when a remote is received or opened, or local notification is opened
    notification.finish(PushNotificationIOS.FetchResult?.NoData);
  },

  // (optional) Called when Registered Action is pressed and invokeApp is false, if true onNotification will be called (Android)
  onAction: function (notification) {
    console.log('ACTION:', notification.action);
    console.log('NOTIFICATION:', notification);
    // process the action
  },

  // (optional) Called when the user fails to register for remote notifications. Typically occurs when APNS is having issues, or the device is a simulator. (iOS)
  onRegistrationError: function (err) {
    console.error(err.message, err);
  },

  // IOS ONLY (optional): default: all - Permissions to register.
  permissions: {
    alert: true,
    badge: true,
    sound: true,
  },

  // Should the initial notification be popped automatically
  // default: true
  popInitialNotification: true,

  /**
   * (optional) default: true
   * - Specified if permissions (ios) and token (android and ios) will requested or not,
   * - if not, you must call PushNotificationsHandler.requestPermissions() later
   * - if you are not using remote notification or do not have Firebase installed, use this:
   *     requestPermissions: Platform.OS === 'ios'
   */
  requestPermissions: true,
});

Object.keys(AVAILABLE_CHANNELS).forEach(channelId => {
  PushNotification.createChannel(AVAILABLE_CHANNELS[channelId], created => console.log(`${channelId} channel created`));
});

AppRegistry.registerComponent(appName, () => App);
