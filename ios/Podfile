require File.join(File.dirname(`node --print "require.resolve('expo/package.json')"`), "scripts/autolinking")

# Resolve react_native_pods.rb with node to allow for hoisting


def node_require(script)
   # Resolve script with node to allow for hoisting
   require Pod::Executable.execute_command('node', ['-p',
     "require.resolve(
       '#{script}',
       {paths: [process.argv[1]]},
     )", __dir__]).strip
end
  
node_require('react-native/scripts/react_native_pods.rb')
node_require('react-native-permissions/scripts/setup.rb') 



platform :ios, min_ios_version_supported
prepare_react_native_project!


setup_permissions([
  # 'AppTrackingTransparency',
  # 'Bluetooth',
  # 'Calendars',
  # 'CalendarsWriteOnly',
  # 'Camera',
  # 'Contacts',
  # 'FaceID',
  # 'LocationAccuracy',
  # 'LocationAlways',
  # 'LocationWhenInUse',
  # 'MediaLibrary',
  # 'Microphone',
  # 'Motion',
  # 'Notifications',
  'PhotoLibrary',
  'PhotoLibraryAddOnly',
  # 'Reminders',
  # 'Siri',
  # 'SpeechRecognition',
  # 'StoreKit',
])


linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

$RNFirebaseAnalyticsWithoutAdIdSupport=true

target 'dsbuyer' do
  use_expo_modules!

  if ENV['EXPO_USE_COMMUNITY_AUTOLINKING'] == '1'
    config_command = ['node', '-e', "process.argv=['', '', 'config'];require('@react-native-community/cli').run()"];
  else
    config_command = [
      'node',
      '--no-warnings',
      '--eval',
      'require(require.resolve(\'expo-modules-autolinking\', { paths: [require.resolve(\'expo/package.json\')] }))(process.argv.slice(1))',
      'react-native-config',
      '--json',
      '--platform',
      'ios'
    ]
  end

  config = use_native_modules!(config_command)

  pod 'FirebaseCore', :modular_headers => true
  pod 'Firebase', :modular_headers => true
  pod 'FirebaseCoreInternal', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true
  # pod 'react-native-compressor', :path => '../node_modules/react-native-compressor', :modular_headers => true


  # Flags change depending on the env values.
  flags = get_default_flags()

  use_react_native!(
    :path => config[:reactNativePath],
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  target 'dsbuyerTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )

    # # fix XCode 14 signing error https://github.com/CocoaPods/CocoaPods/issues/11402
    # installer.pods_project.targets.each do |target|
    #   target.build_configurations.each do |config|
    #       config.build_settings['CODE_SIGN_IDENTITY'] = ''
    #   end
    # end
    # # 
    # installer.generated_projects.each do |project|
    #   project.targets.each do |target|
    #       target.build_configurations.each do |config|
    #           config.build_settings["DEVELOPMENT_TEAM"] = "NT7YPD535F"
    #        end
    #   end
    # end
    # # 
  end
end
