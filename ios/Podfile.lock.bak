PODS:
  - Base64 (1.1.2)
  - boost (1.76.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - CocoaAsyncSocket (7.6.5)
  - CodePush (8.1.0):
    - Base64 (~> 1.1)
    - JWT (~> 3.0.0-beta.12)
    - React-Core
    - SSZipArchive (~> 2.2.2)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.72.5)
  - FBReactNativeSpec (0.72.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.72.5)
    - RCTTypeSafety (= 0.72.5)
    - React-Core (= 0.72.5)
    - React-jsi (= 0.72.5)
    - ReactCommon/turbomodule/core (= 0.72.5)
  - Firebase (10.24.0):
    - Firebase/Core (= 10.24.0)
  - Firebase/AnalyticsWithoutAdIdSupport (10.24.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics/WithoutAdIdSupport (~> 10.24.0)
  - Firebase/Auth (10.24.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 10.24.0)
  - Firebase/Core (10.24.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.24.0)
  - Firebase/CoreOnly (10.24.0):
    - FirebaseCore (= 10.24.0)
  - Firebase/DynamicLinks (10.24.0):
    - Firebase/CoreOnly
    - FirebaseDynamicLinks (~> 10.24.0)
  - Firebase/Messaging (10.24.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.24.0)
  - FirebaseAnalytics (10.24.0):
    - FirebaseAnalytics/AdIdSupport (= 10.24.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.24.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.24.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAnalytics/WithoutAdIdSupport (10.24.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.24.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAppCheckInterop (10.27.0)
  - FirebaseAuth (10.24.0):
    - FirebaseAppCheckInterop (~> 10.17)
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
    - RecaptchaInterop (~> 100.0)
  - FirebaseCore (10.24.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.27.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.27.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseDynamicLinks (10.24.0):
    - FirebaseCore (~> 10.0)
  - FirebaseInstallations (10.27.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.24.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - Flipper (0.182.0):
    - Flipper-Folly (~> 2.6)
  - Flipper-Boost-iOSX (********.11)
  - Flipper-DoubleConversion (*******)
  - Flipper-Fmt (7.1.7)
  - Flipper-Folly (2.6.10):
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt (= 7.1.7)
    - Flipper-Glog
    - libevent (~> 2.1.12)
    - OpenSSL-Universal (= 1.1.1100)
  - Flipper-Glog (*******)
  - Flipper-PeerTalk (0.0.4)
  - FlipperKit (0.182.0):
    - FlipperKit/Core (= 0.182.0)
  - FlipperKit/Core (0.182.0):
    - Flipper (~> 0.182.0)
    - FlipperKit/CppBridge
    - FlipperKit/FBCxxFollyDynamicConvert
    - FlipperKit/FBDefines
    - FlipperKit/FKPortForwarding
    - SocketRocket (~> 0.6.0)
  - FlipperKit/CppBridge (0.182.0):
    - Flipper (~> 0.182.0)
  - FlipperKit/FBCxxFollyDynamicConvert (0.182.0):
    - Flipper-Folly (~> 2.6)
  - FlipperKit/FBDefines (0.182.0)
  - FlipperKit/FKPortForwarding (0.182.0):
    - CocoaAsyncSocket (~> 7.6)
    - Flipper-PeerTalk (~> 0.0.4)
  - FlipperKit/FlipperKitHighlightOverlay (0.182.0)
  - FlipperKit/FlipperKitLayoutHelpers (0.182.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutTextSearchable
  - FlipperKit/FlipperKitLayoutIOSDescriptors (0.182.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutPlugin (0.182.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - FlipperKit/FlipperKitLayoutIOSDescriptors
    - FlipperKit/FlipperKitLayoutTextSearchable
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutTextSearchable (0.182.0)
  - FlipperKit/FlipperKitNetworkPlugin (0.182.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitReactPlugin (0.182.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitUserDefaultsPlugin (0.182.0):
    - FlipperKit/Core
  - FlipperKit/SKIOSNetworkPlugin (0.182.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitNetworkPlugin
  - fmt (6.2.1)
  - glog (0.3.5)
  - GoogleAppMeasurement (10.24.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.24.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.24.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.24.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.24.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities (7.13.3):
    - GoogleUtilities/AppDelegateSwizzler (= 7.13.3)
    - GoogleUtilities/Environment (= 7.13.3)
    - GoogleUtilities/ISASwizzler (= 7.13.3)
    - GoogleUtilities/Logger (= 7.13.3)
    - GoogleUtilities/MethodSwizzler (= 7.13.3)
    - GoogleUtilities/Network (= 7.13.3)
    - "GoogleUtilities/NSData+zlib (= 7.13.3)"
    - GoogleUtilities/Privacy (= 7.13.3)
    - GoogleUtilities/Reachability (= 7.13.3)
    - GoogleUtilities/SwizzlerTestHelpers (= 7.13.3)
    - GoogleUtilities/UserDefaults (= 7.13.3)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/ISASwizzler (7.13.3):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (7.13.3):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMSessionFetcher/Core (3.4.1)
  - hermes-engine (0.72.5):
    - hermes-engine/Pre-built (= 0.72.5)
  - hermes-engine/Pre-built (0.72.5)
  - JWT (3.0.0-beta.14):
    - Base64 (~> 1.1.2)
  - libevent (2.1.12)
  - libwebp (1.3.2):
    - libwebp/demux (= 1.3.2)
    - libwebp/mux (= 1.3.2)
    - libwebp/sharpyuv (= 1.3.2)
    - libwebp/webp (= 1.3.2)
  - libwebp/demux (1.3.2):
    - libwebp/webp
  - libwebp/mux (1.3.2):
    - libwebp/demux
  - libwebp/sharpyuv (1.3.2)
  - libwebp/webp (1.3.2):
    - libwebp/sharpyuv
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - OpenSSL-Universal (1.1.1100)
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.72.5)
  - RCTTypeSafety (0.72.5):
    - FBLazyVector (= 0.72.5)
    - RCTRequired (= 0.72.5)
    - React-Core (= 0.72.5)
  - React (0.72.5):
    - React-Core (= 0.72.5)
    - React-Core/DevSupport (= 0.72.5)
    - React-Core/RCTWebSocket (= 0.72.5)
    - React-RCTActionSheet (= 0.72.5)
    - React-RCTAnimation (= 0.72.5)
    - React-RCTBlob (= 0.72.5)
    - React-RCTImage (= 0.72.5)
    - React-RCTLinking (= 0.72.5)
    - React-RCTNetwork (= 0.72.5)
    - React-RCTSettings (= 0.72.5)
    - React-RCTText (= 0.72.5)
    - React-RCTVibration (= 0.72.5)
  - React-callinvoker (0.72.5)
  - React-Codegen (0.72.5):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.72.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.5)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.72.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.72.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.72.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.5)
    - React-Core/RCTWebSocket (= 0.72.5)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.72.5)
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.72.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.72.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.72.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.72.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.72.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.72.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.72.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.72.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.72.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.72.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.5)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.72.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.5)
    - React-Codegen (= 0.72.5)
    - React-Core/CoreModulesHeaders (= 0.72.5)
    - React-jsi (= 0.72.5)
    - React-RCTBlob
    - React-RCTImage (= 0.72.5)
    - ReactCommon/turbomodule/core (= 0.72.5)
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.72.5):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.5)
    - React-debug (= 0.72.5)
    - React-jsi (= 0.72.5)
    - React-jsinspector (= 0.72.5)
    - React-logger (= 0.72.5)
    - React-perflogger (= 0.72.5)
    - React-runtimeexecutor (= 0.72.5)
  - React-debug (0.72.5)
  - React-hermes (0.72.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - RCT-Folly/Futures (= 2021.07.22.00)
    - React-cxxreact (= 0.72.5)
    - React-jsi
    - React-jsiexecutor (= 0.72.5)
    - React-jsinspector (= 0.72.5)
    - React-perflogger (= 0.72.5)
  - React-jsi (0.72.5):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.72.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.72.5)
    - React-jsi (= 0.72.5)
    - React-perflogger (= 0.72.5)
  - React-jsinspector (0.72.5)
  - React-logger (0.72.5):
    - glog
  - react-native-avoid-softinput (3.0.2):
    - React-Core
  - react-native-cameraroll (5.10.0):
    - React-Core
  - react-native-compressor (1.8.22):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - react-native-document-picker (9.0.1):
    - React-Core
  - react-native-flipper (0.159.0):
    - React-Core
  - react-native-get-random-values (1.8.0):
    - React-Core
  - react-native-image-picker (7.0.2):
    - React-Core
  - react-native-netinfo (9.3.0):
    - React-Core
  - react-native-pager-view (6.2.1):
    - React-Core
  - react-native-safe-area-context (4.7.2):
    - React-Core
  - react-native-video (6.0.0-beta.0):
    - React-Core
    - react-native-video/Video (= 6.0.0-beta.0)
  - react-native-video/Video (6.0.0-beta.0):
    - PromisesSwift
    - React-Core
  - react-native-view-shot (3.8.0):
    - React-Core
  - react-native-webview (11.23.0):
    - React-Core
  - React-NativeModulesApple (0.72.5):
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.72.5)
  - React-RCTActionSheet (0.72.5):
    - React-Core/RCTActionSheetHeaders (= 0.72.5)
  - React-RCTAnimation (0.72.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.5)
    - React-Codegen (= 0.72.5)
    - React-Core/RCTAnimationHeaders (= 0.72.5)
    - React-jsi (= 0.72.5)
    - ReactCommon/turbomodule/core (= 0.72.5)
  - React-RCTAppDelegate (0.72.5):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-hermes
    - React-NativeModulesApple
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.72.5):
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.5)
    - React-Core/RCTBlobHeaders (= 0.72.5)
    - React-Core/RCTWebSocket (= 0.72.5)
    - React-jsi (= 0.72.5)
    - React-RCTNetwork (= 0.72.5)
    - ReactCommon/turbomodule/core (= 0.72.5)
  - React-RCTImage (0.72.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.5)
    - React-Codegen (= 0.72.5)
    - React-Core/RCTImageHeaders (= 0.72.5)
    - React-jsi (= 0.72.5)
    - React-RCTNetwork (= 0.72.5)
    - ReactCommon/turbomodule/core (= 0.72.5)
  - React-RCTLinking (0.72.5):
    - React-Codegen (= 0.72.5)
    - React-Core/RCTLinkingHeaders (= 0.72.5)
    - React-jsi (= 0.72.5)
    - ReactCommon/turbomodule/core (= 0.72.5)
  - React-RCTNetwork (0.72.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.5)
    - React-Codegen (= 0.72.5)
    - React-Core/RCTNetworkHeaders (= 0.72.5)
    - React-jsi (= 0.72.5)
    - ReactCommon/turbomodule/core (= 0.72.5)
  - React-RCTSettings (0.72.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.5)
    - React-Codegen (= 0.72.5)
    - React-Core/RCTSettingsHeaders (= 0.72.5)
    - React-jsi (= 0.72.5)
    - ReactCommon/turbomodule/core (= 0.72.5)
  - React-RCTText (0.72.5):
    - React-Core/RCTTextHeaders (= 0.72.5)
  - React-RCTVibration (0.72.5):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.5)
    - React-Core/RCTVibrationHeaders (= 0.72.5)
    - React-jsi (= 0.72.5)
    - ReactCommon/turbomodule/core (= 0.72.5)
  - React-rncore (0.72.5)
  - React-runtimeexecutor (0.72.5):
    - React-jsi (= 0.72.5)
  - React-runtimescheduler (0.72.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker
    - React-debug
    - React-jsi
    - React-runtimeexecutor
  - React-utils (0.72.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-debug
  - ReactCommon/turbomodule/bridging (0.72.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.5)
    - React-cxxreact (= 0.72.5)
    - React-jsi (= 0.72.5)
    - React-logger (= 0.72.5)
    - React-perflogger (= 0.72.5)
  - ReactCommon/turbomodule/core (0.72.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.5)
    - React-cxxreact (= 0.72.5)
    - React-jsi (= 0.72.5)
    - React-logger (= 0.72.5)
    - React-perflogger (= 0.72.5)
  - RecaptchaInterop (100.0.0)
  - rn-fetch-blob (0.12.0):
    - React-Core
  - RNBootSplash (4.3.2):
    - React-Core
  - RNCAsyncStorage (1.19.3):
    - React-Core
  - RNCClipboard (1.10.0):
    - React-Core
  - RNCPushNotificationIOS (1.10.1):
    - React-Core
  - RNDeviceInfo (10.0.2):
    - React-Core
  - RNFastImage (8.6.1):
    - React-Core
    - SDWebImage (~> 5.11.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - RNFBAnalytics (19.3.0):
    - Firebase/AnalyticsWithoutAdIdSupport (= 10.24.0)
    - React-Core
    - RNFBApp
  - RNFBApp (19.3.0):
    - Firebase/CoreOnly (= 10.24.0)
    - React-Core
  - RNFBAuth (19.3.0):
    - Firebase/Auth (= 10.24.0)
    - React-Core
    - RNFBApp
  - RNFBDynamicLinks (19.3.0):
    - Firebase/DynamicLinks (= 10.24.0)
    - GoogleUtilities/AppDelegateSwizzler
    - React-Core
    - RNFBApp
  - RNFBMessaging (19.3.0):
    - Firebase/Messaging (= 10.24.0)
    - FirebaseCoreExtension
    - React-Core
    - RNFBApp
  - RNFlashList (1.6.1):
    - React-Core
  - RNGestureHandler (2.14.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNImageCropPicker (0.38.0):
    - React-Core
    - React-RCTImage
    - RNImageCropPicker/QBImagePickerController (= 0.38.0)
    - TOCropViewController
  - RNImageCropPicker/QBImagePickerController (0.38.0):
    - React-Core
    - React-RCTImage
    - TOCropViewController
  - RNInAppBrowser (3.7.0):
    - React-Core
  - RNPermissions (4.1.4):
    - React-Core
  - RNRate (1.2.12):
    - React-Core
  - RNReactNativeHapticFeedback (1.14.0):
    - React-Core
  - RNReanimated (3.5.4):
    - DoubleConversion
    - FBLazyVector
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-callinvoker
    - React-Core
    - React-Core/DevSupport
    - React-Core/RCTWebSocket
    - React-CoreModules
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-RCTActionSheet
    - React-RCTAnimation
    - React-RCTAppDelegate
    - React-RCTBlob
    - React-RCTImage
    - React-RCTLinking
    - React-RCTNetwork
    - React-RCTSettings
    - React-RCTText
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.25.0):
    - React-Core
    - React-RCTImage
  - RNSentry (5.22.0):
    - hermes-engine
    - React-Core
    - React-hermes
    - Sentry/HybridSDK (= 8.24.0)
  - RNShare (7.8.0):
    - React-Core
  - RNSVG (12.4.4):
    - React-Core
  - RNVectorIcons (10.0.0):
    - React-Core
  - SDWebImage (5.11.1):
    - SDWebImage/Core (= 5.11.1)
  - SDWebImage/Core (5.11.1)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - Sentry/HybridSDK (8.24.0)
  - SocketRocket (0.6.1)
  - SSZipArchive (2.2.3)
  - TOCropViewController (2.7.4)
  - Yoga (1.14.0)
  - YogaKit (1.18.1):
    - Yoga (~> 1.14)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - CodePush (from `../node_modules/react-native-code-push`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Firebase
  - FirebaseCore
  - FirebaseCoreInternal
  - Flipper (= 0.182.0)
  - Flipper-Boost-iOSX (= ********.11)
  - Flipper-DoubleConversion (= *******)
  - Flipper-Fmt (= 7.1.7)
  - Flipper-Folly (= 2.6.10)
  - Flipper-Glog (= *******)
  - Flipper-PeerTalk (= 0.0.4)
  - FlipperKit (= 0.182.0)
  - FlipperKit/Core (= 0.182.0)
  - FlipperKit/CppBridge (= 0.182.0)
  - FlipperKit/FBCxxFollyDynamicConvert (= 0.182.0)
  - FlipperKit/FBDefines (= 0.182.0)
  - FlipperKit/FKPortForwarding (= 0.182.0)
  - FlipperKit/FlipperKitHighlightOverlay (= 0.182.0)
  - FlipperKit/FlipperKitLayoutPlugin (= 0.182.0)
  - FlipperKit/FlipperKitLayoutTextSearchable (= 0.182.0)
  - FlipperKit/FlipperKitNetworkPlugin (= 0.182.0)
  - FlipperKit/FlipperKitReactPlugin (= 0.182.0)
  - FlipperKit/FlipperKitUserDefaultsPlugin (= 0.182.0)
  - FlipperKit/SKIOSNetworkPlugin (= 0.182.0)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - GoogleUtilities
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - OpenSSL-Universal (= 1.1.1100)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/DevSupport (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-avoid-softinput (from `../node_modules/react-native-avoid-softinput`)
  - "react-native-cameraroll (from `../node_modules/@react-native-camera-roll/camera-roll`)"
  - react-native-compressor (from `../node_modules/react-native-compressor`)
  - react-native-document-picker (from `../node_modules/react-native-document-picker`)
  - react-native-flipper (from `../node_modules/react-native-flipper`)
  - react-native-get-random-values (from `../node_modules/react-native-get-random-values`)
  - react-native-image-picker (from `../node_modules/react-native-image-picker`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-video (from `../node_modules/react-native-video`)
  - react-native-view-shot (from `../node_modules/react-native-view-shot`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - rn-fetch-blob (from `../node_modules/rn-fetch-blob`)
  - RNBootSplash (from `../node_modules/react-native-bootsplash`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-clipboard/clipboard`)"
  - "RNCPushNotificationIOS (from `../node_modules/@react-native-community/push-notification-ios`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNFastImage (from `../node_modules/react-native-fast-image`)
  - "RNFBAnalytics (from `../node_modules/@react-native-firebase/analytics`)"
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBAuth (from `../node_modules/@react-native-firebase/auth`)"
  - "RNFBDynamicLinks (from `../node_modules/@react-native-firebase/dynamic-links`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - "RNFlashList (from `../node_modules/@shopify/flash-list`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNImageCropPicker (from `../node_modules/react-native-image-crop-picker`)
  - RNInAppBrowser (from `../node_modules/react-native-inappbrowser-reborn`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNRate (from `../node_modules/react-native-rate`)
  - RNReactNativeHapticFeedback (from `../node_modules/react-native-haptic-feedback`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - "RNSentry (from `../node_modules/@sentry/react-native`)"
  - RNShare (from `../node_modules/react-native-share`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - Base64
    - CocoaAsyncSocket
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseDynamicLinks
    - FirebaseInstallations
    - FirebaseMessaging
    - Flipper
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt
    - Flipper-Folly
    - Flipper-Glog
    - Flipper-PeerTalk
    - FlipperKit
    - fmt
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - GTMSessionFetcher
    - JWT
    - libevent
    - libwebp
    - nanopb
    - OpenSSL-Universal
    - PromisesObjC
    - PromisesSwift
    - RecaptchaInterop
    - SDWebImage
    - SDWebImageWebPCoder
    - Sentry
    - SocketRocket
    - SSZipArchive
    - TOCropViewController
    - YogaKit

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  CodePush:
    :path: "../node_modules/react-native-code-push"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2023-08-07-RNv0.72.4-813b2def12bc9df02654b3e3653ae4a68d0572e0
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-avoid-softinput:
    :path: "../node_modules/react-native-avoid-softinput"
  react-native-cameraroll:
    :path: "../node_modules/@react-native-camera-roll/camera-roll"
  react-native-compressor:
    :path: "../node_modules/react-native-compressor"
  react-native-document-picker:
    :path: "../node_modules/react-native-document-picker"
  react-native-flipper:
    :path: "../node_modules/react-native-flipper"
  react-native-get-random-values:
    :path: "../node_modules/react-native-get-random-values"
  react-native-image-picker:
    :path: "../node_modules/react-native-image-picker"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-video:
    :path: "../node_modules/react-native-video"
  react-native-view-shot:
    :path: "../node_modules/react-native-view-shot"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  rn-fetch-blob:
    :path: "../node_modules/rn-fetch-blob"
  RNBootSplash:
    :path: "../node_modules/react-native-bootsplash"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-clipboard/clipboard"
  RNCPushNotificationIOS:
    :path: "../node_modules/@react-native-community/push-notification-ios"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFastImage:
    :path: "../node_modules/react-native-fast-image"
  RNFBAnalytics:
    :path: "../node_modules/@react-native-firebase/analytics"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBAuth:
    :path: "../node_modules/@react-native-firebase/auth"
  RNFBDynamicLinks:
    :path: "../node_modules/@react-native-firebase/dynamic-links"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNFlashList:
    :path: "../node_modules/@shopify/flash-list"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNImageCropPicker:
    :path: "../node_modules/react-native-image-crop-picker"
  RNInAppBrowser:
    :path: "../node_modules/react-native-inappbrowser-reborn"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNRate:
    :path: "../node_modules/react-native-rate"
  RNReactNativeHapticFeedback:
    :path: "../node_modules/react-native-haptic-feedback"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSentry:
    :path: "../node_modules/@sentry/react-native"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  Base64: cecfb41a004124895a7bcee567a89bae5a89d49b
  boost: 57d2868c099736d80fcd648bf211b4431e51a558
  BVLinearGradient: 880f91a7854faff2df62518f0281afb1c60d49a3
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  CodePush: 255d7607f428dcbec39085f81a206b170e01d012
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBLazyVector: 71803c074f6325f10b5ec891c443b6bbabef0ca7
  FBReactNativeSpec: 448e08a759d29a96e15725ae532445bf4343567c
  Firebase: 91fefd38712feb9186ea8996af6cbdef41473442
  FirebaseAnalytics: b5efc493eb0f40ec560b04a472e3e1a15d39ca13
  FirebaseAppCheckInterop: 0dd062c9926a76332ca5711dbed6f1a9ac540b54
  FirebaseAuth: 711d01cccefaf10035b3090a92956d0dd4f99088
  FirebaseCore: 11dc8a16dfb7c5e3c3f45ba0e191a33ac4f50894
  FirebaseCoreExtension: 4ec89dd0c6de93d6becde32122d68b7c35f6bf5d
  FirebaseCoreInternal: 4b297a2d56063dbea2c1d0d04222d44a8d058862
  FirebaseDynamicLinks: 96e59750f0c383258c35f5b20e3c18e14b57933a
  FirebaseInstallations: 766dabca09fd94aef922538aaf144cc4a6fb6869
  FirebaseMessaging: 4d52717dd820707cc4eadec5eb981b4832ec8d5d
  Flipper: 6edb735e6c3e332975d1b17956bcc584eccf5818
  Flipper-Boost-iOSX: fd1e2b8cbef7e662a122412d7ac5f5bea715403c
  Flipper-DoubleConversion: 2dc99b02f658daf147069aad9dbd29d8feb06d30
  Flipper-Fmt: 60cbdd92fc254826e61d669a5d87ef7015396a9b
  Flipper-Folly: 584845625005ff068a6ebf41f857f468decd26b3
  Flipper-Glog: 70c50ce58ddaf67dc35180db05f191692570f446
  Flipper-PeerTalk: 116d8f857dc6ef55c7a5a75ea3ceaafe878aadc9
  FlipperKit: 2efad7007d6745a3f95e4034d547be637f89d3f6
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  GoogleAppMeasurement: f3abf08495ef2cba7829f15318c373b8d9226491
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GTMSessionFetcher: 8000756fc1c19d2e5697b90311f7832d2e33f6cd
  hermes-engine: f6cf92a471053245614d9d8097736f6337d5b86c
  JWT: ef71dfb03e1f842081e64dc42eef0e164f35d251
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  libwebp: 1786c9f4ff8a279e4dac1e8f385004d5fc253009
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  OpenSSL-Universal: ebc357f1e6bc71fa463ccb2fe676756aff50e88c
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  RCT-Folly: 424b8c9a7a0b9ab2886ffe9c3b041ef628fd4fb1
  RCTRequired: df81ab637d35fac9e6eb94611cfd20f0feb05455
  RCTTypeSafety: 4636e4a36c7c2df332bda6d59b19b41c443d4287
  React: e0cc5197a804031a6c53fb38483c3485fcb9d6f3
  React-callinvoker: 1a635856fe0c3d8b13fccd4ed7e76283b99b0868
  React-Codegen: 78d61f981cccc68a771a598f71621cb7db14b04c
  React-Core: 252f8e9ca5a4e91af9b9be58670846d662b1c49f
  React-CoreModules: f8b9e91fac7bd5d18729ce961a4978c70b5031cc
  React-cxxreact: 70284b32dcd367439d7dae84d9f72660544181b5
  React-debug: ee33d7ba43766d9b10b32561527b57ccfbcb6bd1
  React-hermes: 91f97ea2669dc5847e1f26c243aaad913319c570
  React-jsi: bd68b7779746014f01ea72d1b738809e132d7f1e
  React-jsiexecutor: ff70a72027dea5cc7d71cfcc6fad7f599f63987a
  React-jsinspector: aef73cbd43b70675f572214d10fa438c89bf11ba
  React-logger: 2e4aee3e11b3ec4fa6cfd8004610bbb3b8d6cca4
  react-native-avoid-softinput: c84084b5adb928dc865c871a7ad0904e21f8983a
  react-native-cameraroll: 4701ae7c3dbcd3f5e9e150ca17f250a276154b35
  react-native-compressor: e459258a76f689af9f236a821700a941f1d30d95
  react-native-document-picker: 2b8f18667caee73a96708a82b284a4f40b30a156
  react-native-flipper: dc5290261fbeeb2faec1bdc57ae6dd8d562e1de4
  react-native-get-random-values: a6ea6a8a65dc93e96e24a11105b1a9c8cfe1d72a
  react-native-image-picker: 2e2e82aba9b6a91a7c78f7d9afde341a2659c7b8
  react-native-netinfo: 129bd99f607a2dc5bb096168f3e5c150fd1f1c95
  react-native-pager-view: d211379f61895b6349bd7e571b44a26d005c2975
  react-native-safe-area-context: 7aa8e6d9d0f3100a820efb1a98af68aa747f9284
  react-native-video: e9e6fe3ab11d515bd79abd5d81e07db538943c56
  react-native-view-shot: 6b7ed61d77d88580fed10954d45fad0eb2d47688
  react-native-webview: e771bc375f789ebfa02a26939a57dbc6fa897336
  React-NativeModulesApple: 797bc6078d566eef3fb3f74127e6e1d2e945a15f
  React-perflogger: cd8886513f68e1c135a1e79d20575c6489641597
  React-RCTActionSheet: 726d2615ca62a77ce3e2c13d87f65379cdc73498
  React-RCTAnimation: 8f2716b881c37c64858e4ecee0f58bfa57ff9afd
  React-RCTAppDelegate: d4a213f29e81682f6b9c7d22f62a2ccab6d125ae
  React-RCTBlob: dfaa933231c3497915bbcc9d98fcff7b6b60582c
  React-RCTImage: 747e3d7b656a67470f9c234baedb8d41bbc4e745
  React-RCTLinking: 148332b5b0396b280b05534f7d168e560a3bbd5f
  React-RCTNetwork: 1d818121a8e678f064de663a6db7aaefc099e53c
  React-RCTSettings: 4b95d26ebc88bfd3b6535b2d7904914ff88dbfc2
  React-RCTText: ce4499e4f2d8f85dc4b93ff0559313a016c4f3e2
  React-RCTVibration: 45372e61b35e96d16893540958d156675afbeb63
  React-rncore: a79d1cb3d6c01b358a8aa0b31ccc04ab5f0dbebc
  React-runtimeexecutor: 7e31e2bc6d0ecc83d4ba05eadc98401007abc10c
  React-runtimescheduler: cc32add98c45c5df18436a6a52a7e1f6edec102c
  React-utils: 7a9918a1ffdd39aba67835d42386f592ea3f8e76
  ReactCommon: 91ece8350ebb3dd2be9cef662abd78b6948233c0
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21
  rn-fetch-blob: f065bb7ab7fb48dd002629f8bdcb0336602d3cba
  RNBootSplash: 5f346163977573d6b2aeba1b25df9d2245c0d73c
  RNCAsyncStorage: c913ede1fa163a71cea118ed4670bbaaa4b511bb
  RNCClipboard: f1736c75ab85b627a4d57587edb4b60999c4dd80
  RNCPushNotificationIOS: 87b8d16d3ede4532745e05b03c42cff33a36cc45
  RNDeviceInfo: 0a7c1d2532aa7691f9b9925a27e43af006db4dae
  RNFastImage: 3207b9eb17c2425d574ca40db35185db6e324f4e
  RNFBAnalytics: b78597282194a68644acbb4f8902c5d08f3bb713
  RNFBApp: dd35ba8340b89c36e42c4c137915c01d3c792143
  RNFBAuth: 5e12865e64ffc58f83790f9bc764b0470ce07b1b
  RNFBDynamicLinks: 102c642c19c3388bcbc7cb16585edfa36fd0d409
  RNFBMessaging: c8d34d9ad44a3258a77e548bba6dc3561af7cf2d
  RNFlashList: 236646d48f224a034f35baa0242e1b77db063b1e
  RNGestureHandler: 32a01c29ecc9bb0b5bf7bc0a33547f61b4dc2741
  RNImageCropPicker: ffbba608264885c241cbf3a8f78eb7aeeb978241
  RNInAppBrowser: e36d6935517101ccba0e875bac8ad7b0cb655364
  RNPermissions: a689a033996d5e17fffbf5470c33e651ddece49e
  RNRate: ef3bcff84f39bb1d1e41c5593d3eea4aab2bd73a
  RNReactNativeHapticFeedback: 1e3efeca9628ff9876ee7cdd9edec1b336913f8c
  RNReanimated: ab2e96c6d5591c3dfbb38a464f54c8d17fb34a87
  RNScreens: 85d3880b52d34db7b8eeebe2f1a0e807c05e69fa
  RNSentry: 7ae2a06a5563de39ec09dcb1e751ac0c67f1883c
  RNShare: 31b418e3ff2084712f0f39618bafa5f97a322c0e
  RNSVG: ecd661f380a07ba690c9c5929c475a44f432d674
  RNVectorIcons: 8b5bb0fa61d54cd2020af4f24a51841ce365c7e9
  SDWebImage: a7f831e1a65eb5e285e3fb046a23fcfbf08e696d
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  Sentry: 2f6baed15a3f8056b875fc903dc3dcb2903117f4
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  SSZipArchive: 62d4947b08730e4cda640473b0066d209ff033c9
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  Yoga: 86fed2e4d425ee4c6eab3813ba1791101ee153c6
  YogaKit: f782866e155069a2cca2517aafea43200b01fd5a

PODFILE CHECKSUM: 4ef56f13baa2e09021ebcf184ab014e03e47341b

COCOAPODS: 1.15.2
