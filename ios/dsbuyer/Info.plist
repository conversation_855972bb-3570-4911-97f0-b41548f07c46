<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>TTS Dropship</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>dsbuyer</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>tts.com.dsbuyer</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>Bundle Id</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>tts.com.ds.buyer</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string></string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>app-1-1096063466071-ios-9f026f741bca29c9bf4112</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>app-1-104446928931-ios-d8b368438f5f19897ee996</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>5</string>
	<key>CodePushDeploymentKey</key>
	<string>$(CODEPUSH_DEPLOYMENT_KEY)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>momo</string>
		<string>zalopay</string>
		<string>fb</string>
		<string>facebook-stories</string>
		<string>facebook-reels</string>
		<string>instagram</string>
		<string>instagram-stories</string>
		<string>mailto</string>
		<string>whatsapp</string>
		<string>fbapi</string>
		<string>fbapi20130214</string>
		<string>fbapi20130410</string>
		<string>fbapi20130702</string>
		<string>fbapi20131010</string>
		<string>fbapi20131219</string>
		<string>fbapi20140410</string>
		<string>fbapi20140116</string>
		<string>fbapi20150313</string>
		<string>fbapi20150629</string>
		<string>fbauth</string>
		<string>fbauth2</string>
		<string>fb-messenger-api20140430</string>
		<string>itms-apps</string>
		<string>file://*</string>
		<string>https://*</string>
		<string>http://*</string>
		<string>zalo</string>
		<string>tts.com.ttsseller</string>
		<string>tts.com.thitruongsi</string>
		<string>bytedance</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>Cho phép $(PRODUCT_NAME) sử dụng camera của bạn để chụp ảnh?</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string></string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Cho phép $(PRODUCT_NAME) lưu ảnh sản phẩm vào album hình ảnh của bạn?</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Cho phép $(PRODUCT_NAME) lưu ảnh sản phẩm vào album hình ảnh của bạn?</string>
	<key>UIAppFonts</key>
	<array>
		<string>MaterialCommunityIcons.ttf</string>
		<string>Ionicons.ttf</string>
		<string>Fontawesome.ttf</string>
		<string>Foundation.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>Entypo.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>BootSplash</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
