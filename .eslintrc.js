module.exports = {
  root: true,
  extends: '@react-native',
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint', 'simple-import-sort'],

  rules: {
    'no-shadow': 'off',
    '@typescript-eslint/no-shadow': ['error'],
    'react-hooks/exhaustive-deps': 'off',
    'react-native/no-inline-styles': 'off',
  },

  globals: {
    NodeJS: true,
    Realm: true,
    HTMLSelectElement: true,
    HTMLInputElement: true,
    File: true,
    Blob: true,
    JSX: true,
  },
};
