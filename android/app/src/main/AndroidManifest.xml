<manifest xmlns:android="http://schemas.android.com/apk/res/android">
	<queries>
		<package android:name="com.zing.zalo" />
		<intent>
			<action android:name="android.intent.action.SEND" />
			<data android:mimeType="image/jpeg" />
		</intent>
	</queries>
	<queries>
		<intent>
			<action android:name="android.media.action.IMAGE_CAPTURE" />
		</intent>
	</queries>
	<uses-permission android:name="android.permission.CAMERA" />
	<uses-feature android:name="android.hardware.camera" android:required="false" />
	<uses-feature android:name="android.hardware.camera.front" android:required="false" />
	<uses-permission android:name="android.permission.INTERNET" />
	<uses-permission android:name="android.permission.VIBRATE" />
	<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
	<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
	<uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
	<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
	<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
	<uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
	<application android:name=".MainApplication" android:label="@string/app_name" android:icon="@mipmap/ic_launcher" android:roundIcon="@mipmap/ic_launcher_round" android:allowBackup="false" android:theme="@style/BootTheme" android:requestLegacyExternalStorage="true" android:supportsRtl="true">
		<meta-data android:name="com.dieam.reactnativepushnotification.notification_foreground" android:value="true" />
		<meta-data android:name="com.dieam.reactnativepushnotification.notification_color" android:resource="@color/white" />
		<activity android:name=".MainActivity" android:label="@string/app_name" android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" android:launchMode="singleTask" android:windowSoftInputMode="adjustResize" android:exported="true" android:theme="@style/BootTheme">
			<intent-filter android:autoVerify="true">
				<action android:name="android.intent.action.MAIN" />
				<category android:name="android.intent.category.LAUNCHER" />
			</intent-filter>
			<intent-filter android:label="@string/filter_view_https" android:autoVerify="true">
				<action android:name="android.intent.action.VIEW" />
				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.BROWSABLE" />
				<data android:scheme="https" android:host="dropship.thitruongsi.com" />
				<data android:scheme="https" android:host="dropship-sandbox.thitruongsi.com" />
			</intent-filter>
			<intent-filter android:label="@string/filter_view_tts">
				<action android:name="android.intent.action.VIEW" />
				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.BROWSABLE" />
				<data android:scheme="tts.com.dsbuyer" host="thitruongsi.com" />
			</intent-filter>
		</activity>
		<receiver android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationActions" />
		<receiver android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationPublisher" />
		<receiver android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationBootEventReceiver" android:exported="true">
			<intent-filter>
				<action android:name="android.intent.action.BOOT_COMPLETED" />
				<action android:name="android.intent.action.QUICKBOOT_POWERON" />
				<action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
			</intent-filter>
		</receiver>
		<service android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationListenerService" android:exported="false">
			<intent-filter>
				<action android:name="com.google.firebase.MESSAGING_EVENT" />
			</intent-filter>
		</service>
	</application>
</manifest>
