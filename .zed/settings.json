// Zed settings
//
// For information on how to configure <PERSON><PERSON>, see the Zed
// documentation: https://zed.dev/docs/configuring-zed
//
// To see all of Zed's default settings without changing your
// custom settings, run `zed: open default settings` from the
// command palette
{
  "features": {
    "inline_completion_provider": "supermaven"
  },
  "language_servers": ["!eslint"],
  "assistant": {
    "default_model": {
      "provider": "zed.dev",
      "model": "claude-3-5-sonnet-20240620"
    },
    "version": "2"
  },
  "telemetry": {
    "metrics": false,
    "diagnostics": false
  },
  "vim_mode": false,
  "base_keymap": "VSCode",
  "ui_font_size": 16,
  "buffer_font_size": 16,
  "theme": {
    "mode": "system",
    "light": "Andromeda",
    "dark": "One Dark"
  }
}
