diff --git a/node_modules/react-native-web/dist/exports/PermissionsAndroid/index.js b/node_modules/react-native-web/dist/exports/PermissionsAndroid/index.js
new file mode 100644
index 0000000..385f22a
--- /dev/null
+++ b/node_modules/react-native-web/dist/exports/PermissionsAndroid/index.js
@@ -0,0 +1,24 @@
+/**
+ * Copyright (c) <PERSON>.
+ * Copyright (c) Meta Platforms, Inc. and affiliates.
+ *
+ * This source code is licensed under the MIT license found in the
+ * LICENSE file in the root directory of this source tree.
+ *
+ * 
+ */
+
+
+var PermissionsAndroid = {
+    check() {
+       return new Promise(() => {
+
+       });
+    },
+    request() {
+        return new Promise(() => {
+
+        });
+    }    
+};
+export default PermissionsAndroid;
\ No newline at end of file
