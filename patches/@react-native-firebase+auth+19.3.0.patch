diff --git a/node_modules/@react-native-firebase/auth/android/src/main/java/io/invertase/firebase/auth/ReactNativeFirebaseAuthModule.java b/node_modules/@react-native-firebase/auth/android/src/main/java/io/invertase/firebase/auth/ReactNativeFirebaseAuthModule.java
index 9db4f9b..b4220fa 100644
--- a/node_modules/@react-native-firebase/auth/android/src/main/java/io/invertase/firebase/auth/ReactNativeFirebaseAuthModule.java
+++ b/node_modules/@react-native-firebase/auth/android/src/main/java/io/invertase/firebase/auth/ReactNativeFirebaseAuthModule.java
@@ -1617,7 +1617,8 @@ class ReactNativeFirebaseAuthModule extends ReactNativeFirebaseModule {
                     promiseWithAuthResult(task.getResult(), promise);
                   } else {
                     Exception exception = task.getException();
-                    if (exception instanceof FirebaseAuthUserCollisionException collEx) {
+                    if (exception instanceof FirebaseAuthUserCollisionException) {
+                      FirebaseAuthUserCollisionException collEx = (FirebaseAuthUserCollisionException) exception;
                       AuthCredential updatedCredential = collEx.getUpdatedCredential();
                       Log.d(TAG, "link:onComplete:collisionFailure", collEx);
                       // If we have a credential in the error, we can return it, otherwise fall
