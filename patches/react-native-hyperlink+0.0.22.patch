diff --git a/node_modules/react-native-hyperlink/src/Hyperlink.tsx b/node_modules/react-native-hyperlink/src/Hyperlink.tsx
index 891a97e..b3ac45a 100644
--- a/node_modules/react-native-hyperlink/src/Hyperlink.tsx
+++ b/node_modules/react-native-hyperlink/src/Hyperlink.tsx
@@ -74,10 +74,11 @@ class Hyperlink extends Component<HyperlinkProps, HyperlinkState> {
 
 		const componentProps = {
 			...component.props,
-			ref: undefined,
-			key: undefined,
 		};
 
+		delete componentProps.key;
+		delete componentProps.ref;
+
 		try {
 			this.state.linkifyIt
 				.match(component.props.children)
@@ -137,10 +138,11 @@ class Hyperlink extends Component<HyperlinkProps, HyperlinkState> {
 
 		const componentProps = {
 			...component.props,
-			ref: undefined,
-			key: undefined,
 		};
 
+		delete componentProps.key;
+		delete componentProps.ref;
+
 		return React.cloneElement(
 			component,
 			componentProps,
