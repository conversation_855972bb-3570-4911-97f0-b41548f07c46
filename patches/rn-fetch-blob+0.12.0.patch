diff --git a/node_modules/rn-fetch-blob/polyfill/Blob.js b/node_modules/rn-fetch-blob/polyfill/Blob.js
old mode 100644
new mode 100755
index 53662a7..379a7ad
--- a/node_modules/rn-fetch-blob/polyfill/Blob.js
+++ b/node_modules/rn-fetch-blob/polyfill/Blob.js
@@ -2,7 +2,9 @@
 // Use of this source code is governed by a MIT-style license that can be
 // found in the LICENSE file.
 
-import RNFetchBlob from '../index.js'
+// import RNFetchBlob from '../index.js'
+import {NativeModules} from 'react-native';
+const RNFetchBlob = NativeModules.RNFetchBlob
 import fs from '../fs.js'
 import getUUID from '../utils/uuid'
 import Log from '../utils/log.js'
diff --git a/node_modules/rn-fetch-blob/polyfill/Fetch.js b/node_modules/rn-fetch-blob/polyfill/Fetch.js
old mode 100644
new mode 100755
index 3ecb591..fbb0702
--- a/node_modules/rn-fetch-blob/polyfill/Fetch.js
+++ b/node_modules/rn-fetch-blob/polyfill/Fetch.js
@@ -1,4 +1,6 @@
-import RNFetchBlob from '../index.js'
+// import RNFetchBlob from '../index.js'
+import {NativeModules} from 'react-native';
+const RNFetchBlob = NativeModules.RNFetchBlob
 import Log from '../utils/log.js'
 import fs from '../fs'
 import unicode from '../utils/unicode'
diff --git a/node_modules/rn-fetch-blob/polyfill/XMLHttpRequest.js b/node_modules/rn-fetch-blob/polyfill/XMLHttpRequest.js
old mode 100644
new mode 100755
index 9036b2b..f1a99e4
--- a/node_modules/rn-fetch-blob/polyfill/XMLHttpRequest.js
+++ b/node_modules/rn-fetch-blob/polyfill/XMLHttpRequest.js
@@ -2,7 +2,9 @@
 // Use of this source code is governed by a MIT-style license that can be
 // found in the LICENSE file.
 
-import RNFetchBlob from '../index.js'
+// import RNFetchBlob from '../index.js'
+import {NativeModules} from 'react-native';
+const RNFetchBlob = NativeModules.RNFetchBlob
 import XMLHttpRequestEventTarget from './XMLHttpRequestEventTarget.js'
 import Log from '../utils/log.js'
 import Blob from './Blob.js'
