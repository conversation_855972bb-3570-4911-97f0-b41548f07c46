{"name": "ds<PERSON>yer", "version": "0.0.1", "private": true, "scripts": {"postinstall": "patch-package", "android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint .", "analyze:web": "source-map-explorer 'web/dist/*.js'", "build:web": "yarn set:env:release && rm -rf web/dist && webpack --mode=production --config web/webpack.config.js", "build:web:staging": "yarn set:env:staging && rm -rf web/dist && webpack --mode=production --config web/webpack.config.js", "set:env:debug": "node ./src/utils/config/env/set-env.js --env debug", "set:env:staging": "node ./src/utils/config/env/set-env.js --env staging", "set:env:release": "node ./src/utils/config/env/set-env.js --env release", "run:android:debug": "yarn set:env:debug && yarn android --mode debug", "run:android:staging": "yarn set:env:staging && yarn android --mode releaseStaging", "run:android:release": "yarn set:env:release && yarn android --mode release", "run:ios:debug": "yarn set:env:debug && yarn ios --mode Debug", "run:ios:staging": "yarn set:env:staging && yarn ios --mode Staging", "run:ios:release": "yarn set:env:release && yarn ios --mode Release", "run:web:debug": "yarn set:env:debug && webpack serve --mode=development --config web/webpack.config.js --port 3000"}, "dependencies": {"@gorhom/bottom-sheet": "^4", "@react-native-async-storage/async-storage": "^2.0.0", "@react-native-camera-roll/camera-roll": "^7.10.0", "@react-native-clipboard/clipboard": "^1.14.1", "@react-native-community/netinfo": "^9.3.0", "@react-native-community/push-notification-ios": "^1.10.1", "@react-native-firebase/analytics": "^19.3.0", "@react-native-firebase/app": "^19.3.0", "@react-native-firebase/auth": "^19.3.0", "@react-native-firebase/dynamic-links": "^19.3.0", "@react-native-firebase/messaging": "^19.3.0", "@react-navigation/bottom-tabs": "^7.1.3", "@react-navigation/elements": "^2.2.4", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.1.14", "@rneui/base": "^4.0.0-rc.6", "@rneui/themed": "^4.0.0-rc.6", "@sentry/react": "^7.14.0", "@sentry/react-native": "^6.10.0", "@sentry/tracing": "^7.14.0", "@shopify/flash-list": "^1.7.1", "@tanstack/query-async-storage-persister": "^4.10.3", "@tanstack/react-query": "^4.0.10", "@tanstack/react-query-devtools": "^4.0.10", "@tanstack/react-query-persist-client": "^4.10.3", "@types/linkify-it": "^3.0.2", "@types/lodash": "^4.14.182", "@types/react-native-push-notification": "^8.1.1", "@types/react-native-vector-icons": "^6.4.11", "@types/react-slick": "^0.23.10", "axios": "^0.24.0", "browser-image-compression": "^2.0.2", "buffer": "^6.0.3", "dayjs": "^1.11.4", "expo": "~52.0.0", "expo-image": "~2.0.7", "firebase": "^9.12.1", "fuse.js": "^6.6.2", "html-entities": "^2.6.0", "immer": "^9.0.15", "linkify-it": "^4.0.1", "lodash": "^4.17.21", "nanoid": "^4.0.1", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "query-string": "^7.1.1", "react": "18.3.1", "react-content-loader": "^6.2.0", "react-gtm-module": "^2.0.11", "react-helmet": "^6.1.0", "react-native": "0.76.9", "react-native-appstate-hook": "^1.0.6", "react-native-avoid-softinput": "^5.0.0", "react-native-bootsplash": "^6.1.3", "react-native-compressor": "^1.8.22", "react-native-confetti-cannon": "^1.5.2", "react-native-device-info": "^10.0.2", "react-native-document-picker": "^9.0.1", "react-native-dot-typing": "^1.0.2", "react-native-gesture-handler": "2.22.0", "react-native-get-random-values": "^1.8.0", "react-native-haptic-feedback": "^1.14.0", "react-native-hyperlink": "^0.0.22", "react-native-image-crop-picker": "^0.38.0", "react-native-image-picker": "^7.1.2", "react-native-image-zoom-viewer": "^3.0.1", "react-native-inappbrowser-reborn": "^3.7.0", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^14.0.0-rc.1", "react-native-otp-textinput": "sgtpsibin/react-native-otp-textinput", "react-native-pager-view": "^6.4.1", "react-native-permissions": "^4.1.4", "react-native-popup-menu": "^0.17.0", "react-native-progress": "^5.0.0", "react-native-push-notification": "^8.1.1", "react-native-rate": "^1.2.12", "react-native-ratings": "^8.1.0", "react-native-reanimated": "3.16.3", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "4.4.0", "react-native-share": "^11.0.2", "react-native-svg": "^15.12.0", "react-native-swiper-flatlist": "^3.0.17", "react-native-tab-view": "^3.1.1", "react-native-toast-notifications": "^3.3.1", "react-native-vector-icons": "^10.0.0", "react-native-video": "^6.8.2", "react-native-view-shot": "^3.8.0", "react-native-web": "^0.20.0", "react-native-web-linear-gradient": "^1.1.2", "react-native-webview": "^13.14.1", "react-number-format": "^5.1.2", "react-responsive": "^10.0.1", "react-slick": "^0.29.0", "rn-fetch-blob": "^0.12.0", "slick-carousel": "^1.8.1", "source-map-explorer": "^2.5.3", "yup": "^0.32.11", "zmp-sdk": "^2.28.0", "zustand": "^4.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/preset-env": "^7.25.3", "@babel/preset-typescript": "^7.18.6", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native-community/eslint-config": "^2.0.0", "@react-native/babel-preset": "0.76.9", "@react-native/eslint-config": "0.76.9", "@react-native/metro-config": "0.76.9", "@react-native/typescript-config": "0.76.9", "@svgr/webpack": "^6.3.1", "@tsconfig/react-native": "^3.0.0", "@types/jest": "^26.0.23", "@types/react": "18.2.39", "@types/react-dom": "^18.0.6", "@types/react-helmet": "^6.1.8", "@types/react-native": "^0.72.7", "@types/react-native-video": "^5.0.14", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.29.0", "@typescript-eslint/parser": "^5.29.0", "babel-jest": "^29.6.3", "babel-loader": "^10.0.0", "babel-plugin-module-resolver": "^4.1.0", "babel-plugin-react-native-web": "^0.20.0", "babel-plugin-transform-remove-console": "^6.9.4", "babel-polyfill": "^6.26.0", "clean-webpack-plugin": "^4.0.0", "css-loader": "^6.7.1", "eslint": "^8.19.0", "eslint-plugin-simple-import-sort": "^8.0.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.0", "jest": "^29.6.3", "metro-react-native-babel-preset": "^0.77.0", "prettier": "2.8.8", "react-dom": "^18.2.0", "react-native-flipper-zustand": "^2.0.1", "react-test-renderer": "18.3.1", "reactotron-react-native": "^5.1.8", "reactotron-react-query": "^1.0.4", "reflect-metadata": "^0.1.13", "style-loader": "^3.3.1", "terser-webpack-plugin": "^5.3.6", "typescript": "5.0.4", "url-loader": "^4.1.1", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}, "engines": {"node": ">=18"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}