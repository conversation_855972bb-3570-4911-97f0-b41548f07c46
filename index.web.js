import 'reflect-metadata';

import {AppRegistry} from 'react-native';
import {name as appName} from './app.json';
import App from './App';
import 'src/utils/helpers/noBounce';

if (module.hot) {
  module.hot.accept();
}

if (!window.APP_VERSION) {
  AppRegistry.registerComponent(appName, () => App);
  AppRegistry.runApplication(appName, {
    initialProps: {},
    rootTag: document.getElementById('app-root'),
  });
}

// Generate required css
import IoniconsFont from 'react-native-vector-icons/Fonts/Ionicons.ttf';
import MaterialCommunityIconsFont from 'react-native-vector-icons/Fonts/MaterialCommunityIcons.ttf';
import React from 'react';
import {createRoot} from 'react-dom/client';

const iconFontStyles = `@font-face {
  src: url(${IoniconsFont});
  font-family: Ionicons;
}
@font-face {
  src: url(${MaterialCommunityIconsFont});
  font-family: MaterialCommunityIcons;
}
`;

// Create stylesheet
const style = document.createElement('style');
style.type = 'text/css';
if (style.styleSheet) {
  style.styleSheet.cssText = iconFontStyles;
} else {
  style.appendChild(document.createTextNode(iconFontStyles));
}

// Inject stylesheet
document.head.appendChild(style);

const extractCSStoHTML = () => {
  if (typeof navigator != 'undefined' && (navigator.userAgent.includes('Prerender') || navigator.userAgent.includes('prerender'))) {
    var cssStyles = '';
    for (let i = 0; i < document.styleSheets.length - 1; i++) {
      let style = null;
      let styleSheet = document.styleSheets[i];
      if (styleSheet.href == null && styleSheet.ownerNode.textContent == '') {
        style = styleSheet.rules;
      }
      for (let item in style) {
        if (style[item].cssText != undefined) {
          cssStyles += style[item].cssText;
        }
      }
    }
    var head = document.head || document.getElementsByTagName('head')[0];
    // eslint-disable-next-line @typescript-eslint/no-shadow
    var style = document.getElementById('styles-for-prerender');
    if (style) {
      // eslint-disable-next-line radix
      style.setAttribute('iteration', parseInt(style.getAttribute('iteration')) + 1);
      while (style.firstChild) {
        style.removeChild(style.firstChild);
      }
    } else {
      style = document.createElement('style');
      style.setAttribute('iteration', '1');
      head.appendChild(style);
      style.id = 'styles-for-prerender';
      style.type = 'text/css';
    }
    style.appendChild(document.createTextNode(cssStyles));
  }
};

extractCSStoHTML();

if (window.APP_VERSION) {
  // for Zalo MiniApp
  // eslint-disable-next-line no-undef
  __webpack_public_path__ = `/zapps/3803906043616540845/${window.APP_VERSION}/`;
  const appElement = document.getElementById('app');
  if (appElement) {
    const resetCss = `
    html,
    body {
      width: 100%;
      height: 100%;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      font-size: 16px;
      line-height: 21px;
    }
    #app {
      display: flex;
      flex: 1 1 100%;
      height: 100%;
      margin: 0 auto;
    }
    `;
    // Create stylesheet
    // eslint-disable-next-line @typescript-eslint/no-shadow
    const style = document.createElement('style');
    style.type = 'text/css';
    if (style.styleSheet) {
      style.styleSheet.cssText = resetCss;
    } else {
      style.appendChild(document.createTextNode(resetCss));
    }

    document.head.appendChild(style);

    const root = createRoot(document.getElementById('app'));
    root.render(React.createElement(App));
  }
}
